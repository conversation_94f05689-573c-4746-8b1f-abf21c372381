using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Commands
{
    /// <summary>
    /// Handler for ClearCartCommand
    /// </summary>
    public class ClearCartCommandHandler : IRequestHandler<ClearCartCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<ClearCartCommandHandler> _logger;

        public ClearCartCommandHandler(
            ICartProcessService cartProcessService,
            ILogger<ClearCartCommandHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(ClearCartCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing clear cart command for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);

                // Validation
                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<NoContentDto>.BadRequest("Either Customer ID or Session ID is required");

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<NoContentDto>.BadRequest("Cannot specify both Customer ID and Session ID");

                // Delegate to process service
                var result = await _cartProcessService.ProcessClearCartAsync(
                    request.CustomerId,
                    request.SessionId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Clear cart command processed successfully for CustomerId: {CustomerId}, SessionId: {SessionId}",
                        request.CustomerId, request.SessionId);
                }
                else
                {
                    _logger.LogWarning("Clear cart command failed for CustomerId: {CustomerId}, SessionId: {SessionId}, Errors: {Errors}",
                        request.CustomerId, request.SessionId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing clear cart command for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while clearing cart");
            }
        }
    }
}
