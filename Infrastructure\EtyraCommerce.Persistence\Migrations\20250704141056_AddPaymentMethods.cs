﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentMethods : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "payment_methods",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    display_order = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    type = table.Column<int>(type: "integer", nullable: false),
                    fee_calculation_type = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    fee_value = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    fee_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    fee_currency_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    fee_currency_symbol = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    fee_currency_decimal_places = table.Column<int>(type: "integer", nullable: true, defaultValue: 2),
                    minimum_order_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    minimum_order_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    maximum_order_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    maximum_order_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    instructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_payment_methods", x => x.id);
                    table.CheckConstraint("ck_payment_methods_code_not_empty", "LENGTH(TRIM(code)) > 0");
                    table.CheckConstraint("ck_payment_methods_display_order_valid", "display_order >= 0");
                    table.CheckConstraint("ck_payment_methods_fee_value_valid", "fee_value >= -999999.9999 AND fee_value <= 999999.9999");
                    table.CheckConstraint("ck_payment_methods_minimum_maximum_order", "minimum_order_amount IS NULL OR maximum_order_amount IS NULL OR minimum_order_amount <= maximum_order_amount");
                    table.CheckConstraint("ck_payment_methods_name_not_empty", "LENGTH(TRIM(name)) > 0");
                },
                comment: "Payment methods available for orders");

            migrationBuilder.CreateIndex(
                name: "ix_payment_methods_code",
                schema: "etyra_core",
                table: "payment_methods",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_payment_methods_display_order",
                schema: "etyra_core",
                table: "payment_methods",
                column: "display_order");

            migrationBuilder.CreateIndex(
                name: "ix_payment_methods_is_active",
                schema: "etyra_core",
                table: "payment_methods",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "ix_payment_methods_type",
                schema: "etyra_core",
                table: "payment_methods",
                column: "type");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "payment_methods",
                schema: "etyra_core");
        }
    }
}
