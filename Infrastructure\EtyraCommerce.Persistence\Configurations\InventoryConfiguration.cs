using EtyraCommerce.Domain.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Inventory entity
    /// </summary>
    public class InventoryConfiguration : IEntityTypeConfiguration<Domain.Entities.Inventory.Inventory>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.Inventory.Inventory> builder)
        {
            // Table configuration
            builder.ToTable("Inventories", "etyra_inventory");

            // Primary key
            builder.HasKey(i => i.Id);

            // Properties
            builder.Property(i => i.ProductId)
                .IsRequired();

            builder.Property(i => i.WarehouseId)
                .IsRequired();

            builder.Property(i => i.AvailableQuantity)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.ReservedQuantity)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.AllocatedQuantity)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.MinStockLevel)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.MaxStockLevel)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.ReorderPoint)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.ReorderQuantity)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(i => i.LocationCode)
                .HasMaxLength(50);

            builder.Property(i => i.SupplierReference)
                .HasMaxLength(100);

            builder.Property(i => i.LeadTimeDays);

            builder.Property(i => i.LastStockUpdate);

            builder.Property(i => i.LastPhysicalCount);

            builder.Property(i => i.Status)
                .IsRequired()
                .HasConversion<int>()
                .HasDefaultValue(InventoryStatus.InStock);

            builder.Property(i => i.Notes)
                .HasMaxLength(1000);

            // Computed columns (read-only)
            builder.Ignore(i => i.TotalQuantity);
            builder.Ignore(i => i.FreeQuantity);
            builder.Ignore(i => i.IsLowStock);
            builder.Ignore(i => i.IsOutOfStock);
            builder.Ignore(i => i.IsOverstocked);

            // Indexes
            builder.HasIndex(i => i.ProductId)
                .HasDatabaseName("IX_Inventories_ProductId");

            builder.HasIndex(i => i.WarehouseId)
                .HasDatabaseName("IX_Inventories_WarehouseId");

            builder.HasIndex(i => new { i.ProductId, i.WarehouseId })
                .IsUnique()
                .HasDatabaseName("IX_Inventories_ProductId_WarehouseId");

            builder.HasIndex(i => i.Status)
                .HasDatabaseName("IX_Inventories_Status");

            builder.HasIndex(i => i.AvailableQuantity)
                .HasDatabaseName("IX_Inventories_AvailableQuantity");

            builder.HasIndex(i => i.ReorderPoint)
                .HasDatabaseName("IX_Inventories_ReorderPoint");

            builder.HasIndex(i => i.LastStockUpdate)
                .HasDatabaseName("IX_Inventories_LastStockUpdate");

            // Relationships
            builder.HasOne(i => i.Product)
                .WithMany()
                .HasForeignKey(i => i.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(i => i.Warehouse)
                .WithMany(w => w.InventoryItems)
                .HasForeignKey(i => i.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(i => i.Transactions)
                .WithOne(t => t.Inventory)
                .HasForeignKey(t => t.InventoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields (inherited from AuditableBaseEntity)
            builder.Property(i => i.CreatedAt)
                .IsRequired();

            builder.Property(i => i.UpdatedAt);

            builder.Property(i => i.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(i => i.DeletedAt);

            builder.Property(i => i.RowVersion)
                .IsRowVersion();

            // Global query filter for soft delete
            builder.HasQueryFilter(i => !i.IsDeleted);
        }
    }
}
