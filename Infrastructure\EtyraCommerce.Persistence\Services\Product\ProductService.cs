using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product;
using EtyraCommerce.Application.Services.Product.Commands;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Product
{
    /// <summary>
    /// Product service implementation for repository operations
    /// </summary>
    public class ProductService : IProductService
    {
        private readonly ILogger<ProductService> _logger;
        private readonly IMediator _mediator;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="logger">Logger</param>
        public ProductService(ILogger<ProductService> logger, IMediator mediator)
        {
            _logger = logger;
            _mediator = mediator;
        }

        #region Command Operations

        /// <summary>
        /// Creates a new product
        /// </summary>
        /// <param name="createDto">Product creation data</param>
        /// <returns>Created product DTO</returns>
        public async Task<CustomResponseDto<ProductDto>> CreateProductAsync(CreateProductDto createDto)
        {
            _logger.LogInformation("Creating product with Model: {Model}, Name: {Name}", createDto.Model,
                createDto.Name);

            var command = new CreateProductCommand
            {
                Model = createDto.Model,
                Name = createDto.Name,
                Stars = createDto.Stars,
                AiPlatformId = createDto.AiPlatformId,
                EAN = createDto.EAN,
                MPN = createDto.MPN,
                Barcode = createDto.Barcode,
                Brand = createDto.Brand,
                UPC = createDto.UPC,
                SKU = createDto.SKU,
                BasePrice = createDto.BasePrice,
                BasePriceCurrency = createDto.BasePriceCurrency,
                Cost = createDto.Cost,
                CostCurrency = createDto.CostCurrency,
                SalePrice = createDto.SalePrice,
                SalePriceCurrency = createDto.SalePriceCurrency,
                DefaultCurrency = createDto.DefaultCurrency,
                Dimensions = createDto.Dimensions,
                MainImage = createDto.MainImage,
                ThumbnailImage = createDto.ThumbnailImage,
                Status = createDto.Status,
                Type = createDto.Type,
                IsActive = createDto.IsActive,
                IsFeatured = createDto.IsFeatured,
                IsDigital = createDto.IsDigital,
                RequiresShipping = createDto.RequiresShipping,
                IsTaxable = createDto.IsTaxable,
                Slug = createDto.Slug,
                MetaTitle = createDto.MetaTitle,
                MetaDescription = createDto.MetaDescription,
                MetaKeywords = createDto.MetaKeywords,
                AvailableStartDate = createDto.AvailableStartDate,
                AvailableEndDate = createDto.AvailableEndDate,
                SaleStartDate = createDto.SaleStartDate,
                SaleEndDate = createDto.SaleEndDate,
                DiscontinueDate = createDto.DiscontinueDate,
                PrimaryCategoryId = createDto.PrimaryCategoryId,
                CategoryIds = createDto.CategoryIds,
                Descriptions = createDto.Descriptions,
                Images = createDto.Images,
                Attributes = createDto.Attributes
            };

            return await _mediator.Send(command);
        }

        /// <summary>
        /// Updates an existing product
        /// </summary>
        /// <param name="productId">Product ID to update</param>
        /// <param name="updateDto">Product update data</param>
        /// <returns>Updated product DTO</returns>
        public async Task<CustomResponseDto<ProductDto>> UpdateProductAsync(Guid productId, UpdateProductDto updateDto)
        {
            _logger.LogInformation("Updating product with ID: {ProductId}, Name: {Name}", productId, updateDto.Name);

            var command = new UpdateProductCommand
            {
                ProductId = productId,
                Name = updateDto.Name,
                Stars = updateDto.Stars,
                AiPlatformId = updateDto.AiPlatformId,
                EAN = updateDto.EAN,
                MPN = updateDto.MPN,
                Barcode = updateDto.Barcode,
                Brand = updateDto.Brand,
                UPC = updateDto.UPC,
                BasePrice = updateDto.BasePrice,
                BasePriceCurrency = updateDto.BasePriceCurrency,
                Cost = updateDto.Cost,
                CostCurrency = updateDto.CostCurrency,
                SalePrice = updateDto.SalePrice,
                SalePriceCurrency = updateDto.SalePriceCurrency,
                DefaultCurrency = updateDto.DefaultCurrency,
                Dimensions = updateDto.Dimensions,
                MainImage = updateDto.MainImage,
                ThumbnailImage = updateDto.ThumbnailImage,
                Status = updateDto.Status,
                Type = updateDto.Type,
                IsActive = updateDto.IsActive,
                IsFeatured = updateDto.IsFeatured,
                IsDigital = updateDto.IsDigital,
                RequiresShipping = updateDto.RequiresShipping,
                IsTaxable = updateDto.IsTaxable,
                Slug = updateDto.Slug,
                MetaTitle = updateDto.MetaTitle,
                MetaDescription = updateDto.MetaDescription,
                MetaKeywords = updateDto.MetaKeywords,
                AvailableStartDate = updateDto.AvailableStartDate,
                AvailableEndDate = updateDto.AvailableEndDate,
                SaleStartDate = updateDto.SaleStartDate,
                SaleEndDate = updateDto.SaleEndDate,
                DiscontinueDate = updateDto.DiscontinueDate,
                PrimaryCategoryId = updateDto.PrimaryCategoryId,
                CategoryIds = updateDto.CategoryIds,
                Descriptions = updateDto.Descriptions,
                Images = updateDto.Images,
                Attributes = updateDto.Attributes
            };

            return await _mediator.Send(command);
        }

        /// <summary>
        /// Deletes a product
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <param name="deletionReason">Reason for deletion</param>
        /// <returns>No content response</returns>
        public async Task<CustomResponseDto<NoContentDto>> DeleteProductAsync(Guid productId, bool hardDelete = false,
            bool forceDelete = false, string? deletionReason = null)
        {
            _logger.LogInformation(
                "Deleting product with ID: {ProductId}, HardDelete: {HardDelete}, ForceDelete: {ForceDelete}",
                productId, hardDelete, forceDelete);

            var command = new DeleteProductCommand(productId, hardDelete, forceDelete, deletionReason);
            return await _mediator.Send(command);
        }

        #endregion

        #region Query Operations

        /// <summary>
        /// Gets a product by ID
        /// </summary>
        public async Task<CustomResponseDto<ProductDto>> GetProductByIdAsync(
            Guid productId,
            bool includeDescriptions = false,
            bool includeImages = false,
            bool includeCategories = false,
            bool includeDiscounts = false,
            bool includeAttributes = false,
            bool includeVariants = false,
            bool includeInventory = false,
            bool includeDeleted = false,
            string? languageCode = null,
            Guid? storeId = null)
        {
            _logger.LogInformation("Getting product by ID: {ProductId}", productId);

            var query = new GetProductByIdQuery(
                productId,
                includeDescriptions,
                includeImages,
                includeCategories,
                includeDiscounts,
                includeAttributes,
                includeVariants,
                includeInventory,
                languageCode,
                storeId)
            {
                IncludeDeleted = includeDeleted
            };

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Gets all products with filtering and pagination
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetAllProductsAsync(ProductFilterDto filterDto)
        {
            _logger.LogInformation("Getting all products - Page: {Page}, PageSize: {PageSize}", filterDto.Page,
                filterDto.PageSize);

            var query = new GetAllProductsQuery(filterDto.Page, filterDto.PageSize)
            {
                Status = filterDto.Status,
                Type = filterDto.Type,
                IsActive = filterDto.IsActive,
                IsFeatured = filterDto.IsFeatured,
                IsDigital = filterDto.IsDigital,
                Brand = filterDto.Brand,
                CategoryId = filterDto.CategoryId,
                MinPrice = filterDto.MinPrice,
                MaxPrice = filterDto.MaxPrice,
                MinStock = filterDto.MinStock,
                MaxStock = filterDto.MaxStock,
                CreatedFrom = filterDto.CreatedFrom,
                CreatedTo = filterDto.CreatedTo,
                UpdatedFrom = filterDto.UpdatedFrom,
                UpdatedTo = filterDto.UpdatedTo,
                SearchTerm = filterDto.SearchTerm,
                SKU = filterDto.SKU,
                Model = filterDto.Model,
                EAN = filterDto.EAN,
                Barcode = filterDto.Barcode,
                SortBy = filterDto.SortBy,
                SortDirection = filterDto.SortDirection,
                IncludeDescriptions = filterDto.IncludeDescriptions,
                IncludeImages = filterDto.IncludeImages,
                IncludeCategories = filterDto.IncludeCategories,
                IncludeDiscounts = filterDto.IncludeDiscounts,
                IncludeAttributes = filterDto.IncludeAttributes,
                IncludeVariants = filterDto.IncludeVariants,
                IncludeInventory = filterDto.IncludeInventory,
                IncludeDeleted = filterDto.IncludeDeleted,
                LanguageCode = filterDto.LanguageCode,
                StoreId = filterDto.StoreId
            };

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Searches products with advanced filtering
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> SearchProductsAsync(ProductSearchDto searchDto)
        {
            _logger.LogInformation("Searching products with term: {SearchTerm}", searchDto.SearchTerm);

            var query = new SearchProductsQuery(searchDto.SearchTerm, searchDto.Page, searchDto.PageSize)
            {
                SearchInName = searchDto.SearchInName,
                SearchInDescription = searchDto.SearchInDescription,
                SearchInModel = searchDto.SearchInModel,
                SearchInSKU = searchDto.SearchInSKU,
                SearchInBrand = searchDto.SearchInBrand,
                SearchInTags = searchDto.SearchInTags,
                SearchInAttributes = searchDto.SearchInAttributes,
                CaseSensitive = searchDto.CaseSensitive,
                ExactMatch = searchDto.ExactMatch,
                CategoryIds = searchDto.CategoryIds,
                Brands = searchDto.Brands,
                Status = searchDto.Status,
                Type = searchDto.Type,
                IsActive = searchDto.IsActive,
                IsFeatured = searchDto.IsFeatured,
                IsDigital = searchDto.IsDigital,
                MinPrice = searchDto.MinPrice,
                MaxPrice = searchDto.MaxPrice,
                MinRating = searchDto.MinRating,
                MaxRating = searchDto.MaxRating,
                MinStock = searchDto.MinStock,
                MaxStock = searchDto.MaxStock,
                InStock = searchDto.InStock,
                OnSale = searchDto.OnSale,
                CreatedFrom = searchDto.CreatedFrom,
                CreatedTo = searchDto.CreatedTo,
                AttributeFilters = searchDto.AttributeFilters,
                Tags = searchDto.Tags,
                SortBy = searchDto.SortBy,
                SortDirection = searchDto.SortDirection,
                IncludeDescriptions = searchDto.IncludeDescriptions,
                IncludeImages = searchDto.IncludeImages,
                IncludeCategories = searchDto.IncludeCategories,
                IncludeDiscounts = searchDto.IncludeDiscounts,
                IncludeAttributes = searchDto.IncludeAttributes,
                IncludeVariants = searchDto.IncludeVariants,
                LanguageCode = searchDto.LanguageCode,
                StoreId = searchDto.StoreId
            };

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Gets products by category
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsByCategoryAsync(
            ProductCategoryFilterDto categoryFilterDto)
        {
            _logger.LogInformation("Getting products by category: {CategoryId}", categoryFilterDto.CategoryId);

            var query = new GetProductsByCategoryQuery(
                categoryFilterDto.CategoryId,
                categoryFilterDto.Page,
                categoryFilterDto.PageSize,
                categoryFilterDto.IncludeChildCategories)
            {
                Status = categoryFilterDto.Status,
                Type = categoryFilterDto.Type,
                IsActive = categoryFilterDto.IsActive,
                IsFeatured = categoryFilterDto.IsFeatured,
                IsDigital = categoryFilterDto.IsDigital,
                Brand = categoryFilterDto.Brand,
                MinPrice = categoryFilterDto.MinPrice,
                MaxPrice = categoryFilterDto.MaxPrice,
                MinRating = categoryFilterDto.MinRating,
                MaxRating = categoryFilterDto.MaxRating,
                MinStock = categoryFilterDto.MinStock,
                MaxStock = categoryFilterDto.MaxStock,
                InStock = categoryFilterDto.InStock,
                OnSale = categoryFilterDto.OnSale,
                SearchTerm = categoryFilterDto.SearchTerm,
                Brands = categoryFilterDto.Brands,
                AttributeFilters = categoryFilterDto.AttributeFilters,
                Tags = categoryFilterDto.Tags,
                SortBy = categoryFilterDto.SortBy,
                SortDirection = categoryFilterDto.SortDirection,
                IncludeDescriptions = categoryFilterDto.IncludeDescriptions,
                IncludeImages = categoryFilterDto.IncludeImages,
                IncludeCategories = categoryFilterDto.IncludeCategories,
                IncludeDiscounts = categoryFilterDto.IncludeDiscounts,
                IncludeAttributes = categoryFilterDto.IncludeAttributes,
                IncludeVariants = categoryFilterDto.IncludeVariants,
                IncludeInventory = categoryFilterDto.IncludeInventory,
                LanguageCode = categoryFilterDto.LanguageCode,
                StoreId = categoryFilterDto.StoreId
            };

            return await _mediator.Send(query);
        }

        #endregion

        #region Convenience Methods

        /// <summary>
        /// Gets active products with basic info
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetActiveProductsAsync(int page = 1,
            int pageSize = 20, string? languageCode = null)
        {
            _logger.LogInformation("Getting active products - Page: {Page}, PageSize: {PageSize}", page, pageSize);

            var query = GetAllProductsQuery.ActiveProducts(page, pageSize);
            query.LanguageCode = languageCode;
            query.IncludeImages = true;
            query.IncludeCategories = true;

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Gets featured products
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetFeaturedProductsAsync(int page = 1,
            int pageSize = 20, string? languageCode = null)
        {
            _logger.LogInformation("Getting featured products - Page: {Page}, PageSize: {PageSize}", page, pageSize);

            var query = GetAllProductsQuery.FeaturedProducts(page, pageSize);
            query.LanguageCode = languageCode;
            query.IncludeImages = true;
            query.IncludeCategories = true;

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Gets products on sale
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsOnSaleAsync(int page = 1,
            int pageSize = 20, string? languageCode = null)
        {
            _logger.LogInformation("Getting products on sale - Page: {Page}, PageSize: {PageSize}", page, pageSize);

            var filterDto = new ProductFilterDto
            {
                Page = page,
                PageSize = pageSize,
                IsActive = true,
                Status = Domain.Entities.Product.ProductStatus.Published,
                LanguageCode = languageCode,
                IncludeImages = true,
                IncludeCategories = true,
                IncludeDiscounts = true
            };

            // Add custom logic to filter products on sale
            var query = new GetAllProductsQuery(page, pageSize)
            {
                IsActive = true,
                Status = Domain.Entities.Product.ProductStatus.Published,
                LanguageCode = languageCode,
                IncludeImages = true,
                IncludeCategories = true,
                IncludeDiscounts = true
            };

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Gets products by brand
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsByBrandAsync(string brand,
            int page = 1, int pageSize = 20, string? languageCode = null)
        {
            _logger.LogInformation("Getting products by brand: {Brand} - Page: {Page}, PageSize: {PageSize}", brand,
                page, pageSize);

            var query = new GetAllProductsQuery(page, pageSize)
            {
                Brand = brand,
                IsActive = true,
                Status = Domain.Entities.Product.ProductStatus.Published,
                LanguageCode = languageCode,
                IncludeImages = true,
                IncludeCategories = true
            };

            return await _mediator.Send(query);
        }

        /// <summary>
        /// Quick search for products
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> QuickSearchAsync(string searchTerm, int page = 1,
            int pageSize = 20, string? languageCode = null)
        {
            _logger.LogInformation(
                "Quick search for products with term: {SearchTerm} - Page: {Page}, PageSize: {PageSize}", searchTerm,
                page, pageSize);

            var query = SearchProductsQuery.QuickSearch(searchTerm, page, pageSize);
            query.LanguageCode = languageCode;

            return await _mediator.Send(query);
        }

        #endregion
    }
}
