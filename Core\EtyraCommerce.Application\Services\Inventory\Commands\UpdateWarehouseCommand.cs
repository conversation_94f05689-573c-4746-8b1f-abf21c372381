using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Domain.Entities.Inventory;
using EtyraCommerce.Domain.ValueObjects;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to update a warehouse
    /// </summary>
    public class UpdateWarehouseCommand : IRequest<CustomResponseDto<WarehouseDto>>
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Address? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; }
        public bool IsMain { get; set; }
        public WarehouseType Type { get; set; }
        public int SortOrder { get; set; }
    }
}
