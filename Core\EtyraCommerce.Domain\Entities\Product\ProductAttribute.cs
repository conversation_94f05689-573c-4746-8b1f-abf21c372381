namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product attribute entity for additional product properties
    /// </summary>
    public class ProductAttribute : BaseEntity
    {
        /// <summary>
        /// Attribute name (e.g., "Material", "Brand", "Warranty")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Attribute value (e.g., "Cotton", "Nike", "2 Years")
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Attribute type
        /// </summary>
        public AttributeType Type { get; set; } = AttributeType.Text;

        /// <summary>
        /// Attribute group (for organizing attributes)
        /// </summary>
        public string? Group { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether attribute is visible to customers
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// Whether attribute is searchable
        /// </summary>
        public bool IsSearchable { get; set; } = false;

        /// <summary>
        /// Whether attribute can be used for filtering
        /// </summary>
        public bool IsFilterable { get; set; } = false;

        /// <summary>
        /// Whether attribute is required
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Unit of measurement (e.g., "cm", "kg", "pieces")
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// Attribute description/help text
        /// </summary>
        public string? Description { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets formatted value with unit
        /// </summary>
        public string FormattedValue
        {
            get
            {
                if (string.IsNullOrEmpty(Unit))
                    return Value;
                return $"{Value} {Unit}";
            }
        }

        /// <summary>
        /// Gets display name (Name with group if available)
        /// </summary>
        public string DisplayName
        {
            get
            {
                if (string.IsNullOrEmpty(Group))
                    return Name;
                return $"{Group} - {Name}";
            }
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates attribute value
        /// </summary>
        public void UpdateValue(string newValue, string? unit = null)
        {
            if (string.IsNullOrWhiteSpace(newValue))
                throw new ArgumentException("Attribute value cannot be empty");

            Value = newValue.Trim();
            if (unit != null) Unit = unit.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates visibility settings
        /// </summary>
        public void UpdateVisibility(bool isVisible, bool isSearchable = false, bool isFilterable = false)
        {
            IsVisible = isVisible;
            IsSearchable = isSearchable;
            IsFilterable = isFilterable;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int order)
        {
            SortOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets as required
        /// </summary>
        public void SetRequired(bool required = true)
        {
            IsRequired = required;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates attribute group
        /// </summary>
        public void UpdateGroup(string? group)
        {
            Group = group?.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Validates attribute value based on type
        /// </summary>
        public bool IsValidValue()
        {
            if (string.IsNullOrWhiteSpace(Value))
                return !IsRequired;

            return Type switch
            {
                AttributeType.Number => decimal.TryParse(Value, out _),
                AttributeType.Boolean => bool.TryParse(Value, out _),
                AttributeType.Date => DateTime.TryParse(Value, out _),
                AttributeType.Email => IsValidEmail(Value),
                AttributeType.Url => IsValidUrl(Value),
                AttributeType.Color => IsValidColor(Value),
                _ => true // Text, TextArea, Select
            };
        }

        /// <summary>
        /// Gets typed value based on attribute type
        /// </summary>
        public object? GetTypedValue()
        {
            if (string.IsNullOrWhiteSpace(Value))
                return null;

            return Type switch
            {
                AttributeType.Number => decimal.TryParse(Value, out var number) ? number : null,
                AttributeType.Boolean => bool.TryParse(Value, out var boolean) ? boolean : null,
                AttributeType.Date => DateTime.TryParse(Value, out var date) ? date : null,
                _ => Value
            };
        }

        #endregion

        #region Private Helper Methods

        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        private static bool IsValidColor(string color)
        {
            // Simple validation for hex colors or color names
            if (color.StartsWith('#') && color.Length == 7)
            {
                return color[1..].All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
            }

            // Common color names
            var colorNames = new[] { "red", "green", "blue", "yellow", "orange", "purple", "pink", "brown", "black", "white", "gray", "grey" };
            return colorNames.Contains(color.ToLowerInvariant());
        }

        #endregion

        public override string ToString()
        {
            return $"ProductAttribute [Name: {Name}, Value: {FormattedValue}, Type: {Type}]";
        }
    }

    /// <summary>
    /// Attribute type enumeration
    /// </summary>
    public enum AttributeType
    {
        Text = 0,
        TextArea = 1,
        Number = 2,
        Boolean = 3,
        Date = 4,
        Select = 5,
        Email = 6,
        Url = 7,
        Color = 8
    }
}
