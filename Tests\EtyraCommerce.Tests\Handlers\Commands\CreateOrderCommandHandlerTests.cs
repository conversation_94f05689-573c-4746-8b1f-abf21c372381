using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Application.Services.Order.Handlers.Commands;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class CreateOrderCommandHandlerTests
    {
        private readonly Mock<IOrderProcessService> _mockOrderProcessService;
        private readonly Mock<ILogger<CreateOrderCommandHandler>> _mockLogger;
        private readonly CreateOrderCommandHandler _handler;

        public CreateOrderCommandHandlerTests()
        {
            _mockOrderProcessService = new Mock<IOrderProcessService>();
            _mockLogger = new Mock<ILogger<CreateOrderCommandHandler>>();
            _handler = new CreateOrderCommandHandler(_mockOrderProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessWithOrderDto()
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                ShippingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto
                    {
                        ProductId = Guid.NewGuid(),
                        Quantity = 2
                    }
                },
                Currency = "USD"
            };

            var expectedOrderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                OrderNumber = "ORD-20241201-ABC123",
                CustomerId = command.CustomerId,
                CustomerEmail = command.CustomerEmail,
                CustomerFirstName = command.CustomerFirstName,
                CustomerLastName = command.CustomerLastName,
                Total = 100.00m,
                Currency = "USD"
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Success(201, expectedOrderDto, "Order created successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(201);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be(expectedOrderDto.Id);
            result.Data.OrderNumber.Should().Be(expectedOrderDto.OrderNumber);
            result.Data.CustomerId.Should().Be(command.CustomerId);
            result.Data.CustomerEmail.Should().Be(command.CustomerEmail);

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.Is<CreateOrderDto>(dto =>
                    dto.CustomerId == command.CustomerId &&
                    dto.CustomerEmail == command.CustomerEmail &&
                    dto.CustomerFirstName == command.CustomerFirstName &&
                    dto.CustomerLastName == command.CustomerLastName &&
                    dto.Currency == command.Currency &&
                    dto.OrderItems.Count == command.OrderItems.Count
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyCustomerId_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.Empty,
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>(),
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Customer ID is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Never
            );
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        public async Task Handle_InvalidCustomerEmail_ReturnsBadRequest(string email)
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = email,
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>(),
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Customer email is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Never
            );
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        public async Task Handle_InvalidCustomerFirstName_ReturnsBadRequest(string firstName)
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = firstName,
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>(),
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Customer first name is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_NullBillingAddress_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = null!,
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>(),
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Billing address is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_EmptyOrderItems_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>(),
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Order must contain at least one item");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                ShippingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto { ProductId = Guid.NewGuid(), Quantity = 1 }
                },
                Currency = "USD"
            };

            _mockOrderProcessService
                .Setup(x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Be("An error occurred while creating the order");

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()),
                Times.Once
            );
        }

        [Theory]
        [InlineData("USD")]
        [InlineData("EUR")]
        [InlineData("GBP")]
        [InlineData("TRY")]
        public async Task Handle_VariousCurrencies_CallsProcessServiceWithCorrectCurrency(string currency)
        {
            // Arrange
            var command = new CreateOrderCommand
            {
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                ShippingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto { ProductId = Guid.NewGuid(), Quantity = 1 }
                },
                Currency = currency
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Success(201, new OrderDto(), "Order created successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCreateOrderAsync(It.IsAny<CreateOrderDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessCreateOrderAsync(It.Is<CreateOrderDto>(dto => dto.Currency == currency)),
                Times.Once
            );
        }

        #endregion
    }
}
