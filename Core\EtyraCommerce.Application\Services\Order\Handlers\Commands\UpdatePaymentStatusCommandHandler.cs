using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Commands
{
    /// <summary>
    /// Handler for UpdatePaymentStatusCommand
    /// </summary>
    public class UpdatePaymentStatusCommandHandler : IRequestHandler<UpdatePaymentStatusCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<UpdatePaymentStatusCommandHandler> _logger;

        public UpdatePaymentStatusCommandHandler(
            IOrderProcessService orderProcessService,
            ILogger<UpdatePaymentStatusCommandHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(UpdatePaymentStatusCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing update payment status command for order: {OrderId} to status: {PaymentStatus}",
                    request.OrderId, request.PaymentStatus);

                // Validation
                if (request.OrderId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Order ID is required");

                if (!Enum.IsDefined(typeof(PaymentStatus), request.PaymentStatus))
                    return CustomResponseDto<NoContentDto>.BadRequest("Invalid payment status");

                // Delegate to process service
                var result = await _orderProcessService.ProcessUpdatePaymentStatusAsync(
                    request.OrderId,
                    request.PaymentStatus,
                    request.PaymentMethod,
                    request.PaymentReference,
                    request.Notes);

                _logger.LogInformation("Update payment status command processed successfully for order: {OrderId}", request.OrderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update payment status command for order: {OrderId}", request.OrderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the payment status");
            }
        }
    }
}
