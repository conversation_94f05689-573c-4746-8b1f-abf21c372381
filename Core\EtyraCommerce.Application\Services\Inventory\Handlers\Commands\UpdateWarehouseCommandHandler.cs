using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for updating warehouses
    /// </summary>
    public class UpdateWarehouseCommandHandler : IRequestHandler<UpdateWarehouseCommand, CustomResponseDto<WarehouseDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<UpdateWarehouseCommandHandler> _logger;

        public UpdateWarehouseCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<UpdateWarehouseCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<WarehouseDto>> Handle(UpdateWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling update warehouse command for ID: {WarehouseId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse ID is required");

                if (string.IsNullOrEmpty(request.Name))
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse name is required");

                if (request.Name.Length > 100)
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse name cannot exceed 100 characters");

                // Create DTO
                var updateDto = new UpdateWarehouseDto
                {
                    Id = request.Id,
                    Name = request.Name,
                    Description = request.Description,
                    Address = request.Address,
                    Phone = request.Phone,
                    Email = request.Email,
                    ManagerName = request.ManagerName,
                    IsActive = request.IsActive,
                    IsMain = request.IsMain,
                    Type = request.Type,
                    SortOrder = request.SortOrder
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessUpdateWarehouseAsync(updateDto);

                _logger.LogInformation("Update warehouse command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling update warehouse command for ID: {WarehouseId}", request.Id);
                return CustomResponseDto<WarehouseDto>.InternalServerError("An error occurred while updating warehouse");
            }
        }
    }
}
