using EtyraCommerce.Application.Repositories.Currency;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Currency;

/// <summary>
/// Read repository implementation for Currency entity
/// </summary>
public class CurrencyReadRepository : ReadRepository<Domain.Entities.Currency.Currency>, ICurrencyReadRepository
{
    public CurrencyReadRepository(EtyraCommerceDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get currency by code
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency or null</returns>
    public async Task<Domain.Entities.Currency.Currency?> GetByCodeAsync(string code, bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .FirstOrDefaultAsync(c => c.Code == code);
    }

    /// <summary>
    /// Get active currencies ordered by display order
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies</returns>
    public async Task<List<Domain.Entities.Currency.Currency>> GetActiveCurrenciesAsync(bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Where(c => c.IsActive)
            .OrderBy(c => c.DisplayOrder)
            .ThenBy(c => c.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Get default currency
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Default currency or null</returns>
    public async Task<Domain.Entities.Currency.Currency?> GetDefaultCurrencyAsync(bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .FirstOrDefaultAsync(c => c.IsDefault && c.IsActive);
    }

    /// <summary>
    /// Check if currency code exists
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    public async Task<bool> CodeExistsAsync(string code, Guid? excludeId = null)
    {
        var query = Table.Where(c => c.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// Check if currency is used in orders
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    public async Task<bool> IsUsedInOrdersAsync(Guid currencyId)
    {
        // Check if any orders use this currency
        return await _context.Orders
            .AnyAsync(o => o.TotalAmount.Currency.Code == 
                Table.Where(c => c.Id == currencyId).Select(c => c.Code).FirstOrDefault());
    }

    /// <summary>
    /// Check if currency is used in products
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    public async Task<bool> IsUsedInProductsAsync(Guid currencyId)
    {
        // Check if any products use this currency
        return await _context.Products
            .AnyAsync(p => p.Price.Currency.Code == 
                Table.Where(c => c.Id == currencyId).Select(c => c.Code).FirstOrDefault());
    }

    /// <summary>
    /// Check if currency is used in payment methods
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    public async Task<bool> IsUsedInPaymentMethodsAsync(Guid currencyId)
    {
        var currencyCode = await Table
            .Where(c => c.Id == currencyId)
            .Select(c => c.Code)
            .FirstOrDefaultAsync();

        if (currencyCode == null) return false;

        // Check if any payment methods use this currency in their amount limits
        return await _context.PaymentMethods
            .AnyAsync(pm => 
                (pm.MinimumOrderAmount != null && pm.MinimumOrderAmount.Currency.Code == currencyCode) ||
                (pm.MaximumOrderAmount != null && pm.MaximumOrderAmount.Currency.Code == currencyCode) ||
                (pm.FixedFee != null && pm.FixedFee.Currency.Code == currencyCode));
    }

    /// <summary>
    /// Check if currency can be deleted (not used anywhere)
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if can be deleted</returns>
    public async Task<bool> CanDeleteAsync(Guid currencyId)
    {
        // Currency cannot be deleted if it's the default currency
        var currency = await Table.FirstOrDefaultAsync(c => c.Id == currencyId);
        if (currency?.IsDefault == true)
        {
            return false;
        }

        // Check if currency is used anywhere
        var isUsedInOrders = await IsUsedInOrdersAsync(currencyId);
        var isUsedInProducts = await IsUsedInProductsAsync(currencyId);
        var isUsedInPaymentMethods = await IsUsedInPaymentMethodsAsync(currencyId);

        return !isUsedInOrders && !isUsedInProducts && !isUsedInPaymentMethods;
    }
}
