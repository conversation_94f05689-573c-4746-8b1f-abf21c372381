using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;

namespace EtyraCommerce.Application.Services.Currency;

/// <summary>
/// Interface for currency process service operations
/// </summary>
public interface ICurrencyProcessService
{
    #region Command Operations

    /// <summary>
    /// Process create currency operation
    /// </summary>
    /// <param name="createDto">Create currency DTO</param>
    /// <returns>Created currency</returns>
    Task<CustomResponseDto<CurrencyDto>> ProcessCreateAsync(CreateCurrencyDto createDto);

    /// <summary>
    /// Process update currency operation
    /// </summary>
    /// <param name="updateDto">Update currency DTO</param>
    /// <returns>Updated currency</returns>
    Task<CustomResponseDto<CurrencyDto>> ProcessUpdateAsync(UpdateCurrencyDto updateDto);

    /// <summary>
    /// Process delete currency operation
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>Delete operation result</returns>
    Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id);

    /// <summary>
    /// Process set default currency operation
    /// </summary>
    /// <param name="id">Currency ID to set as default</param>
    /// <returns>Updated default currency</returns>
    Task<CustomResponseDto<CurrencyDto>> ProcessSetDefaultAsync(Guid id);

    #endregion

    #region Query Operations

    /// <summary>
    /// Get all currencies
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of currencies</returns>
    Task<CustomResponseDto<List<CurrencyDto>>> GetAllAsync(bool tracking = false);

    /// <summary>
    /// Get active currencies
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies</returns>
    Task<CustomResponseDto<List<CurrencyDto>>> GetActiveCurrenciesAsync(bool tracking = false);

    /// <summary>
    /// Get active currencies summary
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies summary</returns>
    Task<CustomResponseDto<List<CurrencySummaryDto>>> GetActiveCurrenciesSummaryAsync(bool tracking = false);

    /// <summary>
    /// Get currency by ID
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency details</returns>
    Task<CustomResponseDto<CurrencyDto>> GetByIdAsync(Guid id, bool tracking = false);

    /// <summary>
    /// Get currency by code
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency details</returns>
    Task<CustomResponseDto<CurrencyDto>> GetByCodeAsync(string code, bool tracking = false);

    /// <summary>
    /// Get default currency
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Default currency</returns>
    Task<CustomResponseDto<CurrencyDto>> GetDefaultCurrencyAsync(bool tracking = false);

    #endregion

    #region Validation Operations

    /// <summary>
    /// Check if currency code exists
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="excludeId">Currency ID to exclude from check</param>
    /// <returns>True if exists</returns>
    Task<bool> CurrencyCodeExistsAsync(string code, Guid? excludeId = null);

    /// <summary>
    /// Check if currency can be deleted
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>True if can be deleted</returns>
    Task<bool> CanDeleteCurrencyAsync(Guid id);

    #endregion
}
