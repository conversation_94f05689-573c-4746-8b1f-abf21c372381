

namespace EtyraCommerce.Domain.Entities.Currency
{
    /// <summary>
    /// Exchange rate entity for currency conversion
    /// </summary>
    public class ExchangeRate : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Base currency ID (from currency)
        /// </summary>
        public Guid FromCurrencyId { get; set; }

        /// <summary>
        /// Target currency ID (to currency)
        /// </summary>
        public Guid ToCurrencyId { get; set; }

        /// <summary>
        /// Exchange rate value (1 FromCurrency = Rate * ToCurrency)
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// Date when this rate is effective from
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Date when this rate expires (optional)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Whether this rate is currently active
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Source of the exchange rate (Manual, API, Bank, etc.)
        /// </summary>
        public ExchangeRateSource Source { get; set; } = ExchangeRateSource.Manual;

        /// <summary>
        /// External reference ID (for API sources)
        /// </summary>
        public string? ExternalReferenceId { get; set; }

        /// <summary>
        /// Additional notes about this rate
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// User who created/updated this rate
        /// </summary>
        public Guid? CreatedByUserId { get; set; }

        /// <summary>
        /// Timestamp when rate was last updated from external source
        /// </summary>
        public DateTime? LastUpdatedFromSource { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Base currency (from)
        /// </summary>
        public Currency FromCurrency { get; set; } = null!;

        /// <summary>
        /// Target currency (to)
        /// </summary>
        public Currency ToCurrency { get; set; } = null!;

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public ExchangeRate()
        {
        }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public ExchangeRate(Guid fromCurrencyId, Guid toCurrencyId, decimal rate, DateTime effectiveDate)
        {
            FromCurrencyId = fromCurrencyId;
            ToCurrencyId = toCurrencyId;
            Rate = rate;
            EffectiveDate = effectiveDate;
            IsActive = true;
        }

        /// <summary>
        /// Constructor with currency entities
        /// </summary>
        public ExchangeRate(Currency fromCurrency, Currency toCurrency, decimal rate, DateTime effectiveDate)
        {
            FromCurrency = fromCurrency ?? throw new ArgumentNullException(nameof(fromCurrency));
            ToCurrency = toCurrency ?? throw new ArgumentNullException(nameof(toCurrency));
            FromCurrencyId = fromCurrency.Id;
            ToCurrencyId = toCurrency.Id;
            Rate = rate;
            EffectiveDate = effectiveDate;
            IsActive = true;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Converts amount from base currency to target currency
        /// </summary>
        public decimal ConvertAmount(decimal amount)
        {
            if (!IsActive)
                throw new InvalidOperationException("Cannot use inactive exchange rate for conversion");

            if (IsExpired())
                throw new InvalidOperationException("Cannot use expired exchange rate for conversion");

            return Math.Round(amount * Rate, ToCurrency?.DecimalPlaces ?? 2);
        }

        /// <summary>
        /// Gets the inverse rate (ToCurrency to FromCurrency)
        /// </summary>
        public decimal GetInverseRate()
        {
            if (Rate == 0)
                throw new InvalidOperationException("Cannot calculate inverse of zero rate");

            return Math.Round(1 / Rate, FromCurrency?.DecimalPlaces ?? 4);
        }

        /// <summary>
        /// Checks if the exchange rate is currently valid
        /// </summary>
        public bool IsCurrentlyValid()
        {
            var now = DateTime.UtcNow;
            return IsActive && 
                   EffectiveDate <= now && 
                   (ExpiryDate == null || ExpiryDate > now);
        }

        /// <summary>
        /// Checks if the exchange rate is expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow;
        }

        /// <summary>
        /// Activates the exchange rate
        /// </summary>
        public void Activate()
        {
            IsActive = true;
        }

        /// <summary>
        /// Deactivates the exchange rate
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
        }

        /// <summary>
        /// Updates the exchange rate
        /// </summary>
        public void UpdateRate(decimal newRate, ExchangeRateSource source = ExchangeRateSource.Manual, 
            string? notes = null, Guid? updatedByUserId = null)
        {
            if (newRate <= 0)
                throw new ArgumentException("Exchange rate must be positive", nameof(newRate));

            Rate = newRate;
            Source = source;
            Notes = notes;
            CreatedByUserId = updatedByUserId;
            
            if (source != ExchangeRateSource.Manual)
            {
                LastUpdatedFromSource = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Sets expiry date for the exchange rate
        /// </summary>
        public void SetExpiryDate(DateTime? expiryDate)
        {
            if (expiryDate.HasValue && expiryDate.Value <= EffectiveDate)
                throw new ArgumentException("Expiry date must be after effective date");

            ExpiryDate = expiryDate;
        }

        /// <summary>
        /// Updates external reference information
        /// </summary>
        public void UpdateExternalReference(string? externalReferenceId)
        {
            ExternalReferenceId = externalReferenceId;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates exchange rate data
        /// </summary>
        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (FromCurrencyId == Guid.Empty)
                errors.Add("From currency is required");

            if (ToCurrencyId == Guid.Empty)
                errors.Add("To currency is required");

            if (FromCurrencyId == ToCurrencyId)
                errors.Add("From and To currencies cannot be the same");

            if (Rate <= 0)
                errors.Add("Exchange rate must be positive");

            if (EffectiveDate == default)
                errors.Add("Effective date is required");

            if (ExpiryDate.HasValue && ExpiryDate.Value <= EffectiveDate)
                errors.Add("Expiry date must be after effective date");

            return errors.Count == 0;
        }

        #endregion
    }

    /// <summary>
    /// Source of exchange rate data
    /// </summary>
    public enum ExchangeRateSource
    {
        /// <summary>
        /// Manually entered by user
        /// </summary>
        Manual = 0,

        /// <summary>
        /// Retrieved from external API
        /// </summary>
        API = 1,

        /// <summary>
        /// Retrieved from central bank
        /// </summary>
        CentralBank = 2,

        /// <summary>
        /// Retrieved from financial data provider
        /// </summary>
        FinancialProvider = 3,

        /// <summary>
        /// System calculated/estimated
        /// </summary>
        System = 4
    }
}
