using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using MediatR;

namespace EtyraCommerce.Application.Services.Payment.Queries;

/// <summary>
/// Query to get a payment method by code
/// </summary>
public class GetPaymentMethodByCodeQuery : IRequest<CustomResponseDto<PaymentMethodDto>>
{
    /// <summary>
    /// Payment method code
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Whether to track changes for the entity
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetPaymentMethodByCodeQuery(string code, bool tracking = false)
    {
        Code = code;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetPaymentMethodByCodeQuery() { }
}
