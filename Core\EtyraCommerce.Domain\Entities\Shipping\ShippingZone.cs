using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Domain.Enums.Shipping;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Shipping zone entity for European shipping system
    /// Represents shipping zones for rate calculation and carrier assignment
    /// </summary>
    public class ShippingZone : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Zone name (e.g., "Romania Domestic", "EU Zone 1", "EU Zone 2")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Zone code (e.g., "RO_DOM", "EU_Z1", "EU_Z2")
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Zone type for categorization
        /// </summary>
        public ZoneType Type { get; set; } = ZoneType.Domestic;

        /// <summary>
        /// Zone description
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Display order for zone selection lists
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Indicates if this zone is active for shipping
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Default delivery time in days (minimum)
        /// </summary>
        public int DefaultMinDeliveryDays { get; set; } = 1;

        /// <summary>
        /// Default delivery time in days (maximum)
        /// </summary>
        public int DefaultMaxDeliveryDays { get; set; } = 7;

        /// <summary>
        /// Additional notes for this shipping zone
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Countries included in this shipping zone
        /// </summary>
        public virtual ICollection<ShippingZoneCountry> Countries { get; set; } = new List<ShippingZoneCountry>();

        /// <summary>
        /// Specific locations included in this shipping zone
        /// </summary>
        public virtual ICollection<ShippingZoneLocation> Locations { get; set; } = new List<ShippingZoneLocation>();

        /// <summary>
        /// Shipping methods available for this zone
        /// </summary>
        public virtual ICollection<ShippingMethod> ShippingMethods { get; set; } = new List<ShippingMethod>();

        /// <summary>
        /// Shipping rates for this zone
        /// </summary>
        public virtual ICollection<ShippingRate> ShippingRates { get; set; } = new List<ShippingRate>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected ShippingZone() { }

        /// <summary>
        /// Constructor for creating a new shipping zone
        /// </summary>
        public ShippingZone(string name, string code, ZoneType type, string? description = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Code = code?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(code));
            Type = type;
            Description = string.IsNullOrWhiteSpace(description) ? null : description.Trim();
            IsActive = true;
            DisplayOrder = 0;
            SetDefaultDeliveryTimes(type);
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Activates the shipping zone
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the shipping zone
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates zone information
        /// </summary>
        public void UpdateInfo(string name, string description = null, string notes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Description = string.IsNullOrWhiteSpace(description) ? null : description.Trim();
            Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets delivery time range
        /// </summary>
        public void SetDeliveryTimes(int minDays, int maxDays)
        {
            if (minDays < 0 || maxDays < 0)
                throw new ArgumentException("Delivery days cannot be negative");
            
            if (minDays > maxDays)
                throw new ArgumentException("Minimum delivery days cannot be greater than maximum");

            DefaultMinDeliveryDays = minDays;
            DefaultMaxDeliveryDays = maxDays;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for zone lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets default delivery times based on zone type
        /// </summary>
        private void SetDefaultDeliveryTimes(ZoneType zoneType)
        {
            (DefaultMinDeliveryDays, DefaultMaxDeliveryDays) = zoneType switch
            {
                ZoneType.Domestic => (1, 3),
                ZoneType.EuZone1 => (2, 5),
                ZoneType.EuZone2 => (3, 7),
                ZoneType.International => (5, 14),
                _ => (1, 7)
            };
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets full zone display name with type
        /// </summary>
        public string GetDisplayName() => $"{Name} ({GetTypeDisplayText()})";

        /// <summary>
        /// Gets zone type display text
        /// </summary>
        public string GetTypeDisplayText()
        {
            return Type switch
            {
                ZoneType.Domestic => "Domestic",
                ZoneType.EuZone1 => "EU Zone 1",
                ZoneType.EuZone2 => "EU Zone 2",
                ZoneType.International => "International",
                _ => Type.ToString()
            };
        }

        /// <summary>
        /// Gets delivery time range text
        /// </summary>
        public string GetDeliveryTimeText()
        {
            if (DefaultMinDeliveryDays == DefaultMaxDeliveryDays)
                return $"{DefaultMinDeliveryDays} day{(DefaultMinDeliveryDays > 1 ? "s" : "")}";
            
            return $"{DefaultMinDeliveryDays}-{DefaultMaxDeliveryDays} days";
        }

        /// <summary>
        /// Checks if zone can be used for shipping
        /// </summary>
        public bool CanShip() => IsActive && !IsDeleted;

        #endregion

        #region Validation

        /// <summary>
        /// Validates shipping zone data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Code) &&
                   DefaultMinDeliveryDays >= 0 &&
                   DefaultMaxDeliveryDays >= DefaultMinDeliveryDays;
        }

        #endregion
    }

}
