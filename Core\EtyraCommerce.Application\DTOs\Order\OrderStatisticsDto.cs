using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Order statistics data transfer object
    /// </summary>
    public class OrderStatisticsDto
    {
        /// <summary>
        /// Total number of orders
        /// </summary>
        public int TotalOrders { get; set; }

        /// <summary>
        /// Total revenue amount
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// Average order value
        /// </summary>
        public decimal AverageOrderValue { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Number of pending orders
        /// </summary>
        public int PendingOrders { get; set; }

        /// <summary>
        /// Number of confirmed orders
        /// </summary>
        public int ConfirmedOrders { get; set; }

        /// <summary>
        /// Number of shipped orders
        /// </summary>
        public int ShippedOrders { get; set; }

        /// <summary>
        /// Number of delivered orders
        /// </summary>
        public int DeliveredOrders { get; set; }

        /// <summary>
        /// Number of cancelled orders
        /// </summary>
        public int CancelledOrders { get; set; }

        /// <summary>
        /// Statistics period start date
        /// </summary>
        public DateTime? PeriodStart { get; set; }

        /// <summary>
        /// Statistics period end date
        /// </summary>
        public DateTime? PeriodEnd { get; set; }

        /// <summary>
        /// Orders by status
        /// </summary>
        public Dictionary<OrderStatus, int> OrdersByStatus { get; set; } = new();

        /// <summary>
        /// Orders by payment status
        /// </summary>
        public Dictionary<PaymentStatus, int> OrdersByPaymentStatus { get; set; } = new();

        /// <summary>
        /// Top customers by order count
        /// </summary>
        public List<CustomerOrderSummaryDto> TopCustomers { get; set; } = new();

        /// <summary>
        /// Recent orders
        /// </summary>
        public List<OrderDto> RecentOrders { get; set; } = new();
    }
}
