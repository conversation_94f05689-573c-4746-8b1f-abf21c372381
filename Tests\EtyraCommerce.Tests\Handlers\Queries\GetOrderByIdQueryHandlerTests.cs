using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Handlers.Queries;
using EtyraCommerce.Application.Services.Order.Queries;
using EtyraCommerce.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Queries
{
    public class GetOrderByIdQueryHandlerTests
    {
        private readonly Mock<IOrderProcessService> _mockOrderProcessService;
        private readonly Mock<ILogger<GetOrderByIdQueryHandler>> _mockLogger;
        private readonly GetOrderByIdQueryHandler _handler;

        public GetOrderByIdQueryHandlerTests()
        {
            _mockOrderProcessService = new Mock<IOrderProcessService>();
            _mockLogger = new Mock<ILogger<GetOrderByIdQueryHandler>>();
            _handler = new GetOrderByIdQueryHandler(_mockOrderProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidQuery_ReturnsOrderDto()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = true,
                IncludeCustomer = false,
                LanguageCode = "en-US"
            };

            var expectedOrderDto = new OrderDto
            {
                Id = orderId,
                OrderNumber = "ORD-20241201-ABC123",
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                Status = OrderStatus.Confirmed,
                PaymentStatus = PaymentStatus.Completed,
                Total = 150.00m,
                Currency = "USD",
                OrderItems = new List<OrderItemDto>
                {
                    new OrderItemDto
                    {
                        Id = Guid.NewGuid(),
                        ProductId = Guid.NewGuid(),
                        ProductName = "Test Product",
                        Quantity = 2,
                        UnitPrice = 75.00m,
                        TotalPrice = 150.00m
                    }
                }
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, expectedOrderDto, "Order retrieved successfully");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, true, false, "en-US"))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be(orderId);
            result.Data.OrderNumber.Should().Be("ORD-20241201-ABC123");
            result.Data.OrderItems.Should().HaveCount(1);

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, true, false, "en-US"),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyOrderId_ReturnsBadRequest()
        {
            // Arrange
            var query = new GetOrderByIdQuery
            {
                OrderId = Guid.Empty,
                IncludeItems = true
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Order ID is required");

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Theory]
        [InlineData(true, true, "en-US")]
        [InlineData(true, false, "tr-TR")]
        [InlineData(false, true, "de-DE")]
        [InlineData(false, false, null)]
        public async Task Handle_VariousIncludeOptions_CallsServiceWithCorrectParameters(
            bool includeItems, bool includeCustomer, string? languageCode)
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = includeItems,
                IncludeCustomer = includeCustomer,
                LanguageCode = languageCode
            };

            var expectedOrderDto = new OrderDto { Id = orderId };
            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, expectedOrderDto, "Order retrieved successfully");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, includeItems, includeCustomer, languageCode))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, includeItems, includeCustomer, languageCode),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_OrderNotFound_ReturnsNotFoundResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = true
            };

            var expectedResponse = CustomResponseDto<OrderDto>.NotFound("Order not found");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, true, false, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, true, false, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = true
            };

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, true, false, null))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Be("An error occurred while retrieving the order");

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, true, false, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ValidQueryWithoutItems_ReturnsOrderWithoutItems()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = false,
                IncludeCustomer = false
            };

            var expectedOrderDto = new OrderDto
            {
                Id = orderId,
                OrderNumber = "ORD-20241201-ABC123",
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                Total = 150.00m,
                OrderItems = new List<OrderItemDto>() // Empty list when not included
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, expectedOrderDto, "Order retrieved successfully");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, false, false, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.OrderItems.Should().BeEmpty();

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, false, false, null),
                Times.Once
            );
        }

        [Theory]
        [InlineData("en-US")]
        [InlineData("tr-TR")]
        [InlineData("de-DE")]
        [InlineData("fr-FR")]
        [InlineData("es-ES")]
        public async Task Handle_VariousLanguageCodes_CallsServiceWithCorrectLanguageCode(string languageCode)
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = true,
                LanguageCode = languageCode
            };

            var expectedOrderDto = new OrderDto { Id = orderId };
            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, expectedOrderDto, "Order retrieved successfully");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, true, false, languageCode))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, true, false, languageCode),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ServiceReturnsUnauthorized_ReturnsUnauthorizedResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = true
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Unauthorized("Access denied");

            _mockOrderProcessService
                .Setup(x => x.GetOrderByIdAsync(orderId, true, false, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(401);
            result.Message.Should().Be("Access denied");

            _mockOrderProcessService.Verify(
                x => x.GetOrderByIdAsync(orderId, true, false, null),
                Times.Once
            );
        }

        #endregion
    }
}
