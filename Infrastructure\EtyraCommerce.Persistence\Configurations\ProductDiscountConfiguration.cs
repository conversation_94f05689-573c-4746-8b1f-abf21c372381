using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ProductDiscount entity
    /// </summary>
    public class ProductDiscountConfiguration : BaseEntityConfiguration<ProductDiscount>
    {
        public override void Configure(EntityTypeBuilder<ProductDiscount> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("product_discounts", "etyra_core");

            #region Properties

            // Name
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasMaxLength(500)
                .IsRequired(false);

            // Type
            builder.Property(x => x.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();

            // Value
            builder.Property(x => x.Value)
                .HasColumnName("value")
                .HasColumnType("decimal(18,4)")
                .IsRequired();

            // Min Quantity
            builder.Property(x => x.MinQuantity)
                .HasColumnName("min_quantity")
                .IsRequired(false);

            // Max Quantity
            builder.Property(x => x.MaxQuantity)
                .HasColumnName("max_quantity")
                .IsRequired(false);

            // Is Active
            builder.Property(x => x.IsActive)
                .HasColumnName("is_active")
                .HasDefaultValue(true)
                .IsRequired();

            // Priority
            builder.Property(x => x.Priority)
                .HasColumnName("priority")
                .HasDefaultValue(0)
                .IsRequired();

            // Max Uses
            builder.Property(x => x.MaxUses)
                .HasColumnName("max_uses")
                .IsRequired(false);

            // Current Uses
            builder.Property(x => x.CurrentUses)
                .HasColumnName("current_uses")
                .HasDefaultValue(0)
                .IsRequired();

            // Can Combine
            builder.Property(x => x.CanCombine)
                .HasColumnName("can_combine")
                .HasDefaultValue(false)
                .IsRequired();

            // Start Date
            builder.Property(x => x.StartDate)
                .HasColumnName("start_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // End Date
            builder.Property(x => x.EndDate)
                .HasColumnName("end_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Product ID
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            #endregion

            #region Value Objects

            // MinOrderAmount (optional Money)
            builder.OwnsOne(x => x.MinOrderAmount, minOrder =>
            {
                minOrder.Property(m => m.Amount)
                    .HasColumnName("min_order_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                minOrder.Property(m => m.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("min_order_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // MaxDiscountAmount (optional Money)
            builder.OwnsOne(x => x.MaxDiscountAmount, maxDiscount =>
            {
                maxDiscount.Property(m => m.Amount)
                    .HasColumnName("max_discount_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                maxDiscount.Property(m => m.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("max_discount_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            #endregion

            #region Navigation Properties

            // Product (Many-to-One)
            builder.HasOne(x => x.Product)
                .WithMany(x => x.Discounts)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Performance Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_product_discounts_product_id");

            builder.HasIndex(x => x.Type)
                .HasDatabaseName("ix_product_discounts_type");

            builder.HasIndex(x => x.IsActive)
                .HasDatabaseName("ix_product_discounts_is_active");

            builder.HasIndex(x => x.Priority)
                .HasDatabaseName("ix_product_discounts_priority");

            builder.HasIndex(x => x.StartDate)
                .HasDatabaseName("ix_product_discounts_start_date");

            builder.HasIndex(x => x.EndDate)
                .HasDatabaseName("ix_product_discounts_end_date");

            // Composite Indexes
            builder.HasIndex(x => new { x.ProductId, x.IsActive })
                .HasDatabaseName("ix_product_discounts_product_active");

            builder.HasIndex(x => new { x.ProductId, x.Priority })
                .HasDatabaseName("ix_product_discounts_product_priority");

            builder.HasIndex(x => new { x.IsActive, x.StartDate, x.EndDate })
                .HasDatabaseName("ix_product_discounts_active_dates");

            builder.HasIndex(x => new { x.ProductId, x.IsActive, x.Priority })
                .HasDatabaseName("ix_product_discounts_product_active_priority");

            // Date range queries
            builder.HasIndex(x => new { x.StartDate, x.EndDate, x.IsActive })
                .HasDatabaseName("ix_product_discounts_date_range_active");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_ProductDiscounts_Name_NotEmpty",
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_Value_Positive",
                "value > 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_MinQuantity_Positive",
                "min_quantity IS NULL OR min_quantity > 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_MaxQuantity_Positive",
                "max_quantity IS NULL OR max_quantity > 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_Quantity_Range",
                "min_quantity IS NULL OR max_quantity IS NULL OR min_quantity <= max_quantity");

            builder.HasCheckConstraint("CK_ProductDiscounts_MaxUses_Positive",
                "max_uses IS NULL OR max_uses > 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_CurrentUses_NonNegative",
                "current_uses >= 0");

            builder.HasCheckConstraint("CK_ProductDiscounts_Uses_Range",
                "max_uses IS NULL OR current_uses <= max_uses");

            builder.HasCheckConstraint("CK_ProductDiscounts_Date_Range",
                "start_date IS NULL OR end_date IS NULL OR start_date <= end_date");

            // Percentage discount should be <= 100
            builder.HasCheckConstraint("CK_ProductDiscounts_Percentage_Range",
                "type != 0 OR value <= 100"); // 0 = Percentage

            #endregion
        }

        protected override string GetTableName() => "product_discounts";
    }
}
