using EtyraCommerce.Application.Features.Shipping.Countries.Queries;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Shipping.Handlers.Queries
{
    /// <summary>
    /// Handler for getting a country by ID
    /// Delegates business logic to CountryProcessService
    /// </summary>
    public class GetCountryByIdQueryHandler : IRequestHandler<GetCountryByIdQuery, GetCountryByIdResponse>
    {
        private readonly ICountryProcessService _countryProcessService;
        private readonly ILogger<GetCountryByIdQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetCountryByIdQueryHandler(
            ICountryProcessService countryProcessService,
            ILogger<GetCountryByIdQueryHandler> logger)
        {
            _countryProcessService = countryProcessService ?? throw new ArgumentNullException(nameof(countryProcessService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get country by ID query
        /// </summary>
        public async Task<GetCountryByIdResponse> Handle(GetCountryByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get country by ID query for CountryId: {CountryId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                {
                    _logger.LogWarning("Get country by ID query failed: Country ID is required");
                    throw new ArgumentException("Country ID is required");
                }

                // Delegate to CountryProcessService for business logic
                var result = await _countryProcessService.ProcessGetCountryByIdAsync(request, cancellationToken);

                if (result != null)
                {
                    _logger.LogInformation("Country retrieval successful for CountryId: {CountryId}, Name: {CountryName}",
                        request.Id, result.Name);
                }
                else
                {
                    _logger.LogWarning("Country retrieval failed for CountryId: {CountryId}", request.Id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get country by ID query for CountryId: {CountryId}", request.Id);
                throw;
            }
        }
    }
}
