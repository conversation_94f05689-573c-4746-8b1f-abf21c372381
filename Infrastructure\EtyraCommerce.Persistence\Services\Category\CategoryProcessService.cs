using AutoMapper;
using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Category;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace EtyraCommerce.Persistence.Services.Category
{
    /// <summary>
    /// Category process service implementation for business logic operations
    /// </summary>
    public class CategoryProcessService : ICategoryProcessService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<CategoryProcessService> _logger;

        public CategoryProcessService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<CategoryProcessService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        #region Category Management

        public async Task<CustomResponseDto<CategoryDto>> ProcessCreateCategoryAsync(CreateCategoryDto createCategoryDto)
        {
            try
            {
                _logger.LogInformation("Processing category creation for: {CategoryName}", createCategoryDto.Name);

                // Validate parent category if specified
                if (createCategoryDto.ParentCategoryId.HasValue)
                {
                    var parentExists = await ValidateParentCategoryAsync(createCategoryDto.ParentCategoryId.Value);
                    if (!parentExists)
                        return CustomResponseDto<CategoryDto>.BadRequest("Parent category not found");
                }

                // Generate unique slug
                var slug = await GenerateUniqueSlugAsync(createCategoryDto.Name);

                // Calculate category level
                var level = await CalculateCategoryLevelAsync(createCategoryDto.ParentCategoryId);

                // Create category entity
                var category = new Domain.Entities.Category.Category(
                    createCategoryDto.Name,
                    createCategoryDto.Description,
                    slug,
                    createCategoryDto.ParentCategoryId,
                    createCategoryDto.ImageUrl,
                    createCategoryDto.Icon,
                    createCategoryDto.SortOrder,
                    createCategoryDto.IsActive,
                    createCategoryDto.ShowInMenu,
                    createCategoryDto.MetaTitle,
                    createCategoryDto.MetaDescription,
                    createCategoryDto.MetaKeywords);

                // Add category to repository
                var categoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Category.Category>();
                await categoryRepo.AddAsync(category);

                // Add category descriptions if provided
                if (createCategoryDto.Descriptions?.Any() == true)
                {
                    var descriptionRepo = _unitOfWork.WriteRepository<CategoryDescription>();

                    foreach (var descDto in createCategoryDto.Descriptions)
                    {
                        var description = new CategoryDescription(
                            category.Id,
                            descDto.Name,
                            descDto.Description,
                            descDto.MetaTitle,
                            descDto.MetaDescription,
                            descDto.MetaKeywords,
                            descDto.Slug ?? await GenerateUniqueSlugAsync(descDto.Name),
                            descDto.LanguageCode,
                            descDto.StoreId);

                        await descriptionRepo.AddAsync(description);
                    }
                }

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO and return
                var categoryDto = _mapper.Map<CategoryDto>(category);
                categoryDto.Level = level;

                _logger.LogInformation("Category created successfully with ID: {CategoryId}", category.Id);
                return CustomResponseDto<CategoryDto>.Success(201, categoryDto, "Category created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category: {CategoryName}", createCategoryDto.Name);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while creating the category");
            }
        }

        public async Task<CustomResponseDto<CategoryDto>> ProcessUpdateCategoryAsync(Guid categoryId, UpdateCategoryDto updateCategoryDto)
        {
            try
            {
                _logger.LogInformation("Processing category update for ID: {CategoryId}", categoryId);

                // Get category
                var categoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Category.Category>();
                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();

                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: true);
                var category = await categoryQuery.FirstOrDefaultAsync(c => c.Id == categoryId);

                if (category == null)
                    return CustomResponseDto<CategoryDto>.NotFound("Category not found");

                // Validate parent category if specified and different from current
                if (updateCategoryDto.ParentCategoryId.HasValue && updateCategoryDto.ParentCategoryId != category.ParentCategoryId)
                {
                    // Validate hierarchy to prevent circular references
                    var isValidHierarchy = await ValidateCategoryHierarchyAsync(categoryId, updateCategoryDto.ParentCategoryId);
                    if (!isValidHierarchy)
                        return CustomResponseDto<CategoryDto>.BadRequest("Invalid parent category - would create circular reference");

                    var parentExists = await ValidateParentCategoryAsync(updateCategoryDto.ParentCategoryId.Value);
                    if (!parentExists)
                        return CustomResponseDto<CategoryDto>.BadRequest("Parent category not found");
                }

                // Generate unique slug if name changed
                var slug = updateCategoryDto.Slug;
                if (string.IsNullOrEmpty(slug) || updateCategoryDto.Name != category.Name)
                {
                    slug = await GenerateUniqueSlugAsync(updateCategoryDto.Name, category.Slug);
                }

                // Update category
                category.UpdateBasicInfo(
                    updateCategoryDto.Name,
                    updateCategoryDto.Description,
                    slug,
                    updateCategoryDto.ImageUrl,
                    updateCategoryDto.Icon);

                category.UpdateHierarchy(updateCategoryDto.ParentCategoryId);
                category.UpdateDisplaySettings(updateCategoryDto.SortOrder, updateCategoryDto.IsActive, updateCategoryDto.ShowInMenu);
                category.UpdateSeoSettings(updateCategoryDto.MetaTitle, updateCategoryDto.MetaDescription, updateCategoryDto.MetaKeywords);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Calculate level
                var level = await CalculateCategoryLevelAsync(category.ParentCategoryId);

                // Map to DTO and return
                var categoryDto = _mapper.Map<CategoryDto>(category);
                categoryDto.Level = level;

                _logger.LogInformation("Category updated successfully with ID: {CategoryId}", categoryId);
                return CustomResponseDto<CategoryDto>.Success(200, categoryDto, "Category updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category with ID: {CategoryId}", categoryId);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while updating the category");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteCategoryAsync(Guid categoryId, bool forceDelete = false, bool deleteChildren = false)
        {
            try
            {
                _logger.LogInformation("Processing category deletion for ID: {CategoryId}, ForceDelete: {ForceDelete}, DeleteChildren: {DeleteChildren}",
                    categoryId, forceDelete, deleteChildren);

                // Get category
                var categoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Category.Category>();
                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();

                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: true);
                var category = await categoryQuery.FirstOrDefaultAsync(c => c.Id == categoryId);

                if (category == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Category not found");

                // Check for child categories
                var hasChildren = await categoryQuery.AnyAsync(c => c.ParentCategoryId == categoryId && !c.IsDeleted);

                if (hasChildren && !deleteChildren)
                    return CustomResponseDto<NoContentDto>.BadRequest("Category has child categories. Set deleteChildren=true to delete them as well");

                // Delete child categories if requested
                if (deleteChildren && hasChildren)
                {
                    var childCategories = await categoryQuery.Where(c => c.ParentCategoryId == categoryId && !c.IsDeleted).ToListAsync();

                    foreach (var child in childCategories)
                    {
                        if (forceDelete)
                            await categoryRepo.RemoveAsync(child);
                        else
                            child.Delete();
                    }
                }

                // Delete category
                if (forceDelete)
                    await categoryRepo.RemoveAsync(category);
                else
                    category.Delete();

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Category deleted successfully with ID: {CategoryId}", categoryId);
                return CustomResponseDto<NoContentDto>.Success(204, new NoContentDto(), "Category deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category with ID: {CategoryId}", categoryId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the category");
            }
        }

        #endregion

        #region Category Queries

        public async Task<CustomResponseDto<CategoryDto>> ProcessGetCategoryByIdAsync(Guid categoryId, bool includeChildren = false, bool includeParent = false, bool includeDescriptions = true, string? languageCode = null)
        {
            try
            {
                _logger.LogInformation("Processing get category by ID: {CategoryId}", categoryId);

                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
                var categories = await categoryQuery.ToListAsync();

                var category = categories.FirstOrDefault(c => c.Id == categoryId);
                if (category == null)
                    return CustomResponseDto<CategoryDto>.NotFound("Category not found");

                // Map to DTO
                var categoryDto = _mapper.Map<CategoryDto>(category);

                // Calculate level
                categoryDto.Level = await CalculateCategoryLevelAsync(category.ParentCategoryId);

                // Include parent if requested
                if (includeParent && category.ParentCategoryId.HasValue)
                {
                    var parent = categories.FirstOrDefault(c => c.Id == category.ParentCategoryId.Value);
                    if (parent != null)
                    {
                        categoryDto.ParentCategory = _mapper.Map<CategoryDto>(parent);
                    }
                }

                // Include children if requested
                if (includeChildren)
                {
                    var children = categories.Where(c => c.ParentCategoryId == categoryId).ToList();
                    categoryDto.ChildCategories = _mapper.Map<List<CategoryDto>>(children);
                }

                // Include descriptions if requested
                if (includeDescriptions)
                {
                    var descriptionRepo = _unitOfWork.ReadRepository<CategoryDescription>();
                    var descriptionQuery = await descriptionRepo.GetAllAsync(tracking: false);
                    var descriptions = await descriptionQuery.Where(d => d.CategoryId == categoryId).ToListAsync();

                    if (!string.IsNullOrEmpty(languageCode))
                    {
                        descriptions = descriptions.Where(d => d.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase)).ToList();
                    }

                    categoryDto.Descriptions = _mapper.Map<List<CategoryDescriptionDto>>(descriptions);
                }

                // Build breadcrumbs
                categoryDto.Breadcrumbs = await BuildCategoryBreadcrumbsAsync(categoryId, languageCode);

                _logger.LogInformation("Category retrieved successfully with ID: {CategoryId}", categoryId);
                return CustomResponseDto<CategoryDto>.Success(200, categoryDto, "Category retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with ID: {CategoryId}", categoryId);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while retrieving the category");
            }
        }

        public async Task<CustomResponseDto<PagedResult<CategoryDto>>> ProcessGetAllCategoriesAsync(
            string? searchTerm = null,
            bool? isActive = null,
            bool? showInMenu = null,
            Guid? parentCategoryId = null,
            int? level = null,
            bool includeChildren = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending,
            int pageNumber = 1,
            int pageSize = 20)
        {
            try
            {
                _logger.LogInformation("Processing get all categories with search term: {SearchTerm}", searchTerm ?? "None");

                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
                var categories = await categoryQuery.ToListAsync();

                // Apply filters
                var filteredCategories = categories.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var searchLower = searchTerm.ToLowerInvariant();
                    filteredCategories = filteredCategories.Where(c =>
                        c.Name.ToLowerInvariant().Contains(searchLower) ||
                        (c.Description != null && c.Description.ToLowerInvariant().Contains(searchLower)));
                }

                if (isActive.HasValue)
                    filteredCategories = filteredCategories.Where(c => c.IsActive == isActive.Value);

                if (showInMenu.HasValue)
                    filteredCategories = filteredCategories.Where(c => c.ShowInMenu == showInMenu.Value);

                if (parentCategoryId.HasValue)
                    filteredCategories = filteredCategories.Where(c => c.ParentCategoryId == parentCategoryId.Value);

                // Apply level filter (requires calculation)
                if (level.HasValue)
                {
                    var categoriesWithLevel = new List<Domain.Entities.Category.Category>();
                    foreach (var cat in filteredCategories)
                    {
                        var catLevel = await CalculateCategoryLevelAsync(cat.ParentCategoryId);
                        if (catLevel == level.Value)
                            categoriesWithLevel.Add(cat);
                    }
                    filteredCategories = categoriesWithLevel.AsQueryable();
                }

                // Apply sorting
                filteredCategories = sortBy switch
                {
                    CategorySortField.Name => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.Name)
                        : filteredCategories.OrderByDescending(c => c.Name),
                    CategorySortField.CreatedAt => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.CreatedAt)
                        : filteredCategories.OrderByDescending(c => c.CreatedAt),
                    CategorySortField.UpdatedAt => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.UpdatedAt)
                        : filteredCategories.OrderByDescending(c => c.UpdatedAt),
                    _ => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.SortOrder).ThenBy(c => c.Name)
                        : filteredCategories.OrderByDescending(c => c.SortOrder).ThenByDescending(c => c.Name)
                };

                // Apply pagination
                var totalCount = filteredCategories.Count();
                var pagedCategories = filteredCategories
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // Map to DTOs
                var categoryDtos = _mapper.Map<List<CategoryDto>>(pagedCategories);

                // Calculate levels and include additional data
                foreach (var dto in categoryDtos)
                {
                    var category = pagedCategories.First(c => c.Id == dto.Id);
                    dto.Level = await CalculateCategoryLevelAsync(category.ParentCategoryId);

                    // Include children if requested
                    if (includeChildren)
                    {
                        var children = categories.Where(c => c.ParentCategoryId == dto.Id).ToList();
                        dto.ChildCategories = _mapper.Map<List<CategoryDto>>(children);
                    }

                    // Include descriptions if requested
                    if (includeDescriptions)
                    {
                        var descriptionRepo = _unitOfWork.ReadRepository<CategoryDescription>();
                        var descriptionQuery = await descriptionRepo.GetAllAsync(tracking: false);
                        var descriptions = await descriptionQuery.Where(d => d.CategoryId == dto.Id).ToListAsync();

                        if (!string.IsNullOrEmpty(languageCode))
                        {
                            descriptions = descriptions.Where(d => d.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase)).ToList();
                        }

                        dto.Descriptions = _mapper.Map<List<CategoryDescriptionDto>>(descriptions);
                    }
                }

                // Create paged result
                var pagedResult = new PagedResult<CategoryDto>
                {
                    Items = categoryDtos,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                _logger.LogInformation("Categories retrieved successfully. Total: {TotalCount}", totalCount);
                return CustomResponseDto<PagedResult<CategoryDto>>.Success(200, pagedResult, "Categories retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories");
                return CustomResponseDto<PagedResult<CategoryDto>>.InternalServerError("An error occurred while retrieving categories");
            }
        }

        #endregion

        #region Additional Query Methods

        public async Task<CustomResponseDto<List<CategoryDto>>> ProcessGetCategoriesByParentAsync(
            Guid? parentCategoryId,
            bool activeOnly = true,
            bool includeChildren = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending)
        {
            try
            {
                _logger.LogInformation("Processing get categories by parent ID: {ParentCategoryId}", parentCategoryId?.ToString() ?? "Root");

                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
                var categories = await categoryQuery.ToListAsync();

                // Filter by parent
                var filteredCategories = categories.Where(c => c.ParentCategoryId == parentCategoryId);

                // Filter by active status
                if (activeOnly)
                    filteredCategories = filteredCategories.Where(c => c.IsActive);

                // Apply sorting
                filteredCategories = sortBy switch
                {
                    CategorySortField.Name => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.Name)
                        : filteredCategories.OrderByDescending(c => c.Name),
                    CategorySortField.CreatedAt => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.CreatedAt)
                        : filteredCategories.OrderByDescending(c => c.CreatedAt),
                    _ => sortDirection == SortDirection.Ascending
                        ? filteredCategories.OrderBy(c => c.SortOrder).ThenBy(c => c.Name)
                        : filteredCategories.OrderByDescending(c => c.SortOrder).ThenByDescending(c => c.Name)
                };

                var categoryList = filteredCategories.ToList();

                // Map to DTOs
                var categoryDtos = _mapper.Map<List<CategoryDto>>(categoryList);

                // Include additional data
                foreach (var dto in categoryDtos)
                {
                    var category = categoryList.First(c => c.Id == dto.Id);
                    dto.Level = await CalculateCategoryLevelAsync(category.ParentCategoryId);

                    // Include children if requested
                    if (includeChildren)
                    {
                        var children = categories.Where(c => c.ParentCategoryId == dto.Id).ToList();
                        dto.ChildCategories = _mapper.Map<List<CategoryDto>>(children);
                    }

                    // Include descriptions if requested
                    if (includeDescriptions)
                    {
                        var descriptionRepo = _unitOfWork.ReadRepository<CategoryDescription>();
                        var descriptionQuery = await descriptionRepo.GetAllAsync(tracking: false);
                        var descriptions = await descriptionQuery.Where(d => d.CategoryId == dto.Id).ToListAsync();

                        if (!string.IsNullOrEmpty(languageCode))
                        {
                            descriptions = descriptions.Where(d => d.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase)).ToList();
                        }

                        dto.Descriptions = _mapper.Map<List<CategoryDescriptionDto>>(descriptions);
                    }
                }

                _logger.LogInformation("Categories by parent retrieved successfully. Count: {Count}", categoryDtos.Count);
                return CustomResponseDto<List<CategoryDto>>.Success(200, categoryDtos, "Categories retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories by parent ID: {ParentCategoryId}", parentCategoryId?.ToString() ?? "Root");
                return CustomResponseDto<List<CategoryDto>>.InternalServerError("An error occurred while retrieving categories by parent");
            }
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> ProcessGetCategoryTreeAsync(
            Guid? rootCategoryId = null,
            int? maxDepth = null,
            bool activeOnly = true,
            bool menuOnly = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending)
        {
            try
            {
                _logger.LogInformation("Processing get category tree for root ID: {RootCategoryId}", rootCategoryId?.ToString() ?? "Full Tree");

                var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
                var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
                var allCategories = await categoryQuery.ToListAsync();

                // Apply filters
                var filteredCategories = allCategories.AsEnumerable();

                if (activeOnly)
                    filteredCategories = filteredCategories.Where(c => c.IsActive);

                if (menuOnly)
                    filteredCategories = filteredCategories.Where(c => c.ShowInMenu);

                var categoryList = filteredCategories.ToList();

                // Build tree structure
                var tree = await BuildCategoryTreeRecursive(categoryList, rootCategoryId, 0, maxDepth, includeDescriptions, languageCode, sortBy, sortDirection);

                _logger.LogInformation("Category tree retrieved successfully. Root count: {Count}", tree.Count);
                return CustomResponseDto<List<CategoryDto>>.Success(200, tree, "Category tree retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category tree for root ID: {RootCategoryId}", rootCategoryId?.ToString() ?? "Full Tree");
                return CustomResponseDto<List<CategoryDto>>.InternalServerError("An error occurred while retrieving category tree");
            }
        }

        #endregion

        #region Business Logic Helpers

        public async Task<bool> ValidateCategoryHierarchyAsync(Guid categoryId, Guid? parentCategoryId)
        {
            if (!parentCategoryId.HasValue)
                return true;

            // Prevent self-referencing
            if (categoryId == parentCategoryId.Value)
                return false;

            // Check for circular reference by traversing up the hierarchy
            var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
            var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
            var categories = await categoryQuery.ToListAsync();

            var currentParentId = parentCategoryId;
            var visitedIds = new HashSet<Guid> { categoryId };

            while (currentParentId.HasValue)
            {
                if (visitedIds.Contains(currentParentId.Value))
                    return false; // Circular reference detected

                visitedIds.Add(currentParentId.Value);

                var parent = categories.FirstOrDefault(c => c.Id == currentParentId.Value);
                if (parent == null)
                    break;

                currentParentId = parent.ParentCategoryId;
            }

            return true;
        }

        public async Task<string> GenerateUniqueSlugAsync(string name, string? existingSlug = null)
        {
            var baseSlug = GenerateSlugFromName(name);

            if (!string.IsNullOrEmpty(existingSlug) && existingSlug.Equals(baseSlug, StringComparison.OrdinalIgnoreCase))
                return existingSlug;

            var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
            var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
            var categories = await categoryQuery.ToListAsync();

            var existingSlugs = categories.Select(c => c.Slug.ToLowerInvariant()).ToHashSet();

            var slug = baseSlug;
            var counter = 1;

            while (existingSlugs.Contains(slug.ToLowerInvariant()))
            {
                slug = $"{baseSlug}-{counter}";
                counter++;
            }

            return slug;
        }

        public async Task<int> CalculateCategoryLevelAsync(Guid? parentCategoryId)
        {
            if (!parentCategoryId.HasValue)
                return 0;

            var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
            var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
            var categories = await categoryQuery.ToListAsync();

            var level = 0;
            var currentParentId = parentCategoryId;

            while (currentParentId.HasValue)
            {
                level++;
                var parent = categories.FirstOrDefault(c => c.Id == currentParentId.Value);
                if (parent == null)
                    break;

                currentParentId = parent.ParentCategoryId;

                // Prevent infinite loop
                if (level > 10)
                    break;
            }

            return level;
        }

        public async Task<List<CategoryBreadcrumbDto>> BuildCategoryBreadcrumbsAsync(Guid categoryId, string? languageCode = null)
        {
            var breadcrumbs = new List<CategoryBreadcrumbDto>();

            var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
            var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
            var categories = await categoryQuery.ToListAsync();

            var category = categories.FirstOrDefault(c => c.Id == categoryId);
            if (category == null)
                return breadcrumbs;

            // Build breadcrumb path from current category to root
            var currentCategory = category;
            var level = 0;

            while (currentCategory != null)
            {
                breadcrumbs.Insert(0, new CategoryBreadcrumbDto
                {
                    Id = currentCategory.Id,
                    Name = currentCategory.Name,
                    Slug = currentCategory.Slug,
                    Level = level
                });

                if (currentCategory.ParentCategoryId.HasValue)
                {
                    currentCategory = categories.FirstOrDefault(c => c.Id == currentCategory.ParentCategoryId.Value);
                    level++;
                }
                else
                {
                    currentCategory = null;
                }

                // Prevent infinite loop
                if (level > 10)
                    break;
            }

            return breadcrumbs;
        }

        #endregion

        #region Private Helper Methods

        private async Task<bool> ValidateParentCategoryAsync(Guid parentCategoryId)
        {
            var categoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Category.Category>();
            var categoryQuery = await categoryReadRepo.GetAllAsync(tracking: false);
            return await categoryQuery.AnyAsync(c => c.Id == parentCategoryId && !c.IsDeleted);
        }

        private static string GenerateSlugFromName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return "category";

            // Convert to lowercase and replace spaces with hyphens
            var slug = name.ToLowerInvariant().Trim();

            // Remove special characters and replace with hyphens
            slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");
            slug = Regex.Replace(slug, @"\s+", "-");
            slug = Regex.Replace(slug, @"-+", "-");

            // Remove leading/trailing hyphens
            slug = slug.Trim('-');

            return string.IsNullOrEmpty(slug) ? "category" : slug;
        }

        private async Task<List<CategoryDto>> BuildCategoryTreeRecursive(
            List<Domain.Entities.Category.Category> allCategories,
            Guid? parentId,
            int currentDepth,
            int? maxDepth,
            bool includeDescriptions,
            string? languageCode,
            CategorySortField sortBy,
            SortDirection sortDirection)
        {
            if (maxDepth.HasValue && currentDepth >= maxDepth.Value)
                return new List<CategoryDto>();

            var children = allCategories.Where(c => c.ParentCategoryId == parentId);

            // Apply sorting
            children = sortBy switch
            {
                CategorySortField.Name => sortDirection == SortDirection.Ascending
                    ? children.OrderBy(c => c.Name)
                    : children.OrderByDescending(c => c.Name),
                CategorySortField.CreatedAt => sortDirection == SortDirection.Ascending
                    ? children.OrderBy(c => c.CreatedAt)
                    : children.OrderByDescending(c => c.CreatedAt),
                _ => sortDirection == SortDirection.Ascending
                    ? children.OrderBy(c => c.SortOrder).ThenBy(c => c.Name)
                    : children.OrderByDescending(c => c.SortOrder).ThenByDescending(c => c.Name)
            };

            var result = new List<CategoryDto>();

            foreach (var category in children)
            {
                var categoryDto = _mapper.Map<CategoryDto>(category);
                categoryDto.Level = currentDepth;

                // Include descriptions if requested
                if (includeDescriptions)
                {
                    var descriptionRepo = _unitOfWork.ReadRepository<CategoryDescription>();
                    var descriptionQuery = await descriptionRepo.GetAllAsync(tracking: false);
                    var descriptions = await descriptionQuery.Where(d => d.CategoryId == category.Id).ToListAsync();

                    if (!string.IsNullOrEmpty(languageCode))
                    {
                        descriptions = descriptions.Where(d => d.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase)).ToList();
                    }

                    categoryDto.Descriptions = _mapper.Map<List<CategoryDescriptionDto>>(descriptions);
                }

                // Recursively get children
                categoryDto.ChildCategories = await BuildCategoryTreeRecursive(
                    allCategories, category.Id, currentDepth + 1, maxDepth, includeDescriptions, languageCode, sortBy, sortDirection);

                result.Add(categoryDto);
            }

            return result;
        }

        #endregion
    }
}
