using AutoMapper;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product;
using EtyraCommerce.Application.UnitOfWork;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Product
{
    /// <summary>
    /// Product process service implementation for business logic operations
    /// </summary>
    public class ProductProcessService : IProductProcessService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ProductProcessService> _logger;

        public ProductProcessService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<ProductProcessService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        #region Command Processing

        /// <summary>
        /// Processes product creation with business logic
        /// </summary>
        /// <param name="createDto">Product creation data</param>
        /// <returns>Created product DTO</returns>
        public async Task<CustomResponseDto<ProductDto>> ProcessCreateProductAsync(CreateProductDto createDto)
        {
            try
            {
                _logger.LogInformation("Processing product creation for: {ProductName}", createDto.Name);

                // Basic implementation - create product entity
                var product = new Domain.Entities.Product.Product
                {
                    Id = Guid.NewGuid(),
                    Model = createDto.Model,
                    Name = createDto.Name,
                    SKU = createDto.SKU,
                    BasePrice = new Domain.ValueObjects.Money(createDto.BasePrice, Domain.ValueObjects.Currency.FromCode(createDto.BasePriceCurrency ?? "USD")),
                    DefaultCurrency = Domain.ValueObjects.Currency.FromCode(createDto.DefaultCurrency),
                    Status = (Domain.Entities.Product.ProductStatus)createDto.Status,
                    Type = (Domain.Entities.Product.ProductType)createDto.Type,
                    Slug = createDto.Slug ?? string.Empty,
                    Brand = createDto.Brand,
                    EAN = createDto.EAN,
                    MPN = createDto.MPN,
                    Barcode = createDto.Barcode,
                    UPC = createDto.UPC,

                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Add to repository
                await _unitOfWork.WriteRepository<Domain.Entities.Product.Product>().AddAsync(product);
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var productDto = _mapper.Map<ProductDto>(product);

                _logger.LogInformation("Product created successfully with ID: {ProductId}", product.Id);
                return CustomResponseDto<ProductDto>.Success(productDto, "Product created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product: {ProductName}", createDto.Name);
                return CustomResponseDto<ProductDto>.InternalServerError("Error creating product");
            }
        }

        /// <summary>
        /// Processes product update with business logic
        /// </summary>
        /// <param name="productId">Product ID to update</param>
        /// <param name="updateDto">Product update data</param>
        /// <returns>Updated product DTO</returns>
        public async Task<CustomResponseDto<ProductDto>> ProcessUpdateProductAsync(Guid productId, UpdateProductDto updateDto)
        {
            try
            {
                _logger.LogInformation("Processing product update for ID: {ProductId}", productId);

                var product = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetByIdAsync(productId);

                if (product == null)
                    return CustomResponseDto<ProductDto>.NotFound("Product not found");

                // Update basic fields
                product.Name = updateDto.Name ?? product.Name;
                product.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.SaveChangesAsync();

                var productDto = _mapper.Map<ProductDto>(product);
                return CustomResponseDto<ProductDto>.Success(productDto, "Product updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product: {ProductId}", productId);
                return CustomResponseDto<ProductDto>.InternalServerError("Error updating product");
            }
        }

        /// <summary>
        /// Processes product deletion with business logic
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <param name="deletionReason">Reason for deletion</param>
        /// <returns>No content response</returns>
        public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteProductAsync(Guid productId, bool hardDelete = false, bool forceDelete = false, string? deletionReason = null)
        {
            try
            {
                _logger.LogInformation("Processing product deletion for ID: {ProductId}", productId);

                var product = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetByIdAsync(productId);

                if (product == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Product not found");

                if (hardDelete)
                {
                    await _unitOfWork.WriteRepository<Domain.Entities.Product.Product>().RemoveAsync(product);
                }
                else
                {
                    product.IsDeleted = true;
                    product.UpdatedAt = DateTime.UtcNow;
                }

                await _unitOfWork.SaveChangesAsync();
                return CustomResponseDto<NoContentDto>.Success(204, "Product deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product: {ProductId}", productId);
                return CustomResponseDto<NoContentDto>.InternalServerError("Error deleting product");
            }
        }

        #endregion

        #region Query Processing

        /// <summary>
        /// Processes get product by ID with business logic
        /// </summary>
        public async Task<CustomResponseDto<ProductDto>> ProcessGetProductByIdAsync(
            Guid productId,
            bool includeDescriptions = false,
            bool includeImages = false,
            bool includeCategories = false,
            bool includeDiscounts = false,
            bool includeAttributes = false,
            bool includeVariants = false,
            bool includeInventory = false,
            bool includeDeleted = false,
            string? languageCode = null,
            Guid? storeId = null)
        {
            try
            {
                var product = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetByIdAsync(productId);

                if (product == null)
                    return CustomResponseDto<ProductDto>.NotFound("Product not found");

                var productDto = _mapper.Map<ProductDto>(product);
                return CustomResponseDto<ProductDto>.Success(productDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product: {ProductId}", productId);
                return CustomResponseDto<ProductDto>.InternalServerError("Error getting product");
            }
        }

        /// <summary>
        /// Processes get all products with business logic
        /// </summary>
        /// <param name="filterDto">Filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessGetAllProductsAsync(ProductFilterDto filterDto)
        {
            try
            {
                var products = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetAllAsync();

                var productDtos = _mapper.Map<List<ProductDto>>(products);
                var pagedResult = PagedResult<ProductDto>.Create(productDtos, productDtos.Count, 1, productDtos.Count);

                return CustomResponseDto<PagedResult<ProductDto>>.Success(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all products");
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("Error getting products");
            }
        }

        /// <summary>
        /// Processes product search with business logic
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>Paged product DTOs</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessSearchProductsAsync(ProductSearchDto searchDto)
        {
            try
            {
                var products = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetAllAsync();

                var productDtos = _mapper.Map<List<ProductDto>>(products);
                var pagedResult = PagedResult<ProductDto>.Create(productDtos, productDtos.Count, 1, productDtos.Count);

                return CustomResponseDto<PagedResult<ProductDto>>.Success(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching products");
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("Error searching products");
            }
        }

        /// <summary>
        /// Processes get products by category with business logic
        /// </summary>
        /// <param name="categoryFilterDto">Category filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessGetProductsByCategoryAsync(ProductCategoryFilterDto categoryFilterDto)
        {
            try
            {
                var products = await _unitOfWork.ReadRepository<Domain.Entities.Product.Product>()
                    .GetAllAsync();

                var productDtos = _mapper.Map<List<ProductDto>>(products);
                var pagedResult = PagedResult<ProductDto>.Create(productDtos, productDtos.Count, 1, productDtos.Count);

                return CustomResponseDto<PagedResult<ProductDto>>.Success(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting products by category");
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("Error getting products by category");
            }
        }

        #endregion

        #region Business Logic Helpers

        /// <summary>
        /// Validates product business rules
        /// </summary>
        /// <param name="product">Product entity</param>
        /// <returns>Validation result</returns>
        public async Task<ValidationResult> ValidateProductBusinessRulesAsync(Domain.Entities.Product.Product product)
        {
            // Basic validation - can be extended
            var result = new ValidationResult
            {
                IsValid = true,
                Errors = new Dictionary<string, List<string>>()
            };

            if (string.IsNullOrWhiteSpace(product.SKU))
            {
                result.IsValid = false;
                result.Errors.Add("SKU", new List<string> { "SKU is required" });
            }

            if (string.IsNullOrWhiteSpace(product.Name))
            {
                result.IsValid = false;
                result.Errors.Add("Name", new List<string> { "Product name is required" });
            }

            if (product.BasePrice.Amount <= 0)
            {
                result.IsValid = false;
                result.Errors.Add("BasePrice", new List<string> { "Base price must be greater than 0" });
            }

            return await Task.FromResult(result);
        }

        /// <summary>
        /// Checks if SKU is unique
        /// </summary>
        /// <param name="sku">SKU to check</param>
        /// <param name="excludeProductId">Product ID to exclude from check</param>
        /// <returns>True if unique</returns>
        public async Task<bool> IsSkuUniqueAsync(string sku, Guid? excludeProductId = null)
        {
            // Basic implementation - should be extended
            return await Task.FromResult(true);
        }

        /// <summary>
        /// Checks if EAN is unique
        /// </summary>
        /// <param name="ean">EAN to check</param>
        /// <param name="excludeProductId">Product ID to exclude from check</param>
        /// <returns>True if unique</returns>
        public async Task<bool> IsEanUniqueAsync(string ean, Guid? excludeProductId = null)
        {
            // Basic implementation - should be extended
            return await Task.FromResult(true);
        }

        /// <summary>
        /// Generates SEO-friendly slug for product
        /// </summary>
        /// <param name="name">Product name</param>
        /// <param name="model">Product model</param>
        /// <param name="excludeProductId">Product ID to exclude from uniqueness check</param>
        /// <returns>Unique slug</returns>
        public async Task<string> GenerateProductSlugAsync(string name, string model, Guid? excludeProductId = null)
        {
            // Basic implementation - should be extended
            var slug = $"{name}-{model}".ToLower().Replace(" ", "-");
            return await Task.FromResult(slug);
        }

        /// <summary>
        /// Checks if product can be deleted
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <returns>True if can be deleted</returns>
        public async Task<bool> CanDeleteProductAsync(Guid productId, bool forceDelete = false)
        {
            // Basic implementation - should be extended
            return await Task.FromResult(true);
        }

        /// <summary>
        /// Updates product stock quantity
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="quantity">New quantity</param>
        /// <param name="reason">Reason for stock change</param>
        /// <returns>Success result</returns>
        public async Task<CustomResponseDto<NoContentDto>> UpdateProductStockAsync(Guid productId, int quantity, string? reason = null)
        {
            // Basic implementation - should be extended
            return await Task.FromResult(CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "Stock updated successfully"));
        }

        /// <summary>
        /// Calculates effective price for product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="customerId">Customer ID for personalized pricing</param>
        /// <returns>Effective price</returns>
        public async Task<decimal> CalculateEffectivePriceAsync(Guid productId, Guid? customerId = null)
        {
            // Basic implementation - should be extended
            return await Task.FromResult(0m);
        }

        #endregion
    }
}
