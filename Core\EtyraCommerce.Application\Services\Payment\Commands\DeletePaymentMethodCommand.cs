using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using MediatR;

namespace EtyraCommerce.Application.Services.Payment.Commands;

/// <summary>
/// Command to delete a payment method
/// </summary>
public class DeletePaymentMethodCommand : IRequest<CustomResponseDto<NoContentDto>>
{
    /// <summary>
    /// Payment method ID to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Payment method ID</param>
    public DeletePaymentMethodCommand(Guid id)
    {
        Id = id;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public DeletePaymentMethodCommand() { }
}

/// <summary>
/// Command to toggle payment method active status
/// </summary>
public class TogglePaymentMethodStatusCommand : IRequest<CustomResponseDto<PaymentMethodDto>>
{
    /// <summary>
    /// Payment method ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// New active status
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    public TogglePaymentMethodStatusCommand(Guid id, bool isActive)
    {
        Id = id;
        IsActive = isActive;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public TogglePaymentMethodStatusCommand() { }
}
