using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Currency.Queries;
using EtyraCommerce.Application.Services.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for GetAllExchangeRatesQuery
/// </summary>
public class GetAllExchangeRatesQueryHandler : IRequestHandler<GetAllExchangeRatesQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetAllExchangeRatesQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetAllExchangeRatesQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetAllAsync(request.Tracking, request.ActiveOnly, request.ValidOnly);
    }
}

/// <summary>
/// Handler for GetExchangeRateByIdQuery
/// </summary>
public class GetExchangeRateByIdQueryHandler : IRequestHandler<GetExchangeRateByIdQuery, CustomResponseDto<ExchangeRateDto>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRateByIdQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<ExchangeRateDto>> Handle(GetExchangeRateByIdQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetByIdAsync(request.Id, request.Tracking);
    }
}

/// <summary>
/// Handler for GetExchangeRatesByCurrencyPairQuery
/// </summary>
public class GetExchangeRatesByCurrencyPairQueryHandler : IRequestHandler<GetExchangeRatesByCurrencyPairQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRatesByCurrencyPairQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExchangeRatesByCurrencyPairQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetByCurrencyPairAsync(
            request.FromCurrencyId,
            request.ToCurrencyId,
            request.Tracking,
            request.ActiveOnly,
            request.ValidOnly);
    }
}

/// <summary>
/// Handler for GetExchangeRatesByCurrencyQuery
/// </summary>
public class GetExchangeRatesByCurrencyQueryHandler : IRequestHandler<GetExchangeRatesByCurrencyQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRatesByCurrencyQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExchangeRatesByCurrencyQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetByCurrencyAsync(
            request.CurrencyId,
            request.AsBaseCurrency,
            request.Tracking,
            request.ActiveOnly,
            request.ValidOnly);
    }
}

/// <summary>
/// Handler for GetCurrentExchangeRateQuery
/// </summary>
public class GetCurrentExchangeRateQueryHandler : IRequestHandler<GetCurrentExchangeRateQuery, CustomResponseDto<ExchangeRateDto>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetCurrentExchangeRateQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<ExchangeRateDto>> Handle(GetCurrentExchangeRateQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetCurrentRateAsync(
            request.FromCurrencyId,
            request.ToCurrencyId,
            request.AsOfDate,
            request.Tracking);
    }
}

/// <summary>
/// Handler for GetCurrentExchangeRateByCurrencyCodesQuery
/// </summary>
public class GetCurrentExchangeRateByCurrencyCodesQueryHandler : IRequestHandler<GetCurrentExchangeRateByCurrencyCodesQuery, CustomResponseDto<ExchangeRateDto>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetCurrentExchangeRateByCurrencyCodesQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<ExchangeRateDto>> Handle(GetCurrentExchangeRateByCurrencyCodesQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetCurrentRateByCurrencyCodesAsync(
            request.FromCurrencyCode,
            request.ToCurrencyCode,
            request.AsOfDate,
            request.Tracking);
    }
}

/// <summary>
/// Handler for GetExchangeRatesBySourceQuery
/// </summary>
public class GetExchangeRatesBySourceQueryHandler : IRequestHandler<GetExchangeRatesBySourceQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRatesBySourceQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExchangeRatesBySourceQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetBySourceAsync(
            request.Source,
            request.Tracking,
            request.ActiveOnly,
            request.ValidOnly);
    }
}

/// <summary>
/// Handler for GetExchangeRatesByDateRangeQuery
/// </summary>
public class GetExchangeRatesByDateRangeQueryHandler : IRequestHandler<GetExchangeRatesByDateRangeQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRatesByDateRangeQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExchangeRatesByDateRangeQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetByDateRangeAsync(
            request.StartDate,
            request.EndDate,
            request.FromCurrencyId,
            request.ToCurrencyId,
            request.Tracking,
            request.ActiveOnly);
    }
}

/// <summary>
/// Handler for GetExpiredExchangeRatesQuery
/// </summary>
public class GetExpiredExchangeRatesQueryHandler : IRequestHandler<GetExpiredExchangeRatesQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExpiredExchangeRatesQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExpiredExchangeRatesQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetExpiredRatesAsync(
            request.AsOfDate,
            request.Tracking,
            request.ActiveOnly);
    }
}

/// <summary>
/// Handler for GetExchangeRateHistoryQuery
/// </summary>
public class GetExchangeRateHistoryQueryHandler : IRequestHandler<GetExchangeRateHistoryQuery, CustomResponseDto<List<ExchangeRateDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRateHistoryQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateDto>>> Handle(GetExchangeRateHistoryQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetHistoryAsync(
            request.FromCurrencyId,
            request.ToCurrencyId,
            request.DaysBack,
            request.MaxRecords,
            request.Tracking);
    }
}

/// <summary>
/// Handler for GetExchangeRateSummaryQuery
/// </summary>
public class GetExchangeRateSummaryQueryHandler : IRequestHandler<GetExchangeRateSummaryQuery, CustomResponseDto<List<ExchangeRateSummaryDto>>>
{
    private readonly IExchangeRateProcessService _exchangeRateProcessService;

    public GetExchangeRateSummaryQueryHandler(IExchangeRateProcessService exchangeRateProcessService)
    {
        _exchangeRateProcessService = exchangeRateProcessService;
    }

    public async Task<CustomResponseDto<List<ExchangeRateSummaryDto>>> Handle(GetExchangeRateSummaryQuery request, CancellationToken cancellationToken)
    {
        return await _exchangeRateProcessService.GetSummaryAsync(
            request.ActiveOnly,
            request.ValidOnly,
            request.Tracking);
    }
}
