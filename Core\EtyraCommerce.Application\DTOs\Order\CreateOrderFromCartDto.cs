using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// DTO for creating an order from shopping cart
    /// </summary>
    public class CreateOrderFromCartDto
    {
        /// <summary>
        /// Session ID for guest carts (not required for authenticated users)
        /// </summary>
        [MaxLength(255)]
        public string? SessionId { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer last name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerLastName { get; set; } = string.Empty;

        /// <summary>
        /// Customer email
        /// </summary>
        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number (optional)
        /// </summary>
        [MaxLength(20)]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        [Required]
        public CreateAddressDto BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address (if different from billing)
        /// </summary>
        [Required]
        public CreateAddressDto ShippingAddress { get; set; } = null!;

        /// <summary>
        /// Payment method
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string PaymentMethod { get; set; } = string.Empty;

        /// <summary>
        /// Shipping method (optional)
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Customer notes (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Currency code (will be taken from cart if not specified)
        /// </summary>
        [MaxLength(3)]
        public string? Currency { get; set; }

        /// <summary>
        /// Whether to clear the cart after creating the order (default: true)
        /// </summary>
        public bool ClearCartAfterOrder { get; set; } = true;
    }
}
