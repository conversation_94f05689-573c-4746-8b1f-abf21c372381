using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Commands
{
    /// <summary>
    /// Handler for CreateOrderCommand
    /// </summary>
    public class CreateOrderCommandHandler : IRequestHandler<CreateOrderCommand, CustomResponseDto<OrderDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<CreateOrderCommandHandler> _logger;

        public CreateOrderCommandHandler(
            IOrderProcessService orderProcessService,
            ILogger<CreateOrderCommandHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<OrderDto>> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing create order command for customer: {CustomerId}", request.CustomerId);

                // Validation
                if (request.CustomerId == Guid.Empty)
                    return CustomResponseDto<OrderDto>.BadRequest("Customer ID is required");

                if (string.IsNullOrWhiteSpace(request.CustomerEmail))
                    return CustomResponseDto<OrderDto>.BadRequest("Customer email is required");

                if (string.IsNullOrWhiteSpace(request.CustomerFirstName))
                    return CustomResponseDto<OrderDto>.BadRequest("Customer first name is required");

                if (string.IsNullOrWhiteSpace(request.CustomerLastName))
                    return CustomResponseDto<OrderDto>.BadRequest("Customer last name is required");

                if (request.BillingAddress == null)
                    return CustomResponseDto<OrderDto>.BadRequest("Billing address is required");

                if (request.ShippingAddress == null)
                    return CustomResponseDto<OrderDto>.BadRequest("Shipping address is required");

                if (request.OrderItems == null || !request.OrderItems.Any())
                    return CustomResponseDto<OrderDto>.BadRequest("Order must contain at least one item");

                if (string.IsNullOrWhiteSpace(request.Currency))
                    return CustomResponseDto<OrderDto>.BadRequest("Currency is required");

                // Create DTO
                var createOrderDto = new CreateOrderDto
                {
                    CustomerId = request.CustomerId,
                    CustomerEmail = request.CustomerEmail,
                    CustomerPhone = request.CustomerPhone,
                    CustomerFirstName = request.CustomerFirstName,
                    CustomerLastName = request.CustomerLastName,
                    BillingAddress = request.BillingAddress,
                    ShippingAddress = request.ShippingAddress,
                    OrderItems = request.OrderItems,
                    Currency = request.Currency,
                    Notes = request.Notes,
                    ShippingMethod = request.ShippingMethod,
                    PaymentMethod = request.PaymentMethod
                };

                // Delegate to process service
                var result = await _orderProcessService.ProcessCreateOrderAsync(createOrderDto);

                _logger.LogInformation("Create order command processed successfully for customer: {CustomerId}", request.CustomerId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing create order command for customer: {CustomerId}", request.CustomerId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating the order");
            }
        }
    }
}
