namespace EtyraCommerce.Application.DTOs.Currency
{
    /// <summary>
    /// Currency data transfer object
    /// </summary>
    public class CurrencyDto
    {
        /// <summary>
        /// Currency unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ISO currency code (e.g., USD, EUR, TRY)
        /// </summary>
        public string Code { get; set; } = null!;

        /// <summary>
        /// Currency name (e.g., US Dollar, Euro, Turkish Lira)
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Currency symbol (e.g., $, €, ₺)
        /// </summary>
        public string Symbol { get; set; } = null!;

        /// <summary>
        /// Number of decimal places for this currency
        /// </summary>
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// Whether this currency is active and can be used
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether this is the default currency for the system
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Display order for currency selection
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Currency format pattern for display
        /// </summary>
        public string? FormatPattern { get; set; }

        /// <summary>
        /// Additional notes about this currency
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime UpdatedDate { get; set; }

        /// <summary>
        /// Sample formatted amount for preview
        /// </summary>
        public string? SampleFormat { get; set; }
    }

    /// <summary>
    /// Currency summary DTO for lists and dropdowns
    /// </summary>
    public class CurrencySummaryDto
    {
        /// <summary>
        /// Currency unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ISO currency code
        /// </summary>
        public string Code { get; set; } = null!;

        /// <summary>
        /// Currency name
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Currency symbol
        /// </summary>
        public string Symbol { get; set; } = null!;

        /// <summary>
        /// Whether this currency is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether this is the default currency
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Display order
        /// </summary>
        public int DisplayOrder { get; set; }
    }

    /// <summary>
    /// Currency create request DTO
    /// </summary>
    public class CreateCurrencyDto
    {
        /// <summary>
        /// ISO currency code (e.g., USD, EUR, TRY)
        /// </summary>
        public string Code { get; set; } = null!;

        /// <summary>
        /// Currency name (e.g., US Dollar, Euro, Turkish Lira)
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Currency symbol (e.g., $, €, ₺)
        /// </summary>
        public string Symbol { get; set; } = null!;

        /// <summary>
        /// Number of decimal places for this currency
        /// </summary>
        public int DecimalPlaces { get; set; } = 2;

        /// <summary>
        /// Whether this currency should be active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for currency selection
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Currency format pattern for display
        /// </summary>
        public string? FormatPattern { get; set; }

        /// <summary>
        /// Additional notes about this currency
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Currency update request DTO
    /// </summary>
    public class UpdateCurrencyDto
    {
        /// <summary>
        /// Currency name (e.g., US Dollar, Euro, Turkish Lira)
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Currency symbol (e.g., $, €, ₺)
        /// </summary>
        public string Symbol { get; set; } = null!;

        /// <summary>
        /// Number of decimal places for this currency
        /// </summary>
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// Whether this currency should be active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Display order for currency selection
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Currency format pattern for display
        /// </summary>
        public string? FormatPattern { get; set; }

        /// <summary>
        /// Additional notes about this currency
        /// </summary>
        public string? Notes { get; set; }
    }
}
