using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Queries
{
    /// <summary>
    /// Query for getting a product by ID
    /// </summary>
    public class GetProductByIdQuery : IRequest<CustomResponseDto<ProductDto>>
    {
        /// <summary>
        /// Product ID to retrieve
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Whether to include product descriptions
        /// </summary>
        public bool IncludeDescriptions { get; set; } = false;

        /// <summary>
        /// Whether to include product images
        /// </summary>
        public bool IncludeImages { get; set; } = false;

        /// <summary>
        /// Whether to include product categories
        /// </summary>
        public bool IncludeCategories { get; set; } = false;

        /// <summary>
        /// Whether to include product discounts
        /// </summary>
        public bool IncludeDiscounts { get; set; } = false;

        /// <summary>
        /// Whether to include product attributes
        /// </summary>
        public bool IncludeAttributes { get; set; } = false;

        /// <summary>
        /// Whether to include product variants
        /// </summary>
        public bool IncludeVariants { get; set; } = false;

        /// <summary>
        /// Whether to include warehouse products (inventory)
        /// </summary>
        public bool IncludeInventory { get; set; } = false;

        /// <summary>
        /// Whether to include deleted products
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Language code for descriptions (e.g., "en-US", "tr-TR")
        /// If null, returns all languages
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Store ID for store-specific descriptions
        /// If null, returns all stores
        /// </summary>
        public Guid? StoreId { get; set; }

        /// <summary>
        /// Constructor for basic query
        /// </summary>
        public GetProductByIdQuery()
        {
        }

        /// <summary>
        /// Constructor with product ID
        /// </summary>
        /// <param name="productId">Product ID to retrieve</param>
        public GetProductByIdQuery(Guid productId)
        {
            ProductId = productId;
        }

        /// <summary>
        /// Constructor with product ID and include options
        /// </summary>
        /// <param name="productId">Product ID to retrieve</param>
        /// <param name="includeDescriptions">Include descriptions</param>
        /// <param name="includeImages">Include images</param>
        /// <param name="includeCategories">Include categories</param>
        /// <param name="includeDiscounts">Include discounts</param>
        /// <param name="includeAttributes">Include attributes</param>
        /// <param name="includeVariants">Include variants</param>
        /// <param name="includeInventory">Include inventory</param>
        /// <param name="languageCode">Language code for descriptions</param>
        /// <param name="storeId">Store ID for store-specific data</param>
        public GetProductByIdQuery(
            Guid productId,
            bool includeDescriptions = false,
            bool includeImages = false,
            bool includeCategories = false,
            bool includeDiscounts = false,
            bool includeAttributes = false,
            bool includeVariants = false,
            bool includeInventory = false,
            string? languageCode = null,
            Guid? storeId = null)
        {
            ProductId = productId;
            IncludeDescriptions = includeDescriptions;
            IncludeImages = includeImages;
            IncludeCategories = includeCategories;
            IncludeDiscounts = includeDiscounts;
            IncludeAttributes = includeAttributes;
            IncludeVariants = includeVariants;
            IncludeInventory = includeInventory;
            LanguageCode = languageCode;
            StoreId = storeId;
        }

        /// <summary>
        /// Creates a query with all includes enabled
        /// </summary>
        /// <param name="productId">Product ID to retrieve</param>
        /// <param name="languageCode">Language code for descriptions</param>
        /// <param name="storeId">Store ID for store-specific data</param>
        /// <returns>Query with all includes</returns>
        public static GetProductByIdQuery WithAllIncludes(Guid productId, string? languageCode = null, Guid? storeId = null)
        {
            return new GetProductByIdQuery(
                productId,
                includeDescriptions: true,
                includeImages: true,
                includeCategories: true,
                includeDiscounts: true,
                includeAttributes: true,
                includeVariants: true,
                includeInventory: true,
                languageCode: languageCode,
                storeId: storeId);
        }

        /// <summary>
        /// Creates a query for basic product info only
        /// </summary>
        /// <param name="productId">Product ID to retrieve</param>
        /// <returns>Query for basic info</returns>
        public static GetProductByIdQuery BasicInfo(Guid productId)
        {
            return new GetProductByIdQuery(productId);
        }

        /// <summary>
        /// Creates a query for product with descriptions and images
        /// </summary>
        /// <param name="productId">Product ID to retrieve</param>
        /// <param name="languageCode">Language code for descriptions</param>
        /// <param name="storeId">Store ID for store-specific data</param>
        /// <returns>Query for product with descriptions and images</returns>
        public static GetProductByIdQuery WithDescriptionsAndImages(Guid productId, string? languageCode = null, Guid? storeId = null)
        {
            return new GetProductByIdQuery(
                productId,
                includeDescriptions: true,
                includeImages: true,
                languageCode: languageCode,
                storeId: storeId);
        }
    }
}
