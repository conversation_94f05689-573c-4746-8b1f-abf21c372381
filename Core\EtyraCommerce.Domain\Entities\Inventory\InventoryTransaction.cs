namespace EtyraCommerce.Domain.Entities.Inventory
{
    /// <summary>
    /// Inventory transaction entity for audit trail of stock movements
    /// </summary>
    public class InventoryTransaction : AuditableBaseEntity
    {
        #region Properties

        /// <summary>
        /// Inventory ID reference
        /// </summary>
        public Guid InventoryId { get; set; }

        /// <summary>
        /// Transaction type
        /// </summary>
        public InventoryTransactionType Type { get; set; }

        /// <summary>
        /// Quantity change (positive for increase, negative for decrease)
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Quantity before this transaction
        /// </summary>
        public int QuantityBefore { get; set; }

        /// <summary>
        /// Quantity after this transaction
        /// </summary>
        public int QuantityAfter { get; set; }

        /// <summary>
        /// Reference number (OrderId, PurchaseOrderId, etc.)
        /// </summary>
        public string? Reference { get; set; }

        /// <summary>
        /// Reference type (Order, Purchase, Adjustment, etc.)
        /// </summary>
        public string? ReferenceType { get; set; }

        /// <summary>
        /// Transaction reason or notes
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Additional notes
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// User who performed the transaction
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Transaction timestamp
        /// </summary>
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Cost per unit at time of transaction
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Total cost of transaction
        /// </summary>
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// Supplier or customer reference
        /// </summary>
        public string? ExternalReference { get; set; }

        /// <summary>
        /// Batch or lot number
        /// </summary>
        public string? BatchNumber { get; set; }

        /// <summary>
        /// Expiry date for perishable items
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Inventory reference
        /// </summary>
        public virtual Inventory Inventory { get; set; } = null!;

        /// <summary>
        /// User who performed the transaction
        /// </summary>
        public virtual User.User? User { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public InventoryTransaction() { }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public InventoryTransaction(Guid inventoryId, InventoryTransactionType type, int quantity,
            int quantityBefore, int quantityAfter, string? reference = null, string? reason = null,
            Guid? userId = null)
        {
            InventoryId = inventoryId;
            Type = type;
            Quantity = quantity;
            QuantityBefore = quantityBefore;
            QuantityAfter = quantityAfter;
            Reference = reference;
            Reason = reason;
            UserId = userId;
            TransactionDate = DateTime.UtcNow;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates transaction details
        /// </summary>
        public void UpdateDetails(string? reason = null, string? notes = null,
            decimal? unitCost = null, string? externalReference = null,
            string? batchNumber = null, DateTime? expiryDate = null)
        {
            if (!string.IsNullOrEmpty(reason))
                Reason = reason;

            if (!string.IsNullOrEmpty(notes))
                Notes = notes;

            if (unitCost.HasValue)
            {
                UnitCost = unitCost.Value;
                TotalCost = Math.Abs(Quantity) * unitCost.Value;
            }

            ExternalReference = externalReference;
            BatchNumber = batchNumber;
            ExpiryDate = expiryDate;

            MarkAsUpdated();
        }

        /// <summary>
        /// Sets reference information
        /// </summary>
        public void SetReference(string reference, string referenceType)
        {
            Reference = reference;
            ReferenceType = referenceType;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"InventoryTransaction [Id: {Id}, Type: {Type}, Quantity: {Quantity}, Reference: {Reference}]";
        }
    }

    /// <summary>
    /// Inventory transaction type enumeration
    /// </summary>
    public enum InventoryTransactionType
    {
        /// <summary>
        /// Stock received from supplier
        /// </summary>
        StockIn = 0,

        /// <summary>
        /// Stock issued/sold
        /// </summary>
        StockOut = 1,

        /// <summary>
        /// Stock reserved for order
        /// </summary>
        Reserved = 2,

        /// <summary>
        /// Stock reservation released
        /// </summary>
        ReservationReleased = 3,

        /// <summary>
        /// Stock allocated for confirmed order
        /// </summary>
        Allocated = 4,

        /// <summary>
        /// Stock shipped/delivered
        /// </summary>
        Shipped = 5,

        /// <summary>
        /// Stock adjustment (count difference)
        /// </summary>
        Adjustment = 6,

        /// <summary>
        /// Stock transfer between warehouses
        /// </summary>
        Transfer = 7,

        /// <summary>
        /// Stock returned from customer
        /// </summary>
        Return = 8,

        /// <summary>
        /// Stock damaged/lost
        /// </summary>
        Damage = 9,

        /// <summary>
        /// Stock expired/obsolete
        /// </summary>
        Expired = 10,

        /// <summary>
        /// Physical count/audit
        /// </summary>
        PhysicalCount = 11
    }
}
