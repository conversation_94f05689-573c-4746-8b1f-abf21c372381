using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;
using ValidationResult = EtyraCommerce.Application.DTOs.Common.ValidationResult;

namespace EtyraCommerce.Application.DTOs.Category
{
    /// <summary>
    /// Category Data Transfer Object for API responses
    /// </summary>
    public class CategoryDto : AuditableDto
    {
        /// <summary>
        /// Category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// Parent category ID (for hierarchical categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Category image URL
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Category icon (CSS class or icon name)
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Whether category is active/visible
        /// </summary>
        public new bool IsActive { get; set; }

        /// <summary>
        /// Whether to show category in menu
        /// </summary>
        public bool ShowInMenu { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Number of products in this category
        /// </summary>
        public int ProductCount { get; set; }

        /// <summary>
        /// Parent category (if any)
        /// </summary>
        public CategoryDto? ParentCategory { get; set; }

        /// <summary>
        /// Child categories
        /// </summary>
        public List<CategoryDto> ChildCategories { get; set; } = new();

        /// <summary>
        /// Category descriptions in different languages
        /// </summary>
        public List<CategoryDescriptionDto> Descriptions { get; set; } = new();

        /// <summary>
        /// Gets the category level (0 = root, 1 = first level, etc.)
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// Checks if this is a root category
        /// </summary>
        public bool IsRoot => ParentCategoryId == null;

        /// <summary>
        /// Checks if this category has children
        /// </summary>
        public bool HasChildren => ChildCategories.Any();

        /// <summary>
        /// Gets the full category path (e.g., "Electronics > Computers > Laptops")
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// Breadcrumb path for navigation
        /// </summary>
        public List<CategoryBreadcrumbDto> Breadcrumbs { get; set; } = new();
    }

    /// <summary>
    /// Category breadcrumb DTO for navigation
    /// </summary>
    public class CategoryBreadcrumbDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public int Level { get; set; }
    }

    /// <summary>
    /// DTO for creating a new category
    /// </summary>
    public class CreateCategoryDto
    {
        /// <summary>
        /// Category name
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// URL slug for SEO (will be auto-generated if not provided)
        /// </summary>
        [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
        public string? Slug { get; set; }

        /// <summary>
        /// Parent category ID (for hierarchical categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Category image URL
        /// </summary>
        [StringLength(500, ErrorMessage = "Image URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Image URL must be a valid URL")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Category icon (CSS class or icon name)
        /// </summary>
        [StringLength(100, ErrorMessage = "Icon cannot exceed 100 characters")]
        public string? Icon { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether category is active/visible
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether to show category in menu
        /// </summary>
        public bool ShowInMenu { get; set; } = true;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        [StringLength(120, ErrorMessage = "Meta title cannot exceed 120 characters")]
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        [StringLength(350, ErrorMessage = "Meta description cannot exceed 350 characters")]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Category descriptions in different languages
        /// </summary>
        public List<CreateCategoryDescriptionDto> Descriptions { get; set; } = new();

        /// <summary>
        /// Gets the display name
        /// </summary>
        public string DisplayName => Name;
    }

    /// <summary>
    /// DTO for updating an existing category
    /// </summary>
    public class UpdateCategoryDto
    {
        /// <summary>
        /// Category name
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
        public string? Slug { get; set; }

        /// <summary>
        /// Parent category ID (for hierarchical categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Category image URL
        /// </summary>
        [StringLength(500, ErrorMessage = "Image URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Image URL must be a valid URL")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Category icon (CSS class or icon name)
        /// </summary>
        [StringLength(100, ErrorMessage = "Icon cannot exceed 100 characters")]
        public string? Icon { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether category is active/visible
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether to show category in menu
        /// </summary>
        public bool ShowInMenu { get; set; } = true;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        [StringLength(120, ErrorMessage = "Meta title cannot exceed 120 characters")]
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        [StringLength(350, ErrorMessage = "Meta description cannot exceed 350 characters")]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Gets the display name
        /// </summary>
        public string DisplayName => Name;

        #region Validation

        /// <summary>
        /// Custom validation to prevent circular references
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Note: Circular reference validation should be done in the service layer
            // where we have access to the repository to check the category hierarchy
            yield break;
        }

        #endregion
    }

    /// <summary>
    /// DTO for category search parameters
    /// </summary>
    public class CategorySearchDto
    {
        /// <summary>
        /// Search term (searches in Name, Description)
        /// </summary>
        [StringLength(100, ErrorMessage = "Search term cannot exceed 100 characters")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by show in menu status
        /// </summary>
        public bool? ShowInMenu { get; set; }

        /// <summary>
        /// Filter by parent category ID
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Filter by level (0 = root categories only)
        /// </summary>
        [Range(0, 10, ErrorMessage = "Level must be between 0 and 10")]
        public int? Level { get; set; }

        /// <summary>
        /// Include child categories in results
        /// </summary>
        public bool IncludeChildren { get; set; } = false;

        /// <summary>
        /// Sort field
        /// </summary>
        public CategorySortField SortBy { get; set; } = CategorySortField.SortOrder;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Category sort fields
    /// </summary>
    public enum CategorySortField
    {
        Name,
        SortOrder,
        CreatedAt,
        UpdatedAt,
        ProductCount,
        Level
    }


}
