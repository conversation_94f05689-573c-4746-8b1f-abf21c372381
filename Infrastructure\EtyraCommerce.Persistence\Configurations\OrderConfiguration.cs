using EtyraCommerce.Domain.Entities.Order;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Order entity
    /// </summary>
    public class OrderConfiguration : AuditableBaseEntityConfiguration<Order>
    {
        public override void Configure(EntityTypeBuilder<Order> builder)
        {
            // Apply base configuration (BaseEntity + AuditableBaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("orders", "etyra_core");

            #region Basic Properties

            // Order Number
            builder.Property(x => x.OrderNumber)
                .HasColumnName("order_number")
                .HasMaxLength(50)
                .IsRequired();

            // Customer Information
            builder.Property(x => x.CustomerId)
                .HasColumnName("customer_id")
                .IsRequired(false); // Allow null for guest orders

            builder.Property(x => x.CustomerFirstName)
                .HasColumnName("customer_first_name")
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(x => x.CustomerLastName)
                .HasColumnName("customer_last_name")
                .HasMaxLength(100)
                .IsRequired();

            #endregion

            #region Value Objects

            // Customer Email (Value Object)
            ValueObjectConversions.ConfigureEmail<Order>(
                builder.Property(x => x.CustomerEmail),
                "customer_email");

            // Customer Phone (Value Object - Optional)
            ValueObjectConversions.ConfigureNullablePhoneNumber<Order>(
                builder.Property(x => x.CustomerPhone),
                "customer_phone");

            // Billing Address (Value Object - Separate Columns)
            ValueObjectConversions.ConfigureAddressSeparateRequired<Order>(
                builder,
                x => x.BillingAddress,
                "billing");

            // Shipping Address (Value Object - Separate Columns)
            ValueObjectConversions.ConfigureAddressSeparateRequired<Order>(
                builder,
                x => x.ShippingAddress,
                "shipping");

            // Financial Value Objects (Money)
            ValueObjectConversions.ConfigureMoneyRequired<Order>(
                builder,
                x => x.Subtotal,
                "subtotal");

            ValueObjectConversions.ConfigureMoneyRequired<Order>(
                builder,
                x => x.TaxAmount,
                "tax");

            ValueObjectConversions.ConfigureMoneyRequired<Order>(
                builder,
                x => x.ShippingCost,
                "shipping_cost");

            ValueObjectConversions.ConfigureMoneyRequired<Order>(
                builder,
                x => x.DiscountAmount,
                "discount");

            ValueObjectConversions.ConfigureMoneyRequired<Order>(
                builder,
                x => x.Total,
                "total");

            // Payment Fee (Value Object - Optional)
            ValueObjectConversions.ConfigureNullableMoney<Order>(
                builder,
                x => x.PaymentFee,
                "payment_fee");

            // Currency (Value Object)
            ValueObjectConversions.ConfigureCurrency<Order>(
                builder.Property(x => x.Currency),
                "currency");

            #endregion

            #region Enums

            // Order Status
            builder.Property(x => x.Status)
                .HasColumnName("status")
                .HasConversion<int>()
                .IsRequired();

            // Payment Status
            builder.Property(x => x.PaymentStatus)
                .HasColumnName("payment_status")
                .HasConversion<int>()
                .IsRequired();

            // Shipping Status
            builder.Property(x => x.ShippingStatus)
                .HasColumnName("shipping_status")
                .HasConversion<int>()
                .IsRequired();

            #endregion

            #region Additional Properties

            // Notes
            builder.Property(x => x.Notes)
                .HasColumnName("notes")
                .HasMaxLength(1000)
                .IsRequired(false);

            builder.Property(x => x.InternalNotes)
                .HasColumnName("internal_notes")
                .HasMaxLength(1000)
                .IsRequired(false);

            // Shipping Information
            builder.Property(x => x.ShippingMethod)
                .HasColumnName("shipping_method")
                .HasMaxLength(100)
                .IsRequired(false);

            builder.Property(x => x.TrackingNumber)
                .HasColumnName("tracking_number")
                .HasMaxLength(100)
                .IsRequired(false);

            // Payment Information
            builder.Property(x => x.PaymentMethodId)
                .HasColumnName("payment_method_id")
                .IsRequired(false);

            builder.Property(x => x.PaymentMethodName)
                .HasColumnName("payment_method_name")
                .HasMaxLength(100)
                .IsRequired(false);

            // Dates
            builder.Property(x => x.ExpectedDeliveryDate)
                .HasColumnName("expected_delivery_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            builder.Property(x => x.ActualDeliveryDate)
                .HasColumnName("actual_delivery_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            #endregion

            #region Relationships

            // Customer relationship (optional for guest orders)
            builder.HasOne(x => x.Customer)
                .WithMany()
                .HasForeignKey(x => x.CustomerId)
                .OnDelete(DeleteBehavior.Restrict)
                .IsRequired(false);

            // Order Items relationship
            builder.HasMany(x => x.OrderItems)
                .WithOne(x => x.Order)
                .HasForeignKey(x => x.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            // Payment Method relationship (optional)
            builder.HasOne(x => x.PaymentMethod)
                .WithMany()
                .HasForeignKey(x => x.PaymentMethodId)
                .OnDelete(DeleteBehavior.SetNull)
                .IsRequired(false);

            #endregion

            #region Indexes

            // Performance indexes
            builder.HasIndex(x => x.OrderNumber)
                .IsUnique()
                .HasDatabaseName("ix_orders_order_number");

            builder.HasIndex(x => x.CustomerId)
                .HasDatabaseName("ix_orders_customer_id");

            builder.HasIndex(x => x.Status)
                .HasDatabaseName("ix_orders_status");

            builder.HasIndex(x => x.PaymentStatus)
                .HasDatabaseName("ix_orders_payment_status");

            builder.HasIndex(x => x.PaymentMethodId)
                .HasDatabaseName("ix_orders_payment_method_id");

            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName("ix_orders_created_at");

            // Composite indexes for common queries
            builder.HasIndex(x => new { x.CustomerId, x.Status })
                .HasDatabaseName("ix_orders_customer_status");

            builder.HasIndex(x => new { x.Status, x.CreatedAt })
                .HasDatabaseName("ix_orders_status_created");

            #endregion

            #region Check Constraints

            // Business rule constraints
            builder.HasCheckConstraint("ck_orders_subtotal_positive",
                "subtotal_amount >= 0");

            builder.HasCheckConstraint("ck_orders_tax_positive",
                "tax_amount >= 0");

            builder.HasCheckConstraint("ck_orders_shipping_positive",
                "shipping_cost_amount >= 0");

            builder.HasCheckConstraint("ck_orders_discount_positive",
                "discount_amount >= 0");

            builder.HasCheckConstraint("ck_orders_total_positive",
                "total_amount >= 0");

            builder.HasCheckConstraint("ck_orders_status_valid",
                "status BETWEEN 0 AND 9");

            builder.HasCheckConstraint("ck_orders_payment_status_valid",
                "payment_status BETWEEN 0 AND 6");

            builder.HasCheckConstraint("ck_orders_shipping_status_valid",
                "shipping_status BETWEEN 0 AND 6");

            #endregion
        }
    }
}
