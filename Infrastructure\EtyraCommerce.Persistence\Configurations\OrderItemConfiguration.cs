using EtyraCommerce.Domain.Entities.Order;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for OrderItem entity
    /// </summary>
    public class OrderItemConfiguration : BaseEntityConfiguration<OrderItem>
    {
        public override void Configure(EntityTypeBuilder<OrderItem> builder)
        {
            // Apply base configuration (BaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("order_items", "etyra_core");

            #region Basic Properties

            // Product Information (Snapshot at time of order)
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            builder.Property(x => x.ProductName)
                .HasColumnName("product_name")
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(x => x.ProductSku)
                .HasColumnName("product_sku")
                .HasMaxLength(100)
                .IsRequired();

            // Quantity
            builder.Property(x => x.Quantity)
                .HasColumnName("quantity")
                .IsRequired();

            #endregion

            #region Value Objects

            // Unit Price (Money Value Object)
            ValueObjectConversions.ConfigureMoneyRequired<OrderItem>(
                builder,
                x => x.UnitPrice,
                "unit_price");

            // Total Price (Money Value Object)
            ValueObjectConversions.ConfigureMoneyRequired<OrderItem>(
                builder,
                x => x.TotalPrice,
                "total_price");

            // Discount Amount (Money Value Object - Optional)
            ValueObjectConversions.ConfigureNullableMoney<OrderItem>(
                builder,
                x => x.DiscountAmount,
                "discount");

            // Tax Amount (Money Value Object - Optional)
            ValueObjectConversions.ConfigureNullableMoney<OrderItem>(
                builder,
                x => x.TaxAmount,
                "tax");

            #endregion

            #region Additional Properties

            // Variant Information
            builder.Property(x => x.VariantInfo)
                .HasColumnName("variant_info")
                .HasMaxLength(500)
                .IsRequired(false);

            // Special Instructions
            builder.Property(x => x.SpecialInstructions)
                .HasColumnName("special_instructions")
                .HasMaxLength(1000)
                .IsRequired(false);

            // Order Relationship
            builder.Property(x => x.OrderId)
                .HasColumnName("order_id")
                .IsRequired();

            #endregion

            #region Relationships

            // Order relationship
            builder.HasOne(x => x.Order)
                .WithMany(x => x.OrderItems)
                .HasForeignKey(x => x.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product relationship
            builder.HasOne(x => x.Product)
                .WithMany()
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            #endregion

            #region Indexes

            // Performance indexes
            builder.HasIndex(x => x.OrderId)
                .HasDatabaseName("ix_order_items_order_id");

            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_order_items_product_id");

            builder.HasIndex(x => x.ProductSku)
                .HasDatabaseName("ix_order_items_product_sku");

            // Composite indexes for common queries
            builder.HasIndex(x => new { x.OrderId, x.ProductId })
                .HasDatabaseName("ix_order_items_order_product");

            #endregion

            #region Check Constraints

            // Business rule constraints
            builder.HasCheckConstraint("ck_order_items_quantity_positive",
                "quantity > 0");

            builder.HasCheckConstraint("ck_order_items_unit_price_positive",
                "unit_price_amount > 0");

            builder.HasCheckConstraint("ck_order_items_total_price_positive",
                "total_price_amount > 0");

            builder.HasCheckConstraint("ck_order_items_discount_positive",
                "discount_amount IS NULL OR discount_amount >= 0");

            builder.HasCheckConstraint("ck_order_items_tax_positive",
                "tax_amount IS NULL OR tax_amount >= 0");

            #endregion
        }
    }
}
