using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Queries;

/// <summary>
/// Query to get all currencies
/// </summary>
public class GetAllCurrenciesQuery : IRequest<CustomResponseDto<List<CurrencyDto>>>
{
    /// <summary>
    /// Whether to include only active currencies
    /// </summary>
    public bool? OnlyActive { get; set; }

    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="onlyActive">Whether to include only active currencies</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetAllCurrenciesQuery(bool? onlyActive = null, bool tracking = false)
    {
        OnlyActive = onlyActive;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetAllCurrenciesQuery() { }
}

/// <summary>
/// Query to get active currencies for selection
/// </summary>
public class GetActiveCurrenciesQuery : IRequest<CustomResponseDto<List<CurrencySummaryDto>>>
{
    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    public GetActiveCurrenciesQuery(bool tracking = false)
    {
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetActiveCurrenciesQuery() { }
}
