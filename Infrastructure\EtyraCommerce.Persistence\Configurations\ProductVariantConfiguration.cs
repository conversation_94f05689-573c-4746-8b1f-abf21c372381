using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ProductVariant entity
    /// </summary>
    public class ProductVariantConfiguration : IEntityTypeConfiguration<ProductVariant>
    {
        public void Configure(EntityTypeBuilder<ProductVariant> builder)
        {
            #region Table Configuration

            builder.ToTable("product_variants", "etyra_core");

            #endregion

            #region Primary Key

            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id)
                .HasColumnName("id")
                .IsRequired();

            #endregion

            #region Basic Properties

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasMaxLength(500)
                .IsRequired(false);

            // SKU
            builder.Property(x => x.SKU)
                .HasColumnName("sku")
                .HasMaxLength(100)
                .IsRequired();

            // Stock Quantity
            builder.Property(x => x.StockQuantity)
                .HasColumnName("stock_quantity")
                .HasDefaultValue(0)
                .IsRequired();

            // Manage Stock
            builder.Property(x => x.ManageStock)
                .HasColumnName("manage_stock")
                .HasDefaultValue(true)
                .IsRequired();

            // Weight
            builder.Property(x => x.Weight)
                .HasColumnName("weight")
                .HasColumnType("decimal(10,3)")
                .IsRequired(false);

            // Image URL
            builder.Property(x => x.ImageUrl)
                .HasColumnName("image_url")
                .HasMaxLength(500)
                .IsRequired(false);

            // Sort Order
            builder.Property(x => x.SortOrder)
                .HasColumnName("sort_order")
                .HasDefaultValue(0)
                .IsRequired();

            // Is Active
            builder.Property(x => x.IsActive)
                .HasColumnName("is_active")
                .HasDefaultValue(true)
                .IsRequired();

            // Is Default
            builder.Property(x => x.IsDefault)
                .HasColumnName("is_default")
                .HasDefaultValue(false)
                .IsRequired();

            // Barcode
            builder.Property(x => x.Barcode)
                .HasColumnName("barcode")
                .HasMaxLength(100)
                .IsRequired(false);

            #endregion

            #region Value Objects

            // Price (Money Value Object)
            builder.OwnsOne(x => x.Price, price =>
            {
                price.Property(p => p.Amount)
                    .HasColumnName("price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                price.Property(p => p.Currency)
                    .HasConversion(
                        c => c.Code,  // Store Currency.Code as string
                        s => Currency.FromCode(s))  // Convert string back to Currency
                    .HasColumnName("price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Cost (Money Value Object)
            builder.OwnsOne(x => x.Cost, cost =>
            {
                cost.Property(c => c.Amount)
                    .HasColumnName("cost_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                cost.Property(c => c.Currency)
                    .HasConversion(
                        c => c.Code,  // Store Currency.Code as string
                        s => Currency.FromCode(s))  // Convert string back to Currency
                    .HasColumnName("cost_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Dimensions (ProductDimensions Value Object)
            builder.OwnsOne(x => x.Dimensions, dimensions =>
            {
                dimensions.Property(d => d.Length)
                    .HasColumnName("dimensions_length")
                    .HasColumnType("decimal(10,2)")
                    .IsRequired(false);

                dimensions.Property(d => d.Width)
                    .HasColumnName("dimensions_width")
                    .HasColumnType("decimal(10,2)")
                    .IsRequired(false);

                dimensions.Property(d => d.Height)
                    .HasColumnName("dimensions_height")
                    .HasColumnType("decimal(10,2)")
                    .IsRequired(false);

                dimensions.Property(d => d.Weight)
                    .HasColumnName("dimensions_weight")
                    .HasColumnType("decimal(10,3)")
                    .IsRequired(false);

                dimensions.Property(d => d.Unit)
                    .HasColumnName("dimensions_unit")
                    .HasMaxLength(10)
                    .IsRequired(false);

                dimensions.Property(d => d.WeightUnit)
                    .HasColumnName("dimensions_weight_unit")
                    .HasMaxLength(10)
                    .IsRequired(false);
            });

            #endregion

            #region Foreign Keys

            // Product relationship
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            builder.HasOne(x => x.Product)
                .WithMany(p => p.Variants)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Unique SKU index
            builder.HasIndex(x => x.SKU)
                .IsUnique()
                .HasDatabaseName("ix_product_variants_sku_unique");

            // Product variants index
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_product_variants_product_id");

            // Active variants index
            builder.HasIndex(x => x.IsActive)
                .HasDatabaseName("ix_product_variants_is_active");

            // Default variant index
            builder.HasIndex(x => x.IsDefault)
                .HasDatabaseName("ix_product_variants_is_default");

            // Sort order index
            builder.HasIndex(x => x.SortOrder)
                .HasDatabaseName("ix_product_variants_sort_order");

            // Composite index for product + active
            builder.HasIndex(x => new { x.ProductId, x.IsActive })
                .HasDatabaseName("ix_product_variants_product_active");

            // Composite index for product + default
            builder.HasIndex(x => new { x.ProductId, x.IsDefault })
                .HasDatabaseName("ix_product_variants_product_default");

            #endregion

            #region Check Constraints

            // Stock quantity constraint
            builder.ToTable(t => t.HasCheckConstraint("ck_product_variants_stock_quantity", "stock_quantity >= 0"));

            // Weight constraint
            builder.ToTable(t => t.HasCheckConstraint("ck_product_variants_weight", "weight IS NULL OR weight >= 0"));

            // Sort order constraint
            builder.ToTable(t => t.HasCheckConstraint("ck_product_variants_sort_order", "sort_order >= 0"));

            // Price amount constraint
            builder.ToTable(t => t.HasCheckConstraint("ck_product_variants_price_amount", "price_amount IS NULL OR price_amount >= 0"));

            // Cost amount constraint
            builder.ToTable(t => t.HasCheckConstraint("ck_product_variants_cost_amount", "cost_amount IS NULL OR cost_amount >= 0"));

            #endregion
        }
    }
}
