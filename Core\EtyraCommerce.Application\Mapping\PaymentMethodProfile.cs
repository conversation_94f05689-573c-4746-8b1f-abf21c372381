using AutoMapper;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment.Commands;
using EtyraCommerce.Domain.Entities.Payment;

namespace EtyraCommerce.Application.Mapping;

/// <summary>
/// AutoMapper profile for PaymentMethod entity and DTOs
/// </summary>
public class PaymentMethodProfile : Profile
{
    public PaymentMethodProfile()
    {
        // Entity to DTO mappings
        CreateMap<PaymentMethod, PaymentMethodDto>()
            .ForMember(dest => dest.FeeCurrencyCode, opt => opt.MapFrom(src => src.FeeCurrency != null ? src.FeeCurrency.Code : null))
            .ForMember(dest => dest.FeeCurrencyName, opt => opt.MapFrom(src => src.FeeCurrency != null ? src.FeeCurrency.Name : null))
            .ForMember(dest => dest.FeeCurrencySymbol, opt => opt.MapFrom(src => src.FeeCurrency != null ? src.FeeCurrency.Symbol : null))
            .ForMember(dest => dest.FeeValue, opt => opt.MapFrom(src => src.FeeValue))
            .ForMember(dest => dest.MinimumOrderAmount, opt => opt.MapFrom(src => src.MinimumOrderAmount != null ? src.MinimumOrderAmount.Amount : (decimal?)null))
            .ForMember(dest => dest.MinimumOrderCurrency, opt => opt.MapFrom(src => src.MinimumOrderAmount != null ? src.MinimumOrderAmount.Currency.Code : null))
            .ForMember(dest => dest.MaximumOrderAmount, opt => opt.MapFrom(src => src.MaximumOrderAmount != null ? src.MaximumOrderAmount.Amount : (decimal?)null))
            .ForMember(dest => dest.MaximumOrderCurrency, opt => opt.MapFrom(src => src.MaximumOrderAmount != null ? src.MaximumOrderAmount.Currency.Code : null))
            .ForMember(dest => dest.CalculatedFee, opt => opt.Ignore()) // Runtime calculated
            .ForMember(dest => dest.IsAvailableForAmount, opt => opt.Ignore()); // Runtime calculated

        // Command to DTO mappings
        CreateMap<CreatePaymentMethodCommand, CreatePaymentMethodDto>();
        CreateMap<UpdatePaymentMethodCommand, UpdatePaymentMethodDto>();

        // DTO to Command mappings (if needed)
        CreateMap<CreatePaymentMethodDto, CreatePaymentMethodCommand>();
        CreateMap<UpdatePaymentMethodDto, UpdatePaymentMethodCommand>();
    }
}
