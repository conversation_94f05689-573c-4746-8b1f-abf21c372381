using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for getting a user by ID
    /// </summary>
    public class GetUserQuery : IRequest<CustomResponseDto<UserDto?>>
    {
        /// <summary>
        /// User ID to retrieve
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Whether to include deleted users in the search
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Whether to track the entity for changes
        /// </summary>
        public bool Tracking { get; set; } = false;

        public GetUserQuery() { }

        public GetUserQuery(Guid userId, bool includeDeleted = false, bool tracking = false)
        {
            UserId = userId;
            IncludeDeleted = includeDeleted;
            Tracking = tracking;
        }
    }
}
