using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.Services.User.Commands;
using EtyraCommerce.Application.Services.User.Handlers.Commands;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class LoginUserCommandHandlerTests
    {
        private readonly Mock<IUserProcessService> _mockUserProcessService;
        private readonly Mock<ILogger<LoginUserCommandHandler>> _mockLogger;
        private readonly LoginUserCommandHandler _handler;

        public LoginUserCommandHandlerTests()
        {
            _mockUserProcessService = new Mock<IUserProcessService>();
            _mockLogger = new Mock<ILogger<LoginUserCommandHandler>>();
            _handler = new LoginUserCommandHandler(_mockUserProcessService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidCredentials_ReturnsSuccessWithUserDto()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "<EMAIL>",
                Password = "ValidPassword123!",
                RememberMe = true
            };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                Username = "testuser",
                FirstName = "Test",
                LastName = "User",
                IsActive = true,
                IsEmailConfirmed = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "Login successful"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(expectedUserDto.Id);
            result.Data.Email.Should().Be(expectedUserDto.Email);
            result.Data.Username.Should().Be(expectedUserDto.Username);

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyEmailOrUsername_ReturnsBadRequest()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "",
                Password = "ValidPassword123!",
                RememberMe = false
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email or username is required");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_WhitespaceEmailOrUsername_ReturnsBadRequest()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "   ",
                Password = "ValidPassword123!",
                RememberMe = false
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email or username is required");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_EmptyPassword_ReturnsBadRequest()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "<EMAIL>",
                Password = "",
                RememberMe = false
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_InvalidCredentials_ReturnsFailureFromProcessService()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "<EMAIL>",
                Password = "WrongPassword",
                RememberMe = false
            };

            var expectedResponse = CustomResponseDto<UserDto>.BadRequest("Invalid email/username or password");

            _mockUserProcessService
                .Setup(x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Invalid email/username or password");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "<EMAIL>",
                Password = "ValidPassword123!",
                RememberMe = false
            };

            _mockUserProcessService
                .Setup(x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during login");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(command.EmailOrUsername, command.Password),
                Times.Once
            );
        }

        [Theory]
        [InlineData("<EMAIL>", "Password123!")]
        [InlineData("username", "AnotherPass456!")]
        [InlineData("<EMAIL>", "ComplexP@ssw0rd")]
        public async Task Handle_VariousValidInputs_CallsProcessServiceWithCorrectParameters(string emailOrUsername, string password)
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = emailOrUsername,
                Password = password,
                RememberMe = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.Success(
                StatusCodes.Status200OK,
                new UserDto { Id = Guid.NewGuid(), Email = emailOrUsername },
                "Login successful"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessLoginAsync(emailOrUsername, password))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(emailOrUsername, password),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_NullEmailOrUsername_ReturnsBadRequest()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = null!,
                Password = "ValidPassword123!",
                RememberMe = false
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email or username is required");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_NullPassword_ReturnsBadRequest()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                EmailOrUsername = "<EMAIL>",
                Password = null!,
                RememberMe = false
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessLoginAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }
    }
}
