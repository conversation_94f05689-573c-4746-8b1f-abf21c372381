﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOrderPaymentMethodRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "payment_method",
                schema: "etyra_core",
                table: "orders",
                newName: "payment_method_name");

            migrationBuilder.AddColumn<decimal>(
                name: "payment_fee_amount",
                schema: "etyra_core",
                table: "orders",
                type: "numeric(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "payment_fee_currency",
                schema: "etyra_core",
                table: "orders",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "payment_method_id",
                schema: "etyra_core",
                table: "orders",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_orders_payment_method_id",
                schema: "etyra_core",
                table: "orders",
                column: "payment_method_id");

            migrationBuilder.AddForeignKey(
                name: "FK_orders_payment_methods_payment_method_id",
                schema: "etyra_core",
                table: "orders",
                column: "payment_method_id",
                principalSchema: "etyra_core",
                principalTable: "payment_methods",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_orders_payment_methods_payment_method_id",
                schema: "etyra_core",
                table: "orders");

            migrationBuilder.DropIndex(
                name: "ix_orders_payment_method_id",
                schema: "etyra_core",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "payment_fee_amount",
                schema: "etyra_core",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "payment_fee_currency",
                schema: "etyra_core",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "payment_method_id",
                schema: "etyra_core",
                table: "orders");

            migrationBuilder.RenameColumn(
                name: "payment_method_name",
                schema: "etyra_core",
                table: "orders",
                newName: "payment_method");
        }
    }
}
