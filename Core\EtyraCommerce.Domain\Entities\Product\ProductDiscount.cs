using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product discount entity for quantity-based or time-based discounts
    /// </summary>
    public class ProductDiscount : BaseEntity
    {
        /// <summary>
        /// Discount name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Discount description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Discount type
        /// </summary>
        public DiscountType Type { get; set; } = DiscountType.Percentage;

        /// <summary>
        /// Discount value (percentage or fixed amount)
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// Minimum quantity required for discount
        /// </summary>
        public int? MinQuantity { get; set; }

        /// <summary>
        /// Maximum quantity for discount (null = unlimited)
        /// </summary>
        public int? MaxQuantity { get; set; }

        /// <summary>
        /// Minimum order amount for discount (Value Object)
        /// </summary>
        public Money? MinOrderAmount { get; set; }

        /// <summary>
        /// Maximum discount amount (for percentage discounts)
        /// </summary>
        public Money? MaxDiscountAmount { get; set; }

        /// <summary>
        /// Discount start date
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Discount end date
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Whether the discount is active
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Priority order (higher number = higher priority)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Maximum number of uses (null = unlimited)
        /// </summary>
        public int? MaxUses { get; set; }

        /// <summary>
        /// Current number of uses
        /// </summary>
        public int CurrentUses { get; set; } = 0;

        /// <summary>
        /// Whether discount can be combined with other discounts
        /// </summary>
        public bool CanCombine { get; set; } = false;

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Checks if the discount is currently valid
        /// </summary>
        public bool IsValid
        {
            get
            {
                if (!IsActive) return false;

                var now = DateTime.UtcNow;
                if (StartDate.HasValue && StartDate > now) return false;
                if (EndDate.HasValue && EndDate < now) return false;
                if (MaxUses.HasValue && CurrentUses >= MaxUses) return false;

                return true;
            }
        }

        /// <summary>
        /// Gets remaining uses
        /// </summary>
        public int? RemainingUses
        {
            get
            {
                if (!MaxUses.HasValue) return null;
                return Math.Max(0, MaxUses.Value - CurrentUses);
            }
        }

        /// <summary>
        /// Checks if discount has usage limit
        /// </summary>
        public bool HasUsageLimit => MaxUses.HasValue;

        /// <summary>
        /// Checks if discount is time-limited
        /// </summary>
        public bool IsTimeLimited => StartDate.HasValue || EndDate.HasValue;

        /// <summary>
        /// Checks if discount is quantity-based
        /// </summary>
        public bool IsQuantityBased => MinQuantity.HasValue || MaxQuantity.HasValue;

        #endregion

        #region Business Methods

        /// <summary>
        /// Calculates discount amount for given quantity and price
        /// </summary>
        public Money CalculateDiscount(int quantity, Money unitPrice)
        {
            if (!IsValid) return Money.Zero(unitPrice.Currency);
            if (MinQuantity.HasValue && quantity < MinQuantity) return Money.Zero(unitPrice.Currency);
            if (MaxQuantity.HasValue && quantity > MaxQuantity) return Money.Zero(unitPrice.Currency);

            var totalAmount = new Money(unitPrice.Amount * quantity, unitPrice.Currency);

            if (MinOrderAmount != null && totalAmount.Amount < MinOrderAmount.Amount)
                return Money.Zero(unitPrice.Currency);

            Money discountAmount;

            switch (Type)
            {
                case DiscountType.Percentage:
                    discountAmount = new Money(totalAmount.Amount * (Value / 100), unitPrice.Currency);
                    break;

                case DiscountType.FixedAmount:
                    discountAmount = new Money(Value, unitPrice.Currency);
                    break;

                case DiscountType.FixedAmountPerItem:
                    discountAmount = new Money(Value * quantity, unitPrice.Currency);
                    break;

                default:
                    return Money.Zero(unitPrice.Currency);
            }

            // Apply maximum discount limit
            if (MaxDiscountAmount != null && discountAmount.Amount > MaxDiscountAmount.Amount)
                discountAmount = MaxDiscountAmount;

            // Ensure discount doesn't exceed total amount
            if (discountAmount.Amount > totalAmount.Amount)
                discountAmount = totalAmount;

            return discountAmount;
        }

        /// <summary>
        /// Applies the discount (increments usage count)
        /// </summary>
        public void ApplyDiscount()
        {
            if (!IsValid)
                throw new InvalidOperationException("Cannot apply invalid discount");

            CurrentUses++;
            MarkAsUpdated();
        }

        /// <summary>
        /// Reverts the discount (decrements usage count)
        /// </summary>
        public void RevertDiscount()
        {
            if (CurrentUses > 0)
            {
                CurrentUses--;
                MarkAsUpdated();
            }
        }

        /// <summary>
        /// Activates the discount
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the discount
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates discount period
        /// </summary>
        public void UpdatePeriod(DateTime? startDate, DateTime? endDate)
        {
            if (startDate.HasValue && endDate.HasValue && startDate > endDate)
                throw new ArgumentException("Start date cannot be after end date");

            StartDate = startDate;
            EndDate = endDate;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates usage limits
        /// </summary>
        public void UpdateUsageLimits(int? maxUses)
        {
            if (maxUses.HasValue && maxUses < 0)
                throw new ArgumentException("Max uses cannot be negative");

            MaxUses = maxUses;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"ProductDiscount [Id: {Id}, Name: {Name}, Type: {Type}, Value: {Value}, IsValid: {IsValid}]";
        }
    }

    /// <summary>
    /// Discount type enumeration
    /// </summary>
    public enum DiscountType
    {
        Percentage = 0,
        FixedAmount = 1,
        FixedAmountPerItem = 2
    }
}
