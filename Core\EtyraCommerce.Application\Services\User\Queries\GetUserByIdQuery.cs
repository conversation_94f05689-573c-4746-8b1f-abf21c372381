using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for getting user by ID
    /// </summary>
    public class GetUserByIdQuery : IRequest<CustomResponseDto<UserDto>>
    {
        /// <summary>
        /// User ID to retrieve
        /// </summary>
        public Guid UserId { get; set; }

        public GetUserByIdQuery() { }

        public GetUserByIdQuery(Guid userId)
        {
            UserId = userId;
        }
    }
}
