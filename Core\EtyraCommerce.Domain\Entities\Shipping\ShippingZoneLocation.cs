using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Shipping zone location mapping entity
    /// Maps specific locations (regions, cities, districts) to shipping zones for granular shipping control
    /// </summary>
    public class ShippingZoneLocation : BaseEntity
    {
        #region Foreign Keys

        /// <summary>
        /// Reference to the shipping zone
        /// </summary>
        public Guid ShippingZoneId { get; set; }

        /// <summary>
        /// Reference to the country (optional - for country-level mapping)
        /// </summary>
        public Guid? CountryId { get; set; }

        /// <summary>
        /// Reference to the region (optional - for region-level mapping)
        /// </summary>
        public Guid? RegionId { get; set; }

        /// <summary>
        /// Reference to the city (optional - for city-level mapping)
        /// </summary>
        public Guid? CityId { get; set; }

        /// <summary>
        /// Reference to the district (optional - for district-level mapping)
        /// </summary>
        public Guid? DistrictId { get; set; }

        #endregion

        #region Properties

        /// <summary>
        /// Location type for this mapping
        /// </summary>
        public LocationType LocationType { get; set; } = LocationType.Country;

        /// <summary>
        /// Indicates if this location mapping is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for location within the zone
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Override minimum delivery days for this specific location
        /// </summary>
        public int? OverrideMinDeliveryDays { get; set; }

        /// <summary>
        /// Override maximum delivery days for this specific location
        /// </summary>
        public int? OverrideMaxDeliveryDays { get; set; }

        /// <summary>
        /// Additional shipping cost modifier for this location (percentage)
        /// </summary>
        public decimal? CostModifierPercentage { get; set; }

        /// <summary>
        /// Additional shipping cost modifier for this location (fixed amount)
        /// </summary>
        public decimal? CostModifierFixed { get; set; }

        /// <summary>
        /// Additional shipping notes for this location within the zone
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Shipping zone
        /// </summary>
        public virtual ShippingZone ShippingZone { get; set; } = null!;

        /// <summary>
        /// Country (optional)
        /// </summary>
        public virtual Country? Country { get; set; }

        /// <summary>
        /// Region (optional)
        /// </summary>
        public virtual Region? Region { get; set; }

        /// <summary>
        /// City (optional)
        /// </summary>
        public virtual City? City { get; set; }

        /// <summary>
        /// District (optional)
        /// </summary>
        public virtual District? District { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected ShippingZoneLocation() { }

        /// <summary>
        /// Constructor for country-level mapping
        /// </summary>
        public ShippingZoneLocation(Guid shippingZoneId, Guid countryId)
        {
            ShippingZoneId = shippingZoneId;
            CountryId = countryId;
            LocationType = LocationType.Country;
            IsActive = true;
            DisplayOrder = 0;
        }

        /// <summary>
        /// Constructor for region-level mapping
        /// </summary>
        public ShippingZoneLocation(Guid shippingZoneId, Guid countryId, Guid regionId)
        {
            ShippingZoneId = shippingZoneId;
            CountryId = countryId;
            RegionId = regionId;
            LocationType = LocationType.Region;
            IsActive = true;
            DisplayOrder = 0;
        }

        /// <summary>
        /// Constructor for city-level mapping
        /// </summary>
        public ShippingZoneLocation(Guid shippingZoneId, Guid countryId, Guid regionId, Guid cityId)
        {
            ShippingZoneId = shippingZoneId;
            CountryId = countryId;
            RegionId = regionId;
            CityId = cityId;
            LocationType = LocationType.City;
            IsActive = true;
            DisplayOrder = 0;
        }

        /// <summary>
        /// Constructor for district-level mapping
        /// </summary>
        public ShippingZoneLocation(Guid shippingZoneId, Guid countryId, Guid regionId, Guid cityId, Guid districtId)
        {
            ShippingZoneId = shippingZoneId;
            CountryId = countryId;
            RegionId = regionId;
            CityId = cityId;
            DistrictId = districtId;
            LocationType = LocationType.District;
            IsActive = true;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Activates this location mapping
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates this location mapping
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets override delivery times for this location
        /// </summary>
        public void SetOverrideDeliveryTimes(int? minDays, int? maxDays)
        {
            if (minDays.HasValue && minDays < 0)
                throw new ArgumentException("Minimum delivery days cannot be negative");
            
            if (maxDays.HasValue && maxDays < 0)
                throw new ArgumentException("Maximum delivery days cannot be negative");
            
            if (minDays.HasValue && maxDays.HasValue && minDays > maxDays)
                throw new ArgumentException("Minimum delivery days cannot be greater than maximum");

            OverrideMinDeliveryDays = minDays;
            OverrideMaxDeliveryDays = maxDays;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets cost modifiers for this location
        /// </summary>
        public void SetCostModifiers(decimal? percentageModifier, decimal? fixedModifier)
        {
            CostModifierPercentage = percentageModifier;
            CostModifierFixed = fixedModifier;
            MarkAsUpdated();
        }

        /// <summary>
        /// Clears all overrides (use zone defaults)
        /// </summary>
        public void ClearOverrides()
        {
            OverrideMinDeliveryDays = null;
            OverrideMaxDeliveryDays = null;
            CostModifierPercentage = null;
            CostModifierFixed = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for location within zone
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates notes for this location mapping
        /// </summary>
        public void UpdateNotes(string? notes)
        {
            Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim();
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the location name based on the location type
        /// </summary>
        public string GetLocationName()
        {
            return LocationType switch
            {
                LocationType.Country => Country?.Name ?? "Unknown Country",
                LocationType.Region => Region?.Name ?? "Unknown Region",
                LocationType.City => City?.Name ?? "Unknown City",
                LocationType.District => District?.Name ?? "Unknown District",
                _ => "Unknown Location"
            };
        }

        /// <summary>
        /// Gets the full location path (Country > Region > City > District)
        /// </summary>
        public string GetFullLocationPath()
        {
            var parts = new List<string>();

            if (Country != null) parts.Add(Country.Name);
            if (Region != null) parts.Add(Region.Name);
            if (City != null) parts.Add(City.Name);
            if (District != null) parts.Add(District.Name);

            return string.Join(" > ", parts);
        }

        /// <summary>
        /// Checks if this location mapping can be used for shipping
        /// </summary>
        public bool CanShip() => IsActive && !IsDeleted && ShippingZone?.CanShip() == true;

        /// <summary>
        /// Checks if delivery times are overridden
        /// </summary>
        public bool HasOverrideDeliveryTimes() => 
            OverrideMinDeliveryDays.HasValue || OverrideMaxDeliveryDays.HasValue;

        /// <summary>
        /// Checks if cost modifiers are set
        /// </summary>
        public bool HasCostModifiers() => 
            CostModifierPercentage.HasValue || CostModifierFixed.HasValue;

        #endregion

        #region Validation

        /// <summary>
        /// Validates shipping zone location mapping data
        /// </summary>
        public bool IsValid()
        {
            // Must have shipping zone
            if (ShippingZoneId == Guid.Empty)
                return false;

            // Must have at least one location reference based on type
            return LocationType switch
            {
                LocationType.Country => CountryId.HasValue,
                LocationType.Region => CountryId.HasValue && RegionId.HasValue,
                LocationType.City => CountryId.HasValue && RegionId.HasValue && CityId.HasValue,
                LocationType.District => CountryId.HasValue && RegionId.HasValue && CityId.HasValue && DistrictId.HasValue,
                _ => false
            };
        }

        #endregion
    }

    /// <summary>
    /// Location type enumeration for shipping zone location mapping
    /// </summary>
    public enum LocationType
    {
        /// <summary>
        /// Country-level mapping
        /// </summary>
        Country = 1,

        /// <summary>
        /// Region/State/County-level mapping
        /// </summary>
        Region = 2,

        /// <summary>
        /// City-level mapping
        /// </summary>
        City = 3,

        /// <summary>
        /// District/Sector-level mapping
        /// </summary>
        District = 4
    }
}
