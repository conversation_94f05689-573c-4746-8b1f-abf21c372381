using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Queries
{
    /// <summary>
    /// Handler for getting order by order number
    /// </summary>
    public class GetOrderByOrderNumberQueryHandler : IRequestHandler<GetOrderByOrderNumberQuery, CustomResponseDto<OrderDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<GetOrderByOrderNumberQueryHandler> _logger;

        public GetOrderByOrderNumberQueryHandler(
            IOrderProcessService orderProcessService,
            ILogger<GetOrderByOrderNumberQueryHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<OrderDto>> Handle(GetOrderByOrderNumberQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get order by order number: {OrderNumber}", request.OrderNumber);

                // Delegate to ProcessService (CQRS pattern: Handler => ProcessService => Repository)
                var result = await _orderProcessService.GetOrderByOrderNumberAsync(
                    request.OrderNumber,
                    request.IncludeItems,
                    request.IncludeCustomer,
                    request.LanguageCode);

                _logger.LogInformation("Get order by order number completed: {OrderNumber}", request.OrderNumber);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get order by order number: {OrderNumber}", request.OrderNumber);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order");
            }
        }
    }
}
