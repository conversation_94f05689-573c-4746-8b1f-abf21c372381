using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category;
using EtyraCommerce.Application.Services.Category.Commands;
using EtyraCommerce.Application.Services.Category.Handlers.Commands;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EtyraCommerce.UnitTests.Services.Category.Handlers
{
    /// <summary>
    /// Unit tests for CreateCategoryCommandHandler
    /// </summary>
    public class CreateCategoryCommandHandlerTests
    {
        private readonly Mock<ICategoryProcessService> _mockCategoryProcessService;
        private readonly Mock<ILogger<CreateCategoryCommandHandler>> _mockLogger;
        private readonly CreateCategoryCommandHandler _handler;

        public CreateCategoryCommandHandlerTests()
        {
            _mockCategoryProcessService = new Mock<ICategoryProcessService>();
            _mockLogger = new Mock<ILogger<CreateCategoryCommandHandler>>();
            _handler = new CreateCategoryCommandHandler(_mockCategoryProcessService.Object, _mockLogger.Object);
        }

        #region Validation Tests

        [Fact]
        public async Task Handle_EmptyName_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand { Name = "" };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Category name is required", result.Message);
        }

        [Fact]
        public async Task Handle_WhitespaceName_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand { Name = "   " };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Category name is required", result.Message);
        }

        [Fact]
        public async Task Handle_NameTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand { Name = new string('a', 201) };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Category name cannot exceed 200 characters", result.Message);
        }

        [Fact]
        public async Task Handle_DescriptionTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Description = new string('a', 1001)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Category description cannot exceed 1000 characters", result.Message);
        }

        [Fact]
        public async Task Handle_SlugTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Slug = new string('a', 201)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Category slug cannot exceed 200 characters", result.Message);
        }

        [Fact]
        public async Task Handle_ImageUrlTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                ImageUrl = new string('a', 501)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Image URL cannot exceed 500 characters", result.Message);
        }

        [Fact]
        public async Task Handle_IconTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Icon = new string('a', 101)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Icon cannot exceed 100 characters", result.Message);
        }

        [Fact]
        public async Task Handle_NegativeSortOrder_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                SortOrder = -1
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Sort order must be non-negative", result.Message);
        }

        [Fact]
        public async Task Handle_MetaTitleTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                MetaTitle = new string('a', 121)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Meta title cannot exceed 120 characters", result.Message);
        }

        [Fact]
        public async Task Handle_MetaDescriptionTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                MetaDescription = new string('a', 351)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Meta description cannot exceed 350 characters", result.Message);
        }

        [Fact]
        public async Task Handle_MetaKeywordsTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                MetaKeywords = new string('a', 201)
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Meta keywords cannot exceed 200 characters", result.Message);
        }

        #endregion

        #region Description Validation Tests

        [Fact]
        public async Task Handle_DescriptionWithEmptyName_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = "",
                        LanguageCode = "en-US"
                    }
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Description name is required for all languages", result.Message);
        }

        [Fact]
        public async Task Handle_DescriptionNameTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = new string('a', 201),
                        LanguageCode = "en-US"
                    }
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Description name cannot exceed 200 characters", result.Message);
        }

        [Fact]
        public async Task Handle_DescriptionTextTooLong_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = "Valid Description Name",
                        Description = new string('a', 2001),
                        LanguageCode = "en-US"
                    }
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Description text cannot exceed 2000 characters", result.Message);
        }

        [Fact]
        public async Task Handle_DescriptionWithEmptyLanguageCode_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = "Valid Description Name",
                        LanguageCode = ""
                    }
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Language code is required for all descriptions", result.Message);
        }

        [Fact]
        public async Task Handle_DuplicateLanguageCodes_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Valid Name",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = "English Name",
                        LanguageCode = "en-US"
                    },
                    new CreateCategoryDescriptionDto
                    {
                        Name = "Another English Name",
                        LanguageCode = "en-us" // Same language code, different case
                    }
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(400, result.StatusCode);
            Assert.Contains("Duplicate language codes found", result.Message);
        }

        #endregion

        #region Success Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessResponse()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Test Category",
                Description = "Test Description",
                IsActive = true,
                ShowInMenu = true
            };

            var expectedCategoryDto = new CategoryDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Category",
                Description = "Test Description",
                IsActive = true,
                ShowInMenu = true
            };

            var expectedResponse = CustomResponseDto<CategoryDto>.Success(201, expectedCategoryDto, "Category created successfully");

            _mockCategoryProcessService.Setup(s => s.ProcessCreateCategoryAsync(It.IsAny<CreateCategoryDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(201, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal("Test Category", result.Data.Name);

            _mockCategoryProcessService.Verify(s => s.ProcessCreateCategoryAsync(It.Is<CreateCategoryDto>(dto =>
                dto.Name == "Test Category" &&
                dto.Description == "Test Description" &&
                dto.IsActive == true &&
                dto.ShowInMenu == true)), Times.Once);
        }

        [Fact]
        public async Task Handle_ValidCommandWithDescriptions_ReturnsSuccessResponse()
        {
            // Arrange
            var command = new CreateCategoryCommand 
            { 
                Name = "Test Category",
                Descriptions = new List<CreateCategoryDescriptionDto>
                {
                    new CreateCategoryDescriptionDto
                    {
                        Name = "Test Category EN",
                        Description = "English Description",
                        LanguageCode = "en-US"
                    },
                    new CreateCategoryDescriptionDto
                    {
                        Name = "Test Kategori TR",
                        Description = "Türkçe Açıklama",
                        LanguageCode = "tr-TR"
                    }
                }
            };

            var expectedCategoryDto = new CategoryDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Category"
            };

            var expectedResponse = CustomResponseDto<CategoryDto>.Success(201, expectedCategoryDto, "Category created successfully");

            _mockCategoryProcessService.Setup(s => s.ProcessCreateCategoryAsync(It.IsAny<CreateCategoryDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(201, result.StatusCode);

            _mockCategoryProcessService.Verify(s => s.ProcessCreateCategoryAsync(It.Is<CreateCategoryDto>(dto =>
                dto.Name == "Test Category" &&
                dto.Descriptions.Count == 2 &&
                dto.Descriptions.Any(d => d.LanguageCode == "en-US") &&
                dto.Descriptions.Any(d => d.LanguageCode == "tr-TR"))), Times.Once);
        }

        #endregion

        #region Exception Handling Tests

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CreateCategoryCommand { Name = "Test Category" };

            _mockCategoryProcessService.Setup(s => s.ProcessCreateCategoryAsync(It.IsAny<CreateCategoryDto>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(500, result.StatusCode);
            Assert.Contains("An error occurred while creating the category", result.Message);
        }

        #endregion
    }
}
