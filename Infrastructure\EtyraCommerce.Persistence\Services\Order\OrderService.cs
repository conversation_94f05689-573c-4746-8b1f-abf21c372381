using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Application.Services.Order.Queries;
using EtyraCommerce.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Order
{
    /// <summary>
    /// Order service implementation for MediatR command/query dispatch
    /// </summary>
    public class OrderService : IOrderService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<OrderService> _logger;

        public OrderService(IMediator mediator, ILogger<OrderService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        #region Order Queries

        public async Task<CustomResponseDto<OrderDto>> GetOrderByIdAsync(Guid orderId, bool includeItems = true, bool includeCustomer = false, string? languageCode = null)
        {
            _logger.LogInformation("OrderService: Getting order by ID: {OrderId}", orderId);

            // CQRS pattern: Service => Handler => ProcessService => Repository
            var query = new GetOrderByIdQuery
            {
                OrderId = orderId,
                IncludeItems = includeItems,
                IncludeCustomer = includeCustomer,
                LanguageCode = languageCode
            };

            var result = await _mediator.Send(query);

            _logger.LogInformation("OrderService: Order retrieval completed for ID: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<PagedResult<OrderDto>>> SearchOrdersAsync(OrderSearchDto searchDto)
        {
            _logger.LogInformation("OrderService: Searching orders with term: {SearchTerm}", searchDto.SearchTerm ?? "None");

            // CQRS pattern: Service => Handler => ProcessService => Repository
            var query = new SearchOrdersQuery
            {
                SearchTerm = searchDto.SearchTerm,
                CustomerId = searchDto.CustomerId,
                Status = searchDto.Status,
                PaymentStatus = searchDto.PaymentStatus,
                ShippingStatus = searchDto.ShippingStatus,
                MinTotal = searchDto.MinTotal,
                MaxTotal = searchDto.MaxTotal,
                Currency = searchDto.Currency,
                StartDate = searchDto.StartDate,
                EndDate = searchDto.EndDate,
                PaymentMethod = searchDto.PaymentMethod,
                ShippingMethod = searchDto.ShippingMethod,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize,
                IncludeItems = searchDto.IncludeItems,
                IncludeCustomer = searchDto.IncludeCustomer,
                SortBy = searchDto.SortBy,
                SortDirection = searchDto.SortDirection
            };

            var result = await _mediator.Send(query);

            _logger.LogInformation("OrderService: Order search completed");
            return result;
        }

        public async Task<CustomResponseDto<OrderStatisticsDto>> GetOrderStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? customerId = null,
            string? currency = null,
            int topCustomersCount = 10,
            int recentOrdersCount = 5)
        {
            _logger.LogInformation("OrderService: Getting order statistics");

            // CQRS pattern: Service => Handler => ProcessService => Repository
            var query = new GetOrderStatisticsQuery
            {
                StartDate = startDate,
                EndDate = endDate,
                CustomerId = customerId,
                Currency = currency,
                TopCustomersCount = topCustomersCount,
                RecentOrdersCount = recentOrdersCount
            };

            var result = await _mediator.Send(query);

            _logger.LogInformation("OrderService: Order statistics retrieval completed");
            return result;
        }

        public async Task<CustomResponseDto<PagedResult<OrderDto>>> GetOrdersByCustomerIdAsync(Guid customerId, int page = 1, int pageSize = 10)
        {
            _logger.LogInformation("OrderService: Getting orders for customer: {CustomerId}", customerId);

            // CQRS pattern: Service => Handler => ProcessService => Repository
            var query = new GetUserOrdersQuery
            {
                UserId = customerId,
                Page = page,
                PageSize = pageSize,
                IncludeItems = false
            };

            var result = await _mediator.Send(query);

            _logger.LogInformation("OrderService: Customer orders retrieval completed for: {CustomerId}", customerId);
            return result;
        }

        public async Task<CustomResponseDto<OrderDto>> GetOrderByOrderNumberAsync(string orderNumber, bool includeItems = true)
        {
            _logger.LogInformation("OrderService: Getting order by order number: {OrderNumber}", orderNumber);

            // CQRS pattern: Service => Handler => ProcessService => Repository
            var query = new GetOrderByOrderNumberQuery
            {
                OrderNumber = orderNumber,
                IncludeItems = includeItems
            };

            var result = await _mediator.Send(query);

            _logger.LogInformation("OrderService: Order retrieval completed for order number: {OrderNumber}", orderNumber);
            return result;
        }

        public async Task<bool> OrderExistsAsync(Guid orderId)
        {
            _logger.LogInformation("OrderService: Checking if order exists: {OrderId}", orderId);

            var result = await GetOrderByIdAsync(orderId, includeItems: false, includeCustomer: false);
            var exists = result.IsSuccess;

            _logger.LogInformation("OrderService: Order existence check completed for: {OrderId}, Exists: {Exists}", orderId, exists);
            return exists;
        }

        public async Task<bool> OrderBelongsToCustomerAsync(Guid orderId, Guid customerId)
        {
            _logger.LogInformation("OrderService: Checking if order {OrderId} belongs to customer {CustomerId}", orderId, customerId);

            var result = await GetOrderByIdAsync(orderId, includeItems: false, includeCustomer: false);

            if (!result.IsSuccess || result.Data == null)
            {
                _logger.LogWarning("OrderService: Order not found: {OrderId}", orderId);
                return false;
            }

            var belongs = result.Data.CustomerId == customerId;
            _logger.LogInformation("OrderService: Order ownership check completed for: {OrderId}, Belongs: {Belongs}", orderId, belongs);
            return belongs;
        }

        #endregion

        #region Order Management Commands

        public async Task<CustomResponseDto<OrderDto>> CreateOrderAsync(CreateOrderDto createOrderDto)
        {
            _logger.LogInformation("OrderService: Creating order for customer: {CustomerId}", createOrderDto.CustomerId);

            var command = new CreateOrderCommand
            {
                CustomerId = createOrderDto.CustomerId,
                CustomerEmail = createOrderDto.CustomerEmail,
                CustomerFirstName = createOrderDto.CustomerFirstName,
                CustomerLastName = createOrderDto.CustomerLastName,
                CustomerPhone = createOrderDto.CustomerPhone,
                BillingAddress = createOrderDto.BillingAddress,
                ShippingAddress = createOrderDto.ShippingAddress,
                OrderItems = createOrderDto.OrderItems,
                Currency = createOrderDto.Currency,
                Notes = createOrderDto.Notes,
                ShippingMethod = createOrderDto.ShippingMethod,
                PaymentMethod = createOrderDto.PaymentMethod
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order creation completed for customer: {CustomerId}", createOrderDto.CustomerId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> UpdateOrderStatusAsync(Guid orderId, UpdateOrderStatusDto updateStatusDto)
        {
            _logger.LogInformation("OrderService: Updating order status for order: {OrderId} to {Status}", orderId, updateStatusDto.Status);

            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = updateStatusDto.Status,
                Reason = updateStatusDto.Reason,
                TrackingNumber = updateStatusDto.TrackingNumber,
                ExpectedDeliveryDate = updateStatusDto.ExpectedDeliveryDate,
                ActualDeliveryDate = updateStatusDto.ActualDeliveryDate
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order status update completed for order: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> UpdatePaymentStatusAsync(Guid orderId, UpdatePaymentStatusDto updatePaymentDto)
        {
            _logger.LogInformation("OrderService: Updating payment status for order: {OrderId} to {PaymentStatus}", orderId, updatePaymentDto.PaymentStatus);

            var command = new UpdatePaymentStatusCommand
            {
                OrderId = orderId,
                PaymentStatus = updatePaymentDto.PaymentStatus,
                PaymentMethod = updatePaymentDto.PaymentMethod,
                PaymentReference = updatePaymentDto.PaymentReference,
                Notes = updatePaymentDto.Notes
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Payment status update completed for order: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> ConfirmOrderAsync(Guid orderId)
        {
            _logger.LogInformation("OrderService: Confirming order: {OrderId}", orderId);

            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Confirmed,
                Reason = "Order confirmed"
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order confirmation completed for order: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> CancelOrderAsync(Guid orderId, string? reason = null, Guid? cancelledBy = null)
        {
            _logger.LogInformation("OrderService: Cancelling order: {OrderId}", orderId);

            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = reason,
                CancelledBy = cancelledBy
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order cancellation completed for order: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> ShipOrderAsync(Guid orderId, string? trackingNumber = null, DateTime? expectedDeliveryDate = null)
        {
            _logger.LogInformation("OrderService: Shipping order: {OrderId}", orderId);

            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Shipped,
                Reason = "Order shipped",
                TrackingNumber = trackingNumber,
                ExpectedDeliveryDate = expectedDeliveryDate
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order shipping completed for order: {OrderId}", orderId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> DeliverOrderAsync(Guid orderId, DateTime? deliveryDate = null)
        {
            _logger.LogInformation("OrderService: Delivering order: {OrderId}", orderId);

            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Delivered,
                Reason = "Order delivered",
                ActualDeliveryDate = deliveryDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("OrderService: Order delivery completed for order: {OrderId}", orderId);
            return result;
        }

        #endregion
    }
}
