using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Domain.Entities.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Queries
{
    /// <summary>
    /// Query to get inventory transactions with filtering and pagination
    /// </summary>
    public class GetInventoryTransactionsQuery : IRequest<CustomResponseDto<PagedResult<InventoryTransactionDto>>>
    {
        public Guid? InventoryId { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public InventoryTransactionType? Type { get; set; }
        public string? Reference { get; set; }
        public string? ReferenceType { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? TransactionDateFrom { get; set; }
        public DateTime? TransactionDateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "TransactionDate";
        public string? SortDirection { get; set; } = "desc";
    }
}
