using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Commands
{
    /// <summary>
    /// Handler for UpdateOrderStatusCommand
    /// </summary>
    public class UpdateOrderStatusCommandHandler : IRequestHandler<UpdateOrderStatusCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<UpdateOrderStatusCommandHandler> _logger;

        public UpdateOrderStatusCommandHandler(
            IOrderProcessService orderProcessService,
            ILogger<UpdateOrderStatusCommandHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(UpdateOrderStatusCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing update order status command for order: {OrderId} to status: {Status}",
                    request.OrderId, request.Status);

                // Validation
                if (request.OrderId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Order ID is required");

                if (!Enum.IsDefined(typeof(OrderStatus), request.Status))
                    return CustomResponseDto<NoContentDto>.BadRequest("Invalid order status");

                // Delegate to process service
                var result = await _orderProcessService.ProcessUpdateOrderStatusAsync(
                    request.OrderId,
                    request.Status,
                    request.Reason,
                    request.TrackingNumber,
                    request.ExpectedDeliveryDate,
                    request.ActualDeliveryDate);

                _logger.LogInformation("Update order status command processed successfully for order: {OrderId}", request.OrderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update order status command for order: {OrderId}", request.OrderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the order status");
            }
        }
    }
}
