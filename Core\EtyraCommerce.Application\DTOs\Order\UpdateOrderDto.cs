using EtyraCommerce.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Update order data transfer object
    /// </summary>
    public class UpdateOrderDto
    {
        /// <summary>
        /// Order ID
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// Customer email
        /// </summary>
        [EmailAddress]
        [MaxLength(254)]
        public string? CustomerEmail { get; set; }

        /// <summary>
        /// Customer phone number
        /// </summary>
        [Phone]
        [MaxLength(20)]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        [MaxLength(100)]
        public string? CustomerFirstName { get; set; }

        /// <summary>
        /// Customer last name
        /// </summary>
        [MaxLength(100)]
        public string? CustomerLastName { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        public UpdateAddressDto? BillingAddress { get; set; }

        /// <summary>
        /// Shipping address
        /// </summary>
        public UpdateAddressDto? ShippingAddress { get; set; }

        /// <summary>
        /// Customer notes
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Internal notes
        /// </summary>
        [MaxLength(1000)]
        public string? InternalNotes { get; set; }

        /// <summary>
        /// Shipping method
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        [MaxLength(100)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Expected delivery date
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }
    }

    /// <summary>
    /// Update address DTO
    /// </summary>
    public class UpdateAddressDto
    {
        /// <summary>
        /// Street address
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Address line 2 (optional)
        /// </summary>
        [MaxLength(200)]
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State/Province
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal/ZIP code
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Country { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update order status DTO
    /// </summary>
    public class UpdateOrderStatusDto
    {
        /// <summary>
        /// Order ID
        /// </summary>
        [Required]
        public Guid OrderId { get; set; }

        /// <summary>
        /// New order status
        /// </summary>
        [Required]
        public OrderStatus Status { get; set; }

        /// <summary>
        /// Reason for status change (optional)
        /// </summary>
        [MaxLength(500)]
        public string? Reason { get; set; }

        /// <summary>
        /// Tracking number (for shipped status)
        /// </summary>
        [MaxLength(100)]
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// Expected delivery date (for shipped status)
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// Actual delivery date (for delivered status)
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }
    }

    /// <summary>
    /// Update payment status DTO
    /// </summary>
    public class UpdatePaymentStatusDto
    {
        /// <summary>
        /// Order ID
        /// </summary>
        [Required]
        public Guid OrderId { get; set; }

        /// <summary>
        /// New payment status
        /// </summary>
        [Required]
        public PaymentStatus PaymentStatus { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        [MaxLength(100)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Payment reference/transaction ID
        /// </summary>
        [MaxLength(200)]
        public string? PaymentReference { get; set; }

        /// <summary>
        /// Notes about payment status change
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }
    }
}
