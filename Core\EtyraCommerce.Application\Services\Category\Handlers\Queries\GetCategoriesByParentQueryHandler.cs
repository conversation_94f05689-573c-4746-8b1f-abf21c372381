using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Queries
{
    /// <summary>
    /// Handler for GetCategoriesByParentQuery
    /// </summary>
    public class GetCategoriesByParentQueryHandler : IRequestHandler<GetCategoriesByParentQuery, CustomResponseDto<List<CategoryDto>>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<GetCategoriesByParentQueryHandler> _logger;

        public GetCategoriesByParentQueryHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<GetCategoriesByParentQueryHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> Handle(GetCategoriesByParentQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get categories by parent query for parent ID: {ParentCategoryId}",
                    request.ParentCategoryId?.ToString() ?? "Root");

                // Delegate to process service
                var result = await _categoryProcessService.ProcessGetCategoriesByParentAsync(
                    request.ParentCategoryId,
                    request.ActiveOnly,
                    request.IncludeChildren,
                    request.IncludeDescriptions,
                    request.LanguageCode,
                    request.SortBy,
                    request.SortDirection);

                _logger.LogInformation("Get categories by parent query processed successfully for parent ID: {ParentCategoryId}",
                    request.ParentCategoryId?.ToString() ?? "Root");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get categories by parent query for parent ID: {ParentCategoryId}",
                    request.ParentCategoryId?.ToString() ?? "Root");
                return CustomResponseDto<List<CategoryDto>>.InternalServerError("An error occurred while retrieving categories by parent");
            }
        }
    }
}
