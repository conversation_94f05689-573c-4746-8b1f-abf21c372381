using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Queries
{
    /// <summary>
    /// Handler for SearchOrdersQuery
    /// </summary>
    public class SearchOrdersQueryHandler : IRequestHandler<SearchOrdersQuery, CustomResponseDto<PagedResult<OrderDto>>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<SearchOrdersQueryHandler> _logger;

        public SearchOrdersQueryHandler(
            IOrderProcessService orderProcessService,
            ILogger<SearchOrdersQueryHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<OrderDto>>> Handle(SearchOrdersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing search orders query with search term: {SearchTerm}", request.SearchTerm);

                // Validation
                if (request.Page < 1)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Page size must be between 1 and 100");

                if (request.MinTotal.HasValue && request.MaxTotal.HasValue && request.MinTotal > request.MaxTotal)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Minimum total cannot be greater than maximum total");

                if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Start date cannot be greater than end date");

                // Create search criteria
                var searchDto = new OrderSearchDto
                {
                    SearchTerm = request.SearchTerm,
                    CustomerId = request.CustomerId,
                    Status = request.Status,
                    PaymentStatus = request.PaymentStatus,
                    ShippingStatus = request.ShippingStatus,
                    MinTotal = request.MinTotal,
                    MaxTotal = request.MaxTotal,
                    Currency = request.Currency,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    PaymentMethod = request.PaymentMethod,
                    ShippingMethod = request.ShippingMethod,
                    IncludeItems = request.IncludeItems,
                    IncludeCustomer = request.IncludeCustomer,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    Page = request.Page,
                    PageSize = request.PageSize
                };

                // Delegate to ProcessService (CQRS pattern: Handler => ProcessService => Repository)
                var result = await _orderProcessService.SearchOrdersAsync(searchDto);

                _logger.LogInformation("Search orders query processed successfully");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing search orders query");
                return CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while searching orders");
            }
        }
    }
}
