using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// DTO for cancelling an order
    /// </summary>
    public class CancelOrderDto
    {
        /// <summary>
        /// Reason for cancellation
        /// </summary>
        [MaxLength(500)]
        public string? Reason { get; set; }

        /// <summary>
        /// ID of the user who cancelled the order
        /// </summary>
        public Guid? CancelledBy { get; set; }
    }
}
