using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;

namespace EtyraCommerce.Application.Services.Category
{
    /// <summary>
    /// Category service interface for MediatR command/query dispatch
    /// </summary>
    public interface ICategoryService
    {
        #region Category Management

        /// <summary>
        /// Creates a new category
        /// </summary>
        /// <param name="createCategoryDto">Category creation data</param>
        /// <returns>Created category</returns>
        Task<CustomResponseDto<CategoryDto>> CreateCategoryAsync(CreateCategoryDto createCategoryDto);

        /// <summary>
        /// Updates an existing category
        /// </summary>
        /// <param name="categoryId">Category ID to update</param>
        /// <param name="updateCategoryDto">Category update data</param>
        /// <returns>Updated category</returns>
        Task<CustomResponseDto<CategoryDto>> UpdateCategoryAsync(Guid categoryId, UpdateCategoryDto updateCategoryDto);

        /// <summary>
        /// Deletes a category (soft delete)
        /// </summary>
        /// <param name="categoryId">Category ID to delete</param>
        /// <param name="forceDelete">Whether to force delete (hard delete)</param>
        /// <param name="deleteChildren">Whether to delete child categories as well</param>
        /// <returns>Operation result</returns>
        Task<CustomResponseDto<NoContentDto>> DeleteCategoryAsync(Guid categoryId, bool forceDelete = false, bool deleteChildren = false);

        #endregion

        #region Category Queries

        /// <summary>
        /// Gets a category by ID
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="includeChildren">Whether to include child categories</param>
        /// <param name="includeParent">Whether to include parent category</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Category data</returns>
        Task<CustomResponseDto<CategoryDto>> GetCategoryByIdAsync(Guid categoryId, bool includeChildren = false, bool includeParent = false, bool includeDescriptions = true, string? languageCode = null);

        /// <summary>
        /// Gets all categories with filtering and pagination
        /// </summary>
        /// <param name="searchDto">Search and filter parameters</param>
        /// <returns>Paged category results</returns>
        Task<CustomResponseDto<PagedResult<CategoryDto>>> GetAllCategoriesAsync(CategorySearchDto searchDto);

        /// <summary>
        /// Gets categories by parent category ID
        /// </summary>
        /// <param name="parentCategoryId">Parent category ID (null for root categories)</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="includeChildren">Whether to include child categories recursively</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>List of categories</returns>
        Task<CustomResponseDto<List<CategoryDto>>> GetCategoriesByParentAsync(Guid? parentCategoryId, bool activeOnly = true, bool includeChildren = false, bool includeDescriptions = true, string? languageCode = null);

        /// <summary>
        /// Gets category tree (hierarchical structure)
        /// </summary>
        /// <param name="rootCategoryId">Root category ID to start from (null for full tree)</param>
        /// <param name="maxDepth">Maximum depth to retrieve</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="menuOnly">Whether to include only menu categories</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Category tree</returns>
        Task<CustomResponseDto<List<CategoryDto>>> GetCategoryTreeAsync(Guid? rootCategoryId = null, int? maxDepth = null, bool activeOnly = true, bool menuOnly = false, string? languageCode = null);

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets root categories (categories without parent)
        /// </summary>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="includeChildren">Whether to include child categories</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>List of root categories</returns>
        Task<CustomResponseDto<List<CategoryDto>>> GetRootCategoriesAsync(bool activeOnly = true, bool includeChildren = false, string? languageCode = null);

        /// <summary>
        /// Gets menu categories (categories marked as show in menu)
        /// </summary>
        /// <param name="maxDepth">Maximum depth to retrieve</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Menu category tree</returns>
        Task<CustomResponseDto<List<CategoryDto>>> GetMenuCategoriesAsync(int? maxDepth = 3, string? languageCode = null);

        #endregion
    }
}
