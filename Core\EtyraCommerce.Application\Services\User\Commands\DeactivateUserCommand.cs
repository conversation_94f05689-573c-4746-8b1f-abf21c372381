using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for deactivating a user account
    /// </summary>
    public class DeactivateUserCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// User ID to deactivate
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Reason for deactivation
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// ID of the admin performing the deactivation
        /// </summary>
        public Guid? DeactivatedByUserId { get; set; }

        public DeactivateUserCommand() { }

        public DeactivateUserCommand(Guid userId, string reason, Guid? deactivatedByUserId = null)
        {
            UserId = userId;
            Reason = reason;
            DeactivatedByUserId = deactivatedByUserId;
        }
    }
}
