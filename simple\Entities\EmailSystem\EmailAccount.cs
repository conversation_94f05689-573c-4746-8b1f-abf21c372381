using System;
using EtyraApp.Domain.Entities.Common;

namespace EtyraApp.Domain.Entities.EmailSystem
{
    public class EmailAccount :BaseEntity
    {
        public string Email { get; set; } // E-posta adresi
        public string Password { get; set; } // E-posta şifresi
        public string ImapServer { get; set; } // IMAP sunucu adresi
        public int ImapPort { get; set; } // IMAP port numarası
        public string SmtpServer { get; set; } // SMTP sunucu adresi
        public int SmtpPort { get; set; } // SMTP port numarası
        public bool IsActive { get; set; } // Hesap durumu (aktif/pasif)
        public DateTime CreatedDate { get; set; } // Hesap oluşturulma tarihi
        public DateTime? LastChecked { get; set; } // Son kontrol tarihi
        public uint? UidValidity { get; set; } // IMAP UIDVALIDITY değeri
        public uint? UidNext { get; set; } // IMAP UIDNEXT değeri
        public string Signature { get; set; } // E-posta imzası
    }
}
