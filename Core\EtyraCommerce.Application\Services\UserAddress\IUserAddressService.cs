using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;

namespace EtyraCommerce.Application.Services.UserAddress
{
    /// <summary>
    /// Service interface for UserAddress operations
    /// Provides high-level business operations for user address management
    /// </summary>
    public interface IUserAddressService
    {
        #region Command Operations

        /// <summary>
        /// Creates a new user address
        /// </summary>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <param name="createDto">Address creation data</param>
        /// <returns>Created address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> CreateUserAddressAsync(Guid userId, CreateUserAddressDto createDto);

        /// <summary>
        /// Updates an existing user address
        /// </summary>
        /// <param name="addressId">ID of the address to update</param>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <param name="updateDto">Address update data</param>
        /// <returns>Updated address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> UpdateUserAddressAsync(Guid addressId, Guid userId, UpdateUserAddressDto updateDto);

        /// <summary>
        /// Deletes a user address
        /// </summary>
        /// <param name="addressId">ID of the address to delete</param>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> DeleteUserAddressAsync(Guid addressId, Guid userId, bool hardDelete = false);

        /// <summary>
        /// Sets an address as the default address for a user
        /// </summary>
        /// <param name="addressId">ID of the address to set as default</param>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> SetDefaultAddressAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Toggles the active status of an address
        /// </summary>
        /// <param name="addressId">ID of the address to toggle</param>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <param name="isActive">New active status</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> ToggleAddressStatusAsync(Guid addressId, Guid userId, bool isActive);

        #endregion

        #region Query Operations

        /// <summary>
        /// Gets all addresses for a specific user
        /// </summary>
        /// <param name="userId">ID of the user whose addresses to retrieve</param>
        /// <param name="addressType">Filter by address type (optional)</param>
        /// <param name="isActive">Filter by active status (optional)</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of user address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> GetUserAddressesAsync(Guid userId, int? addressType = null, bool? isActive = null, bool includeInactive = false);

        /// <summary>
        /// Gets a specific user address by ID
        /// </summary>
        /// <param name="addressId">ID of the address to retrieve</param>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <returns>User address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> GetUserAddressByIdAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Gets the default address for a user
        /// </summary>
        /// <param name="userId">ID of the user whose default address to retrieve</param>
        /// <param name="addressType">Filter by address type for default address (optional)</param>
        /// <returns>Default user address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> GetDefaultAddressAsync(Guid userId, int? addressType = null);

        /// <summary>
        /// Gets addresses suitable for billing
        /// </summary>
        /// <param name="userId">ID of the user whose billing addresses to retrieve</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of billing address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> GetBillingAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Gets addresses suitable for shipping
        /// </summary>
        /// <param name="userId">ID of the user whose shipping addresses to retrieve</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of shipping address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> GetShippingAddressesAsync(Guid userId, bool includeInactive = false);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Validates if a user can have a new address
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result</returns>
        Task<CustomResponseDto<bool>> ValidateUserCanHaveNewAddressAsync(Guid userId);

        /// <summary>
        /// Validates if an address belongs to a specific user
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result</returns>
        Task<CustomResponseDto<bool>> ValidateAddressBelongsToUserAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Validates if an address can be deleted
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result</returns>
        Task<CustomResponseDto<bool>> ValidateAddressCanBeDeletedAsync(Guid addressId, Guid userId);

        #endregion

        #region Business Logic Operations

        /// <summary>
        /// Gets the count of addresses for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses in count</param>
        /// <returns>Address count</returns>
        Task<CustomResponseDto<int>> GetUserAddressCountAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Checks if a user has any addresses
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>True if user has addresses, false otherwise</returns>
        Task<CustomResponseDto<bool>> UserHasAddressesAsync(Guid userId);

        /// <summary>
        /// Gets address statistics for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Address statistics</returns>
        Task<CustomResponseDto<UserAddressStatsDto>> GetUserAddressStatsAsync(Guid userId);

        /// <summary>
        /// Searches user addresses by text
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="searchText">Text to search for</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of matching addresses</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> SearchUserAddressesAsync(Guid userId, string searchText, bool includeInactive = false);

        /// <summary>
        /// Gets recently used addresses for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="count">Number of recent addresses to return</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of recently used addresses</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> GetRecentUserAddressesAsync(Guid userId, int count = 5, bool includeInactive = false);

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Creates multiple addresses for a user in a single transaction
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="createDtos">List of address creation data</param>
        /// <returns>List of created address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> BulkCreateUserAddressesAsync(Guid userId, List<CreateUserAddressDto> createDtos);

        /// <summary>
        /// Activates multiple addresses
        /// </summary>
        /// <param name="addressIds">List of address IDs to activate</param>
        /// <param name="userId">ID of the user (for validation)</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> BulkActivateAddressesAsync(List<Guid> addressIds, Guid userId);

        /// <summary>
        /// Deactivates multiple addresses
        /// </summary>
        /// <param name="addressIds">List of address IDs to deactivate</param>
        /// <param name="userId">ID of the user (for validation)</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> BulkDeactivateAddressesAsync(List<Guid> addressIds, Guid userId);

        #endregion

        #region Maintenance Operations

        /// <summary>
        /// Ensures data consistency for user addresses
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> EnsureAddressConsistencyAsync(Guid userId);

        /// <summary>
        /// Archives old unused addresses
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="olderThanDays">Archive addresses older than this many days</param>
        /// <returns>Number of addresses archived</returns>
        Task<CustomResponseDto<int>> ArchiveOldAddressesAsync(Guid userId, int olderThanDays);

        #endregion
    }
}
