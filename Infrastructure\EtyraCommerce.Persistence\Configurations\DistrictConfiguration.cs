using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for District entity
    /// </summary>
    public class DistrictConfiguration : IEntityTypeConfiguration<District>
    {
        public void Configure(EntityTypeBuilder<District> builder)
        {
            // Table configuration
            builder.ToTable("Districts", "etyra_shipping");

            // Primary key
            builder.HasKey(d => d.Id);

            // Properties
            builder.Property(d => d.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(d => d.PostalCode)
                .HasMaxLength(20);

            builder.Property(d => d.Latitude)
                .HasPrecision(10, 7);

            builder.Property(d => d.Longitude)
                .HasPrecision(10, 7);

            builder.Property(d => d.IsShippingEnabled)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(d => d.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Foreign key relationships
            builder.HasOne(d => d.City)
                .WithMany(c => c.Districts)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(d => d.CityId)
                .HasDatabaseName("IX_Districts_CityId");

            builder.HasIndex(d => new { d.Name, d.CityId })
                .IsUnique()
                .HasDatabaseName("IX_Districts_Name_City_Unique");

            builder.HasIndex(d => d.PostalCode)
                .HasDatabaseName("IX_Districts_PostalCode");

            builder.HasIndex(d => d.IsShippingEnabled)
                .HasDatabaseName("IX_Districts_IsShippingEnabled");

            builder.HasIndex(d => d.DisplayOrder)
                .HasDatabaseName("IX_Districts_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Districts_Latitude",
                "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Districts_Longitude",
                "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Districts_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Districts within cities for precise shipping address management"));
        }
    }
}
