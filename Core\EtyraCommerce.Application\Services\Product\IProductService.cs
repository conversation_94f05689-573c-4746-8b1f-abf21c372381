using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;

namespace EtyraCommerce.Application.Services.Product
{
    /// <summary>
    /// Product service interface for MediatR command/query dispatch
    /// </summary>
    public interface IProductService
    {
        #region Command Operations

        /// <summary>
        /// Creates a new product
        /// </summary>
        /// <param name="createDto">Product creation data</param>
        /// <returns>Created product DTO</returns>
        Task<CustomResponseDto<ProductDto>> CreateProductAsync(CreateProductDto createDto);

        /// <summary>
        /// Updates an existing product
        /// </summary>
        /// <param name="productId">Product ID to update</param>
        /// <param name="updateDto">Product update data</param>
        /// <returns>Updated product DTO</returns>
        Task<CustomResponseDto<ProductDto>> UpdateProductAsync(Guid productId, UpdateProductDto updateDto);

        /// <summary>
        /// Deletes a product
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <param name="deletionReason">Reason for deletion</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> DeleteProductAsync(Guid productId, bool hardDelete = false, bool forceDelete = false, string? deletionReason = null);

        #endregion

        #region Query Operations

        /// <summary>
        /// Gets a product by ID
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="includeDescriptions">Include descriptions</param>
        /// <param name="includeImages">Include images</param>
        /// <param name="includeCategories">Include categories</param>
        /// <param name="includeDiscounts">Include discounts</param>
        /// <param name="includeAttributes">Include attributes</param>
        /// <param name="includeVariants">Include variants</param>
        /// <param name="includeInventory">Include inventory</param>
        /// <param name="includeDeleted">Include deleted products</param>
        /// <param name="languageCode">Language code for descriptions</param>
        /// <param name="storeId">Store ID for store-specific data</param>
        /// <returns>Product DTO</returns>
        Task<CustomResponseDto<ProductDto>> GetProductByIdAsync(
            Guid productId,
            bool includeDescriptions = false,
            bool includeImages = false,
            bool includeCategories = false,
            bool includeDiscounts = false,
            bool includeAttributes = false,
            bool includeVariants = false,
            bool includeInventory = false,
            bool includeDeleted = false,
            string? languageCode = null,
            Guid? storeId = null);

        /// <summary>
        /// Gets all products with filtering and pagination
        /// </summary>
        /// <param name="filterDto">Filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetAllProductsAsync(ProductFilterDto filterDto);

        /// <summary>
        /// Searches products with advanced filtering
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> SearchProductsAsync(ProductSearchDto searchDto);

        /// <summary>
        /// Gets products by category
        /// </summary>
        /// <param name="categoryFilterDto">Category filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsByCategoryAsync(ProductCategoryFilterDto categoryFilterDto);

        #endregion

        #region Convenience Methods

        /// <summary>
        /// Gets active products with basic info
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetActiveProductsAsync(int page = 1, int pageSize = 20, string? languageCode = null);

        /// <summary>
        /// Gets featured products
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetFeaturedProductsAsync(int page = 1, int pageSize = 20, string? languageCode = null);

        /// <summary>
        /// Gets products on sale
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsOnSaleAsync(int page = 1, int pageSize = 20, string? languageCode = null);

        /// <summary>
        /// Gets products by brand
        /// </summary>
        /// <param name="brand">Brand name</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> GetProductsByBrandAsync(string brand, int page = 1, int pageSize = 20, string? languageCode = null);

        /// <summary>
        /// Quick search for products
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> QuickSearchAsync(string searchTerm, int page = 1, int pageSize = 20, string? languageCode = null);

        #endregion
    }
}
