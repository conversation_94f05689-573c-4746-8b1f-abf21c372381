﻿using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.Settings;

namespace EtyraApp.Domain.Entities.RelationsTable;

public class WarehouseProduct
{
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }
    public int MinStockQuantityAlert { get; set; }
    public decimal Cost { get; set; }
    public StockStatus? StockStatus { get; set; }
    public int? StockStatusId { get; set; }
    public int? WarehouseId { get; set; }
    public int ProductId { get; set; }

    public Warehouse Warehouse { get; set; }
    public Product Product { get; set; }

}