using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.Services.Payment.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Payment.Handlers;

/// <summary>
/// Handler for creating payment methods
/// </summary>
public class CreatePaymentMethodCommandHandler : IRequestHandler<CreatePaymentMethodCommand, CustomResponseDto<PaymentMethodDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreatePaymentMethodCommandHandler> _logger;

    public CreatePaymentMethodCommandHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        IMapper mapper,
        ILogger<CreatePaymentMethodCommandHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Handle create payment method command
    /// </summary>
    /// <param name="request">Create payment method command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> Handle(CreatePaymentMethodCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing CreatePaymentMethodCommand for payment method: {Name}", request.Name);

            var createDto = _mapper.Map<CreatePaymentMethodDto>(request);
            var result = await _paymentMethodProcessService.ProcessCreateAsync(createDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment method created successfully with ID: {PaymentMethodId}", result.Data?.Id);
            }
            else
            {
                _logger.LogWarning("Failed to create payment method: {Message}", result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing CreatePaymentMethodCommand for payment method: {Name}", request.Name);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while creating the payment method");
        }
    }
}

/// <summary>
/// Handler for updating payment methods
/// </summary>
public class UpdatePaymentMethodCommandHandler : IRequestHandler<UpdatePaymentMethodCommand, CustomResponseDto<PaymentMethodDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdatePaymentMethodCommandHandler> _logger;

    public UpdatePaymentMethodCommandHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        IMapper mapper,
        ILogger<UpdatePaymentMethodCommandHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Handle update payment method command
    /// </summary>
    /// <param name="request">Update payment method command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> Handle(UpdatePaymentMethodCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing UpdatePaymentMethodCommand for payment method ID: {PaymentMethodId}", request.Id);

            var updateDto = _mapper.Map<UpdatePaymentMethodDto>(request);
            var result = await _paymentMethodProcessService.ProcessUpdateAsync(updateDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment method updated successfully with ID: {PaymentMethodId}", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to update payment method {PaymentMethodId}: {Message}", request.Id, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing UpdatePaymentMethodCommand for payment method ID: {PaymentMethodId}", request.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while updating the payment method");
        }
    }
}
