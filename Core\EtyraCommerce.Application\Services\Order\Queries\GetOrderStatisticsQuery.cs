using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Queries
{
    /// <summary>
    /// Query to get order statistics
    /// </summary>
    public class GetOrderStatisticsQuery : IRequest<CustomResponseDto<OrderStatisticsDto>>
    {
        /// <summary>
        /// Start date for statistics calculation
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for statistics calculation
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Customer ID filter (for customer-specific statistics)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Currency filter
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Number of top customers to include
        /// </summary>
        public int TopCustomersCount { get; set; } = 10;

        /// <summary>
        /// Number of recent orders to include
        /// </summary>
        public int RecentOrdersCount { get; set; } = 5;
    }
}
