using EtyraCommerce.Application.Features.Shipping.Countries.Queries;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Shipping.Handlers.Queries
{
    /// <summary>
    /// Handler for getting all countries with optional filtering
    /// Delegates business logic to CountryProcessService
    /// </summary>
    public class GetAllCountriesQueryHandler : IRequestHandler<GetAllCountriesQuery, GetAllCountriesResponse>
    {
        private readonly ICountryProcessService _countryProcessService;
        private readonly ILogger<GetAllCountriesQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetAllCountriesQueryHandler(
            ICountryProcessService countryProcessService,
            ILogger<GetAllCountriesQueryHandler> logger)
        {
            _countryProcessService = countryProcessService ?? throw new ArgumentNullException(nameof(countryProcessService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get all countries query
        /// </summary>
        public async Task<GetAllCountriesResponse> Handle(GetAllCountriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get all countries query with filters - EU: {IsEuMember}, Shipping: {IsShippingEnabled}, Search: {SearchTerm}",
                    request.IsEuMember, request.IsShippingEnabled, request.SearchTerm);

                // Delegate to CountryProcessService for business logic
                var result = await _countryProcessService.ProcessGetAllCountriesAsync(request, cancellationToken);

                if (result != null)
                {
                    _logger.LogInformation("Countries retrieval successful. Total: {TotalCount}, EU Members: {EuMemberCount}, Shipping Enabled: {ShippingEnabledCount}",
                        result.TotalCount, result.EuMemberCount, result.ShippingEnabledCount);
                }
                else
                {
                    _logger.LogWarning("Countries retrieval failed");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get all countries query");
                throw;
            }
        }
    }
}
