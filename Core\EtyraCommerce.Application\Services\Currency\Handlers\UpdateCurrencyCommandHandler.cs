using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for updating currencies
/// </summary>
public class UpdateCurrencyCommandHandler : IRequestHandler<UpdateCurrencyCommand, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateCurrencyCommandHandler> _logger;

    public UpdateCurrencyCommandHandler(
        ICurrencyProcessService currencyProcessService,
        IMapper mapper,
        ILogger<UpdateCurrencyCommandHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Handle update currency command
    /// </summary>
    /// <param name="request">Update currency command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(UpdateCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing UpdateCurrencyCommand for currency ID: {CurrencyId}", request.Id);

            var updateDto = _mapper.Map<UpdateCurrencyDto>(request);
            var result = await _currencyProcessService.ProcessUpdateAsync(updateDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} updated successfully with code: {Code}", 
                    request.Id, request.Code);
            }
            else
            {
                _logger.LogWarning("Failed to update currency {CurrencyId}. Errors: {Errors}", 
                    request.Id, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing UpdateCurrencyCommand for currency ID: {CurrencyId}", request.Id);
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while updating the currency");
        }
    }
}
