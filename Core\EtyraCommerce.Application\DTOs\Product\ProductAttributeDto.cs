using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// Product attribute DTO
    /// </summary>
    public class ProductAttributeDto : BaseDto
    {
        /// <summary>
        /// Attribute name (e.g., "Material", "Brand", "Warranty")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Attribute value (e.g., "Cotton", "Nike", "2 Years")
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Attribute type
        /// </summary>
        public AttributeType Type { get; set; }

        /// <summary>
        /// Attribute group (for organizing attributes)
        /// </summary>
        public string? Group { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Whether attribute is visible to customers
        /// </summary>
        public bool IsVisible { get; set; }

        /// <summary>
        /// Whether attribute is searchable
        /// </summary>
        public bool IsSearchable { get; set; }

        /// <summary>
        /// Whether attribute can be used for filtering
        /// </summary>
        public bool IsFilterable { get; set; }

        /// <summary>
        /// Whether attribute is required
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Unit of measurement (e.g., "cm", "kg", "pieces")
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// Attribute description/help text
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Gets formatted value with unit
        /// </summary>
        public string FormattedValue
        {
            get
            {
                if (string.IsNullOrEmpty(Unit))
                    return Value;
                return $"{Value} {Unit}";
            }
        }

        /// <summary>
        /// Gets display name (Name with group if available)
        /// </summary>
        public string DisplayName
        {
            get
            {
                if (string.IsNullOrEmpty(Group))
                    return Name;
                return $"{Group} - {Name}";
            }
        }
    }

    /// <summary>
    /// DTO for creating product attribute
    /// </summary>
    public class CreateProductAttributeDto
    {
        /// <summary>
        /// Attribute name (e.g., "Material", "Brand", "Warranty")
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 100 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Attribute value (e.g., "Cotton", "Nike", "2 Years")
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [StringLength(500, MinimumLength = 1, ErrorMessage = "Value must be between 1 and 500 characters")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Attribute type
        /// </summary>
        public AttributeType Type { get; set; } = AttributeType.Text;

        /// <summary>
        /// Attribute group (for organizing attributes)
        /// </summary>
        [StringLength(50, ErrorMessage = "Group cannot exceed 50 characters")]
        public string? Group { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether attribute is visible to customers
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// Whether attribute is searchable
        /// </summary>
        public bool IsSearchable { get; set; } = false;

        /// <summary>
        /// Whether attribute can be used for filtering
        /// </summary>
        public bool IsFilterable { get; set; } = false;

        /// <summary>
        /// Whether attribute is required
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Unit of measurement (e.g., "cm", "kg", "pieces")
        /// </summary>
        [StringLength(20, ErrorMessage = "Unit cannot exceed 20 characters")]
        public string? Unit { get; set; }

        /// <summary>
        /// Attribute description/help text
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        #region Validation

        /// <summary>
        /// Custom validation based on attribute type
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            switch (Type)
            {
                case AttributeType.Number:
                    if (!decimal.TryParse(Value, out _))
                    {
                        yield return new ValidationResult(
                            "Value must be a valid number for Number type",
                            new[] { nameof(Value) });
                    }
                    break;

                case AttributeType.Boolean:
                    if (!bool.TryParse(Value, out _))
                    {
                        yield return new ValidationResult(
                            "Value must be true or false for Boolean type",
                            new[] { nameof(Value) });
                    }
                    break;

                case AttributeType.Date:
                    if (!DateTime.TryParse(Value, out _))
                    {
                        yield return new ValidationResult(
                            "Value must be a valid date for Date type",
                            new[] { nameof(Value) });
                    }
                    break;

                case AttributeType.Email:
                    if (!IsValidEmail(Value))
                    {
                        yield return new ValidationResult(
                            "Value must be a valid email address for Email type",
                            new[] { nameof(Value) });
                    }
                    break;

                case AttributeType.Url:
                    if (!IsValidUrl(Value))
                    {
                        yield return new ValidationResult(
                            "Value must be a valid URL for Url type",
                            new[] { nameof(Value) });
                    }
                    break;

                case AttributeType.Color:
                    if (!IsValidColor(Value))
                    {
                        yield return new ValidationResult(
                            "Value must be a valid color (hex code or color name) for Color type",
                            new[] { nameof(Value) });
                    }
                    break;
            }
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        private static bool IsValidColor(string color)
        {
            // Simple validation for hex colors or color names
            if (color.StartsWith('#') && color.Length == 7)
            {
                return color[1..].All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
            }

            // Common color names
            var colorNames = new[] { "red", "green", "blue", "yellow", "orange", "purple", "pink", "brown", "black", "white", "gray", "grey" };
            return colorNames.Contains(color.ToLowerInvariant());
        }

        #endregion
    }

    /// <summary>
    /// DTO for updating product attribute
    /// </summary>
    public class UpdateProductAttributeDto
    {
        /// <summary>
        /// Attribute value (e.g., "Cotton", "Nike", "2 Years")
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [StringLength(500, MinimumLength = 1, ErrorMessage = "Value must be between 1 and 500 characters")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Attribute group (for organizing attributes)
        /// </summary>
        [StringLength(50, ErrorMessage = "Group cannot exceed 50 characters")]
        public string? Group { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether attribute is visible to customers
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// Whether attribute is searchable
        /// </summary>
        public bool IsSearchable { get; set; } = false;

        /// <summary>
        /// Whether attribute can be used for filtering
        /// </summary>
        public bool IsFilterable { get; set; } = false;

        /// <summary>
        /// Whether attribute is required
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Unit of measurement (e.g., "cm", "kg", "pieces")
        /// </summary>
        [StringLength(20, ErrorMessage = "Unit cannot exceed 20 characters")]
        public string? Unit { get; set; }

        /// <summary>
        /// Attribute description/help text
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// Attribute type enumeration
    /// </summary>
    public enum AttributeType
    {
        Text = 0,
        TextArea = 1,
        Number = 2,
        Boolean = 3,
        Date = 4,
        Select = 5,
        Email = 6,
        Url = 7,
        Color = 8
    }
}
