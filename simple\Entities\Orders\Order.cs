﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Orders;

public class Order : BaseEntity
{

    //public List<History> Histories { get; set; }

    //public List<Total> Totals { get; set; }
    //public List<Coupon> Coupons { get; set; }

    public Todo? Todo { get; set; }
    public ICollection<OrderImage>? OrderImages { get; set; }

    public ICollection<OrderProduct>? OrderProducts { get; set; }

    [MaxLength(100)] public string? InvoicePrefix { get; set; }
    public int? StoreOrderId { get; set; } = 0;

    public int? StoreId { get; set; } = 0;

    public int? BazaarOrderId { get; set; } = 0;

    public int? BazaarId { get; set; } = 0;
    public int? CustomerId { get; set; }

    [MaxLength(100)] public string? Email { get; set; }

    [MaxLength(20)] public string? Telephone { get; set; }

    [MaxLength(200)] public string? PaymentFirstname { get; set; }

    [MaxLength(200)] public string? PaymentLastname { get; set; }

    [MaxLength(250)] public string? PaymentCompany { get; set; }

    [MaxLength(500)] public string? PaymentAddress1 { get; set; }

    [MaxLength(500)] public string? PaymentAddress2 { get; set; }

    [MaxLength(10)] public string? PaymentPostcode { get; set; }

    [MaxLength(50)] public string? PaymentCity { get; set; }

    [MaxLength(50)] public string? PaymentZoneId { get; set; }

    [MaxLength(50)] public string? PaymentZone { get; set; }

    [MaxLength(50)] public string? PaymentZoneCode { get; set; }

    public int? PaymentCountryId { get; set; }

    [MaxLength(50)] public string? PaymentCountry { get; set; }

    [MaxLength(100)] public string? PaymentMethod { get; set; }

    public int PaymentMethodId { get; set; }

    [MaxLength(200)] public string? PaymentCustomField { get; set; }

    [MaxLength(200)] public string? ShippingFirstname { get; set; }

    [MaxLength(200)] public string? ShippingLastname { get; set; }

    [MaxLength(250)] public string? ShippingCompany { get; set; }

    [MaxLength(500)] public string? ShippingAddress1 { get; set; }

    [MaxLength(500)] public string? ShippingAddress2 { get; set; }

    [MaxLength(10)] public string? ShippingPostcode { get; set; }

    [MaxLength(50)] public string? ShippingCity { get; set; }

    [MaxLength(50)] public string? ShippingZoneId { get; set; }

    [MaxLength(10)] public string? ShippingZone { get; set; }

    [MaxLength(10)] public string? ShippingZoneCode { get; set; }

    public int? ShippingCountryId { get; set; }

    [MaxLength(50)] public string? ShippingCountry { get; set; }

    public string? ShippingMethod { get; set; }

    public int ShippingMethodId { get; set; }

    [MaxLength(200)] public string? ShippingCustomField { get; set; }


    [MaxLength(500)] public string? Comment { get; set; }

    public decimal? Total { get; set; }
    public decimal? PercentDiscountValue { get; set; } = 0;
    public decimal? DiscountValue { get; set; } = 0;
    public decimal? TotalDiscount { get; set; }

    public int OrderStatusId { get; set; }

    [MaxLength(20)] public string? OrderStatus { get; set; }

    public int? CurrencyId { get; set; }

    public int? LanguageId { get; set; }

    [MaxLength(10)] public string? CurrencyCode { get; set; }

    public decimal? CurrencyValue { get; set; }

    [MaxLength(20)] public string? Ip { get; set; }

    public DateTime? DateAdded { get; set; }

    public DateTime? DateModified { get; set; }

    public decimal? ShippingExcludeTax { get; set; }

    public decimal? ItemTotalExcludeTax { get; set; }

    [MaxLength(200)] public string? locker_id { get; set; }
    [MaxLength(200)] public string? locker_name { get; set; }
    [MaxLength(50)] public string? shipping_locality_id { get; set; }

    public decimal? Subtotal { get; set; }

    [MaxLength(200)] public string? ShippingControlUrl { get; set; }

    [MaxLength(200)] public string? ShippingBarcode { get; set; }

    public int? InvoiceNo { get; set; }
    [MaxLength(20)] public string? InvoiceSeries { get; set; }

    public DateTime? InvoiceDateTime { get; set; }

    public decimal AddressValidation { get; set; }
    public bool IsDeleted { get; set; } = false;
    public bool IsProforma { get; set; } = false;

}
