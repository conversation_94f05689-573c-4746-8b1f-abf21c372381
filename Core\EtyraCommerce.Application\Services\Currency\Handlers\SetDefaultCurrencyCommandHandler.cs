using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for setting default currency
/// </summary>
public class SetDefaultCurrencyCommandHandler : IRequestHandler<SetDefaultCurrencyCommand, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<SetDefaultCurrencyCommandHandler> _logger;

    public SetDefaultCurrencyCommandHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<SetDefaultCurrencyCommandHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle set default currency command
    /// </summary>
    /// <param name="request">Set default currency command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated default currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(SetDefaultCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing SetDefaultCurrencyCommand for currency ID: {CurrencyId}", request.Id);

            var result = await _currencyProcessService.ProcessSetDefaultAsync(request.Id);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} set as default successfully", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to set currency {CurrencyId} as default. Errors: {Errors}", 
                    request.Id, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing SetDefaultCurrencyCommand for currency ID: {CurrencyId}", request.Id);
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while setting the default currency");
        }
    }
}
