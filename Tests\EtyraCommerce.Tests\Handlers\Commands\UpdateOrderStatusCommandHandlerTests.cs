using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Application.Services.Order.Handlers.Commands;
using EtyraCommerce.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class UpdateOrderStatusCommandHandlerTests
    {
        private readonly Mock<IOrderProcessService> _mockOrderProcessService;
        private readonly Mock<ILogger<UpdateOrderStatusCommandHandler>> _mockLogger;
        private readonly UpdateOrderStatusCommandHandler _handler;

        public UpdateOrderStatusCommandHandlerTests()
        {
            _mockOrderProcessService = new Mock<IOrderProcessService>();
            _mockLogger = new Mock<ILogger<UpdateOrderStatusCommandHandler>>();
            _handler = new UpdateOrderStatusCommandHandler(_mockOrderProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccess()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Confirmed,
                Reason = "Payment received"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(
                    orderId,
                    OrderStatus.Confirmed,
                    "Payment received",
                    null,
                    null,
                    null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Message.Should().Be("Order status updated successfully");

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(
                    orderId,
                    OrderStatus.Confirmed,
                    "Payment received",
                    null,
                    null,
                    null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyOrderId_ReturnsBadRequest()
        {
            // Arrange
            var command = new UpdateOrderStatusCommand
            {
                OrderId = Guid.Empty,
                Status = OrderStatus.Confirmed
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Order ID is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(It.IsAny<Guid>(), It.IsAny<OrderStatus>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_InvalidOrderStatus_ReturnsBadRequest()
        {
            // Arrange
            var command = new UpdateOrderStatusCommand
            {
                OrderId = Guid.NewGuid(),
                Status = (OrderStatus)999 // Invalid enum value
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Invalid order status");

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(It.IsAny<Guid>(), It.IsAny<OrderStatus>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()),
                Times.Never
            );
        }

        [Theory]
        [InlineData(OrderStatus.Draft)]
        [InlineData(OrderStatus.Confirmed)]
        [InlineData(OrderStatus.Processing)]
        [InlineData(OrderStatus.Shipped)]
        [InlineData(OrderStatus.Delivered)]
        [InlineData(OrderStatus.Cancelled)]
        public async Task Handle_ValidOrderStatuses_CallsProcessServiceWithCorrectStatus(OrderStatus status)
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = status
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(orderId, status, null, null, null, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(orderId, status, null, null, null, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShippedStatusWithTrackingNumber_CallsProcessServiceWithTrackingInfo()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var trackingNumber = "TRK123456789";
            var expectedDeliveryDate = DateTime.UtcNow.AddDays(3);
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Shipped,
                TrackingNumber = trackingNumber,
                ExpectedDeliveryDate = expectedDeliveryDate
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Shipped, null, trackingNumber, expectedDeliveryDate, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Shipped, null, trackingNumber, expectedDeliveryDate, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_DeliveredStatusWithDeliveryDate_CallsProcessServiceWithDeliveryInfo()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var actualDeliveryDate = DateTime.UtcNow;
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Delivered,
                ActualDeliveryDate = actualDeliveryDate
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Delivered, null, null, null, actualDeliveryDate))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Delivered, null, null, null, actualDeliveryDate),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_CancelledStatusWithReason_CallsProcessServiceWithReason()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var reason = "Customer requested cancellation";
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Cancelled,
                Reason = reason
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Cancelled, reason, null, null, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Cancelled, reason, null, null, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new UpdateOrderStatusCommand
            {
                OrderId = Guid.NewGuid(),
                Status = OrderStatus.Confirmed
            };

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(It.IsAny<Guid>(), It.IsAny<OrderStatus>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Be("An error occurred while updating the order status");

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(It.IsAny<Guid>(), It.IsAny<OrderStatus>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceReturnsFailure_ReturnsFailureResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = OrderStatus.Confirmed
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.NotFound("Order not found");

            _mockOrderProcessService
                .Setup(x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Confirmed, null, null, null, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockOrderProcessService.Verify(
                x => x.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Confirmed, null, null, null, null),
                Times.Once
            );
        }

        #endregion
    }
}
