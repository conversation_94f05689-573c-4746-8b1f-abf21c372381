using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for SearchUsersQuery
    /// </summary>
    public class SearchUsersQueryHandler : IRequestHandler<SearchUsersQuery, CustomResponseDto<PagedResult<UserDto>>>
    {
        private readonly IUserService _userService;
        private readonly ILogger<SearchUsersQueryHandler> _logger;

        public SearchUsersQueryHandler(
            IUserService userService,
            ILogger<SearchUsersQueryHandler> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<UserDto>>> Handle(SearchUsersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing search users query with SearchTerm: {SearchTerm}, PageNumber: {PageNumber}, PageSize: {PageSize}",
                    request.SearchTerm, request.PageNumber, request.PageSize);

                // Validate pagination parameters
                if (request.PageNumber < 1)
                {
                    _logger.LogWarning("Search users query failed: Invalid page number {PageNumber}", request.PageNumber);
                    return CustomResponseDto<PagedResult<UserDto>>.BadRequest("Page number must be greater than 0");
                }

                if (request.PageSize < 1 || request.PageSize > 100)
                {
                    _logger.LogWarning("Search users query failed: Invalid page size {PageSize}", request.PageSize);
                    return CustomResponseDto<PagedResult<UserDto>>.BadRequest("Page size must be between 1 and 100");
                }

                // Create search DTO from query
                var searchDto = new UserSearchDto
                {
                    SearchTerm = request.SearchTerm,
                    IsEnabled = request.IsEnabled,
                    IsEmailConfirmed = request.IsEmailConfirmed,
                    IsPhoneConfirmed = request.IsPhoneConfirmed,
                    CreatedFrom = request.CreatedFrom,
                    CreatedTo = request.CreatedTo,
                    LastLoginFrom = request.LastLoginFrom,
                    LastLoginTo = request.LastLoginTo,
                    Culture = request.Culture,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection
                };

                // Use user service method for search
                var result = await _userService.SearchUsersAsync(searchDto);

                if (result.IsSuccess)
                {
                    _logger.LogDebug("Search users query successful. Found {TotalCount} users, returning page {PageNumber} of {TotalPages}",
                        result.Data?.TotalCount, result.Data?.PageNumber, result.Data?.TotalPages);
                }
                else
                {
                    _logger.LogWarning("Search users query failed. Reason: {Message}", result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing search users query");
                return CustomResponseDto<PagedResult<UserDto>>.InternalServerError("An error occurred while searching users");
            }
        }
    }
}
