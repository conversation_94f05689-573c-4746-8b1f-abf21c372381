using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Commands
{
    /// <summary>
    /// Handler for toggling address status (active/inactive)
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class ToggleAddressStatusCommandHandler : IRequestHandler<ToggleAddressStatusCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<ToggleAddressStatusCommandHandler> _logger;

        public ToggleAddressStatusCommandHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<ToggleAddressStatusCommandHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(ToggleAddressStatusCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling ToggleAddressStatusCommand for AddressId: {AddressId}, UserId: {UserId}, IsActive: {IsActive}",
                    request.AddressId, request.UserId, request.IsActive);

                // Validate request
                if (request.AddressId == Guid.Empty)
                {
                    _logger.LogWarning("ToggleAddressStatusCommand received with empty AddressId for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<NoContentDto>.BadRequest("Address ID is required");
                }

                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("ToggleAddressStatusCommand received with empty UserId for AddressId: {AddressId}", request.AddressId);
                    return CustomResponseDto<NoContentDto>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessToggleAddressStatusAsync(request.AddressId, request.UserId, request.IsActive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("ToggleAddressStatusCommand handled successfully for AddressId: {AddressId}, UserId: {UserId}",
                        request.AddressId, request.UserId);
                }
                else
                {
                    _logger.LogWarning("ToggleAddressStatusCommand failed for AddressId: {AddressId}, UserId: {UserId}, Error: {Error}",
                        request.AddressId, request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling ToggleAddressStatusCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while toggling the address status");
            }
        }
    }
}
