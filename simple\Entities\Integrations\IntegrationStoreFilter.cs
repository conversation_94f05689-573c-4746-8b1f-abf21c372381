﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;

namespace EtyraApp.Domain.Entities.Integrations;

public class IntegrationStoreFilter : BaseEntity
{
    public int? FilterId { get; set; }
    public int? FilterGroupId { get; set; }

    public Store Store { get; set; }
    public int? StoreId { get; set; }
    public int? RemoteFilterId { get; set; }
    public int? RemoteFilterGroupId { get; set; }

    public FilterGroup FilterGroup { get; set; }

}