[{"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "GetCart", "RelativePath": "api/Cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeExpired", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "AddToCart", "RelativePath": "api/Cart/add-item", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "addToCartDto", "Type": "EtyraCommerce.Application.DTOs.Cart.AddToCartDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "ClearCart", "RelativePath": "api/Cart/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "MergeCart", "RelativePath": "api/Cart/merge", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "mergeCartDto", "Type": "EtyraCommerce.Application.DTOs.Cart.MergeCartDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "GetMyCart", "RelativePath": "api/Cart/my-cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "RemoveFromCart", "RelativePath": "api/Cart/remove-item", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "removeFromCartDto", "Type": "EtyraCommerce.Application.DTOs.Cart.RemoveFromCartDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "GetCartSummary", "RelativePath": "api/Cart/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CartController", "Method": "UpdateCartItem", "RelativePath": "api/Cart/update-item", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateCartItemDto", "Type": "EtyraCommerce.Application.DTOs.Cart.UpdateCartItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "CreateCategory", "RelativePath": "api/Category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCategoryDto", "Type": "EtyraCommerce.Application.DTOs.Category.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategories", "RelativePath": "api/Category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "showInMenu", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "parentCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "level", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "UpdateCategory", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateCategoryDto", "Type": "EtyraCommerce.Application.DTOs.Category.UpdateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "DeleteCategory", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "forceDelete", "Type": "System.Boolean", "IsRequired": false}, {"Name": "deleteC<PERSON><PERSON>n", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoryById", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeParent", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoriesByParent", "RelativePath": "api/Category/by-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetMenuCategories", "RelativePath": "api/Category/menu", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "max<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetRootCategories", "RelativePath": "api/Category/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetAllCategories", "RelativePath": "api/Category/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchDto", "Type": "EtyraCommerce.Application.DTOs.Category.CategorySearchDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoryTree", "RelativePath": "api/Category/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "rootCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "max<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "menuOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "CreateInventory", "RelativePath": "api/Inventory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.CreateInventoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "UpdateInventory", "RelativePath": "api/Inventory", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.UpdateInventoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryTransactionController", "Method": "GetInventoryTransactions", "RelativePath": "api/inventory-transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inventoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "warehouseId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "reference", "Type": "System.String", "IsRequired": false}, {"Name": "referenceType", "Type": "System.String", "IsRequired": false}, {"Name": "userId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "transactionDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "transactionDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryTransactionDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryTransactionController", "Method": "GetTransactionsByProduct", "RelativePath": "api/inventory-transactions/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "warehouseId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "transactionDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "transactionDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryTransactionDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryTransactionController", "Method": "GetTransactionsByReference", "RelativePath": "api/inventory-transactions/reference/{reference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reference", "Type": "System.String", "IsRequired": true}, {"Name": "referenceType", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryTransactionDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryTransactionController", "Method": "GetTransactionsByWarehouse", "RelativePath": "api/inventory-transactions/warehouse/{warehouseId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "warehouseId", "Type": "System.Guid", "IsRequired": true}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "transactionDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "transactionDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryTransactionDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "GetInventoryById", "RelativePath": "api/Inventory/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "AdjustStock", "RelativePath": "api/Inventory/adjust", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "adjustmentDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.StockAdjustmentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "AllocateStock", "RelativePath": "api/Inventory/allocate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "allocationDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.StockAllocationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "GetLowStockItems", "RelativePath": "api/Inventory/low-stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "warehouseId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeWarehousesOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "maxItems", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Collections.Generic.List`1[[EtyraCommerce.Application.DTOs.Inventory.LowStockItemDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "GetInventoryByProduct", "RelativePath": "api/Inventory/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "warehouseId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeWarehousesOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Collections.Generic.List`1[[EtyraCommerce.Application.DTOs.Inventory.InventoryDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "ReleaseReservation", "RelativePath": "api/Inventory/release", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtyraCommerce.Application.DTOs.Inventory.ReleaseReservationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "ReserveStock", "RelativePath": "api/Inventory/reserve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reservationDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.StockReservationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "GetStockStatus", "RelativePath": "api/Inventory/stock-status/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "activeWarehousesOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.StockStatusDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.InventoryController", "Method": "TransferStock", "RelativePath": "api/Inventory/transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transferDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.StockTransferDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "CreateOrder", "RelativePath": "api/Order", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createOrderDto", "Type": "EtyraCommerce.Application.DTOs.Order.CreateOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "SearchOrders", "RelativePath": "api/Order", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "customerId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "paymentStatus", "Type": "System.String", "IsRequired": false}, {"Name": "shippingStatus", "Type": "System.String", "IsRequired": false}, {"Name": "minTotal", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxTotal", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "currency", "Type": "System.String", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "paymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "shippingMethod", "Type": "System.String", "IsRequired": false}, {"Name": "includeItems", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeCustomer", "Type": "System.Boolean", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "GetOrderById", "RelativePath": "api/Order/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "includeItems", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeCustomer", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "OrderBelongsToCustomer", "RelativePath": "api/Order/{orderId}/belongs-to/{customerId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "customerId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "CancelOrder", "RelativePath": "api/Order/{orderId}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "cancelOrderDto", "Type": "EtyraCommerce.Application.DTOs.Order.CancelOrderDto", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "ConfirmOrder", "RelativePath": "api/Order/{orderId}/confirm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "DeliverOrder", "RelativePath": "api/Order/{orderId}/deliver", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "deliverOrderDto", "Type": "EtyraCommerce.Application.DTOs.Order.DeliverOrderDto", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "OrderExists", "RelativePath": "api/Order/{orderId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "UpdatePaymentStatus", "RelativePath": "api/Order/{orderId}/payment-status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updatePaymentDto", "Type": "EtyraCommerce.Application.DTOs.Order.UpdatePaymentStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "ShipOrder", "RelativePath": "api/Order/{orderId}/ship", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "shipOrderDto", "Type": "EtyraCommerce.Application.DTOs.Order.ShipOrderDto", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "UpdateOrderStatus", "RelativePath": "api/Order/{orderId}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateStatusDto", "Type": "EtyraCommerce.Application.DTOs.Order.UpdateOrderStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "GetOrderByNumber", "RelativePath": "api/Order/by-number/{orderNumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderNumber", "Type": "System.String", "IsRequired": true}, {"Name": "includeItems", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "GetCustomerOrders", "RelativePath": "api/Order/customer/{customerId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "customerId", "Type": "System.Guid", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "GetMyOrders", "RelativePath": "api/Order/my-orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.OrderController", "Method": "GetOrderStatistics", "RelativePath": "api/Order/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "customerId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "currency", "Type": "System.String", "IsRequired": false}, {"Name": "topCustomersCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "recentOrdersCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "GetProducts", "RelativePath": "api/Product", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "CreateProduct", "RelativePath": "api/Product", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "EtyraCommerce.Application.Services.Product.Commands.CreateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "GetProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "UpdateProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "EtyraCommerce.Application.Services.Product.Commands.UpdateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "DeleteProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "UpdateProductPrice", "RelativePath": "api/Product/{id}/price", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "EtyraCommerce.Application.Services.Product.Commands.UpdateProductPriceCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "UpdateProductStatus", "RelativePath": "api/Product/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "EtyraCommerce.Application.Services.Product.Commands.UpdateProductStatusCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "UpdateProductStock", "RelativePath": "api/Product/{id}/stock", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "EtyraCommerce.Application.Services.Product.Commands.UpdateProductStockCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "GetProductsByCategory", "RelativePath": "api/Product/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "includeChildCategories", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.ProductController", "Method": "SearchProducts", "RelativePath": "api/Product/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Product.ProductDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "EtyraCommerce.Application.DTOs.User.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/User/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "EtyraCommerce.Application.DTOs.User.UserLoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "LogoutAll", "RelativePath": "api/User/logout-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "GetProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "RefreshToken", "RelativePath": "api/User/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "EtyraCommerce.Application.DTOs.Authentication.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "Register", "RelativePath": "api/User/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "EtyraCommerce.Application.DTOs.User.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "RevokeToken", "RelativePath": "api/User/revoke-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "revokeTokenDto", "Type": "EtyraCommerce.Application.DTOs.Authentication.RevokeTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "CreateUser<PERSON>dd<PERSON>", "RelativePath": "api/UserAddress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "EtyraCommerce.Application.DTOs.User.CreateUserAddressDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetUserAddresses", "RelativePath": "api/UserAddress", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "UpdateUserAdd<PERSON>", "RelativePath": "api/UserAddress/{addressId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateDto", "Type": "EtyraCommerce.Application.DTOs.User.UpdateUserAddressDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "DeleteUserAddress", "RelativePath": "api/UserAddress/{addressId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressId", "Type": "System.Guid", "IsRequired": true}, {"Name": "hardDelete", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetUserAddressById", "RelativePath": "api/UserAddress/{addressId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "Set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/UserAddress/{addressId}/set-default", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "ToggleAddressStatus", "RelativePath": "api/UserAddress/{addressId}/toggle-status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressId", "Type": "System.Guid", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetBillingAddresses", "RelativePath": "api/UserAddress/billing", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetDefaultAddress", "RelativePath": "api/UserAddress/default", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "addressType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetRecentUserAddresses", "RelativePath": "api/UserAddress/recent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "SearchUserAddresses", "RelativePath": "api/UserAddress/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetShippingAddresses", "RelativePath": "api/UserAddress/shipping", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserAddressController", "Method": "GetUserAddressStats", "RelativePath": "api/UserAddress/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.WarehouseController", "Method": "GetAllWarehouses", "RelativePath": "api/Warehouse", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Common.PagedResult`1[[EtyraCommerce.Application.DTOs.Inventory.WarehouseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.WarehouseController", "Method": "CreateWarehouse", "RelativePath": "api/Warehouse", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.CreateWarehouseDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.WarehouseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "EtyraCommerce.API.Controllers.WarehouseController", "Method": "UpdateWarehouse", "RelativePath": "api/Warehouse", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "EtyraCommerce.Application.DTOs.Inventory.UpdateWarehouseDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Inventory.WarehouseDto, EtyraCommerce.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "EtyraCommerce.API.Controllers.WarehouseController", "Method": "DeleteWarehouse", "RelativePath": "api/Warehouse/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[EtyraCommerce.API.WeatherForecast, EtyraCommerce.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]