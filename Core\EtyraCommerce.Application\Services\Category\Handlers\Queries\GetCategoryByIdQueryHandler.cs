using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Queries
{
    /// <summary>
    /// Handler for GetCategoryByIdQuery
    /// </summary>
    public class GetCategoryByIdQueryHandler : IRequestHandler<GetCategoryByIdQuery, CustomResponseDto<CategoryDto>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<GetCategoryByIdQueryHandler> _logger;

        public GetCategoryByIdQueryHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<GetCategoryByIdQueryHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CategoryDto>> Handle(GetCategoryByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get category by ID query for category ID: {CategoryId}", request.CategoryId);

                // Validation
                if (request.CategoryId == Guid.Empty)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category ID is required");

                // Delegate to process service
                var result = await _categoryProcessService.ProcessGetCategoryByIdAsync(
                    request.CategoryId,
                    request.IncludeChildren,
                    request.IncludeParent,
                    request.IncludeDescriptions,
                    request.LanguageCode);

                _logger.LogInformation("Get category by ID query processed successfully for category ID: {CategoryId}", request.CategoryId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get category by ID query for category ID: {CategoryId}", request.CategoryId);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while retrieving the category");
            }
        }
    }
}
