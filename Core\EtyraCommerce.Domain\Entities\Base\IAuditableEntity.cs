namespace EtyraCommerce.Domain.Entities
{
    /// <summary>
    /// Interface for entities that require audit information (who created/updated)
    /// </summary>
    public interface IAuditableEntity
    {
        /// <summary>
        /// ID of the user who created this entity
        /// </summary>
        Guid? CreatedBy { get; set; }

        /// <summary>
        /// ID of the user who last updated this entity
        /// </summary>
        Guid? UpdatedBy { get; set; }

        /// <summary>
        /// ID of the user who deleted this entity (for soft deletes)
        /// </summary>
        Guid? DeletedBy { get; set; }

        /// <summary>
        /// Sets the audit information for entity creation
        /// </summary>
        /// <param name="userId">ID of the user creating the entity</param>
        void SetCreatedBy(Guid userId);

        /// <summary>
        /// Sets the audit information for entity update
        /// </summary>
        /// <param name="userId">ID of the user updating the entity</param>
        void SetUpdatedBy(Guid userId);

        /// <summary>
        /// Sets the audit information for entity deletion
        /// </summary>
        /// <param name="userId">ID of the user deleting the entity</param>
        void SetDeletedBy(Guid userId);
    }
}
