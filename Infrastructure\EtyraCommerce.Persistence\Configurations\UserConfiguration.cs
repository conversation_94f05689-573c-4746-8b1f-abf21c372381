using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for User entity
    /// </summary>
    public class UserConfiguration : AuditableBaseEntityConfiguration<User>
    {
        public override void Configure(EntityTypeBuilder<User> builder)
        {
            // Apply base configuration (BaseEntity + AuditableBaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("users", "etyra_core");

            #region Basic Properties

            // First Name
            builder.Property(x => x.FirstName)
                .HasColumnName("first_name")
                .HasMaxLength(100)
                .IsRequired();

            // Last Name
            builder.Property(x => x.LastName)
                .HasColumnName("last_name")
                .HasMaxLength(100)
                .IsRequired();

            // Username
            builder.Property(x => x.Username)
                .HasColumnName("username")
                .HasMaxLength(50)
                .IsRequired();

            // Password Hash
            builder.Property(x => x.PasswordHash)
                .HasColumnName("password_hash")
                .HasMaxLength(500)
                .IsRequired();

            // Password Salt
            builder.Property(x => x.PasswordSalt)
                .HasColumnName("password_salt")
                .HasMaxLength(500)
                .IsRequired();

            #endregion

            #region Value Objects

            // Email Value Object (Required)
            ValueObjectConversions.ConfigureEmail<User>(
                builder.Property(x => x.Email),
                "email");

            // PhoneNumber Value Object (Optional)
            ValueObjectConversions.ConfigureNullablePhoneNumber<User>(
                builder.Property(x => x.PhoneNumber),
                "phone_number");

            #endregion

            #region Boolean Properties

            // Is Enabled
            builder.Property(x => x.IsEnabled)
                .HasColumnName("is_enabled")
                .HasDefaultValue(true)
                .IsRequired();

            // Is Email Confirmed
            builder.Property(x => x.IsEmailConfirmed)
                .HasColumnName("is_email_confirmed")
                .HasDefaultValue(false)
                .IsRequired();

            // Is Phone Confirmed
            builder.Property(x => x.IsPhoneConfirmed)
                .HasColumnName("is_phone_confirmed")
                .HasDefaultValue(false)
                .IsRequired();

            #endregion

            #region DateTime Properties

            // Last Login At
            builder.Property(x => x.LastLoginAt)
                .HasColumnName("last_login_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Locked Until
            builder.Property(x => x.LockedUntil)
                .HasColumnName("locked_until")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Password Reset Token Expiry
            builder.Property(x => x.PasswordResetTokenExpiry)
                .HasColumnName("password_reset_token_expiry")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Refresh Token Expires At
            builder.Property(x => x.RefreshTokenExpiresAt)
                .HasColumnName("refresh_token_expires_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Refresh Token Created At
            builder.Property(x => x.RefreshTokenCreatedAt)
                .HasColumnName("refresh_token_created_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Refresh Token Revoked At
            builder.Property(x => x.RefreshTokenRevokedAt)
                .HasColumnName("refresh_token_revoked_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            #endregion

            #region Security Properties

            // Failed Login Attempts
            builder.Property(x => x.FailedLoginAttempts)
                .HasColumnName("failed_login_attempts")
                .HasDefaultValue(0)
                .IsRequired();

            // Email Confirmation Token
            builder.Property(x => x.EmailConfirmationToken)
                .HasColumnName("email_confirmation_token")
                .HasMaxLength(500)
                .IsRequired(false);

            // Password Reset Token
            builder.Property(x => x.PasswordResetToken)
                .HasColumnName("password_reset_token")
                .HasMaxLength(500)
                .IsRequired(false);

            #endregion

            #region Localization Properties

            // Culture
            builder.Property(x => x.Culture)
                .HasColumnName("culture")
                .HasMaxLength(10)
                .HasDefaultValue("en-US")
                .IsRequired();

            // Time Zone
            builder.Property(x => x.TimeZone)
                .HasColumnName("time_zone")
                .HasMaxLength(50)
                .HasDefaultValue("UTC")
                .IsRequired();

            #endregion

            #region Optional Properties

            // Profile Picture URL
            builder.Property(x => x.ProfilePictureUrl)
                .HasColumnName("profile_picture_url")
                .HasMaxLength(500)
                .IsRequired(false);

            // Notes
            builder.Property(x => x.Notes)
                .HasColumnName("notes")
                .HasMaxLength(1000)
                .IsRequired(false);

            #endregion

            #region JWT Authentication Properties

            // Refresh Token
            builder.Property(x => x.RefreshToken)
                .HasColumnName("refresh_token")
                .HasMaxLength(500)
                .IsRequired(false);

            // Refresh Token Created By IP
            builder.Property(x => x.RefreshTokenCreatedByIp)
                .HasColumnName("refresh_token_created_by_ip")
                .HasMaxLength(45) // IPv6 max length
                .IsRequired(false);

            // Refresh Token Revoked By IP
            builder.Property(x => x.RefreshTokenRevokedByIp)
                .HasColumnName("refresh_token_revoked_by_ip")
                .HasMaxLength(45) // IPv6 max length
                .IsRequired(false);

            // Refresh Token Revoked Reason
            builder.Property(x => x.RefreshTokenRevokedReason)
                .HasColumnName("refresh_token_revoked_reason")
                .HasMaxLength(200)
                .IsRequired(false);

            #endregion

            #region Indexes

            // Unique Indexes
            builder.HasIndex(x => x.Email)
                .IsUnique()
                .HasDatabaseName("ix_users_email_unique");

            builder.HasIndex(x => x.Username)
                .IsUnique()
                .HasDatabaseName("ix_users_username_unique");

            // Performance Indexes
            builder.HasIndex(x => x.IsEnabled)
                .HasDatabaseName("ix_users_is_enabled");

            builder.HasIndex(x => x.IsEmailConfirmed)
                .HasDatabaseName("ix_users_is_email_confirmed");

            builder.HasIndex(x => x.LastLoginAt)
                .HasDatabaseName("ix_users_last_login_at");

            // Composite Indexes
            builder.HasIndex(x => new { x.IsEnabled, x.IsEmailConfirmed })
                .HasDatabaseName("ix_users_enabled_email_confirmed");

            // JWT Authentication Indexes
            builder.HasIndex(x => x.RefreshToken)
                .HasDatabaseName("ix_users_refresh_token");

            builder.HasIndex(x => x.RefreshTokenExpiresAt)
                .HasDatabaseName("ix_users_refresh_token_expires_at");

            builder.HasIndex(x => new { x.FirstName, x.LastName })
                .HasDatabaseName("ix_users_full_name");

            // Security Indexes
            builder.HasIndex(x => x.PasswordResetToken)
                .HasDatabaseName("ix_users_password_reset_token");

            builder.HasIndex(x => x.EmailConfirmationToken)
                .HasDatabaseName("ix_users_email_confirmation_token");

            #endregion

            #region Computed Columns (PostgreSQL)

            // Full Name computed column (if needed for searching)
            // builder.Property(x => x.FullName)
            //     .HasComputedColumnSql("CONCAT(first_name, ' ', last_name)")
            //     .HasColumnName("full_name");

            #endregion

            #region Constraints

            // Check constraints for business rules
            builder.HasCheckConstraint("CK_Users_FirstName_NotEmpty",
                "LENGTH(TRIM(first_name)) > 0");

            builder.HasCheckConstraint("CK_Users_LastName_NotEmpty",
                "LENGTH(TRIM(last_name)) > 0");

            builder.HasCheckConstraint("CK_Users_Username_NotEmpty",
                "LENGTH(TRIM(username)) > 0");

            builder.HasCheckConstraint("CK_Users_FailedLoginAttempts_NonNegative",
                "failed_login_attempts >= 0");

            #endregion
        }

        protected override string GetTableName() => "users";
    }
}
