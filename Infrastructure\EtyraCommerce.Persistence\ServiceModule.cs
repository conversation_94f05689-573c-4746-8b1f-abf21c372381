﻿using Autofac;
using AutoMapper;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Persistence.Mapping;
using EtyraCommerce.Persistence.Repositories;
using MediatR;
using System.Reflection;
using Module = Autofac.Module;

namespace EtyraCommerce.Persistence;

public class ServiceModule : Module
{
    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterGeneric(typeof(ReadRepository<>)).As(typeof(IReadRepository<>)).InstancePerLifetimeScope();
        builder.RegisterGeneric(typeof(WriteRepository<>)).As(typeof(IWriteRepository<>)).InstancePerLifetimeScope();

        // Register specific User repositories
        builder.RegisterType<Repositories.User.UserReadRepository>().As<Application.Repositories.User.IUserReadRepository>().InstancePerLifetimeScope();
        builder.RegisterType<Repositories.User.UserWriteRepository>().As<Application.Repositories.User.IUserWriteRepository>().InstancePerLifetimeScope();

        // Register specific UserAddress repositories
        builder.RegisterType<Repositories.UserAddress.UserAddressReadRepository>().As<Application.Repositories.UserAddress.IUserAddressReadRepository>().InstancePerLifetimeScope();
        builder.RegisterType<Repositories.UserAddress.UserAddressWriteRepository>().As<Application.Repositories.UserAddress.IUserAddressWriteRepository>().InstancePerLifetimeScope();

        builder.RegisterType<UnitOfWork.UnitOfWork>().As<IUnitOfWork>().InstancePerLifetimeScope();

        // Register Services
        builder.RegisterType<Services.User.UserService>().As<Application.Services.User.IUserService>().InstancePerLifetimeScope();
        builder.RegisterType<Services.User.UserProcessService>().As<Application.Services.User.IUserProcessService>().InstancePerLifetimeScope();

        builder.RegisterType<Services.Category.CategoryService>().As<Application.Services.Category.ICategoryService>().InstancePerLifetimeScope();
        builder.RegisterType<Services.Category.CategoryProcessService>().As<Application.Services.Category.ICategoryProcessService>().InstancePerLifetimeScope();

        builder.RegisterType<Services.Inventory.InventoryService>().As<Application.Services.Inventory.IInventoryService>().InstancePerLifetimeScope();
        builder.RegisterType<Services.Inventory.InventoryProcessService>().As<Application.Services.Inventory.IInventoryProcessService>().InstancePerLifetimeScope();

        // Register JWT Service
        builder.RegisterType<Services.Authentication.JwtService>().As<Application.Services.Authentication.IJwtService>().InstancePerLifetimeScope();

        var persistenceAssembly = Assembly.GetExecutingAssembly();
        var appAssembly = Assembly.GetAssembly(typeof(IReadRepository<>)) ?? throw new InvalidOperationException("Application assembly not found");

        //var infrastructureAssembly = Assembly.GetAssembly(typeof(BazaarValidators));
        //var infrastructureServicesAssembly = Assembly.GetAssembly(typeof(ProductPopularityMetricsService));

        // Register specific repositories first (before assembly scanning)
        builder.RegisterAssemblyTypes(persistenceAssembly)
            .Where(x => x.Name.EndsWith("Repository"))
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope();

        // Register services from persistence assembly
        builder.RegisterAssemblyTypes(persistenceAssembly)
            .Where(x => x.Name.EndsWith("Service"))
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope();

        // Register MediatR
        builder.RegisterType<Mediator>().As<IMediator>().InstancePerLifetimeScope();

        // Register MediatR handlers from Application assembly
        builder.RegisterAssemblyTypes(appAssembly)
            .Where(t => t.IsClosedTypeOf(typeof(IRequestHandler<,>)))
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope();

        builder.RegisterAssemblyTypes(appAssembly)
            .Where(t => t.IsClosedTypeOf(typeof(INotificationHandler<>)))
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope();

        //builder.RegisterAssemblyTypes(infrastructureAssembly, infrastructureAssembly, appAssembly)
        //    .Where(x => x.Name.EndsWith("Service")).AsImplementedInterfaces().InstancePerLifetimeScope();

        // Infrastructure Services Assembly'yi de tara
        //if (infrastructureServicesAssembly != null)
        //{
        //    builder.RegisterAssemblyTypes(infrastructureServicesAssembly)
        //        .Where(x => x.Name.EndsWith("Service")).AsImplementedInterfaces().InstancePerLifetimeScope();
        //}


        //builder.RegisterType<InstagramService>().SingleInstance();

        builder.Register(ctx => new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MapProfile>();

        }).CreateMapper()).As<IMapper>().InstancePerLifetimeScope();


        //var apiAssembly = Assembly.GetAssembly(typeof(ProductApiService));

        //builder.RegisterAssemblyTypes(persistenceAssembly, persistenceAssembly, apiAssembly)
        //    .Where(x => x.Name.EndsWith("Service")).AsSelf();


        builder.Register(c =>
        {
            var httpClientFactory = c.Resolve<IHttpClientFactory>();
            return httpClientFactory.CreateClient("EtyraCommerceClient");
        }).As<HttpClient>().InstancePerLifetimeScope();




    }



}