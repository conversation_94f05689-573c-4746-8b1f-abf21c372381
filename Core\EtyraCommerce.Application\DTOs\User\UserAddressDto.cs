using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// User Address Data Transfer Object for API responses
    /// </summary>
    public class UserAddressDto : BaseDto
    {
        /// <summary>
        /// Reference to the user who owns this address
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Type of address (1=Billing, 2=Shipping, 3=Both)
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// Indicates if this is the default address for the user
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// First name of the person at this address
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the person at this address
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number for this address
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User-friendly label for this address
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// Additional delivery instructions
        /// </summary>
        public string? DeliveryInstructions { get; set; }

        /// <summary>
        /// Indicates if this address is active and can be used
        /// </summary>
        public bool IsActive { get; set; }

        #region Address Fields

        /// <summary>
        /// Street address
        /// </summary>
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Second address line (optional)
        /// </summary>
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State or province
        /// </summary>
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal code
        /// </summary>
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        public string Country { get; set; } = string.Empty;

        #endregion

        #region Company Information

        /// <summary>
        /// Company name for corporate addresses
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// Tax number for corporate addresses
        /// </summary>
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Tax office for corporate addresses
        /// </summary>
        public string? TaxOffice { get; set; }

        /// <summary>
        /// Company title/legal name for corporate addresses
        /// </summary>
        public string? CompanyTitle { get; set; }

        /// <summary>
        /// Indicates if this is a corporate/company address
        /// </summary>
        public bool IsCompanyAddress { get; set; } = false;

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the full name of the person at this address
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Gets the address type as a display string
        /// </summary>
        public string TypeDisplay => Type switch
        {
            1 => "Billing Only",
            2 => "Shipping Only",
            3 => "Billing & Shipping",
            _ => "Unknown"
        };

        /// <summary>
        /// Gets a single-line address summary
        /// </summary>
        public string AddressSummary
        {
            get
            {
                var parts = new List<string> { Street };

                if (!string.IsNullOrEmpty(AddressLine2))
                    parts.Add(AddressLine2);

                parts.Add($"{City}, {State} {PostalCode}, {Country}");

                var summary = string.Join(", ", parts);

                if (!string.IsNullOrEmpty(Label))
                    summary = $"{Label}: {summary}";

                return summary;
            }
        }

        /// <summary>
        /// Gets a multi-line formatted address
        /// </summary>
        public string FormattedAddress
        {
            get
            {
                var parts = new List<string> { Street };

                if (!string.IsNullOrEmpty(AddressLine2))
                    parts.Add(AddressLine2);

                parts.Add($"{City}, {State} {PostalCode}");
                parts.Add(Country);

                return string.Join(Environment.NewLine, parts);
            }
        }

        /// <summary>
        /// Checks if this address can be used for billing
        /// </summary>
        public bool CanBeUsedForBilling => IsActive && (Type == 1 || Type == 3);

        /// <summary>
        /// Checks if this address can be used for shipping
        /// </summary>
        public bool CanBeUsedForShipping => IsActive && (Type == 2 || Type == 3);

        #endregion
    }

    /// <summary>
    /// DTO for creating a new user address
    /// </summary>
    public class CreateUserAddressDto
    {
        /// <summary>
        /// Type of address (1=Billing, 2=Shipping, 3=Both)
        /// </summary>
        [Required(ErrorMessage = "Address type is required")]
        [Range(1, 3, ErrorMessage = "Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)")]
        public int Type { get; set; } = 3; // Default to Both

        /// <summary>
        /// Indicates if this should be the default address
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// First name of the person at this address
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the person at this address
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number for this address
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User-friendly label for this address
        /// </summary>
        [StringLength(50, ErrorMessage = "Label cannot exceed 50 characters")]
        public string? Label { get; set; }

        /// <summary>
        /// Additional delivery instructions
        /// </summary>
        [StringLength(500, ErrorMessage = "Delivery instructions cannot exceed 500 characters")]
        public string? DeliveryInstructions { get; set; }

        #region Address Fields

        /// <summary>
        /// Street address
        /// </summary>
        [Required(ErrorMessage = "Street address is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Street address must be between 1 and 200 characters")]
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Second address line (optional)
        /// </summary>
        [StringLength(200, ErrorMessage = "Address line 2 cannot exceed 200 characters")]
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [Required(ErrorMessage = "City is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "City must be between 1 and 100 characters")]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State or province
        /// </summary>
        [Required(ErrorMessage = "State is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "State must be between 1 and 100 characters")]
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal code
        /// </summary>
        [Required(ErrorMessage = "Postal code is required")]
        [StringLength(20, MinimumLength = 1, ErrorMessage = "Postal code must be between 1 and 20 characters")]
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        [Required(ErrorMessage = "Country is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Country must be between 1 and 100 characters")]
        public string Country { get; set; } = string.Empty;

        #endregion

        #region Company Information

        /// <summary>
        /// Company name for corporate addresses
        /// </summary>
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string? CompanyName { get; set; }

        /// <summary>
        /// Tax number for corporate addresses
        /// </summary>
        [StringLength(50, ErrorMessage = "Tax number cannot exceed 50 characters")]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Tax office for corporate addresses
        /// </summary>
        [StringLength(100, ErrorMessage = "Tax office cannot exceed 100 characters")]
        public string? TaxOffice { get; set; }

        /// <summary>
        /// Company title/legal name for corporate addresses
        /// </summary>
        [StringLength(300, ErrorMessage = "Company title cannot exceed 300 characters")]
        public string? CompanyTitle { get; set; }

        /// <summary>
        /// Indicates if this is a corporate/company address
        /// </summary>
        public bool IsCompanyAddress { get; set; } = false;

        #endregion
    }

    /// <summary>
    /// DTO for updating an existing user address
    /// </summary>
    public class UpdateUserAddressDto
    {
        /// <summary>
        /// Type of address (1=Billing, 2=Shipping, 3=Both)
        /// </summary>
        [Required(ErrorMessage = "Address type is required")]
        [Range(1, 3, ErrorMessage = "Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)")]
        public int Type { get; set; }

        /// <summary>
        /// Indicates if this should be the default address
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// First name of the person at this address
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the person at this address
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number for this address
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User-friendly label for this address
        /// </summary>
        [StringLength(50, ErrorMessage = "Label cannot exceed 50 characters")]
        public string? Label { get; set; }

        /// <summary>
        /// Additional delivery instructions
        /// </summary>
        [StringLength(500, ErrorMessage = "Delivery instructions cannot exceed 500 characters")]
        public string? DeliveryInstructions { get; set; }

        /// <summary>
        /// Indicates if this address is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        #region Address Fields

        /// <summary>
        /// Street address
        /// </summary>
        [Required(ErrorMessage = "Street address is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Street address must be between 1 and 200 characters")]
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Second address line (optional)
        /// </summary>
        [StringLength(200, ErrorMessage = "Address line 2 cannot exceed 200 characters")]
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [Required(ErrorMessage = "City is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "City must be between 1 and 100 characters")]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State or province
        /// </summary>
        [Required(ErrorMessage = "State is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "State must be between 1 and 100 characters")]
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal code
        /// </summary>
        [Required(ErrorMessage = "Postal code is required")]
        [StringLength(20, MinimumLength = 1, ErrorMessage = "Postal code must be between 1 and 20 characters")]
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        [Required(ErrorMessage = "Country is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Country must be between 1 and 100 characters")]
        public string Country { get; set; } = string.Empty;

        #endregion

        #region Company Information

        /// <summary>
        /// Company name for corporate addresses
        /// </summary>
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string? CompanyName { get; set; }

        /// <summary>
        /// Tax number for corporate addresses
        /// </summary>
        [StringLength(50, ErrorMessage = "Tax number cannot exceed 50 characters")]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Tax office for corporate addresses
        /// </summary>
        [StringLength(100, ErrorMessage = "Tax office cannot exceed 100 characters")]
        public string? TaxOffice { get; set; }

        /// <summary>
        /// Company title/legal name for corporate addresses
        /// </summary>
        [StringLength(300, ErrorMessage = "Company title cannot exceed 300 characters")]
        public string? CompanyTitle { get; set; }

        /// <summary>
        /// Indicates if this is a corporate/company address
        /// </summary>
        public bool IsCompanyAddress { get; set; } = false;

        #endregion
    }
}
