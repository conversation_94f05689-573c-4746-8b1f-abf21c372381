using AutoMapper;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Services.Product;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Services
{
    /// <summary>
    /// Unit tests for ProductProcessService
    /// </summary>
    public class ProductProcessServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<ProductProcessService>> _mockLogger;
        private readonly Mock<IReadRepository<Product>> _mockProductReadRepo;
        private readonly Mock<IWriteRepository<Product>> _mockProductWriteRepo;
        private readonly ProductProcessService _productProcessService;

        public ProductProcessServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<ProductProcessService>>();
            _mockProductReadRepo = new Mock<IReadRepository<Product>>();
            _mockProductWriteRepo = new Mock<IWriteRepository<Product>>();

            _mockUnitOfWork.Setup(u => u.ReadRepository<Product>()).Returns(_mockProductReadRepo.Object);
            _mockUnitOfWork.Setup(u => u.WriteRepository<Product>()).Returns(_mockProductWriteRepo.Object);

            _productProcessService = new ProductProcessService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object);
        }

        #region ProcessCreateProductAsync Tests

        [Fact]
        public async Task ProcessCreateProductAsync_ValidDto_ReturnsSuccessResponse()
        {
            // Arrange
            var createDto = new CreateProductDto
            {
                Name = "Test Product",
                Model = "TP-001",
                SKU = "SKU-001",
                BasePrice = 100.00m
            };

            var productDto = new ProductDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Product",
                Model = "TP-001",
                SKU = "SKU-001",
                BasePrice = 100.00m
            };

            var products = new List<Product>().AsQueryable();

            _mockProductReadRepo.Setup(r => r.GetAllAsync(false)).ReturnsAsync(products);
            _mockProductWriteRepo.Setup(w => w.AddAsync(It.IsAny<Product>())).ReturnsAsync(It.IsAny<Product>());
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).ReturnsAsync(1);
            _mockMapper.Setup(m => m.Map<ProductDto>(It.IsAny<Product>())).Returns(productDto);

            // Act
            var result = await _productProcessService.ProcessCreateProductAsync(createDto);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal("Test Product", result.Data.Name);

            _mockProductWriteRepo.Verify(w => w.AddAsync(It.IsAny<Product>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessCreateProductAsync_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var createDto = new CreateProductDto { Name = "Test Product", Model = "TP-001" };

            _mockProductWriteRepo.Setup(w => w.AddAsync(It.IsAny<Product>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _productProcessService.ProcessCreateProductAsync(createDto);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(500, result.StatusCode);
            Assert.Contains("Error creating product", result.Message);
        }

        #endregion

        #region ProcessGetProductByIdAsync Tests

        [Fact]
        public async Task ProcessGetProductByIdAsync_ExistingProduct_ReturnsSuccessResponse()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                Model = "TP-001",
                SKU = "SKU-001",
                BasePrice = new Money(100.00m, Currency.USD)
            };

            var productDto = new ProductDto
            {
                Id = productId,
                Name = "Test Product",
                Model = "TP-001",
                SKU = "SKU-001",
                BasePrice = 100.00m
            };

            _mockProductReadRepo.Setup(r => r.GetByIdAsync(productId, true)).ReturnsAsync(product);
            _mockMapper.Setup(m => m.Map<ProductDto>(product)).Returns(productDto);

            // Act
            var result = await _productProcessService.ProcessGetProductByIdAsync(productId);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(productId, result.Data.Id);
            Assert.Equal("Test Product", result.Data.Name);
        }

        [Fact]
        public async Task ProcessGetProductByIdAsync_ProductNotFound_ReturnsNotFoundResponse()
        {
            // Arrange
            var productId = Guid.NewGuid();

            _mockProductReadRepo.Setup(r => r.GetByIdAsync(productId, true)).ReturnsAsync((Product)null);

            // Act
            var result = await _productProcessService.ProcessGetProductByIdAsync(productId);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsSuccess);
            Assert.Equal(404, result.StatusCode);
            Assert.Equal("Product not found", result.Message);
        }

        #endregion

        #region Business Logic Helper Tests

        [Fact]
        public async Task IsSkuUniqueAsync_UniqueSku_ReturnsTrue()
        {
            // Arrange
            var sku = "UNIQUE-SKU";

            // Act
            var result = await _productProcessService.IsSkuUniqueAsync(sku);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GenerateProductSlugAsync_ValidInput_ReturnsSlug()
        {
            // Arrange
            var name = "Test Product";
            var model = "TP-001";

            // Act
            var result = await _productProcessService.GenerateProductSlugAsync(name, model);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("test-product-tp-001", result);
        }

        [Fact]
        public async Task CanDeleteProductAsync_ValidProduct_ReturnsTrue()
        {
            // Arrange
            var productId = Guid.NewGuid();

            // Act
            var result = await _productProcessService.CanDeleteProductAsync(productId);

            // Assert
            Assert.True(result);
        }

        #endregion
    }
}
