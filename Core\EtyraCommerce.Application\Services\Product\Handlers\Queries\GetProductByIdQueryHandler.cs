using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Queries
{
    /// <summary>
    /// Handler for getting a product by ID
    /// </summary>
    public class GetProductByIdQueryHandler : IRequestHandler<GetProductByIdQuery, CustomResponseDto<ProductDto>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<GetProductByIdQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public GetProductByIdQueryHandler(
            IProductProcessService productProcessService,
            ILogger<GetProductByIdQueryHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the get product by ID query
        /// </summary>
        /// <param name="request">Get product by ID query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Product DTO response</returns>
        public async Task<CustomResponseDto<ProductDto>> Handle(GetProductByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get product by ID query for ProductId: {ProductId}", request.ProductId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<ProductDto>.BadRequest("Product ID is required");

                // Delegate to business logic service
                var result = await _productProcessService.ProcessGetProductByIdAsync(
                    request.ProductId,
                    request.IncludeDescriptions,
                    request.IncludeImages,
                    request.IncludeCategories,
                    request.IncludeDiscounts,
                    request.IncludeAttributes,
                    request.IncludeVariants,
                    request.IncludeInventory,
                    request.IncludeDeleted,
                    request.LanguageCode,
                    request.StoreId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Get product by ID query processed successfully for ProductId: {ProductId}",
                        request.ProductId);
                }
                else
                {
                    _logger.LogWarning("Get product by ID query failed for ProductId: {ProductId}, Errors: {Errors}",
                        request.ProductId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get product by ID query for ProductId: {ProductId}",
                    request.ProductId);
                return CustomResponseDto<ProductDto>.InternalServerError("An error occurred while retrieving the product");
            }
        }
    }
}
