# EtyraCommerce Environment Variables

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=etyracommerce
DB_USER=etyra
DB_PASSWORD=EtyraCommerce2024!

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=EtyraRedis2024!

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://+:5000

# JWT Configuration
JWT_SECRET=EtyraCommerce_JWT_Secret_Key_2024_Very_Long_And_Secure
JWT_ISSUER=EtyraCommerce
JWT_AUDIENCE=EtyraCommerce.API
JWT_EXPIRY_MINUTES=60

# File Upload Configuration
MAX_FILE_SIZE_MB=10
UPLOAD_PATH=./uploads

# Email Configuration (for later use)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM_EMAIL=<EMAIL>

# Logging
LOG_LEVEL=Information
