using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for validating if user exists by email or username
    /// </summary>
    public class ValidateUserExistsQuery : IRequest<CustomResponseDto<bool>>
    {
        /// <summary>
        /// Email address to check
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Username to check
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// User ID to exclude from the check (useful for updates)
        /// </summary>
        public Guid? ExcludeUserId { get; set; }

        /// <summary>
        /// Whether to include deleted users in the check
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        public ValidateUserExistsQuery() { }

        public ValidateUserExistsQuery(string? email = null, string? username = null, Guid? excludeUserId = null)
        {
            Email = email;
            Username = username;
            ExcludeUserId = excludeUserId;
        }

        /// <summary>
        /// Factory method for email existence check
        /// </summary>
        public static ValidateUserExistsQuery ForEmail(string email, Guid? excludeUserId = null)
        {
            return new ValidateUserExistsQuery(email: email, excludeUserId: excludeUserId);
        }

        /// <summary>
        /// Factory method for username existence check
        /// </summary>
        public static ValidateUserExistsQuery ForUsername(string username, Guid? excludeUserId = null)
        {
            return new ValidateUserExistsQuery(username: username, excludeUserId: excludeUserId);
        }
    }
}
