﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Competitor;

namespace EtyraApp.Domain.Entities.Integrations;

public class IntegrationCompetitorProduct : BaseEntity
{
    public Product Product { get; set; }
    public int? ProductId { get; set; }
    public Competitor.Competitor Competitor { get; set; }
    public int? CompetitorId { get; set; }
    public CompetitorProduct CompetitorProduct { get; set; }
    public int CompetitorProductId { get; set; }

}