using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Shipping.Handlers.Commands
{
    /// <summary>
    /// Handler for updating an existing country
    /// Delegates business logic to CountryProcessService
    /// </summary>
    public class UpdateCountryCommandHandler : IRequestHandler<UpdateCountryCommand, UpdateCountryResponse>
    {
        private readonly ICountryProcessService _countryProcessService;
        private readonly ILogger<UpdateCountryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateCountryCommandHandler(
            ICountryProcessService countryProcessService,
            ILogger<UpdateCountryCommandHandler> logger)
        {
            _countryProcessService = countryProcessService ?? throw new ArgumentNullException(nameof(countryProcessService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the update country command
        /// </summary>
        public async Task<UpdateCountryResponse> Handle(UpdateCountryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing update country command for CountryId: {CountryId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                {
                    _logger.LogWarning("Update country command failed: Country ID is required");
                    throw new ArgumentException("Country ID is required");
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    _logger.LogWarning("Update country command failed: Country name is required");
                    throw new ArgumentException("Country name is required");
                }

                if (string.IsNullOrWhiteSpace(request.CurrencyCode))
                {
                    _logger.LogWarning("Update country command failed: Currency code is required");
                    throw new ArgumentException("Currency code is required");
                }

                // Delegate to CountryProcessService for business logic
                var result = await _countryProcessService.ProcessUpdateCountryAsync(request, cancellationToken);

                if (result != null)
                {
                    _logger.LogInformation("Country update successful for CountryId: {CountryId}, Name: {CountryName}",
                        request.Id, result.Name);
                }
                else
                {
                    _logger.LogWarning("Country update failed for CountryId: {CountryId}", request.Id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update country command for CountryId: {CountryId}", request.Id);
                throw;
            }
        }
    }
}
