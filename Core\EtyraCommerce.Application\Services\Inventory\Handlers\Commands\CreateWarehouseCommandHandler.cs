using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for creating warehouses
    /// </summary>
    public class CreateWarehouseCommandHandler : IRequestHandler<CreateWarehouseCommand, CustomResponseDto<WarehouseDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<CreateWarehouseCommandHandler> _logger;

        public CreateWarehouseCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<CreateWarehouseCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<WarehouseDto>> Handle(CreateWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling create warehouse command: {Name} ({Code})", request.Name, request.Code);

                // Validation
                if (string.IsNullOrEmpty(request.Name))
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse name is required");

                if (string.IsNullOrEmpty(request.Code))
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse code is required");

                if (request.Name.Length > 100)
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse name cannot exceed 100 characters");

                if (request.Code.Length > 20)
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse code cannot exceed 20 characters");

                // Create DTO
                var createDto = new CreateWarehouseDto
                {
                    Name = request.Name,
                    Code = request.Code,
                    Description = request.Description,
                    Address = request.Address,
                    Phone = request.Phone,
                    Email = request.Email,
                    ManagerName = request.ManagerName,
                    IsActive = request.IsActive,
                    IsMain = request.IsMain,
                    Type = request.Type,
                    SortOrder = request.SortOrder
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessCreateWarehouseAsync(createDto);

                _logger.LogInformation("Create warehouse command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling create warehouse command: {Name} ({Code})", request.Name, request.Code);
                return CustomResponseDto<WarehouseDto>.InternalServerError("An error occurred while creating warehouse");
            }
        }
    }
}
