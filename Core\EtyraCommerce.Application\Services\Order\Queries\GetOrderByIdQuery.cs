using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Queries
{
    /// <summary>
    /// Query to get order by ID
    /// </summary>
    public class GetOrderByIdQuery : IRequest<CustomResponseDto<OrderDto>>
    {
        /// <summary>
        /// Order ID
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// Include order items in response
        /// </summary>
        public bool IncludeItems { get; set; } = true;

        /// <summary>
        /// Include customer information in response
        /// </summary>
        public bool IncludeCustomer { get; set; } = false;

        /// <summary>
        /// Language code for localized content
        /// </summary>
        public string? LanguageCode { get; set; }
    }
}
