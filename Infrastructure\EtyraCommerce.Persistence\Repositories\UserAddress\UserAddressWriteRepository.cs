using EtyraCommerce.Application.Repositories.UserAddress;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.UserAddress
{
    /// <summary>
    /// Write repository implementation for UserAddress entity
    /// </summary>
    public class UserAddressWriteRepository : WriteRepository<Domain.Entities.User.UserAddress>, IUserAddressWriteRepository
    {
        public UserAddressWriteRepository(EtyraCommerceDbContext context) : base(context)
        {
        }

        public async Task SetAsDefaultAddressAsync(Guid addressId, Guid userId)
        {
            // First, remove default status from all other addresses
            await RemoveAllDefaultStatusAsync(userId);

            // Then set the specified address as default
            var address = await Table.FirstOrDefaultAsync(a => a.Id == addressId && a.UserId == userId);
            if (address != null)
            {
                address.SetAsDefault();
                await UpdateAsync(address);
            }
        }

        public async Task RemoveAllDefaultStatusAsync(Guid userId)
        {
            var defaultAddresses = await Table
                .Where(a => a.UserId == userId && a.IsDefault && !a.IsDeleted)
                .ToListAsync();

            foreach (var address in defaultAddresses)
            {
                address.RemoveDefaultStatus();
                await UpdateAsync(address);
            }
        }

        public async Task ActivateAddressAsync(Guid addressId)
        {
            var address = await Table.FindAsync(addressId);
            if (address != null)
            {
                address.Activate();
                await UpdateAsync(address);
            }
        }

        public async Task DeactivateAddressAsync(Guid addressId)
        {
            var address = await Table.FindAsync(addressId);
            if (address != null)
            {
                address.Deactivate();
                await UpdateAsync(address);
            }
        }

        public async Task BulkActivateAddressesAsync(List<Guid> addressIds)
        {
            var addresses = await Table
                .Where(a => addressIds.Contains(a.Id) && !a.IsDeleted)
                .ToListAsync();

            foreach (var address in addresses)
            {
                address.Activate();
                UpdateAsync(address);
            }
        }

        public async Task BulkDeactivateAddressesAsync(List<Guid> addressIds)
        {
            var addresses = await Table
                .Where(a => addressIds.Contains(a.Id) && !a.IsDeleted)
                .ToListAsync();

            foreach (var address in addresses)
            {
                address.Deactivate();
                UpdateAsync(address);
            }
        }

        public async Task SoftDeleteUserAddressesAsync(Guid userId)
        {
            var addresses = await Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .ToListAsync();

            foreach (var address in addresses)
            {
                await SoftDeleteAsync(address);
            }
        }

        public async Task HardDeleteUserAddressesAsync(Guid userId)
        {
            var addresses = await Table
                .Where(a => a.UserId == userId)
                .ToListAsync();

            foreach (var address in addresses)
            {
                await RemoveAsync(address);
            }
        }

        public async Task UpdateLastUsedAsync(Guid addressId)
        {
            var address = await Table.FindAsync(addressId);
            if (address != null)
            {
                // Update the UpdatedAt timestamp to track last usage
                // This is handled automatically by the base entity's MarkAsUpdated method
                await UpdateAsync(address);
            }
        }

        public async Task BulkUpdateAsync(List<Domain.Entities.User.UserAddress> addresses)
        {
            foreach (var address in addresses)
            {
                UpdateAsync(address);
            }
        }

        public async Task BulkAddAsync(List<Domain.Entities.User.UserAddress> addresses)
        {
            foreach (var address in addresses)
            {
                await AddAsync(address);
            }
        }

        public async Task EnsureSingleDefaultAddressAsync(Guid userId)
        {
            var defaultAddresses = await Table
                .Where(a => a.UserId == userId && a.IsDefault && a.IsActive && !a.IsDeleted)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            if (defaultAddresses.Count > 1)
            {
                // Keep the most recently created as default, remove default status from others
                for (int i = 1; i < defaultAddresses.Count; i++)
                {
                    defaultAddresses[i].RemoveDefaultStatus();
                    await UpdateAsync(defaultAddresses[i]);
                }
            }
            else if (defaultAddresses.Count == 0)
            {
                // If no default address exists, set the first active address as default
                var firstActiveAddress = await Table
                    .Where(a => a.UserId == userId && a.IsActive && !a.IsDeleted)
                    .OrderBy(a => a.CreatedAt)
                    .FirstOrDefaultAsync();

                if (firstActiveAddress != null)
                {
                    firstActiveAddress.SetAsDefault();
                    await UpdateAsync(firstActiveAddress);
                }
            }
        }

        public async Task<int> ArchiveOldAddressesAsync(Guid userId, int olderThanDays)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);

            var oldAddresses = await Table
                .Where(a => a.UserId == userId &&
                           !a.IsDeleted &&
                           !a.IsDefault &&
                           a.CreatedAt < cutoffDate &&
                           (a.UpdatedAt == null || a.UpdatedAt < cutoffDate))
                .ToListAsync();

            foreach (var address in oldAddresses)
            {
                await SoftDeleteAsync(address);
            }

            return oldAddresses.Count;
        }

        public async Task RestoreAddressAsync(Guid addressId)
        {
            var address = await Table
                .IgnoreQueryFilters() // Include soft-deleted entities
                .FirstOrDefaultAsync(a => a.Id == addressId);

            if (address != null && address.IsDeleted)
            {
                address.IsDeleted = false;
                address.DeletedAt = null;
                UpdateAsync(address);
            }
        }

        public async Task BulkRestoreAddressesAsync(List<Guid> addressIds)
        {
            var addresses = await Table
                .IgnoreQueryFilters() // Include soft-deleted entities
                .Where(a => addressIds.Contains(a.Id) && a.IsDeleted)
                .ToListAsync();

            foreach (var address in addresses)
            {
                address.IsDeleted = false;
                address.DeletedAt = null;
                UpdateAsync(address);
            }
        }
    }
}
