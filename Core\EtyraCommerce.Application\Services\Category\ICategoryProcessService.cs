using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;

namespace EtyraCommerce.Application.Services.Category
{
    /// <summary>
    /// Category process service interface for business logic operations
    /// </summary>
    public interface ICategoryProcessService
    {
        #region Category Management

        /// <summary>
        /// Processes category creation with business logic
        /// </summary>
        /// <param name="createCategoryDto">Category creation data</param>
        /// <returns>Created category</returns>
        Task<CustomResponseDto<CategoryDto>> ProcessCreateCategoryAsync(CreateCategoryDto createCategoryDto);

        /// <summary>
        /// Processes category update with business logic
        /// </summary>
        /// <param name="categoryId">Category ID to update</param>
        /// <param name="updateCategoryDto">Category update data</param>
        /// <returns>Updated category</returns>
        Task<CustomResponseDto<CategoryDto>> ProcessUpdateCategoryAsync(Guid categoryId, UpdateCategoryDto updateCategoryDto);

        /// <summary>
        /// Processes category deletion with business logic
        /// </summary>
        /// <param name="categoryId">Category ID to delete</param>
        /// <param name="forceDelete">Whether to force delete (hard delete)</param>
        /// <param name="deleteChildren">Whether to delete child categories as well</param>
        /// <returns>Operation result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessDeleteCategoryAsync(Guid categoryId, bool forceDelete = false, bool deleteChildren = false);

        #endregion

        #region Category Queries

        /// <summary>
        /// Processes category retrieval by ID with business logic
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="includeChildren">Whether to include child categories</param>
        /// <param name="includeParent">Whether to include parent category</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Category data</returns>
        Task<CustomResponseDto<CategoryDto>> ProcessGetCategoryByIdAsync(Guid categoryId, bool includeChildren = false, bool includeParent = false, bool includeDescriptions = true, string? languageCode = null);

        /// <summary>
        /// Processes category search with business logic
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="showInMenu">Filter by show in menu status</param>
        /// <param name="parentCategoryId">Filter by parent category ID</param>
        /// <param name="level">Filter by level</param>
        /// <param name="includeChildren">Include child categories</param>
        /// <param name="includeDescriptions">Include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <param name="sortBy">Sort field</param>
        /// <param name="sortDirection">Sort direction</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paged category results</returns>
        Task<CustomResponseDto<PagedResult<CategoryDto>>> ProcessGetAllCategoriesAsync(
            string? searchTerm = null,
            bool? isActive = null,
            bool? showInMenu = null,
            Guid? parentCategoryId = null,
            int? level = null,
            bool includeChildren = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending,
            int pageNumber = 1,
            int pageSize = 20);

        /// <summary>
        /// Processes category retrieval by parent with business logic
        /// </summary>
        /// <param name="parentCategoryId">Parent category ID (null for root categories)</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="includeChildren">Whether to include child categories recursively</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <param name="sortBy">Sort field</param>
        /// <param name="sortDirection">Sort direction</param>
        /// <returns>List of categories</returns>
        Task<CustomResponseDto<List<CategoryDto>>> ProcessGetCategoriesByParentAsync(
            Guid? parentCategoryId,
            bool activeOnly = true,
            bool includeChildren = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending);

        /// <summary>
        /// Processes category tree retrieval with business logic
        /// </summary>
        /// <param name="rootCategoryId">Root category ID to start from (null for full tree)</param>
        /// <param name="maxDepth">Maximum depth to retrieve</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="menuOnly">Whether to include only menu categories</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <param name="sortBy">Sort field</param>
        /// <param name="sortDirection">Sort direction</param>
        /// <returns>Category tree</returns>
        Task<CustomResponseDto<List<CategoryDto>>> ProcessGetCategoryTreeAsync(
            Guid? rootCategoryId = null,
            int? maxDepth = null,
            bool activeOnly = true,
            bool menuOnly = false,
            bool includeDescriptions = true,
            string? languageCode = null,
            CategorySortField sortBy = CategorySortField.SortOrder,
            SortDirection sortDirection = SortDirection.Ascending);

        #endregion

        #region Business Logic Helpers

        /// <summary>
        /// Validates category hierarchy to prevent circular references
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="parentCategoryId">Parent category ID</param>
        /// <returns>Validation result</returns>
        Task<bool> ValidateCategoryHierarchyAsync(Guid categoryId, Guid? parentCategoryId);

        /// <summary>
        /// Generates unique slug for category
        /// </summary>
        /// <param name="name">Category name</param>
        /// <param name="existingSlug">Existing slug (for updates)</param>
        /// <returns>Unique slug</returns>
        Task<string> GenerateUniqueSlugAsync(string name, string? existingSlug = null);

        /// <summary>
        /// Calculates category level in hierarchy
        /// </summary>
        /// <param name="parentCategoryId">Parent category ID</param>
        /// <returns>Category level</returns>
        Task<int> CalculateCategoryLevelAsync(Guid? parentCategoryId);

        /// <summary>
        /// Builds category breadcrumb path
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="languageCode">Language code</param>
        /// <returns>Breadcrumb list</returns>
        Task<List<CategoryBreadcrumbDto>> BuildCategoryBreadcrumbsAsync(Guid categoryId, string? languageCode = null);

        #endregion
    }
}
