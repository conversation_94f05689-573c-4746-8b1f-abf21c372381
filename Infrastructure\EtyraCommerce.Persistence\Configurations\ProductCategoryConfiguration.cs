using EtyraCommerce.Domain.Entities.Product;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ProductCategory entity (Many-to-Many relation)
    /// </summary>
    public class ProductCategoryConfiguration : BaseEntityConfiguration<ProductCategory>
    {
        public override void Configure(EntityTypeBuilder<ProductCategory> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("product_categories", "etyra_core");

            #region Properties

            // Product ID
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            // Category ID
            builder.Property(x => x.CategoryId)
                .HasColumnName("category_id")
                .IsRequired();

            // Is Primary
            builder.Property(x => x.IsPrimary)
                .HasColumnName("is_primary")
                .HasDefaultValue(false)
                .IsRequired();

            // Sort Order
            builder.Property(x => x.SortOrder)
                .HasColumnName("sort_order")
                .HasDefaultValue(0)
                .IsRequired();

            #endregion

            #region Navigation Properties

            // Product (Many-to-One)
            builder.HasOne(x => x.Product)
                .WithMany(x => x.ProductCategories)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Category (Many-to-One)
            builder.HasOne(x => x.Category)
                .WithMany(x => x.ProductCategories)
                .HasForeignKey(x => x.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Composite Primary Key Alternative (Product + Category)
            builder.HasIndex(x => new { x.ProductId, x.CategoryId })
                .IsUnique()
                .HasDatabaseName("ix_product_categories_product_category_unique");

            // Performance Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_product_categories_product_id");

            builder.HasIndex(x => x.CategoryId)
                .HasDatabaseName("ix_product_categories_category_id");

            builder.HasIndex(x => x.IsPrimary)
                .HasDatabaseName("ix_product_categories_is_primary");

            builder.HasIndex(x => x.SortOrder)
                .HasDatabaseName("ix_product_categories_sort_order");

            // Composite Indexes
            builder.HasIndex(x => new { x.ProductId, x.IsPrimary })
                .HasDatabaseName("ix_product_categories_product_primary");

            builder.HasIndex(x => new { x.CategoryId, x.SortOrder })
                .HasDatabaseName("ix_product_categories_category_sort");

            builder.HasIndex(x => new { x.CategoryId, x.IsPrimary })
                .HasDatabaseName("ix_product_categories_category_primary");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_ProductCategories_SortOrder_NonNegative",
                "sort_order >= 0");

            #endregion
        }

        protected override string GetTableName() => "product_categories";
    }
}
