using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Commands
{
    /// <summary>
    /// Command to cancel an order
    /// </summary>
    public class CancelOrderCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Order ID to cancel
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// Reason for cancellation
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// User ID who is cancelling the order
        /// </summary>
        public Guid? CancelledBy { get; set; }
    }
}
