using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Domain.Enums.Shipping;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Infrastructure.Data.SeedData
{
    /// <summary>
    /// Seed data for European shipping system
    /// Focused on Romania and European markets
    /// </summary>
    public static class ShippingSeedData
    {
        /// <summary>
        /// Seeds European countries with focus on Romania and EU markets
        /// </summary>
        public static void SeedCountries(ModelBuilder modelBuilder)
        {
            var countries = new List<Country>
            {
                // Primary Market - Romania
                new Country("Romania", "RO", "ROU", "RON", true, "+40")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                    IsShippingEnabled = true,
                    DisplayOrder = 1,
                    CreatedAt = DateTime.UtcNow
                },

                // Major EU Markets
                new Country("Germany", "DE", "DEU", "EUR", true, "+49")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111112"),
                    IsShippingEnabled = true,
                    DisplayOrder = 2,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("France", "FR", "FRA", "EUR", true, "+33")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111113"),
                    IsShippingEnabled = true,
                    DisplayOrder = 3,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Italy", "IT", "ITA", "EUR", true, "+39")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111114"),
                    IsShippingEnabled = true,
                    DisplayOrder = 4,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Spain", "ES", "ESP", "EUR", true, "+34")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111115"),
                    IsShippingEnabled = true,
                    DisplayOrder = 5,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Netherlands", "NL", "NLD", "EUR", true, "+31")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111116"),
                    IsShippingEnabled = true,
                    DisplayOrder = 6,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Belgium", "BE", "BEL", "EUR", true, "+32")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111117"),
                    IsShippingEnabled = true,
                    DisplayOrder = 7,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Austria", "AT", "AUT", "EUR", true, "+43")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111118"),
                    IsShippingEnabled = true,
                    DisplayOrder = 8,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Poland", "PL", "POL", "PLN", true, "+48")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111119"),
                    IsShippingEnabled = true,
                    DisplayOrder = 9,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Czech Republic", "CZ", "CZE", "CZK", true, "+420")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111A"),
                    IsShippingEnabled = true,
                    DisplayOrder = 10,
                    CreatedAt = DateTime.UtcNow
                },

                // Additional EU Markets
                new Country("Hungary", "HU", "HUN", "HUF", true, "+36")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111B"),
                    IsShippingEnabled = true,
                    DisplayOrder = 11,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Slovakia", "SK", "SVK", "EUR", true, "+421")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111C"),
                    IsShippingEnabled = true,
                    DisplayOrder = 12,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Slovenia", "SI", "SVN", "EUR", true, "+386")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111D"),
                    IsShippingEnabled = true,
                    DisplayOrder = 13,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Croatia", "HR", "HRV", "EUR", true, "+385")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111E"),
                    IsShippingEnabled = true,
                    DisplayOrder = 14,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Bulgaria", "BG", "BGR", "BGN", true, "+359")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-11111111111F"),
                    IsShippingEnabled = true,
                    DisplayOrder = 15,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Greece", "GR", "GRC", "EUR", true, "+30")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111120"),
                    IsShippingEnabled = true,
                    DisplayOrder = 16,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Portugal", "PT", "PRT", "EUR", true, "+351")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111121"),
                    IsShippingEnabled = true,
                    DisplayOrder = 17,
                    CreatedAt = DateTime.UtcNow
                },

                // Nordic Countries (EU Members)
                new Country("Sweden", "SE", "SWE", "SEK", true, "+46")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111122"),
                    IsShippingEnabled = true,
                    DisplayOrder = 18,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Denmark", "DK", "DNK", "DKK", true, "+45")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111123"),
                    IsShippingEnabled = true,
                    DisplayOrder = 19,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Finland", "FI", "FIN", "EUR", true, "+358")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111124"),
                    IsShippingEnabled = true,
                    DisplayOrder = 20,
                    CreatedAt = DateTime.UtcNow
                },

                // Non-EU but important
                new Country("United Kingdom", "GB", "GBR", "GBP", false, "+44")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111125"),
                    IsShippingEnabled = true,
                    DisplayOrder = 21,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Switzerland", "CH", "CHE", "CHF", false, "+41")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111126"),
                    IsShippingEnabled = true,
                    DisplayOrder = 22,
                    CreatedAt = DateTime.UtcNow
                },
                new Country("Norway", "NO", "NOR", "NOK", false, "+47")
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111127"),
                    IsShippingEnabled = true,
                    DisplayOrder = 23,
                    CreatedAt = DateTime.UtcNow
                }
            };

            modelBuilder.Entity<Country>().HasData(countries);
        }

        /// <summary>
        /// Seeds European shipping zones
        /// </summary>
        public static void SeedShippingZones(ModelBuilder modelBuilder)
        {
            var zones = new List<ShippingZone>
            {
                new ShippingZone("Domestic Romania", "Domestic shipping within Romania", ZoneType.Domestic)
                {
                    Id = Guid.Parse("*************-2222-2222-************"),
                    IsActive = true,
                    DisplayOrder = 1,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZone("EU Zone 1", "Close EU neighbors and major markets", ZoneType.EuZone1)
                {
                    Id = Guid.Parse("*************-2222-2222-************"),
                    IsActive = true,
                    DisplayOrder = 2,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZone("EU Zone 2", "Farther EU countries", ZoneType.EuZone2)
                {
                    Id = Guid.Parse("*************-2222-2222-************"),
                    IsActive = true,
                    DisplayOrder = 3,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZone("International", "Non-EU countries", ZoneType.International)
                {
                    Id = Guid.Parse("*************-2222-2222-************"),
                    IsActive = true,
                    DisplayOrder = 4,
                    CreatedAt = DateTime.UtcNow
                }
            };

            modelBuilder.Entity<ShippingZone>().HasData(zones);
        }

        /// <summary>
        /// Seeds shipping zone country mappings
        /// </summary>
        public static void SeedShippingZoneCountries(ModelBuilder modelBuilder)
        {
            var zoneCountries = new List<ShippingZoneCountry>
            {
                // Domestic Romania
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111111"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },

                // EU Zone 1 - Close neighbors
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111112"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111118"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-11111111111B"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },

                // EU Zone 2 - Farther countries  
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111115"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111122"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                },

                // International
                new ShippingZoneCountry(Guid.Parse("*************-2222-2222-************"), Guid.Parse("11111111-1111-1111-1111-111111111125"))
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    CreatedAt = DateTime.UtcNow
                }
            };

            modelBuilder.Entity<ShippingZoneCountry>().HasData(zoneCountries);
        }

        /// <summary>
        /// Seeds European shipping methods
        /// </summary>
        public static void SeedShippingMethods(ModelBuilder modelBuilder)
        {
            var shippingMethods = new List<ShippingMethod>
            {
                // Fan Courier (Romania)
                new ShippingMethod("Fan Courier", "Standard", "Standard delivery service", "Reliable domestic delivery", ShippingMethodType.Standard,
                    Guid.Parse("*************-2222-2222-************"), 1, 3, 15.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = false,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingMethod("Fan Courier", "Express", "Express delivery service", "Fast domestic delivery", ShippingMethodType.Express,
                    Guid.Parse("*************-2222-2222-************"), 1, 1, 25.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = true,
                    CreatedAt = DateTime.UtcNow
                },

                // DHL (EU Zone 1)
                new ShippingMethod("DHL", "Express", "DHL Express International", "Fast international delivery", ShippingMethodType.Express,
                    Guid.Parse("*************-2222-2222-************"), 2, 4, 45.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = false,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingMethod("DHL", "Standard", "DHL Standard International", "Standard international delivery", ShippingMethodType.Standard,
                    Guid.Parse("*************-2222-2222-************"), 3, 7, 30.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = false,
                    CreatedAt = DateTime.UtcNow
                },

                // UPS (EU Zone 2)
                new ShippingMethod("UPS", "Express", "UPS Express Saver", "Express delivery to farther EU", ShippingMethodType.Express,
                    Guid.Parse("*************-2222-2222-************"), 3, 5, 55.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = false,
                    CreatedAt = DateTime.UtcNow
                },
                new ShippingMethod("UPS", "Standard", "UPS Standard", "Standard delivery to farther EU", ShippingMethodType.Standard,
                    Guid.Parse("*************-2222-2222-************"), 5, 10, 40.00m)
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    IsActive = true,
                    HasTracking = true,
                    HasInsurance = true,
                    SupportsCashOnDelivery = false,
                    CreatedAt = DateTime.UtcNow
                }
            };

            modelBuilder.Entity<ShippingMethod>().HasData(shippingMethods);
        }
    }
}
