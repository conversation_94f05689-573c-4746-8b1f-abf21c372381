using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Domain.Entities.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Commands
{
    /// <summary>
    /// Command for updating an existing product
    /// </summary>
    public class UpdateProductCommand : IRequest<CustomResponseDto<ProductDto>>
    {
        /// <summary>
        /// Product ID to update
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Product name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product rating (1-5 stars)
        /// </summary>
        public int? Stars { get; set; }

        /// <summary>
        /// AI Platform ID for integration
        /// </summary>
        public int? AiPlatformId { get; set; }

        #region Product Codes & Identifiers

        /// <summary>
        /// European Article Number (EAN-13)
        /// </summary>
        public string? EAN { get; set; }

        /// <summary>
        /// Manufacturer Part Number
        /// </summary>
        public string? MPN { get; set; }

        /// <summary>
        /// Product barcode
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// Product brand
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Universal Product Code
        /// </summary>
        public string? UPC { get; set; }

        #endregion

        #region Pricing & Currency

        /// <summary>
        /// Base price amount
        /// </summary>
        public decimal BasePrice { get; set; }

        /// <summary>
        /// Base price currency code
        /// </summary>
        public string BasePriceCurrency { get; set; } = "USD";

        /// <summary>
        /// Cost price amount
        /// </summary>
        public decimal? Cost { get; set; }

        /// <summary>
        /// Cost price currency code
        /// </summary>
        public string? CostCurrency { get; set; }

        /// <summary>
        /// Sale price amount
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// Sale price currency code
        /// </summary>
        public string? SalePriceCurrency { get; set; }

        /// <summary>
        /// Default currency for this product
        /// </summary>
        public string DefaultCurrency { get; set; } = "USD";

        #endregion

        #region Dimensions

        /// <summary>
        /// Product dimensions
        /// </summary>
        public UpdateProductDimensionsDto? Dimensions { get; set; }

        #endregion

        #region Images

        /// <summary>
        /// Main product image URL
        /// </summary>
        public string? MainImage { get; set; }

        /// <summary>
        /// Thumbnail image URL
        /// </summary>
        public string? ThumbnailImage { get; set; }

        #endregion

        #region Inventory & Status

        /// <summary>
        /// Total stock quantity across all warehouses
        /// </summary>
        public int TotalStockQuantity { get; set; } = 0;

        /// <summary>
        /// Minimum stock alert level
        /// </summary>
        public int MinStockAlert { get; set; } = 0;

        /// <summary>
        /// Product status
        /// </summary>
        public ProductStatus Status { get; set; } = ProductStatus.Draft;

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Type { get; set; } = ProductType.Simple;

        /// <summary>
        /// Whether to manage stock for this product
        /// </summary>
        public bool ManageStock { get; set; } = true;

        /// <summary>
        /// Whether product is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// Whether product is digital
        /// </summary>
        public bool IsDigital { get; set; } = false;

        /// <summary>
        /// Whether product requires shipping
        /// </summary>
        public bool RequiresShipping { get; set; } = true;

        /// <summary>
        /// Whether product is taxable
        /// </summary>
        public bool IsTaxable { get; set; } = true;

        #endregion

        #region SEO & Marketing

        /// <summary>
        /// Product slug for SEO
        /// </summary>
        public string? Slug { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        #endregion

        #region Dates

        /// <summary>
        /// Product availability date
        /// </summary>
        public DateTime? AvailableStartDate { get; set; }

        /// <summary>
        /// Product availability end date
        /// </summary>
        public DateTime? AvailableEndDate { get; set; }

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Product discontinue date
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        #endregion

        #region Related Data

        /// <summary>
        /// Primary category ID
        /// </summary>
        public Guid? PrimaryCategoryId { get; set; }

        /// <summary>
        /// Additional category IDs
        /// </summary>
        public List<Guid> CategoryIds { get; set; } = new();

        /// <summary>
        /// Product descriptions in different languages
        /// </summary>
        public List<UpdateProductDescriptionDto> Descriptions { get; set; } = new();

        /// <summary>
        /// Product images
        /// </summary>
        public List<UpdateProductImageDto> Images { get; set; } = new();

        /// <summary>
        /// Product attributes
        /// </summary>
        public List<UpdateProductAttributeDto> Attributes { get; set; } = new();

        #endregion
    }
}
