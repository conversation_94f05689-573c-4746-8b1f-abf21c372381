namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Base DTO class with audit information
    /// Extends BaseDto with user tracking capabilities
    /// </summary>
    public abstract class AuditableDto : BaseDto
    {
        /// <summary>
        /// ID of the user who created this entity
        /// </summary>
        public Guid? CreatedBy { get; set; }

        /// <summary>
        /// ID of the user who last updated this entity
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        /// <summary>
        /// ID of the user who deleted this entity (for soft deletes)
        /// </summary>
        public Guid? DeletedBy { get; set; }

        /// <summary>
        /// Name of the user who created this entity
        /// </summary>
        public string CreatedByName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the user who last updated this entity
        /// </summary>
        public string UpdatedByName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the user who deleted this entity
        /// </summary>
        public string DeletedByName { get; set; } = string.Empty;

        /// <summary>
        /// Email of the user who created this entity
        /// </summary>
        public string CreatedByEmail { get; set; } = string.Empty;

        /// <summary>
        /// Email of the user who last updated this entity
        /// </summary>
        public string UpdatedByEmail { get; set; } = string.Empty;

        /// <summary>
        /// Date and time when the entity was soft deleted
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Indicates if the entity is soft deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Gets the display name for the creator
        /// </summary>
        public string CreatorDisplayName =>
            !string.IsNullOrEmpty(CreatedByName) ? CreatedByName :
            !string.IsNullOrEmpty(CreatedByEmail) ? CreatedByEmail :
            CreatedBy?.ToString() ?? "System";

        /// <summary>
        /// Gets the display name for the last updater
        /// </summary>
        public string UpdaterDisplayName =>
            !string.IsNullOrEmpty(UpdatedByName) ? UpdatedByName :
            !string.IsNullOrEmpty(UpdatedByEmail) ? UpdatedByEmail :
            UpdatedBy?.ToString() ?? "System";

        /// <summary>
        /// Gets the display name for the deleter
        /// </summary>
        public string DeleterDisplayName =>
            !string.IsNullOrEmpty(DeletedByName) ? DeletedByName :
            DeletedBy?.ToString() ?? "System";

        /// <summary>
        /// Checks if the entity was created by a specific user
        /// </summary>
        public bool IsCreatedBy(Guid userId) => CreatedBy == userId;

        /// <summary>
        /// Checks if the entity was last updated by a specific user
        /// </summary>
        public bool IsUpdatedBy(Guid userId) => UpdatedBy == userId;

        /// <summary>
        /// Checks if the entity was deleted by a specific user
        /// </summary>
        public bool IsDeletedBy(Guid userId) => DeletedBy == userId;

        /// <summary>
        /// Checks if the entity was created by the same user who last updated it
        /// </summary>
        public bool IsSelfUpdated => CreatedBy.HasValue && UpdatedBy.HasValue && CreatedBy == UpdatedBy;

        /// <summary>
        /// Gets the time since deletion
        /// </summary>
        public TimeSpan? TimeSinceDeletion => DeletedAt.HasValue ? DateTime.UtcNow - DeletedAt.Value : null;

        /// <summary>
        /// Checks if the entity was deleted today
        /// </summary>
        public bool IsDeletedToday => DeletedAt?.Date == DateTime.UtcNow.Date;

        /// <summary>
        /// Checks if the entity was recently deleted (within last hour)
        /// </summary>
        public bool IsRecentlyDeleted => TimeSinceDeletion?.TotalHours <= 1;

        /// <summary>
        /// Gets formatted deletion date
        /// </summary>
        public string FormattedDeletedAt => DeletedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Not deleted";

        /// <summary>
        /// Gets relative time since deletion
        /// </summary>
        public string RelativeDeletedAt => DeletedAt.HasValue ? GetRelativeTime(DeletedAt.Value) : "Not deleted";

        /// <summary>
        /// Gets audit trail summary
        /// </summary>
        public string GetAuditTrail()
        {
            var trail = new List<string>
            {
                $"Created by {CreatorDisplayName} {RelativeCreatedAt}"
            };

            if (UpdatedAt.HasValue)
            {
                trail.Add($"Updated by {UpdaterDisplayName} {RelativeUpdatedAt}");
            }

            if (IsDeleted && DeletedAt.HasValue)
            {
                trail.Add($"Deleted by {DeleterDisplayName} {RelativeDeletedAt}");
            }

            return string.Join(" | ", trail);
        }

        /// <summary>
        /// Gets audit information for a specific action
        /// </summary>
        public AuditInfo GetAuditInfo(AuditAction action)
        {
            return action switch
            {
                AuditAction.Created => new AuditInfo
                {
                    Action = AuditAction.Created,
                    UserId = CreatedBy,
                    UserName = CreatedByName,
                    UserEmail = CreatedByEmail,
                    Timestamp = CreatedAt,
                    DisplayName = CreatorDisplayName
                },
                AuditAction.Updated => new AuditInfo
                {
                    Action = AuditAction.Updated,
                    UserId = UpdatedBy,
                    UserName = UpdatedByName,
                    UserEmail = UpdatedByEmail,
                    Timestamp = UpdatedAt,
                    DisplayName = UpdaterDisplayName
                },
                AuditAction.Deleted => new AuditInfo
                {
                    Action = AuditAction.Deleted,
                    UserId = DeletedBy,
                    UserName = DeletedByName,
                    UserEmail = string.Empty,
                    Timestamp = DeletedAt,
                    DisplayName = DeleterDisplayName
                },
                _ => throw new ArgumentException($"Unknown audit action: {action}")
            };
        }

        /// <summary>
        /// Gets all audit actions for this entity
        /// </summary>
        public List<AuditInfo> GetAllAuditInfo()
        {
            var audits = new List<AuditInfo> { GetAuditInfo(AuditAction.Created) };

            if (UpdatedAt.HasValue)
                audits.Add(GetAuditInfo(AuditAction.Updated));

            if (IsDeleted && DeletedAt.HasValue)
                audits.Add(GetAuditInfo(AuditAction.Deleted));

            return audits.OrderBy(a => a.Timestamp).ToList();
        }

        /// <summary>
        /// Validates the auditable DTO
        /// </summary>
        public override ValidationResult Validate()
        {
            var result = base.Validate();

            if (UpdatedAt.HasValue && !UpdatedBy.HasValue)
                result.Errors.Add(nameof(UpdatedBy), new List<string> { "UpdatedBy is required when UpdatedAt is set" });

            if (IsDeleted && !DeletedAt.HasValue)
                result.Errors.Add(nameof(DeletedAt), new List<string> { "DeletedAt is required when IsDeleted is true" });

            if (IsDeleted && !DeletedBy.HasValue)
                result.Errors.Add(nameof(DeletedBy), new List<string> { "DeletedBy is required when IsDeleted is true" });

            if (DeletedAt.HasValue && DeletedAt.Value < CreatedAt)
                result.Errors.Add(nameof(DeletedAt), new List<string> { "DeletedAt cannot be before CreatedAt" });

            result.IsValid = !result.Errors.Any();
            return result;
        }

        /// <summary>
        /// Gets a summary including audit information
        /// </summary>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            var auditSummary = $"Created by {CreatorDisplayName}";

            if (UpdatedAt.HasValue)
                auditSummary += $", Updated by {UpdaterDisplayName}";

            if (IsDeleted)
                auditSummary += $", Deleted by {DeleterDisplayName}";

            return $"{baseSummary} | {auditSummary}";
        }

        /// <summary>
        /// Converts a DateTime to relative time string (helper method)
        /// </summary>
        private static string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;

            if (timeSpan.TotalDays >= 365)
                return $"{(int)(timeSpan.TotalDays / 365)} year{((int)(timeSpan.TotalDays / 365) == 1 ? "" : "s")} ago";

            if (timeSpan.TotalDays >= 30)
                return $"{(int)(timeSpan.TotalDays / 30)} month{((int)(timeSpan.TotalDays / 30) == 1 ? "" : "s")} ago";

            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays == 1 ? "" : "s")} ago";

            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours == 1 ? "" : "s")} ago";

            if (timeSpan.TotalMinutes >= 1)
                return $"{(int)timeSpan.TotalMinutes} minute{((int)timeSpan.TotalMinutes == 1 ? "" : "s")} ago";

            return "Just now";
        }
    }

    /// <summary>
    /// Audit action enumeration
    /// </summary>
    public enum AuditAction
    {
        Created,
        Updated,
        Deleted
    }

    /// <summary>
    /// Audit information for a specific action
    /// </summary>
    public class AuditInfo
    {
        public AuditAction Action { get; set; }
        public Guid? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public DateTime? Timestamp { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string RelativeTime => Timestamp.HasValue ? GetRelativeTime(Timestamp.Value) : "Unknown";

        private static string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;

            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays == 1 ? "" : "s")} ago";

            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours == 1 ? "" : "s")} ago";

            if (timeSpan.TotalMinutes >= 1)
                return $"{(int)timeSpan.TotalMinutes} minute{((int)timeSpan.TotalMinutes == 1 ? "" : "s")} ago";

            return "Just now";
        }
    }
}
