using EtyraCommerce.Application.Interfaces.Repositories.Shipping;
using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Shipping
{
    /// <summary>
    /// Repository implementation for country management
    /// </summary>
    public class CountryRepository : ICountryRepository
    {
        private readonly EtyraCommerceDbContext _context;

        /// <summary>
        /// Constructor
        /// </summary>
        public CountryRepository(EtyraCommerceDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// Gets all countries as queryable
        /// </summary>
        public IQueryable<Country> GetAllCountries()
        {
            return _context.Countries.AsQueryable();
        }

        /// <summary>
        /// Gets a country by ID
        /// </summary>
        public async Task<Country?> GetCountryByIdAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .Include(c => c.Regions)
                .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// Gets a country by code
        /// </summary>
        public async Task<Country?> GetCountryByCodeAsync(string code, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .FirstOrDefaultAsync(c => c.Code == code && !c.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// Gets a country by ISO code
        /// </summary>
        public async Task<Country?> GetCountryByIsoCodeAsync(string isoCode, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .FirstOrDefaultAsync(c => c.IsoCode == isoCode && !c.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// Checks if a country exists by code
        /// </summary>
        public async Task<bool> CountryExistsByCodeAsync(string code, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .AnyAsync(c => c.Code == code && !c.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// Checks if a country exists by ISO code
        /// </summary>
        public async Task<bool> CountryExistsByIsoCodeAsync(string isoCode, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .AnyAsync(c => c.IsoCode == isoCode && !c.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// Gets countries by EU membership status
        /// </summary>
        public async Task<List<Country>> GetCountriesByEuMembershipAsync(bool isEuMember, CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .Where(c => c.IsEuMember == isEuMember && !c.IsDeleted)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets countries with shipping enabled
        /// </summary>
        public async Task<List<Country>> GetShippingEnabledCountriesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .Where(c => c.IsShippingEnabled && !c.IsDeleted)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets countries with region count
        /// </summary>
        public async Task<List<(Country Country, int RegionCount)>> GetCountriesWithRegionCountAsync(CancellationToken cancellationToken = default)
        {
            return await _context.Countries
                .Where(c => !c.IsDeleted)
                .Select(c => new { Country = c, RegionCount = c.Regions.Count(r => !r.IsDeleted) })
                .OrderBy(x => x.Country.DisplayOrder)
                .ThenBy(x => x.Country.Name)
                .Select(x => new ValueTuple<Country, int>(x.Country, x.RegionCount))
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Adds a new country
        /// </summary>
        public async Task AddCountryAsync(Country country, CancellationToken cancellationToken = default)
        {
            await _context.Countries.AddAsync(country, cancellationToken);
        }

        /// <summary>
        /// Updates an existing country
        /// </summary>
        public async Task UpdateCountryAsync(Country country, CancellationToken cancellationToken = default)
        {
            _context.Countries.Update(country);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Deletes a country (soft delete)
        /// </summary>
        public async Task DeleteCountryAsync(Country country, CancellationToken cancellationToken = default)
        {
            country.MarkAsDeleted();
            _context.Countries.Update(country);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Saves changes to the database
        /// </summary>
        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
