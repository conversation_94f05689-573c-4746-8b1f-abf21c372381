using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for changing user password
    /// </summary>
    public class ChangePasswordCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// User ID whose password is being changed
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Current password for verification
        /// </summary>
        public string CurrentPassword { get; set; } = string.Empty;

        /// <summary>
        /// New password
        /// </summary>
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// New password confirmation
        /// </summary>
        public string ConfirmNewPassword { get; set; } = string.Empty;

        /// <summary>
        /// IP address of the password change request
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the browser
        /// </summary>
        public string? UserAgent { get; set; }

        public ChangePasswordCommand() { }

        public ChangePasswordCommand(Guid userId, ChangePasswordDto changePasswordDto)
        {
            UserId = userId;
            CurrentPassword = changePasswordDto.CurrentPassword;
            NewPassword = changePasswordDto.NewPassword;
            ConfirmNewPassword = changePasswordDto.ConfirmNewPassword;
        }
    }
}
