using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.User
{
    /// <summary>
    /// User address entity for managing multiple addresses per user
    /// </summary>
    public class UserAddress : BaseEntity
    {
        /// <summary>
        /// Reference to the user who owns this address
        /// </summary>
        public Guid UserId { get; set; }
        public User User { get; set; } = null!;

        /// <summary>
        /// Type of address (Billing, Shipping, Both)
        /// </summary>
        public AddressType Type { get; set; } = AddressType.Both;

        /// <summary>
        /// Indicates if this is the default address for the user
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Address information using Address Value Object
        /// </summary>
        public Address Address { get; set; } = null!;

        /// <summary>
        /// First name of the person at this address (can be different from user)
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the person at this address (can be different from user)
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number for this address (Value Object - Optional)
        /// </summary>
        public PhoneNumber? PhoneNumber { get; set; }

        /// <summary>
        /// User-friendly label for this address (e.g., "Home", "Office", "Mom's House")
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// Company name for corporate addresses
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// Tax number for corporate addresses
        /// </summary>
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Tax office for corporate addresses
        /// </summary>
        public string? TaxOffice { get; set; }

        /// <summary>
        /// Company title/legal name for corporate addresses
        /// </summary>
        public string? CompanyTitle { get; set; }

        /// <summary>
        /// Indicates if this is a corporate/company address
        /// </summary>
        public bool IsCompanyAddress { get; set; } = false;

        /// <summary>
        /// Additional delivery instructions
        /// </summary>
        public string? DeliveryInstructions { get; set; }

        /// <summary>
        /// Indicates if this address is active and can be used
        /// </summary>
        public new bool IsActive { get; set; } = true;

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public UserAddress() { }

        /// <summary>
        /// Constructor for creating a new user address
        /// </summary>
        public UserAddress(Guid userId, Address address, string firstName, string lastName,
            AddressType type = AddressType.Both, string? label = null, bool isDefault = false,
            string? companyName = null, string? taxNumber = null, string? taxOffice = null,
            string? companyTitle = null, bool isCompanyAddress = false)
        {
            UserId = userId;
            Address = address ?? throw new ArgumentNullException(nameof(address));
            FirstName = firstName?.Trim() ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName?.Trim() ?? throw new ArgumentNullException(nameof(lastName));
            Type = type;
            Label = string.IsNullOrWhiteSpace(label) ? null : label.Trim();
            IsDefault = isDefault;
            IsActive = true;

            // Company information
            CompanyName = string.IsNullOrWhiteSpace(companyName) ? null : companyName.Trim();
            TaxNumber = string.IsNullOrWhiteSpace(taxNumber) ? null : taxNumber.Trim();
            TaxOffice = string.IsNullOrWhiteSpace(taxOffice) ? null : taxOffice.Trim();
            CompanyTitle = string.IsNullOrWhiteSpace(companyTitle) ? null : companyTitle.Trim();
            IsCompanyAddress = isCompanyAddress;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Gets the full name of the person at this address
        /// </summary>
        public string GetFullName() => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Updates the address information
        /// </summary>
        public void UpdateAddress(Address newAddress, string firstName, string lastName, string? label = null)
        {
            if (newAddress == null)
                throw new ArgumentNullException(nameof(newAddress));
            if (string.IsNullOrWhiteSpace(firstName))
                throw new ArgumentException("First name cannot be empty", nameof(firstName));
            if (string.IsNullOrWhiteSpace(lastName))
                throw new ArgumentException("Last name cannot be empty", nameof(lastName));

            Address = newAddress;
            FirstName = firstName.Trim();
            LastName = lastName.Trim();
            Label = string.IsNullOrWhiteSpace(label) ? null : label.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets this address as default (should be called through service to ensure only one default)
        /// </summary>
        public void SetAsDefault()
        {
            IsDefault = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes default status from this address
        /// </summary>
        public void RemoveDefaultStatus()
        {
            IsDefault = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Activates this address
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates this address
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            IsDefault = false; // Cannot be default if inactive
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates the phone number
        /// </summary>
        public void UpdatePhoneNumber(PhoneNumber? phoneNumber)
        {
            PhoneNumber = phoneNumber;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates delivery instructions
        /// </summary>
        public void UpdateDeliveryInstructions(string? instructions)
        {
            DeliveryInstructions = string.IsNullOrWhiteSpace(instructions) ? null : instructions.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Checks if this address can be used for billing
        /// </summary>
        public bool CanBeUsedForBilling() => IsActive && (Type == AddressType.Billing || Type == AddressType.Both);

        /// <summary>
        /// Checks if this address can be used for shipping
        /// </summary>
        public bool CanBeUsedForShipping() => IsActive && (Type == AddressType.Shipping || Type == AddressType.Both);

        #endregion

        #region Display Methods

        /// <summary>
        /// Gets a display-friendly address summary
        /// </summary>
        public string GetAddressSummary()
        {
            var summary = Address.GetSingleLineAddress();
            if (!string.IsNullOrEmpty(Label))
                summary = $"{Label}: {summary}";
            return summary;
        }

        /// <summary>
        /// Gets the address type as a display string
        /// </summary>
        public string GetTypeDisplay()
        {
            return Type switch
            {
                AddressType.Billing => "Billing Only",
                AddressType.Shipping => "Shipping Only",
                AddressType.Both => "Billing & Shipping",
                _ => "Unknown"
            };
        }

        #endregion
    }

    /// <summary>
    /// Enumeration for address types
    /// </summary>
    public enum AddressType
    {
        /// <summary>
        /// Address can only be used for billing
        /// </summary>
        Billing = 1,

        /// <summary>
        /// Address can only be used for shipping
        /// </summary>
        Shipping = 2,

        /// <summary>
        /// Address can be used for both billing and shipping
        /// </summary>
        Both = 3
    }
}
