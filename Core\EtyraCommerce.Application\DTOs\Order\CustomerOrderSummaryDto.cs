namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Customer order summary data transfer object
    /// </summary>
    public class CustomerOrderSummaryDto
    {
        /// <summary>
        /// Customer ID
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// Customer email
        /// </summary>
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Total number of orders
        /// </summary>
        public int OrderCount { get; set; }

        /// <summary>
        /// Total order value
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// Average order value
        /// </summary>
        public decimal AverageOrderValue { get; set; }

        /// <summary>
        /// Last order date
        /// </summary>
        public DateTime? LastOrderDate { get; set; }
    }
}
