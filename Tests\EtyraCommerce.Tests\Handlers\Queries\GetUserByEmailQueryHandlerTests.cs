using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.Services.User.Handlers.Queries;
using EtyraCommerce.Application.Services.User.Queries;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Queries
{
    public class GetUserByEmailQueryHandlerTests
    {
        private readonly Mock<IUserProcessService> _mockUserProcessService;
        private readonly Mock<ILogger<GetUserByEmailQueryHandler>> _mockLogger;
        private readonly GetUserByEmailQueryHandler _handler;

        public GetUserByEmailQueryHandlerTests()
        {
            _mockUserProcessService = new Mock<IUserProcessService>();
            _mockLogger = new Mock<ILogger<GetUserByEmailQueryHandler>>();
            _handler = new GetUserByEmailQueryHandler(_mockUserProcessService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidEmail_ReturnsUserDto()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = email,
                Username = "testuser",
                FirstName = "Test",
                LastName = "User",
                IsActive = true,
                IsEmailConfirmed = true
            };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "User found"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(expectedUserDto.Id);
            result.Data.Email.Should().Be(expectedUserDto.Email);
            result.Data.Username.Should().Be(expectedUserDto.Username);
            result.Data.FirstName.Should().Be(expectedUserDto.FirstName);
            result.Data.LastName.Should().Be(expectedUserDto.LastName);

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_UserNotFound_ReturnsNullData()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                null,
                "User not found"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeNull();
            result.Message.Should().Be("User not found");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyEmail_ReturnsBadRequest()
        {
            // Arrange
            var query = new GetUserByEmailQuery { Email = "" };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email is required");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_WhitespaceEmail_ReturnsBadRequest()
        {
            // Arrange
            var query = new GetUserByEmailQuery { Email = "   " };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email is required");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_NullEmail_ReturnsBadRequest()
        {
            // Arrange
            var query = new GetUserByEmailQuery { Email = null! };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email is required");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred while retrieving user by email");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public async Task Handle_VariousValidEmails_CallsProcessServiceWithCorrectParameters(string email)
        {
            // Arrange
            var query = new GetUserByEmailQuery { Email = email };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = email,
                Username = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "User found"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Email.Should().Be(email);

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceReturnsFailure_ReturnsFailureResponse()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            var expectedResponse = CustomResponseDto<UserDto?>.BadRequest("Invalid email format");

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Invalid email format");

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ValidEmailCaseInsensitive_CallsProcessServiceWithOriginalCase()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = email.ToLowerInvariant(),
                Username = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "User found"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            // Verify that the original case is passed to the process service
            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmailWithSpecialCharacters_CallsProcessService()
        {
            // Arrange
            var email = "<EMAIL>";
            var query = new GetUserByEmailQuery { Email = email };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = email,
                Username = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "User found"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessGetUserByEmailAsync(email))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Email.Should().Be(email);

            _mockUserProcessService.Verify(
                x => x.ProcessGetUserByEmailAsync(email),
                Times.Once
            );
        }
    }
}
