using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Inventory.Handlers.Queries;
using EtyraCommerce.Application.Services.Inventory.Queries;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Queries
{
    public class GetInventoryByIdQueryHandlerTests
    {
        private readonly Mock<IInventoryProcessService> _mockInventoryProcessService;
        private readonly Mock<ILogger<GetInventoryByIdQueryHandler>> _mockLogger;
        private readonly GetInventoryByIdQueryHandler _handler;

        public GetInventoryByIdQueryHandlerTests()
        {
            _mockInventoryProcessService = new Mock<IInventoryProcessService>();
            _mockLogger = new Mock<ILogger<GetInventoryByIdQueryHandler>>();
            _handler = new GetInventoryByIdQueryHandler(_mockInventoryProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidQuery_ReturnsSuccessWithInventoryDto()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            var expectedDto = new InventoryDto
            {
                Id = inventoryId,
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                TotalQuantity = 100,
                AvailableQuantity = 85,
                ReservedQuantity = 10,
                AllocatedQuantity = 5,
                ReorderPoint = 20,
                MaxStockLevel = 500,
                LocationCode = "A1-B2-C3",
                Notes = "Test inventory",
                ProductName = "Test Product",
                ProductModel = "TP-001",
                WarehouseName = "Main Warehouse",
                WarehouseCode = "WH-001",
                IsLowStock = false,
                IsOutOfStock = false,
                IsOverstocked = false,
                FreeQuantity = 85,

                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var expectedResponse = CustomResponseDto<InventoryDto>.Success(expectedDto);

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(inventoryId);
            result.Data.TotalQuantity.Should().Be(100);
            result.Data.AvailableQuantity.Should().Be(85);
            result.Data.ReservedQuantity.Should().Be(10);
            result.Data.AllocatedQuantity.Should().Be(5);
            result.Data.ReorderPoint.Should().Be(20);
            result.Data.MaxStockLevel.Should().Be(500);
            result.Data.LocationCode.Should().Be("A1-B2-C3");
            result.Data.ProductName.Should().Be("Test Product");
            result.Data.WarehouseName.Should().Be("Main Warehouse");
            result.Data.IsLowStock.Should().BeFalse();
            result.Data.IsOutOfStock.Should().BeFalse();


            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_InvalidId_ReturnsBadRequest()
        {
            // Arrange
            var query = new GetInventoryByIdQuery { Id = Guid.Empty };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Inventory ID is required");

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(It.IsAny<Guid>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_InventoryNotFound_ReturnsNotFound()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            var notFoundResponse = CustomResponseDto<InventoryDto>.NotFound("Inventory not found");

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ReturnsAsync(notFoundResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Be("Inventory not found");

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_LowStockInventory_ReturnsCorrectFlags()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            var expectedDto = new InventoryDto
            {
                Id = inventoryId,
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                TotalQuantity = 15,
                AvailableQuantity = 15,
                ReservedQuantity = 0,
                AllocatedQuantity = 0,
                ReorderPoint = 20, // Higher than total quantity
                MaxStockLevel = 500,
                IsLowStock = true, // Should be true
                IsOutOfStock = false,
                IsOverstocked = false,

            };

            var expectedResponse = CustomResponseDto<InventoryDto>.Success(expectedDto);

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.IsLowStock.Should().BeTrue();
            result.Data.IsOutOfStock.Should().BeFalse();
            result.Data.IsOverstocked.Should().BeFalse();

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_OutOfStockInventory_ReturnsCorrectFlags()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            var expectedDto = new InventoryDto
            {
                Id = inventoryId,
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                TotalQuantity = 0,
                AvailableQuantity = 0,
                ReservedQuantity = 0,
                AllocatedQuantity = 0,
                ReorderPoint = 20,
                MaxStockLevel = 500,
                IsLowStock = false,
                IsOutOfStock = true, // Should be true
                IsOverstocked = false,

            };

            var expectedResponse = CustomResponseDto<InventoryDto>.Success(expectedDto);

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.IsLowStock.Should().BeFalse();
            result.Data.IsOutOfStock.Should().BeTrue();
            result.Data.IsOverstocked.Should().BeFalse();

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_OverstockedInventory_ReturnsCorrectFlags()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            var expectedDto = new InventoryDto
            {
                Id = inventoryId,
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                TotalQuantity = 600,
                AvailableQuantity = 600,
                ReservedQuantity = 0,
                AllocatedQuantity = 0,
                ReorderPoint = 20,
                MaxStockLevel = 500, // Lower than total quantity
                IsLowStock = false,
                IsOutOfStock = false,
                IsOverstocked = true, // Should be true

            };

            var expectedResponse = CustomResponseDto<InventoryDto>.Success(expectedDto);

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.IsLowStock.Should().BeFalse();
            result.Data.IsOutOfStock.Should().BeFalse();
            result.Data.IsOverstocked.Should().BeTrue();

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var inventoryId = Guid.NewGuid();
            var query = new GetInventoryByIdQuery { Id = inventoryId };

            _mockInventoryProcessService
                .Setup(x => x.ProcessGetInventoryByIdAsync(inventoryId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("An error occurred while retrieving inventory");

            _mockInventoryProcessService.Verify(
                x => x.ProcessGetInventoryByIdAsync(inventoryId),
                Times.Once);
        }

        #endregion
    }
}
