using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Domain.Entities.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Queries
{
    /// <summary>
    /// Query to get all warehouses with filtering and pagination
    /// </summary>
    public class GetAllWarehousesQuery : IRequest<CustomResponseDto<PagedResult<WarehouseDto>>>
    {
        public string? SearchTerm { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsMain { get; set; }
        public WarehouseType? Type { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "Name";
        public string? SortDirection { get; set; } = "asc";
    }
}
