using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// District entity for European shipping system
    /// Represents districts/sectors/neighborhoods within cities (optional for large cities)
    /// </summary>
    public class District : BaseEntity
    {
        #region Properties

        /// <summary>
        /// District name (e.g., "Sector 1", "Altstadt", "1st Arrondissement", "Centru")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Postal code specific to this district
        /// </summary>
        [MaxLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Type of district (Sector, District, Neighborhood, Arrondissement, etc.)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display order for district selection lists
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Indicates if shipping is available to this district
        /// </summary>
        public bool IsShippingEnabled { get; set; } = true;

        /// <summary>
        /// Indicates if this is a central/downtown district
        /// </summary>
        public bool IsCentralDistrict { get; set; } = false;

        /// <summary>
        /// Latitude coordinate for mapping and distance calculations
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Longitude coordinate for mapping and distance calculations
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Additional shipping notes for this district
        /// </summary>
        [MaxLength(500)]
        public string? ShippingNotes { get; set; }

        #endregion

        #region Foreign Keys

        /// <summary>
        /// Reference to the parent city
        /// </summary>
        public Guid CityId { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Parent city
        /// </summary>
        public virtual City City { get; set; } = null!;

        /// <summary>
        /// Shipping zone locations that reference this district
        /// </summary>
        public virtual ICollection<ShippingZoneLocation> ShippingZoneLocations { get; set; } = new List<ShippingZoneLocation>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected District() { }

        /// <summary>
        /// Constructor for creating a new district
        /// </summary>
        public District(Guid cityId, string name, string type, string? postalCode = null)
        {
            CityId = cityId;
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            PostalCode = string.IsNullOrWhiteSpace(postalCode) ? null : postalCode.Trim();
            IsShippingEnabled = true;
            IsCentralDistrict = false;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Enables shipping to this district
        /// </summary>
        public void EnableShipping(string? notes = null)
        {
            IsShippingEnabled = true;
            if (!string.IsNullOrWhiteSpace(notes))
                ShippingNotes = notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Disables shipping to this district
        /// </summary>
        public void DisableShipping(string? reason = null)
        {
            IsShippingEnabled = false;
            if (!string.IsNullOrWhiteSpace(reason))
                ShippingNotes = reason.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates district information
        /// </summary>
        public void UpdateInfo(string name, string type, string? postalCode = null, string? shippingNotes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            PostalCode = string.IsNullOrWhiteSpace(postalCode) ? null : postalCode.Trim();
            ShippingNotes = string.IsNullOrWhiteSpace(shippingNotes) ? null : shippingNotes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets district as central district
        /// </summary>
        public void SetAsCentralDistrict(bool isCentral = true)
        {
            IsCentralDistrict = isCentral;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets geographic coordinates
        /// </summary>
        public void SetCoordinates(decimal latitude, decimal longitude)
        {
            Latitude = latitude;
            Longitude = longitude;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for district lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets full district display name with postal code
        /// </summary>
        public string GetDisplayName() => 
            string.IsNullOrWhiteSpace(PostalCode) ? Name : $"{Name} ({PostalCode})";

        /// <summary>
        /// Gets full district display name with type
        /// </summary>
        public string GetDisplayNameWithType() => $"{Name} - {GetTypeDisplayText()}";

        /// <summary>
        /// Checks if district supports shipping
        /// </summary>
        public bool CanShipTo() => IsShippingEnabled && !IsDeleted;

        /// <summary>
        /// Gets district type display text
        /// </summary>
        public string GetTypeDisplayText()
        {
            return Type.ToLowerInvariant() switch
            {
                "sector" => "Sector",
                "district" => "District",
                "neighborhood" => "Neighborhood",
                "arrondissement" => "Arrondissement",
                "quarter" => "Quarter",
                "ward" => "Ward",
                "borough" => "Borough",
                _ => Type
            };
        }

        /// <summary>
        /// Checks if district has geographic coordinates
        /// </summary>
        public bool HasCoordinates() => Latitude.HasValue && Longitude.HasValue;

        /// <summary>
        /// Gets formatted coordinates string
        /// </summary>
        public string GetCoordinatesString() => 
            HasCoordinates() ? $"{Latitude:F6}, {Longitude:F6}" : "No coordinates";

        #endregion

        #region Validation

        /// <summary>
        /// Validates district data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Type) &&
                   CityId != Guid.Empty;
        }

        /// <summary>
        /// Validates postal code format (basic validation)
        /// </summary>
        public bool IsPostalCodeValid()
        {
            if (string.IsNullOrWhiteSpace(PostalCode))
                return true; // Optional field

            // Basic validation - can be extended for country-specific formats
            return PostalCode.Length >= 3 && PostalCode.Length <= 20;
        }

        #endregion
    }
}
