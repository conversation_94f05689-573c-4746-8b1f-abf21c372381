using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Linq.Expressions;
using System.Text.Json;

namespace EtyraCommerce.Persistence.Configurations.ValueObjects
{
    /// <summary>
    /// Static class containing value object conversions for EF Core
    /// </summary>
    public static class ValueObjectConversions
    {
        /// <summary>
        /// Email value object converter
        /// </summary>
        public static ValueConverter<Email, string> EmailConverter =>
            new(
                email => email.Value,
                value => new Email(value)
            );

        /// <summary>
        /// PhoneNumber value object converter
        /// </summary>
        public static ValueConverter<PhoneNumber, string> PhoneNumberConverter =>
            new(
                phone => phone.Value,
                value => new PhoneNumber(value)
            );

        /// <summary>
        /// Currency value object converter
        /// </summary>
        public static ValueConverter<Currency, string> CurrencyConverter =>
            new(
                currency => currency.Code,
                code => Currency.FromCode(code)
            );

        /// <summary>
        /// Money value object converter (stores as JSON)
        /// Alternative approach - stores Money as JSON string
        /// </summary>
        public static ValueConverter<Money, string> MoneyJsonConverter =>
            new(
                money => SerializeMoneyToJson(money),
                json => CreateMoneyFromJson(json)
            );

        /// <summary>
        /// Configures Email property with proper conversion and constraints
        /// </summary>
        public static void ConfigureEmail<T>(PropertyBuilder<Email> propertyBuilder, string columnName = "email")
        {
            propertyBuilder
                .HasConversion(EmailConverter)
                .HasColumnName(columnName)
                .HasMaxLength(254) // RFC 5321 limit
                .IsRequired();
        }

        /// <summary>
        /// Configures PhoneNumber property with proper conversion and constraints
        /// </summary>
        public static void ConfigurePhoneNumber<T>(PropertyBuilder<PhoneNumber> propertyBuilder, string columnName = "phone_number")
        {
            propertyBuilder
                .HasConversion(PhoneNumberConverter)
                .HasColumnName(columnName)
                .HasMaxLength(20) // International format max length
                .IsRequired();
        }

        /// <summary>
        /// Configures Currency property with proper conversion and constraints
        /// </summary>
        public static void ConfigureCurrency<T>(PropertyBuilder<Currency> propertyBuilder, string columnName = "currency")
        {
            propertyBuilder
                .HasConversion(CurrencyConverter)
                .HasColumnName(columnName)
                .HasMaxLength(3) // ISO 4217 currency code length
                .IsRequired();
        }

        /// <summary>
        /// Configures Money property as JSON (single column approach)
        /// </summary>
        public static void ConfigureMoneyAsJson<T>(PropertyBuilder<Money> propertyBuilder, string columnName = "money")
        {
            propertyBuilder
                .HasConversion(MoneyJsonConverter)
                .HasColumnName(columnName)
                .HasColumnType("jsonb") // PostgreSQL JSONB type
                .IsRequired();
        }

        /// <summary>
        /// Configures Money property as separate Amount and Currency columns
        /// This is the preferred approach for better querying and indexing
        /// </summary>
        public static void ConfigureMoneyAsSeparateColumns<T>(
            EntityTypeBuilder<T> builder,
            string moneyPropertyName,
            string amountColumnName = "amount",
            string currencyColumnName = "currency") where T : class
        {
            builder.OwnsOne(typeof(Money), moneyPropertyName, money =>
            {
                money.Property("Amount")
                    .HasColumnName(amountColumnName)
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                money.Property("Currency")
                    .HasConversion(CurrencyConverter)
                    .HasColumnName(currencyColumnName)
                    .HasMaxLength(3)
                    .IsRequired();
            });
        }

        /// <summary>
        /// Helper method to serialize Money to JSON
        /// </summary>
        private static string SerializeMoneyToJson(Money money)
        {
            return JsonSerializer.Serialize(new { Amount = money.Amount, Currency = money.Currency.Code });
        }

        /// <summary>
        /// Helper method to serialize ProductDimensions to JSON
        /// </summary>
        private static string SerializeProductDimensionsToJson(ProductDimensions dimensions)
        {
            return JsonSerializer.Serialize(new
            {
                Length = dimensions.Length,
                Width = dimensions.Width,
                Height = dimensions.Height,
                Weight = dimensions.Weight,
                Unit = dimensions.Unit,
                WeightUnit = dimensions.WeightUnit
            });
        }

        /// <summary>
        /// Helper method to create Money from JSON
        /// </summary>
        private static Money CreateMoneyFromJson(string json)
        {
            try
            {
                var data = JsonSerializer.Deserialize<JsonElement>(json);
                var amount = data.GetProperty("Amount").GetDecimal();
                var currencyCode = data.GetProperty("Currency").GetString();

                return new Money(amount, Currency.FromCode(currencyCode!));
            }
            catch
            {
                // Return zero money with USD as fallback
                return Money.Zero(Currency.USD);
            }
        }

        /// <summary>
        /// Configures nullable Email property
        /// </summary>
        public static void ConfigureNullableEmail<T>(PropertyBuilder<Email?> propertyBuilder, string columnName = "email")
        {
            propertyBuilder
                .HasConversion(
                    email => email != null ? email.Value : null,
                    value => value != null ? new Email(value) : null)
                .HasColumnName(columnName)
                .HasMaxLength(254)
                .IsRequired(false);
        }

        /// <summary>
        /// Configures nullable PhoneNumber property
        /// </summary>
        public static void ConfigureNullablePhoneNumber<T>(PropertyBuilder<PhoneNumber?> propertyBuilder, string columnName = "phone_number")
        {
            propertyBuilder
                .HasConversion(
                    phone => phone != null ? phone.Value : null,
                    value => value != null ? new PhoneNumber(value) : null)
                .HasColumnName(columnName)
                .HasMaxLength(20)
                .IsRequired(false);
        }

        /// <summary>
        /// Configures nullable Money property as JSON
        /// </summary>
        public static void ConfigureNullableMoneyAsJson<T>(PropertyBuilder<Money?> propertyBuilder, string columnName = "money")
        {
            propertyBuilder
                .HasConversion(
                    money => money != null ? SerializeMoneyToJson(money) : null,
                    json => json != null ? CreateMoneyFromJson(json) : null)
                .HasColumnName(columnName)
                .HasColumnType("jsonb")
                .IsRequired(false);
        }

        /// <summary>
        /// Configures required Money property as separate columns (Amount + Currency)
        /// </summary>
        public static void ConfigureMoneyRequired<TEntity>(EntityTypeBuilder<TEntity> builder,
            Expression<Func<TEntity, Money>> propertyExpression, string columnPrefix = "money")
            where TEntity : class
        {
            builder.OwnsOne(propertyExpression, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName($"{columnPrefix}_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                money.Property(m => m.Currency)
                    .HasConversion(
                        c => c.Code,
                        s => Currency.FromCode(s))
                    .HasColumnName($"{columnPrefix}_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });
        }

        /// <summary>
        /// Configures nullable Money property as separate columns (Amount + Currency)
        /// </summary>
        public static void ConfigureNullableMoney<TEntity>(EntityTypeBuilder<TEntity> builder,
            Expression<Func<TEntity, Money?>> propertyExpression, string columnPrefix = "money")
            where TEntity : class
        {
            builder.OwnsOne(propertyExpression, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName($"{columnPrefix}_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                money.Property(m => m.Currency)
                    .HasConversion(
                        c => c.Code,
                        s => Currency.FromCode(s))
                    .HasColumnName($"{columnPrefix}_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });
        }

        /// <summary>
        /// ProductDimensions value object converter (stores as JSON)
        /// </summary>
        public static ValueConverter<ProductDimensions, string> ProductDimensionsConverter =>
            new(
                dimensions => SerializeProductDimensionsToJson(dimensions),
                json => CreateProductDimensionsFromJson(json)
            );

        /// <summary>
        /// Configures ProductDimensions property as JSON
        /// </summary>
        public static void ConfigureProductDimensionsAsJson<T>(PropertyBuilder<ProductDimensions> propertyBuilder, string columnName = "dimensions")
        {
            propertyBuilder
                .HasConversion(ProductDimensionsConverter)
                .HasColumnName(columnName)
                .HasColumnType("jsonb")
                .IsRequired();
        }

        /// <summary>
        /// Configures nullable ProductDimensions property as JSON
        /// </summary>
        public static void ConfigureNullableProductDimensionsAsJson<T>(PropertyBuilder<ProductDimensions?> propertyBuilder, string columnName = "dimensions")
        {
            propertyBuilder
                .HasConversion(
                    dimensions => dimensions != null ? SerializeProductDimensionsToJson(dimensions) : null,
                    json => json != null ? CreateProductDimensionsFromJson(json) : null)
                .HasColumnName(columnName)
                .HasColumnType("jsonb")
                .IsRequired(false);
        }

        /// <summary>
        /// Helper method to create ProductDimensions from JSON
        /// </summary>
        private static ProductDimensions CreateProductDimensionsFromJson(string json)
        {
            try
            {
                var data = JsonSerializer.Deserialize<JsonElement>(json);
                var length = data.GetProperty("Length").GetDecimal();
                var width = data.GetProperty("Width").GetDecimal();
                var height = data.GetProperty("Height").GetDecimal();
                var weight = data.GetProperty("Weight").GetDecimal();
                var unit = data.GetProperty("Unit").GetString() ?? "cm";
                var weightUnit = data.GetProperty("WeightUnit").GetString() ?? "kg";

                return new ProductDimensions(length, width, height, weight, unit, weightUnit);
            }
            catch
            {
                // Return default dimensions as fallback
                return new ProductDimensions(0, 0, 0, 0, "cm", "kg");
            }
        }


        #region Address Value Object

        /// <summary>
        /// Configures Address Value Object as JSON column
        /// </summary>
        public static void ConfigureAddress<TEntity>(PropertyBuilder<Address?> propertyBuilder, string columnName)
            where TEntity : class
        {
            propertyBuilder
                .HasColumnName(columnName)
                .HasColumnType("jsonb") // PostgreSQL JSONB for better performance
                .HasConversion(
                    v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => string.IsNullOrEmpty(v) ? null : JsonSerializer.Deserialize<Address>(v, (JsonSerializerOptions?)null));
        }

        /// <summary>
        /// Configures required Address Value Object as JSON column
        /// </summary>
        public static void ConfigureAddressRequired<TEntity>(PropertyBuilder<Address> propertyBuilder, string columnName)
            where TEntity : class
        {
            propertyBuilder
                .HasColumnName(columnName)
                .HasColumnType("jsonb") // PostgreSQL JSONB for better performance
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Address>(v, (JsonSerializerOptions?)null));
        }

        /// <summary>
        /// Configures Address Value Object as separate columns
        /// </summary>
        public static void ConfigureAddressSeparate<TEntity>(EntityTypeBuilder<TEntity> builder,
            Expression<Func<TEntity, Address?>> propertyExpression, string columnPrefix = "address")
            where TEntity : class
        {
            builder.OwnsOne(propertyExpression, address =>
            {
                address.Property(a => a.Street)
                    .HasColumnName($"{columnPrefix}_street")
                    .HasMaxLength(200)
                    .IsRequired();

                address.Property(a => a.AddressLine2)
                    .HasColumnName($"{columnPrefix}_line2")
                    .HasMaxLength(200)
                    .IsRequired(false);

                address.Property(a => a.City)
                    .HasColumnName($"{columnPrefix}_city")
                    .HasMaxLength(100)
                    .IsRequired();

                address.Property(a => a.State)
                    .HasColumnName($"{columnPrefix}_state")
                    .HasMaxLength(100)
                    .IsRequired();

                address.Property(a => a.PostalCode)
                    .HasColumnName($"{columnPrefix}_postal_code")
                    .HasMaxLength(20)
                    .IsRequired();

                address.Property(a => a.Country)
                    .HasColumnName($"{columnPrefix}_country")
                    .HasMaxLength(100)
                    .IsRequired();
            });
        }

        /// <summary>
        /// Configures required Address Value Object as separate columns
        /// </summary>
        public static void ConfigureAddressSeparateRequired<TEntity>(EntityTypeBuilder<TEntity> builder,
            Expression<Func<TEntity, Address>> propertyExpression, string columnPrefix = "address")
            where TEntity : class
        {
            builder.OwnsOne(propertyExpression, address =>
            {
                address.Property(a => a.Street)
                    .HasColumnName($"{columnPrefix}_street")
                    .HasMaxLength(200)
                    .IsRequired();

                address.Property(a => a.AddressLine2)
                    .HasColumnName($"{columnPrefix}_line2")
                    .HasMaxLength(200)
                    .IsRequired(false);

                address.Property(a => a.City)
                    .HasColumnName($"{columnPrefix}_city")
                    .HasMaxLength(100)
                    .IsRequired();

                address.Property(a => a.State)
                    .HasColumnName($"{columnPrefix}_state")
                    .HasMaxLength(100)
                    .IsRequired();

                address.Property(a => a.PostalCode)
                    .HasColumnName($"{columnPrefix}_postal_code")
                    .HasMaxLength(20)
                    .IsRequired();

                address.Property(a => a.Country)
                    .HasColumnName($"{columnPrefix}_country")
                    .HasMaxLength(100)
                    .IsRequired();
            });
        }

        #endregion
    }
}
