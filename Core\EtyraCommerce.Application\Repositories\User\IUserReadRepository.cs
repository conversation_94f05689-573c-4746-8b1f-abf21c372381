namespace EtyraCommerce.Application.Repositories.User
{
    /// <summary>
    /// User-specific read repository interface
    /// Extends generic read repository with user-specific query methods
    /// </summary>
    public interface IUserReadRepository : IReadRepository<Domain.Entities.User.User>
    {
        /// <summary>
        /// Gets user by email address
        /// </summary>
        /// <param name="email">Email address</param>
        /// <param name="tracking">Whether to track the entity</param>
        /// <returns>User entity or null</returns>
        Task<Domain.Entities.User.User?> GetByEmailAsync(string email, bool tracking = true);

        /// <summary>
        /// Gets user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="tracking">Whether to track the entity</param>
        /// <returns>User entity or null</returns>
        Task<Domain.Entities.User.User?> GetByUsernameAsync(string username, bool tracking = true);

        /// <summary>
        /// Gets user by email or username
        /// </summary>
        /// <param name="emailOrUsername">Email or username</param>
        /// <param name="tracking">Whether to track the entity</param>
        /// <returns>User entity or null</returns>
        Task<Domain.Entities.User.User?> GetByEmailOrUsernameAsync(string emailOrUsername, bool tracking = true);

        /// <summary>
        /// Checks if email exists
        /// </summary>
        /// <param name="email">Email address</param>
        /// <param name="excludeUserId">User ID to exclude from check</param>
        /// <returns>True if email exists</returns>
        Task<bool> EmailExistsAsync(string email, Guid? excludeUserId = null);

        /// <summary>
        /// Checks if username exists
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="excludeUserId">User ID to exclude from check</param>
        /// <returns>True if username exists</returns>
        Task<bool> UsernameExistsAsync(string username, Guid? excludeUserId = null);

        /// <summary>
        /// Gets users by email confirmation status
        /// </summary>
        /// <param name="isConfirmed">Email confirmation status</param>
        /// <param name="tracking">Whether to track entities</param>
        /// <returns>List of users</returns>
        Task<IEnumerable<Domain.Entities.User.User>> GetByEmailConfirmationStatusAsync(bool isConfirmed, bool tracking = false);

        /// <summary>
        /// Gets locked users
        /// </summary>
        /// <param name="tracking">Whether to track entities</param>
        /// <returns>List of locked users</returns>
        Task<IEnumerable<Domain.Entities.User.User>> GetLockedUsersAsync(bool tracking = false);

        /// <summary>
        /// Gets users registered within date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="tracking">Whether to track entities</param>
        /// <returns>List of users</returns>
        Task<IEnumerable<Domain.Entities.User.User>> GetUsersRegisteredBetweenAsync(DateTime fromDate, DateTime toDate, bool tracking = false);

        /// <summary>
        /// Gets users who logged in within date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="tracking">Whether to track entities</param>
        /// <returns>List of users</returns>
        Task<IEnumerable<Domain.Entities.User.User>> GetUsersLoggedInBetweenAsync(DateTime fromDate, DateTime toDate, bool tracking = false);

        /// <summary>
        /// Gets user statistics
        /// </summary>
        /// <returns>User statistics object</returns>
        Task<UserStatistics> GetUserStatisticsAsync();
    }

    /// <summary>
    /// User statistics data structure
    /// </summary>
    public class UserStatistics
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int EmailConfirmedUsers { get; set; }
        public int EmailUnconfirmedUsers { get; set; }
        public int LockedUsers { get; set; }
        public int UsersRegisteredToday { get; set; }
        public int UsersRegisteredThisWeek { get; set; }
        public int UsersRegisteredThisMonth { get; set; }
        public int UsersLoggedInToday { get; set; }
        public int UsersLoggedInThisWeek { get; set; }
        public int UsersLoggedInThisMonth { get; set; }
    }
}
