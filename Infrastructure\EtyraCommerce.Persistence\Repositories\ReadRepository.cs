﻿using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace EtyraCommerce.Persistence.Repositories
{
    /// <summary>
    /// Generic read repository implementation
    /// </summary>
    /// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
    public class ReadRepository<T> : IReadRepository<T> where T : BaseEntity
    {

        protected readonly EtyraCommerceDbContext _context;

        public ReadRepository(EtyraCommerceDbContext context)
        {
            _context = context;
        }
        public DbSet<T> Table => _context.Set<T>();



        public async Task<IQueryable<T>> GetAllAsync(bool tracking = true)
        {
            var query = Table.AsQueryable();
            if (!tracking)
                query = query.AsNoTracking();

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Gets entities matching the expression as IQueryable
        /// </summary>
        public async Task<IQueryable<T>> GetWhereAsync(Expression<Func<T, bool>> expression, bool tracking = true)
        {
            var query = Table.AsQueryable();
            if (!tracking)
                query = query.AsNoTracking();

            query = query.Where(expression);
            return await Task.FromResult(query);
        }

        /// <summary>
        /// Gets first entity matching the expression or null
        /// </summary>
        public async Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> expression, bool tracking = true)
        {
            var query = Table.AsQueryable();
            if (!tracking)
                query = query.AsNoTracking();

            return await query.FirstOrDefaultAsync(expression);
        }

        /// <summary>
        /// Gets entity by ID or null if not found
        /// </summary>
        public async Task<T?> GetByIdAsync(Guid id, bool tracking = true)
        {
            if (!tracking)
            {
                return await Table.AsNoTracking().FirstOrDefaultAsync(e => e.Id == id);
            }

            return await Table.FindAsync(id);
        }

        /// <summary>
        /// Checks if any entity matches the expression
        /// </summary>
        public async Task<bool> AnyAsync(Expression<Func<T, bool>> expression, bool tracking = false)
        {
            return await Table.AnyAsync(expression);
        }

        /// <summary>
        /// Gets count of entities matching the expression
        /// </summary>
        public async Task<int> CountAsync(Expression<Func<T, bool>>? expression = null)
        {
            if (expression == null)
                return await Table.CountAsync();

            return await Table.CountAsync(expression);
        }

        /// <summary>
        /// Gets paginated results
        /// </summary>
        public async Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize, bool tracking = true)
        {
            var query = Table.AsQueryable();
            if (!tracking)
                query = query.AsNoTracking();

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return PagedResult<T>.Create(items, totalCount, pageNumber, pageSize);
        }

        /// <summary>
        /// Gets paginated results matching the expression
        /// </summary>
        public async Task<PagedResult<T>> GetPagedWhereAsync(Expression<Func<T, bool>> expression, int pageNumber, int pageSize, bool tracking = true)
        {
            var query = Table.AsQueryable();
            if (!tracking)
                query = query.AsNoTracking();

            query = query.Where(expression);

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return PagedResult<T>.Create(items, totalCount, pageNumber, pageSize);
        }
    }
}