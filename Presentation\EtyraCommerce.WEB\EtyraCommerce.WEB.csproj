<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\EtyraCommerce.Application\EtyraCommerce.Application.csproj" />
    <ProjectReference Include="..\..\Core\EtyraCommerce.Domain\EtyraCommerce.Domain.csproj" />
    <ProjectReference Include="..\..\Infrastructure\EtyraCommerce.Infrastructure\EtyraCommerce.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Infrastructure\EtyraCommerce.Persistence\EtyraCommerce.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
  </ItemGroup>

</Project>
