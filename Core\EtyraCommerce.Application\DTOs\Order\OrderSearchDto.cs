using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Order search and filter DTO
    /// </summary>
    public class OrderSearchDto : SearchRequest
    {
        /// <summary>
        /// Search term (order number, customer name, email)
        /// </summary>
        [MaxLength(200)]
        public new string? SearchTerm { get; set; }

        /// <summary>
        /// Customer ID filter
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Order status filter
        /// </summary>
        public OrderStatus? Status { get; set; }

        /// <summary>
        /// Payment status filter
        /// </summary>
        public PaymentStatus? PaymentStatus { get; set; }

        /// <summary>
        /// Shipping status filter
        /// </summary>
        public ShippingStatus? ShippingStatus { get; set; }

        /// <summary>
        /// Minimum order total
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? MinTotal { get; set; }

        /// <summary>
        /// Maximum order total
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? MaxTotal { get; set; }

        /// <summary>
        /// Currency filter
        /// </summary>
        [MaxLength(3)]
        public string? Currency { get; set; }

        /// <summary>
        /// Start date for order creation filter
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for order creation filter
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Payment method filter
        /// </summary>
        [MaxLength(100)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Shipping method filter
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Include order items in results
        /// </summary>
        public bool IncludeItems { get; set; } = false;

        /// <summary>
        /// Include customer information in results
        /// </summary>
        public bool IncludeCustomer { get; set; } = false;

        /// <summary>
        /// Sort field
        /// </summary>
        public new OrderSortField SortBy { get; set; } = OrderSortField.CreatedAt;

        /// <summary>
        /// Sort direction
        /// </summary>
        public new SortDirection SortDirection { get; set; } = SortDirection.Descending;

        /// <summary>
        /// Page number (1-based) - alias for PageNumber
        /// </summary>
        public int Page
        {
            get => PageNumber;
            set => PageNumber = value;
        }
    }

    /// <summary>
    /// Order sort fields
    /// </summary>
    public enum OrderSortField
    {
        /// <summary>
        /// Sort by order number
        /// </summary>
        OrderNumber = 0,

        /// <summary>
        /// Sort by customer name
        /// </summary>
        CustomerName = 1,

        /// <summary>
        /// Sort by order total
        /// </summary>
        Total = 2,

        /// <summary>
        /// Sort by order status
        /// </summary>
        Status = 3,

        /// <summary>
        /// Sort by payment status
        /// </summary>
        PaymentStatus = 4,

        /// <summary>
        /// Sort by creation date
        /// </summary>
        CreatedAt = 5,

        /// <summary>
        /// Sort by update date
        /// </summary>
        UpdatedAt = 6
    }


}
