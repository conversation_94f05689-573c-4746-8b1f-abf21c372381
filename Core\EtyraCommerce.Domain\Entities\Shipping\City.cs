using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// City entity for European shipping system
    /// Represents cities/municipalities within regions
    /// </summary>
    public class City : BaseEntity
    {
        #region Properties

        /// <summary>
        /// City name (e.g., "Bucharest", "Munich", "Paris")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Primary postal code for this city
        /// </summary>
        [MaxLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Type of city (Municipality, City, Town, Village, etc.)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display order for city selection lists
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Indicates if shipping is available to this city
        /// </summary>
        public bool IsShippingEnabled { get; set; } = true;

        /// <summary>
        /// Indicates if this is a major city (for priority shipping)
        /// </summary>
        public bool IsMajorCity { get; set; } = false;

        /// <summary>
        /// Population of the city (optional, for analytics)
        /// </summary>
        public int? Population { get; set; }

        /// <summary>
        /// Latitude coordinate for mapping and distance calculations
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Longitude coordinate for mapping and distance calculations
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Additional shipping notes for this city
        /// </summary>
        [MaxLength(500)]
        public string? ShippingNotes { get; set; }

        #endregion

        #region Foreign Keys

        /// <summary>
        /// Reference to the parent region
        /// </summary>
        public Guid RegionId { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Parent region
        /// </summary>
        public virtual Region Region { get; set; } = null!;

        /// <summary>
        /// Districts within this city (for large cities)
        /// </summary>
        public virtual ICollection<District> Districts { get; set; } = new List<District>();

        /// <summary>
        /// Shipping zone locations that reference this city
        /// </summary>
        public virtual ICollection<ShippingZoneLocation> ShippingZoneLocations { get; set; } = new List<ShippingZoneLocation>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected City() { }

        /// <summary>
        /// Constructor for creating a new city
        /// </summary>
        public City(Guid regionId, string name, string type, string? postalCode = null)
        {
            RegionId = regionId;
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            PostalCode = string.IsNullOrWhiteSpace(postalCode) ? null : postalCode.Trim();
            IsShippingEnabled = true;
            IsMajorCity = false;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Enables shipping to this city
        /// </summary>
        public void EnableShipping(string? notes = null)
        {
            IsShippingEnabled = true;
            if (!string.IsNullOrWhiteSpace(notes))
                ShippingNotes = notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Disables shipping to this city
        /// </summary>
        public void DisableShipping(string? reason = null)
        {
            IsShippingEnabled = false;
            if (!string.IsNullOrWhiteSpace(reason))
                ShippingNotes = reason.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates city information
        /// </summary>
        public void UpdateInfo(string name, string type, string? postalCode = null, 
            int? population = null, string? shippingNotes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            PostalCode = string.IsNullOrWhiteSpace(postalCode) ? null : postalCode.Trim();
            Population = population;
            ShippingNotes = string.IsNullOrWhiteSpace(shippingNotes) ? null : shippingNotes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets city as major city
        /// </summary>
        public void SetAsMajorCity(bool isMajor = true)
        {
            IsMajorCity = isMajor;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets geographic coordinates
        /// </summary>
        public void SetCoordinates(decimal latitude, decimal longitude)
        {
            Latitude = latitude;
            Longitude = longitude;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for city lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets full city display name with postal code
        /// </summary>
        public string GetDisplayName() => 
            string.IsNullOrWhiteSpace(PostalCode) ? Name : $"{Name} ({PostalCode})";

        /// <summary>
        /// Gets full city display name with type
        /// </summary>
        public string GetDisplayNameWithType() => $"{Name} - {GetTypeDisplayText()}";

        /// <summary>
        /// Checks if city supports shipping
        /// </summary>
        public bool CanShipTo() => IsShippingEnabled && !IsDeleted;

        /// <summary>
        /// Gets city type display text
        /// </summary>
        public string GetTypeDisplayText()
        {
            return Type.ToLowerInvariant() switch
            {
                "municipality" => "Municipality",
                "city" => "City",
                "town" => "Town",
                "village" => "Village",
                "commune" => "Commune",
                "borough" => "Borough",
                _ => Type
            };
        }

        /// <summary>
        /// Checks if city has geographic coordinates
        /// </summary>
        public bool HasCoordinates() => Latitude.HasValue && Longitude.HasValue;

        /// <summary>
        /// Gets formatted coordinates string
        /// </summary>
        public string GetCoordinatesString() => 
            HasCoordinates() ? $"{Latitude:F6}, {Longitude:F6}" : "No coordinates";

        #endregion

        #region Validation

        /// <summary>
        /// Validates city data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Type) &&
                   RegionId != Guid.Empty;
        }

        /// <summary>
        /// Validates postal code format (basic validation)
        /// </summary>
        public bool IsPostalCodeValid()
        {
            if (string.IsNullOrWhiteSpace(PostalCode))
                return true; // Optional field

            // Basic validation - can be extended for country-specific formats
            return PostalCode.Length >= 3 && PostalCode.Length <= 20;
        }

        #endregion
    }
}
