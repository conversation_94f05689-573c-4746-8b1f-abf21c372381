using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class ExchangeRate : BaseEntity
{
    public int FromCurrencyId { get; set; }
    public Currency FromCurrency { get; set; }
    
    public int ToCurrencyId { get; set; }
    public Currency ToCurrency { get; set; }
    
    public decimal Rate { get; set; }
    
    public DateTime Date { get; set; }
    
    [MaxLength(100)]
    public string Source { get; set; } // <PERSON><PERSON> ka<PERSON> (örn. Romanya Merkez Bankası)
} 