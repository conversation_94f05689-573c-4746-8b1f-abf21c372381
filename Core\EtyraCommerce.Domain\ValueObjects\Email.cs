using System.Text.RegularExpressions;

namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Email value object with validation
    /// </summary>
    public class Email : IEquatable<Email>
    {
        private static readonly Regex EmailRegex = new(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public string Value { get; }
        public string Domain => Value.Split('@')[1];
        public string LocalPart => Value.Split('@')[0];

        public Email(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Email cannot be null or empty", nameof(value));

            var trimmedValue = value.Trim().ToLowerInvariant();

            if (!IsValidEmail(trimmedValue))
                throw new ArgumentException($"Invalid email format: {value}", nameof(value));

            if (trimmedValue.Length > 254) // RFC 5321 limit
                throw new ArgumentException("Email address is too long (max 254 characters)", nameof(value));

            Value = trimmedValue;
        }

        /// <summary>
        /// Creates email from string with validation
        /// </summary>
        public static Email Create(string value) => new(value);

        /// <summary>
        /// Tries to create email, returns null if invalid
        /// </summary>
        public static Email? TryCreate(string value)
        {
            try
            {
                return new Email(value);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Validates email format
        /// </summary>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Basic regex validation
                if (!EmailRegex.IsMatch(email))
                    return false;

                // Additional checks
                var parts = email.Split('@');
                if (parts.Length != 2)
                    return false;

                var localPart = parts[0];
                var domain = parts[1];

                // Local part validation
                if (localPart.Length == 0 || localPart.Length > 64)
                    return false;

                if (localPart.StartsWith('.') || localPart.EndsWith('.'))
                    return false;

                if (localPart.Contains(".."))
                    return false;

                // Domain validation
                if (domain.Length == 0 || domain.Length > 253)
                    return false;

                if (domain.StartsWith('.') || domain.EndsWith('.'))
                    return false;

                if (domain.StartsWith('-') || domain.EndsWith('-'))
                    return false;

                if (!domain.Contains('.'))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if email is from a specific domain
        /// </summary>
        public bool IsFromDomain(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain))
                return false;

            return Domain.Equals(domain.Trim().ToLowerInvariant(), StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if email is from any of the specified domains
        /// </summary>
        public bool IsFromDomains(params string[] domains)
        {
            if (domains == null || domains.Length == 0)
                return false;

            return domains.Any(d => IsFromDomain(d));
        }

        /// <summary>
        /// Checks if email is from a common email provider
        /// </summary>
        public bool IsFromCommonProvider()
        {
            var commonProviders = new[]
            {
                "gmail.com", "yahoo.com", "hotmail.com", "outlook.com",
                "icloud.com", "aol.com", "protonmail.com", "yandex.com",
                "mail.ru", "live.com", "msn.com", "yahoo.co.uk",
                "googlemail.com", "me.com", "mac.com"
            };

            return IsFromDomains(commonProviders);
        }

        /// <summary>
        /// Gets masked email for display (e.g., j***@example.com)
        /// </summary>
        public string GetMasked()
        {
            if (LocalPart.Length <= 2)
                return $"{LocalPart[0]}***@{Domain}";

            var visibleChars = Math.Min(2, LocalPart.Length / 2);
            var maskedPart = LocalPart[..visibleChars] + new string('*', LocalPart.Length - visibleChars);
            return $"{maskedPart}@{Domain}";
        }

        /// <summary>
        /// Implicit conversion from string
        /// </summary>
        public static implicit operator string(Email email) => email.Value;

        /// <summary>
        /// Explicit conversion from string
        /// </summary>
        public static explicit operator Email(string value) => new(value);

        #region Equality

        public bool Equals(Email? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Value == other.Value;
        }

        public override bool Equals(object? obj)
        {
            return obj is Email other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Value.GetHashCode();
        }

        public static bool operator ==(Email? left, Email? right)
        {
            if (left is null && right is null) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(Email? left, Email? right) => !(left == right);

        #endregion

        public override string ToString() => Value;
    }
}
