namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// DTO for user login history
    /// </summary>
    public class UserLoginHistoryDto
    {
        /// <summary>
        /// Login history record ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Login timestamp
        /// </summary>
        public DateTime LoginAt { get; set; }

        /// <summary>
        /// IP address of the login
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// Login was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Failure reason if login failed
        /// </summary>
        public string? FailureReason { get; set; }

        /// <summary>
        /// Location based on IP (city, country)
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Device type (Mobile, Desktop, Tablet)
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// Browser name
        /// </summary>
        public string? Browser { get; set; }

        /// <summary>
        /// Operating system
        /// </summary>
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// Session duration in minutes (if available)
        /// </summary>
        public int? SessionDurationMinutes { get; set; }

        /// <summary>
        /// Logout timestamp (if available)
        /// </summary>
        public DateTime? LogoutAt { get; set; }
    }
}
