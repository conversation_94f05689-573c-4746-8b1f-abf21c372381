﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Accounting;

namespace EtyraApp.Domain.Entities.Global_Trade;

public class Import : BaseEntity
{
    public DateTime? EstimatedImportDate { get; set; }
    public DateTime? ImportDate { get; set; }
    public Currency? Currency { get; set; }
    public int? CurrencyId { get; set; } // Foreign Key

    public decimal? FreightCost { get; set; } // navlun
    public decimal? TotalWeight { get; set; }
    public decimal? TotalFreightCost { get; set; } // total navlun
    public decimal? TotalCustomsDuty { get; set; } // navlun
    public decimal? TotalCustomsFees { get; set; } // customs

    public decimal? TotalTVA { get; set; } // customs

    public ICollection<ImportProduct>? Products { get; set; }
    [MaxLength(150)]
    public string? InvoiceNo { get; set; }
    public Supplier? Supplier { get; set; }
    public int? SupplierId { get; set; } // Foreign Key
    public int? ProductQuantity { get; set; }

    public decimal? Total { get; set; }
    public int Status { get; set; }

    [MaxLength(500)]
    public string? Comment { get; set; }

    public TradeShippingType TradeShippingType { get; set; } // Foreign Key
    public int TradeShippingTypeId { get; set; } // Foreign Key

}