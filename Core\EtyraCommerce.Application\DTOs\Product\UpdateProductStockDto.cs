using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for updating product stock
    /// </summary>
    public class UpdateProductStockDto
    {
        /// <summary>
        /// New total stock quantity
        /// </summary>
        [Required(ErrorMessage = "Total stock quantity is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Stock quantity must be non-negative")]
        public int TotalStockQuantity { get; set; }

        /// <summary>
        /// Minimum stock alert level
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Min stock alert must be non-negative")]
        public int? MinStockAlert { get; set; }

        /// <summary>
        /// Whether to manage stock for this product
        /// </summary>
        public bool? ManageStock { get; set; }

        /// <summary>
        /// Reason for stock change
        /// </summary>
        [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
        public string? Reason { get; set; }

        /// <summary>
        /// Stock adjustment type
        /// </summary>
        public StockAdjustmentType AdjustmentType { get; set; } = StockAdjustmentType.Set;
    }

    /// <summary>
    /// Stock adjustment type enumeration
    /// </summary>
    public enum StockAdjustmentType
    {
        /// <summary>
        /// Set stock to exact value
        /// </summary>
        Set = 0,

        /// <summary>
        /// Add to current stock
        /// </summary>
        Add = 1,

        /// <summary>
        /// Subtract from current stock
        /// </summary>
        Subtract = 2
    }
}
