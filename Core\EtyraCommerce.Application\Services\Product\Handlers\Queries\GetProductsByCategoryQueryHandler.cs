using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Queries
{
    /// <summary>
    /// Handler for getting products by category
    /// </summary>
    public class GetProductsByCategoryQueryHandler : IRequestHandler<GetProductsByCategoryQuery, CustomResponseDto<PagedResult<ProductDto>>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<GetProductsByCategoryQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public GetProductsByCategoryQueryHandler(
            IProductProcessService productProcessService,
            ILogger<GetProductsByCategoryQueryHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the get products by category query
        /// </summary>
        /// <param name="request">Get products by category query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paged product DTO response</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> Handle(GetProductsByCategoryQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get products by category query - CategoryId: {CategoryId}, Page: {Page}, PageSize: {PageSize}, IncludeChildCategories: {IncludeChildCategories}",
                    request.CategoryId, request.Page, request.PageSize, request.IncludeChildCategories);

                // Validation
                if (request.CategoryId == Guid.Empty)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Category ID is required");

                if (request.Page < 1)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page size must be between 1 and 100");

                // Validate price range
                if (request.MinPrice.HasValue && request.MaxPrice.HasValue && request.MinPrice > request.MaxPrice)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum price cannot be greater than maximum price");

                // Validate rating range
                if (request.MinRating.HasValue && request.MaxRating.HasValue && request.MinRating > request.MaxRating)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum rating cannot be greater than maximum rating");

                // Validate stock range
                if (request.MinStock.HasValue && request.MaxStock.HasValue && request.MinStock > request.MaxStock)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum stock cannot be greater than maximum stock");

                // Validate sort direction
                if (!string.IsNullOrEmpty(request.SortDirection) &&
                    !request.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
                    !request.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Sort direction must be 'asc' or 'desc'");

                // Create category filter DTO for business logic
                var categoryFilterDto = new ProductCategoryFilterDto
                {
                    CategoryId = request.CategoryId,
                    IncludeChildCategories = request.IncludeChildCategories,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    Status = request.Status,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    IsDigital = request.IsDigital,
                    Brand = request.Brand,
                    MinPrice = request.MinPrice,
                    MaxPrice = request.MaxPrice,
                    MinRating = request.MinRating,
                    MaxRating = request.MaxRating,
                    MinStock = request.MinStock,
                    MaxStock = request.MaxStock,
                    InStock = request.InStock,
                    OnSale = request.OnSale,
                    SearchTerm = request.SearchTerm,
                    Brands = request.Brands,
                    AttributeFilters = request.AttributeFilters,
                    Tags = request.Tags,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    IncludeDescriptions = request.IncludeDescriptions,
                    IncludeImages = request.IncludeImages,
                    IncludeCategories = request.IncludeCategories,
                    IncludeDiscounts = request.IncludeDiscounts,
                    IncludeAttributes = request.IncludeAttributes,
                    IncludeVariants = request.IncludeVariants,
                    IncludeInventory = request.IncludeInventory,
                    LanguageCode = request.LanguageCode,
                    StoreId = request.StoreId
                };

                // Delegate to business logic service
                var result = await _productProcessService.ProcessGetProductsByCategoryAsync(categoryFilterDto);

                _logger.LogInformation("Get products by category query processed successfully - CategoryId: {CategoryId}, Total: {Total}, Page: {Page}",
                    request.CategoryId, result.Data?.TotalCount, request.Page);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get products by category query - CategoryId: {CategoryId}, Page: {Page}",
                    request.CategoryId, request.Page);
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("An error occurred while retrieving products by category");
            }
        }
    }
}
