using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Queries
{
    /// <summary>
    /// Query to get order by order number
    /// </summary>
    public class GetOrderByOrderNumberQuery : IRequest<CustomResponseDto<OrderDto>>
    {
        /// <summary>
        /// Order number to search for
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// Whether to include order items in the response
        /// </summary>
        public bool IncludeItems { get; set; } = true;

        /// <summary>
        /// Whether to include customer information in the response
        /// </summary>
        public bool IncludeCustomer { get; set; } = false;

        /// <summary>
        /// Language code for localized content
        /// </summary>
        public string? LanguageCode { get; set; }
    }
}
