using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Application.Services.Order.Handlers.Commands;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class CancelOrderCommandHandlerTests
    {
        private readonly Mock<IOrderProcessService> _mockOrderProcessService;
        private readonly Mock<ILogger<CancelOrderCommandHandler>> _mockLogger;
        private readonly CancelOrderCommandHandler _handler;

        public CancelOrderCommandHandlerTests()
        {
            _mockOrderProcessService = new Mock<IOrderProcessService>();
            _mockLogger = new Mock<ILogger<CancelOrderCommandHandler>>();
            _handler = new CancelOrderCommandHandler(_mockOrderProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccess()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var reason = "Customer requested cancellation";
            var cancelledBy = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = reason,
                CancelledBy = cancelledBy
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order cancelled successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, reason, cancelledBy))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Message.Should().Be("Order cancelled successfully");

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, reason, cancelledBy),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyOrderId_ReturnsBadRequest()
        {
            // Arrange
            var command = new CancelOrderCommand
            {
                OrderId = Guid.Empty,
                Reason = "Test reason"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Order ID is required");

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<Guid?>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_ValidCommandWithoutReason_CallsProcessServiceWithNullReason()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = null,
                CancelledBy = null
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order cancelled successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, null, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, null, null),
                Times.Once
            );
        }

        [Theory]
        [InlineData("Customer requested cancellation")]
        [InlineData("Payment failed")]
        [InlineData("Product out of stock")]
        [InlineData("Duplicate order")]
        public async Task Handle_VariousReasons_CallsProcessServiceWithCorrectReason(string reason)
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = reason
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order cancelled successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, reason, null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, reason, null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CancelOrderCommand
            {
                OrderId = Guid.NewGuid(),
                Reason = "Test reason"
            };

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<Guid?>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Be("An error occurred while cancelling the order");

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<Guid?>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceReturnsNotFound_ReturnsNotFoundResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = "Test reason"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.NotFound("Order not found");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, "Test reason", null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, "Test reason", null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceReturnsBadRequest_ReturnsBadRequestResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = "Test reason"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.BadRequest("Order cannot be cancelled in its current state");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, "Test reason", null))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Order cannot be cancelled in its current state");

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, "Test reason", null),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ValidCommandWithCancelledBy_CallsProcessServiceWithCancelledBy()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var cancelledBy = Guid.NewGuid();
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
                Reason = "Admin cancellation",
                CancelledBy = cancelledBy
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(200, "Order cancelled successfully");

            _mockOrderProcessService
                .Setup(x => x.ProcessCancelOrderAsync(orderId, "Admin cancellation", cancelledBy))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderProcessService.Verify(
                x => x.ProcessCancelOrderAsync(orderId, "Admin cancellation", cancelledBy),
                Times.Once
            );
        }

        #endregion
    }
}
