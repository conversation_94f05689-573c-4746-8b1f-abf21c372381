using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.Services.Category;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Category management API controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [EnableCors("AllowAll")]
    public class CategoryController : ControllerBase
    {
        private readonly ICategoryService _categoryService;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(ICategoryService categoryService, ILogger<CategoryController> logger)
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        #region Category Management

        /// <summary>
        /// Creates a new category
        /// </summary>
        /// <param name="createCategoryDto">Category creation data</param>
        /// <returns>Created category</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateCategory([FromBody] CreateCategoryDto createCategoryDto)
        {
            _logger.LogInformation("Creating category with name: {CategoryName}", createCategoryDto.Name);

            var result = await _categoryService.CreateCategoryAsync(createCategoryDto);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing category
        /// </summary>
        /// <param name="categoryId">Category ID to update</param>
        /// <param name="updateCategoryDto">Category update data</param>
        /// <returns>Updated category</returns>
        [HttpPut("{categoryId:guid}")]
        [Authorize]
        public async Task<IActionResult> UpdateCategory(Guid categoryId, [FromBody] UpdateCategoryDto updateCategoryDto)
        {
            _logger.LogInformation("Updating category with ID: {CategoryId}", categoryId);

            var result = await _categoryService.UpdateCategoryAsync(categoryId, updateCategoryDto);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a category (soft delete)
        /// </summary>
        /// <param name="categoryId">Category ID to delete</param>
        /// <param name="forceDelete">Whether to force delete (hard delete)</param>
        /// <param name="deleteChildren">Whether to delete child categories as well</param>
        /// <returns>Operation result</returns>
        [HttpDelete("{categoryId:guid}")]
        [Authorize]
        public async Task<IActionResult> DeleteCategory(Guid categoryId, [FromQuery] bool forceDelete = false, [FromQuery] bool deleteChildren = false)
        {
            _logger.LogInformation("Deleting category with ID: {CategoryId}, ForceDelete: {ForceDelete}, DeleteChildren: {DeleteChildren}",
                categoryId, forceDelete, deleteChildren);

            var result = await _categoryService.DeleteCategoryAsync(categoryId, forceDelete, deleteChildren);
            return StatusCode(result.StatusCode, result);
        }

        #endregion

        #region Category Queries

        /// <summary>
        /// Gets a category by ID
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="includeChildren">Whether to include child categories</param>
        /// <param name="includeParent">Whether to include parent category</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Category data</returns>
        [HttpGet("{categoryId:guid}")]
        public async Task<IActionResult> GetCategoryById(
            Guid categoryId,
            [FromQuery] bool includeChildren = false,
            [FromQuery] bool includeParent = false,
            [FromQuery] bool includeDescriptions = true,
            [FromQuery] string? languageCode = null)
        {
            _logger.LogInformation("Getting category by ID: {CategoryId}", categoryId);

            var result = await _categoryService.GetCategoryByIdAsync(categoryId, includeChildren, includeParent, includeDescriptions, languageCode);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets all categories with filtering and pagination
        /// </summary>
        /// <param name="searchDto">Search and filter parameters</param>
        /// <returns>Paged category results</returns>
        [HttpPost("search")]
        public async Task<IActionResult> GetAllCategories([FromBody] CategorySearchDto searchDto)
        {
            _logger.LogInformation("Getting all categories with search term: {SearchTerm}", searchDto.SearchTerm ?? "None");

            var result = await _categoryService.GetAllCategoriesAsync(searchDto);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets all categories with query parameters (alternative to POST search)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCategories(
            [FromQuery] string? searchTerm = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool? showInMenu = null,
            [FromQuery] Guid? parentCategoryId = null,
            [FromQuery] int? level = null,
            [FromQuery] bool includeChildren = false,
            [FromQuery] bool includeDescriptions = true,
            [FromQuery] string? languageCode = null,
            [FromQuery] CategorySortField sortBy = CategorySortField.SortOrder,
            [FromQuery] SortDirection sortDirection = SortDirection.Ascending,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var searchDto = new CategorySearchDto
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                ShowInMenu = showInMenu,
                ParentCategoryId = parentCategoryId,
                Level = level,
                IncludeChildren = includeChildren,
                SortBy = sortBy,
                SortDirection = sortDirection,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            _logger.LogInformation("Getting categories with search term: {SearchTerm}", searchTerm ?? "None");

            var result = await _categoryService.GetAllCategoriesAsync(searchDto);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets categories by parent category ID
        /// </summary>
        /// <param name="parentCategoryId">Parent category ID (null for root categories)</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="includeChildren">Whether to include child categories recursively</param>
        /// <param name="includeDescriptions">Whether to include descriptions</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>List of categories</returns>
        [HttpGet("by-parent")]
        public async Task<IActionResult> GetCategoriesByParent(
            [FromQuery] Guid? parentCategoryId = null,
            [FromQuery] bool activeOnly = true,
            [FromQuery] bool includeChildren = false,
            [FromQuery] bool includeDescriptions = true,
            [FromQuery] string? languageCode = null)
        {
            _logger.LogInformation("Getting categories by parent ID: {ParentCategoryId}", parentCategoryId?.ToString() ?? "Root");

            var result = await _categoryService.GetCategoriesByParentAsync(parentCategoryId, activeOnly, includeChildren, includeDescriptions, languageCode);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets root categories (categories without parent)
        /// </summary>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="includeChildren">Whether to include child categories</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>List of root categories</returns>
        [HttpGet("root")]
        public async Task<IActionResult> GetRootCategories(
            [FromQuery] bool activeOnly = true,
            [FromQuery] bool includeChildren = false,
            [FromQuery] string? languageCode = null)
        {
            _logger.LogInformation("Getting root categories");

            var result = await _categoryService.GetRootCategoriesAsync(activeOnly, includeChildren, languageCode);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets category tree (hierarchical structure)
        /// </summary>
        /// <param name="rootCategoryId">Root category ID to start from (null for full tree)</param>
        /// <param name="maxDepth">Maximum depth to retrieve</param>
        /// <param name="activeOnly">Whether to include only active categories</param>
        /// <param name="menuOnly">Whether to include only menu categories</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Category tree</returns>
        [HttpGet("tree")]
        public async Task<IActionResult> GetCategoryTree(
            [FromQuery] Guid? rootCategoryId = null,
            [FromQuery] int? maxDepth = null,
            [FromQuery] bool activeOnly = true,
            [FromQuery] bool menuOnly = false,
            [FromQuery] string? languageCode = null)
        {
            _logger.LogInformation("Getting category tree for root ID: {RootCategoryId}", rootCategoryId?.ToString() ?? "Full Tree");

            var result = await _categoryService.GetCategoryTreeAsync(rootCategoryId, maxDepth, activeOnly, menuOnly, languageCode);
            return StatusCode(result.StatusCode, result);
        }

        /// <summary>
        /// Gets menu categories (categories marked as show in menu)
        /// </summary>
        /// <param name="maxDepth">Maximum depth to retrieve</param>
        /// <param name="languageCode">Specific language code</param>
        /// <returns>Menu category tree</returns>
        [HttpGet("menu")]
        public async Task<IActionResult> GetMenuCategories(
            [FromQuery] int? maxDepth = 3,
            [FromQuery] string? languageCode = null)
        {
            _logger.LogInformation("Getting menu categories with max depth: {MaxDepth}", maxDepth);

            var result = await _categoryService.GetMenuCategoriesAsync(maxDepth, languageCode);
            return StatusCode(result.StatusCode, result);
        }

        #endregion
    }
}
