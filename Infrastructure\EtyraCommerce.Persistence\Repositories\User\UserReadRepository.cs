using EtyraCommerce.Application.Repositories.User;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.User
{
    /// <summary>
    /// User-specific read repository implementation
    /// </summary>
    public class UserReadRepository : ReadRepository<Domain.Entities.User.User>, IUserReadRepository
    {
        public UserReadRepository(EtyraCommerceDbContext context) : base(context)
        {
        }

        public async Task<Domain.Entities.User.User?> GetByEmailAsync(string email, bool tracking = true)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.FirstOrDefaultAsync(u => u.Email.Value == email);
        }

        public async Task<Domain.Entities.User.User?> GetByUsernameAsync(string username, bool tracking = true)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<Domain.Entities.User.User?> GetByEmailOrUsernameAsync(string emailOrUsername, bool tracking = true)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.FirstOrDefaultAsync(u =>
                u.Email.Value == emailOrUsername ||
                u.Username == emailOrUsername);
        }

        public async Task<bool> EmailExistsAsync(string email, Guid? excludeUserId = null)
        {
            var query = Table.AsNoTracking();

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(u => u.Email.Value == email);
        }

        public async Task<bool> UsernameExistsAsync(string username, Guid? excludeUserId = null)
        {
            var query = Table.AsNoTracking();

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(u => u.Username == username);
        }

        public async Task<IEnumerable<Domain.Entities.User.User>> GetByEmailConfirmationStatusAsync(bool isConfirmed, bool tracking = false)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.Where(u => u.IsEmailConfirmed == isConfirmed).ToListAsync();
        }

        public async Task<IEnumerable<Domain.Entities.User.User>> GetLockedUsersAsync(bool tracking = false)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            var now = DateTime.UtcNow;
            return await query.Where(u => u.LockedUntil.HasValue && u.LockedUntil.Value > now).ToListAsync();
        }

        public async Task<IEnumerable<Domain.Entities.User.User>> GetUsersRegisteredBetweenAsync(DateTime fromDate, DateTime toDate, bool tracking = false)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.Where(u => u.CreatedAt >= fromDate && u.CreatedAt <= toDate).ToListAsync();
        }

        public async Task<IEnumerable<Domain.Entities.User.User>> GetUsersLoggedInBetweenAsync(DateTime fromDate, DateTime toDate, bool tracking = false)
        {
            var query = tracking ? Table : Table.AsNoTracking();
            return await query.Where(u => u.LastLoginAt.HasValue &&
                                         u.LastLoginAt.Value >= fromDate &&
                                         u.LastLoginAt.Value <= toDate).ToListAsync();
        }

        public async Task<UserStatistics> GetUserStatisticsAsync()
        {
            var now = DateTime.UtcNow;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);

            var query = Table.AsNoTracking();

            var statistics = new UserStatistics
            {
                TotalUsers = await query.CountAsync(),
                ActiveUsers = await query.CountAsync(u => u.IsEnabled && !u.IsDeleted),
                InactiveUsers = await query.CountAsync(u => !u.IsEnabled || u.IsDeleted),
                EmailConfirmedUsers = await query.CountAsync(u => u.IsEmailConfirmed),
                EmailUnconfirmedUsers = await query.CountAsync(u => !u.IsEmailConfirmed),
                LockedUsers = await query.CountAsync(u => u.LockedUntil.HasValue && u.LockedUntil.Value > now),
                UsersRegisteredToday = await query.CountAsync(u => u.CreatedAt.Date == today),
                UsersRegisteredThisWeek = await query.CountAsync(u => u.CreatedAt >= weekStart),
                UsersRegisteredThisMonth = await query.CountAsync(u => u.CreatedAt >= monthStart),
                UsersLoggedInToday = await query.CountAsync(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value.Date == today),
                UsersLoggedInThisWeek = await query.CountAsync(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= weekStart),
                UsersLoggedInThisMonth = await query.CountAsync(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= monthStart)
            };

            return statistics;
        }
    }
}
