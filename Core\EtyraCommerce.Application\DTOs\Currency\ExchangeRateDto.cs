namespace EtyraCommerce.Application.DTOs.Currency
{
    /// <summary>
    /// Exchange rate data transfer object
    /// </summary>
    public class ExchangeRateDto
    {
        /// <summary>
        /// Exchange rate unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Base currency ID (from currency)
        /// </summary>
        public Guid FromCurrencyId { get; set; }

        /// <summary>
        /// Target currency ID (to currency)
        /// </summary>
        public Guid ToCurrencyId { get; set; }

        /// <summary>
        /// Base currency information
        /// </summary>
        public CurrencySummaryDto FromCurrency { get; set; } = null!;

        /// <summary>
        /// Target currency information
        /// </summary>
        public CurrencySummaryDto ToCurrency { get; set; } = null!;

        /// <summary>
        /// Exchange rate value (1 FromCurrency = Rate * ToCurrency)
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// Inverse rate (1 ToCurrency = InverseRate * FromCurrency)
        /// </summary>
        public decimal InverseRate { get; set; }

        /// <summary>
        /// Date when this rate is effective from
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Date when this rate expires (optional)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Whether this rate is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether this rate is currently valid (active and not expired)
        /// </summary>
        public bool IsCurrentlyValid { get; set; }

        /// <summary>
        /// Source of the exchange rate
        /// </summary>
        public string Source { get; set; } = null!;

        /// <summary>
        /// External reference ID (for API sources)
        /// </summary>
        public string? ExternalReferenceId { get; set; }

        /// <summary>
        /// Additional notes about this rate
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Timestamp when rate was last updated from external source
        /// </summary>
        public DateTime? LastUpdatedFromSource { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime UpdatedDate { get; set; }
    }

    /// <summary>
    /// Exchange rate summary DTO for lists
    /// </summary>
    public class ExchangeRateSummaryDto
    {
        /// <summary>
        /// Exchange rate unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Currency pair (e.g., "USD/TRY")
        /// </summary>
        public string CurrencyPair { get; set; } = null!;

        /// <summary>
        /// Exchange rate value
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// Effective date
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Whether this rate is currently valid
        /// </summary>
        public bool IsCurrentlyValid { get; set; }

        /// <summary>
        /// Source of the exchange rate
        /// </summary>
        public string Source { get; set; } = null!;
    }

    /// <summary>
    /// Exchange rate create request DTO
    /// </summary>
    public class CreateExchangeRateDto
    {
        /// <summary>
        /// Base currency ID (from currency)
        /// </summary>
        public Guid FromCurrencyId { get; set; }

        /// <summary>
        /// Target currency ID (to currency)
        /// </summary>
        public Guid ToCurrencyId { get; set; }

        /// <summary>
        /// Exchange rate value (1 FromCurrency = Rate * ToCurrency)
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// Date when this rate is effective from
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Date when this rate expires (optional)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Source of the exchange rate
        /// </summary>
        public string Source { get; set; } = "Manual";

        /// <summary>
        /// External reference ID (for API sources)
        /// </summary>
        public string? ExternalReferenceId { get; set; }

        /// <summary>
        /// Additional notes about this rate
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Exchange rate update request DTO
    /// </summary>
    public class UpdateExchangeRateDto
    {
        /// <summary>
        /// Exchange rate value (1 FromCurrency = Rate * ToCurrency)
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// Date when this rate is effective from
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Date when this rate expires (optional)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Whether this rate should be active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Source of the exchange rate
        /// </summary>
        public string Source { get; set; } = "Manual";

        /// <summary>
        /// External reference ID (for API sources)
        /// </summary>
        public string? ExternalReferenceId { get; set; }

        /// <summary>
        /// Additional notes about this rate
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Currency conversion request DTO
    /// </summary>
    public class CurrencyConversionDto
    {
        /// <summary>
        /// Amount to convert
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Source currency code
        /// </summary>
        public string FromCurrencyCode { get; set; } = null!;

        /// <summary>
        /// Target currency code
        /// </summary>
        public string ToCurrencyCode { get; set; } = null!;

        /// <summary>
        /// Date for conversion (optional, uses current date if not specified)
        /// </summary>
        public DateTime? ConversionDate { get; set; }
    }

    /// <summary>
    /// Currency conversion result DTO
    /// </summary>
    public class CurrencyConversionResultDto
    {
        /// <summary>
        /// Original amount
        /// </summary>
        public decimal OriginalAmount { get; set; }

        /// <summary>
        /// Converted amount
        /// </summary>
        public decimal ConvertedAmount { get; set; }

        /// <summary>
        /// Exchange rate used for conversion
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Source currency
        /// </summary>
        public CurrencySummaryDto FromCurrency { get; set; } = null!;

        /// <summary>
        /// Target currency
        /// </summary>
        public CurrencySummaryDto ToCurrency { get; set; } = null!;

        /// <summary>
        /// Conversion date
        /// </summary>
        public DateTime ConversionDate { get; set; }

        /// <summary>
        /// Formatted original amount
        /// </summary>
        public string FormattedOriginalAmount { get; set; } = null!;

        /// <summary>
        /// Formatted converted amount
        /// </summary>
        public string FormattedConvertedAmount { get; set; } = null!;
    }
}
