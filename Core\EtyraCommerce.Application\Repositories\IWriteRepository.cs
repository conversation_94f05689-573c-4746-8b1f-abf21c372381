﻿using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Application.Repositories
{
    /// <summary>
    /// Write repository interface for modifying entities
    /// </summary>
    /// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
    public interface IWriteRepository<T> : IRepository<T> where T : BaseEntity
    {
        /// <summary>
        /// Adds a new entity
        /// </summary>
        Task<T> AddAsync(T model);

        /// <summary>
        /// Adds multiple entities
        /// </summary>
        Task AddRangeAsync(IEnumerable<T> models);

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        Task<T> UpdateAsync(T model);

        /// <summary>
        /// Removes entity by ID
        /// </summary>
        Task<bool> RemoveByIdAsync(Guid id);

        /// <summary>
        /// Removes an entity
        /// </summary>
        Task RemoveAsync(T model);

        /// <summary>
        /// Removes multiple entities
        /// </summary>
        Task RemoveRangeAsync(IEnumerable<T> models);

        /// <summary>
        /// Marks entity as deleted (soft delete)
        /// </summary>
        Task<bool> SoftDeleteByIdAsync(Guid id);

        /// <summary>
        /// Marks entity as deleted (soft delete)
        /// </summary>
        Task SoftDeleteAsync(T model);

        /// <summary>
        /// Restores a soft deleted entity
        /// </summary>
        Task<bool> RestoreByIdAsync(Guid id);
    }
}