using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Domain.Entities.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Queries
{
    /// <summary>
    /// Query for getting products by category with pagination and filtering
    /// </summary>
    public class GetProductsByCategoryQuery : IRequest<CustomResponseDto<PagedResult<ProductDto>>>
    {
        /// <summary>
        /// Category ID to filter products
        /// </summary>
        public Guid CategoryId { get; set; }

        /// <summary>
        /// Whether to include products from child categories
        /// </summary>
        public bool IncludeChildCategories { get; set; } = false;

        #region Pagination

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size (number of items per page)
        /// </summary>
        public int PageSize { get; set; } = 20;

        #endregion

        #region Filtering

        /// <summary>
        /// Filter by product status
        /// </summary>
        public ProductStatus? Status { get; set; }

        /// <summary>
        /// Filter by product type
        /// </summary>
        public ProductType? Type { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by featured status
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Filter by digital status
        /// </summary>
        public bool? IsDigital { get; set; }

        /// <summary>
        /// Filter by brand
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Filter by minimum price
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Filter by maximum price
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// Filter by minimum rating
        /// </summary>
        public int? MinRating { get; set; }

        /// <summary>
        /// Filter by maximum rating
        /// </summary>
        public int? MaxRating { get; set; }

        /// <summary>
        /// Filter by minimum stock quantity
        /// </summary>
        public int? MinStock { get; set; }

        /// <summary>
        /// Filter by maximum stock quantity
        /// </summary>
        public int? MaxStock { get; set; }

        /// <summary>
        /// Filter by availability (in stock)
        /// </summary>
        public bool? InStock { get; set; }

        /// <summary>
        /// Filter by sale status (on sale)
        /// </summary>
        public bool? OnSale { get; set; }

        /// <summary>
        /// Search term for product name, model, or description
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by brands (multiple brands)
        /// </summary>
        public List<string> Brands { get; set; } = new();

        /// <summary>
        /// Filter by product attributes (key-value pairs)
        /// </summary>
        public Dictionary<string, string> AttributeFilters { get; set; } = new();

        /// <summary>
        /// Filter by tags
        /// </summary>
        public List<string> Tags { get; set; } = new();

        #endregion

        #region Sorting

        /// <summary>
        /// Sort field
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        #endregion

        #region Include Options

        /// <summary>
        /// Whether to include product descriptions
        /// </summary>
        public bool IncludeDescriptions { get; set; } = false;

        /// <summary>
        /// Whether to include product images
        /// </summary>
        public bool IncludeImages { get; set; } = false;

        /// <summary>
        /// Whether to include product categories
        /// </summary>
        public bool IncludeCategories { get; set; } = false;

        /// <summary>
        /// Whether to include product discounts
        /// </summary>
        public bool IncludeDiscounts { get; set; } = false;

        /// <summary>
        /// Whether to include product attributes
        /// </summary>
        public bool IncludeAttributes { get; set; } = false;

        /// <summary>
        /// Whether to include product variants
        /// </summary>
        public bool IncludeVariants { get; set; } = false;

        /// <summary>
        /// Whether to include warehouse products (inventory)
        /// </summary>
        public bool IncludeInventory { get; set; } = false;

        #endregion

        #region Localization

        /// <summary>
        /// Language code for descriptions (e.g., "en-US", "tr-TR")
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Store ID for store-specific descriptions
        /// </summary>
        public Guid? StoreId { get; set; }

        #endregion

        /// <summary>
        /// Constructor for basic query
        /// </summary>
        public GetProductsByCategoryQuery()
        {
        }

        /// <summary>
        /// Constructor with category ID
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="includeChildCategories">Include child categories</param>
        public GetProductsByCategoryQuery(Guid categoryId, int page = 1, int pageSize = 20, bool includeChildCategories = false)
        {
            CategoryId = categoryId;
            Page = page;
            PageSize = pageSize;
            IncludeChildCategories = includeChildCategories;
        }

        /// <summary>
        /// Creates a query for active products in category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="includeChildCategories">Include child categories</param>
        /// <returns>Query for active products</returns>
        public static GetProductsByCategoryQuery ActiveProducts(Guid categoryId, int page = 1, int pageSize = 20, bool includeChildCategories = false)
        {
            return new GetProductsByCategoryQuery(categoryId, page, pageSize, includeChildCategories)
            {
                IsActive = true,
                Status = ProductStatus.Published,
                IncludeImages = true,
                IncludeCategories = true
            };
        }

        /// <summary>
        /// Creates a query for featured products in category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="includeChildCategories">Include child categories</param>
        /// <returns>Query for featured products</returns>
        public static GetProductsByCategoryQuery FeaturedProducts(Guid categoryId, int page = 1, int pageSize = 20, bool includeChildCategories = false)
        {
            return new GetProductsByCategoryQuery(categoryId, page, pageSize, includeChildCategories)
            {
                IsActive = true,
                IsFeatured = true,
                Status = ProductStatus.Published,
                IncludeImages = true,
                IncludeCategories = true
            };
        }

        /// <summary>
        /// Creates a query for products on sale in category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="includeChildCategories">Include child categories</param>
        /// <returns>Query for products on sale</returns>
        public static GetProductsByCategoryQuery OnSaleProducts(Guid categoryId, int page = 1, int pageSize = 20, bool includeChildCategories = false)
        {
            return new GetProductsByCategoryQuery(categoryId, page, pageSize, includeChildCategories)
            {
                IsActive = true,
                OnSale = true,
                Status = ProductStatus.Published,
                IncludeImages = true,
                IncludeCategories = true,
                IncludeDiscounts = true
            };
        }
    }
}
