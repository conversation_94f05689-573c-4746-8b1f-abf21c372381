using AutoMapper;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Inventory
{
    /// <summary>
    /// Inventory process service implementation for business logic operations
    /// </summary>
    public class InventoryProcessService : IInventoryProcessService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<InventoryProcessService> _logger;

        public InventoryProcessService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<InventoryProcessService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        #region Inventory Management

        public async Task<CustomResponseDto<InventoryDto>> ProcessCreateInventoryAsync(CreateInventoryDto createDto)
        {
            try
            {
                _logger.LogInformation("Processing create inventory for ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                    createDto.ProductId, createDto.WarehouseId);

                // Validate product exists
                var productRepo = _unitOfWork.ReadRepository<Domain.Entities.Product.Product>();
                var productQuery = await productRepo.GetAllAsync(tracking: false);
                var product = await productQuery.FirstOrDefaultAsync(p => p.Id == createDto.ProductId);
                if (product == null)
                    return CustomResponseDto<InventoryDto>.NotFound("Product not found");

                // Validate warehouse exists
                var warehouseRepo = _unitOfWork.ReadRepository<Warehouse>();
                var warehouseQuery = await warehouseRepo.GetAllAsync(tracking: false);
                var warehouse = await warehouseQuery.FirstOrDefaultAsync(w => w.Id == createDto.WarehouseId);
                if (warehouse == null)
                    return CustomResponseDto<InventoryDto>.NotFound("Warehouse not found");

                // Check if inventory already exists for this product-warehouse combination
                var inventoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryReadRepo.GetAllAsync(tracking: false);
                var existingInventory = await inventoryQuery.FirstOrDefaultAsync(i =>
                    i.ProductId == createDto.ProductId && i.WarehouseId == createDto.WarehouseId);

                if (existingInventory != null)
                    return CustomResponseDto<InventoryDto>.BadRequest("Inventory already exists for this product-warehouse combination");

                // Create inventory entity
                var inventory = new Domain.Entities.Inventory.Inventory(
                    createDto.ProductId,
                    createDto.WarehouseId,
                    createDto.AvailableQuantity,
                    createDto.MinStockLevel,
                    createDto.ReorderPoint,
                    createDto.ReorderQuantity);

                inventory.UpdateStockLevels(createDto.MinStockLevel, createDto.MaxStockLevel,
                    createDto.ReorderPoint, createDto.ReorderQuantity);
                inventory.UpdateLocationInfo(createDto.LocationCode, createDto.SupplierReference, createDto.LeadTimeDays);

                if (!string.IsNullOrEmpty(createDto.Notes))
                    inventory.Notes = createDto.Notes;

                // Add to repository
                var inventoryWriteRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                await inventoryWriteRepo.AddAsync(inventory);

                // Create initial transaction if quantity > 0
                if (createDto.AvailableQuantity > 0)
                {
                    var transaction = new InventoryTransaction(
                        inventory.Id,
                        InventoryTransactionType.StockIn,
                        createDto.AvailableQuantity,
                        0,
                        createDto.AvailableQuantity,
                        "INITIAL_STOCK",
                        "Initial stock entry");

                    var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();
                    await transactionRepo.AddAsync(transaction);
                }

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var inventoryDto = _mapper.Map<InventoryDto>(inventory);
                inventoryDto.ProductName = product.Name;
                inventoryDto.ProductModel = product.Model;
                inventoryDto.WarehouseName = warehouse.Name;
                inventoryDto.WarehouseCode = warehouse.Code;

                _logger.LogInformation("Inventory created successfully with ID: {InventoryId}", inventory.Id);
                return CustomResponseDto<InventoryDto>.Success(201, inventoryDto, "Inventory created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inventory for ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                    createDto.ProductId, createDto.WarehouseId);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while creating inventory");
            }
        }

        public async Task<CustomResponseDto<InventoryDto>> ProcessUpdateInventoryAsync(UpdateInventoryDto updateDto)
        {
            try
            {
                _logger.LogInformation("Processing update inventory with ID: {InventoryId}", updateDto.Id);

                // Get inventory
                var inventoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventory = await inventoryReadRepo.GetByIdAsync(updateDto.Id);
                if (inventory == null)
                    return CustomResponseDto<InventoryDto>.NotFound("Inventory not found");

                var inventoryWriteRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryEntity = await inventoryWriteRepo.Table.FirstOrDefaultAsync(i => i.Id == updateDto.Id);
                if (inventoryEntity == null)
                    return CustomResponseDto<InventoryDto>.NotFound("Inventory not found");

                // Update available quantity if provided
                if (updateDto.AvailableQuantity.HasValue)
                {
                    var oldQuantity = inventoryEntity.AvailableQuantity;
                    inventoryEntity.UpdateAvailableQuantity(updateDto.AvailableQuantity.Value, "Manual update");

                    // Create transaction for quantity change
                    if (oldQuantity != updateDto.AvailableQuantity.Value)
                    {
                        var quantityDiff = updateDto.AvailableQuantity.Value - oldQuantity;
                        var transactionType = quantityDiff > 0 ? InventoryTransactionType.StockIn : InventoryTransactionType.StockOut;

                        var transaction = new InventoryTransaction(
                            inventoryEntity.Id,
                            transactionType,
                            Math.Abs(quantityDiff),
                            oldQuantity,
                            updateDto.AvailableQuantity.Value,
                            "MANUAL_UPDATE",
                            "Manual quantity update");

                        var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();
                        await transactionRepo.AddAsync(transaction);
                    }
                }

                // Update stock levels if provided
                if (updateDto.MinStockLevel.HasValue || updateDto.MaxStockLevel.HasValue ||
                    updateDto.ReorderPoint.HasValue || updateDto.ReorderQuantity.HasValue)
                {
                    inventoryEntity.UpdateStockLevels(
                        updateDto.MinStockLevel ?? inventoryEntity.MinStockLevel,
                        updateDto.MaxStockLevel ?? inventoryEntity.MaxStockLevel,
                        updateDto.ReorderPoint ?? inventoryEntity.ReorderPoint,
                        updateDto.ReorderQuantity ?? inventoryEntity.ReorderQuantity);
                }

                // Update location info if provided
                if (updateDto.LocationCode != null || updateDto.SupplierReference != null || updateDto.LeadTimeDays.HasValue)
                {
                    inventoryEntity.UpdateLocationInfo(
                        updateDto.LocationCode ?? inventoryEntity.LocationCode,
                        updateDto.SupplierReference ?? inventoryEntity.SupplierReference,
                        updateDto.LeadTimeDays ?? inventoryEntity.LeadTimeDays);
                }

                // Update notes if provided
                if (updateDto.Notes != null)
                    inventoryEntity.Notes = updateDto.Notes;

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Get updated inventory with navigation properties
                var inventoryQuery = await inventoryReadRepo.GetAllAsync(tracking: false);
                var updatedInventory = await inventoryQuery
                    .Include(i => i.Product)
                    .Include(i => i.Warehouse)
                    .FirstOrDefaultAsync(i => i.Id == updateDto.Id);

                // Map to DTO
                var inventoryDto = _mapper.Map<InventoryDto>(updatedInventory);
                inventoryDto.ProductName = updatedInventory?.Product?.Name;
                inventoryDto.ProductModel = updatedInventory?.Product?.Model;
                inventoryDto.WarehouseName = updatedInventory?.Warehouse?.Name;
                inventoryDto.WarehouseCode = updatedInventory?.Warehouse?.Code;

                _logger.LogInformation("Inventory updated successfully with ID: {InventoryId}", updateDto.Id);
                return CustomResponseDto<InventoryDto>.Success(200, inventoryDto, "Inventory updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inventory with ID: {InventoryId}", updateDto.Id);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while updating inventory");
            }
        }

        public async Task<CustomResponseDto<InventoryDto>> ProcessGetInventoryByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Processing get inventory by ID: {InventoryId}", id);

                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);
                var inventory = await inventoryQuery
                    .Include(i => i.Product)
                    .Include(i => i.Warehouse)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (inventory == null)
                    return CustomResponseDto<InventoryDto>.NotFound("Inventory not found");

                // Map to DTO
                var inventoryDto = _mapper.Map<InventoryDto>(inventory);
                inventoryDto.ProductName = inventory.Product?.Name;
                inventoryDto.ProductModel = inventory.Product?.Model;
                inventoryDto.WarehouseName = inventory.Warehouse?.Name;
                inventoryDto.WarehouseCode = inventory.Warehouse?.Code;

                _logger.LogInformation("Inventory retrieved successfully with ID: {InventoryId}", id);
                return CustomResponseDto<InventoryDto>.Success(200, inventoryDto, "Inventory retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by ID: {InventoryId}", id);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while retrieving inventory");
            }
        }

        public async Task<CustomResponseDto<List<InventoryDto>>> ProcessGetInventoryByProductAsync(Guid productId, Guid? warehouseId = null, bool activeWarehousesOnly = true)
        {
            try
            {
                _logger.LogInformation("Processing get inventory by ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                    productId, warehouseId?.ToString() ?? "All");

                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);

                var query = inventoryQuery
                    .Include(i => i.Product)
                    .Include(i => i.Warehouse)
                    .Where(i => i.ProductId == productId);

                if (warehouseId.HasValue)
                    query = query.Where(i => i.WarehouseId == warehouseId.Value);

                if (activeWarehousesOnly)
                    query = query.Where(i => i.Warehouse.IsActive);

                var inventories = await query.ToListAsync();

                // Map to DTOs
                var inventoryDtos = inventories.Select(inventory =>
                {
                    var dto = _mapper.Map<InventoryDto>(inventory);
                    dto.ProductName = inventory.Product?.Name;
                    dto.ProductModel = inventory.Product?.Model;
                    dto.WarehouseName = inventory.Warehouse?.Name;
                    dto.WarehouseCode = inventory.Warehouse?.Code;
                    return dto;
                }).ToList();

                _logger.LogInformation("Retrieved {Count} inventory items for ProductId: {ProductId}", inventoryDtos.Count, productId);
                return CustomResponseDto<List<InventoryDto>>.Success(200, inventoryDtos, "Inventory items retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by ProductId: {ProductId}", productId);
                return CustomResponseDto<List<InventoryDto>>.InternalServerError("An error occurred while retrieving inventory");
            }
        }

        public async Task<CustomResponseDto<StockStatusDto>> ProcessGetStockStatusAsync(Guid productId, bool activeWarehousesOnly = true)
        {
            try
            {
                _logger.LogInformation("Processing get stock status for ProductId: {ProductId}", productId);

                // Get product
                var productRepo = _unitOfWork.ReadRepository<Domain.Entities.Product.Product>();
                var productQuery = await productRepo.GetAllAsync(tracking: false);
                var product = await productQuery.FirstOrDefaultAsync(p => p.Id == productId);
                if (product == null)
                    return CustomResponseDto<StockStatusDto>.NotFound("Product not found");

                // Get inventory items
                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);

                var query = inventoryQuery
                    .Include(i => i.Warehouse)
                    .Where(i => i.ProductId == productId);

                if (activeWarehousesOnly)
                    query = query.Where(i => i.Warehouse.IsActive);

                var inventories = await query.ToListAsync();

                // Calculate totals
                var totalAvailable = inventories.Sum(i => i.AvailableQuantity);
                var totalReserved = inventories.Sum(i => i.ReservedQuantity);
                var totalAllocated = inventories.Sum(i => i.AllocatedQuantity);

                // Create warehouse stocks
                var warehouseStocks = inventories.Select(i => new WarehouseStockDto
                {
                    WarehouseId = i.WarehouseId,
                    WarehouseName = i.Warehouse?.Name ?? "",
                    WarehouseCode = i.Warehouse?.Code ?? "",
                    AvailableQuantity = i.AvailableQuantity,
                    ReservedQuantity = i.ReservedQuantity,
                    AllocatedQuantity = i.AllocatedQuantity,
                    Status = i.Status,
                    LocationCode = i.LocationCode
                }).ToList();

                var stockStatus = new StockStatusDto
                {
                    ProductId = productId,
                    ProductName = product.Name,
                    TotalAvailable = totalAvailable,
                    TotalReserved = totalReserved,
                    TotalAllocated = totalAllocated,
                    IsInStock = totalAvailable > 0,
                    IsLowStock = inventories.Any(i => i.AvailableQuantity <= i.ReorderPoint && i.ReorderPoint > 0),
                    WarehouseStocks = warehouseStocks
                };

                _logger.LogInformation("Stock status retrieved for ProductId: {ProductId}, TotalAvailable: {TotalAvailable}",
                    productId, totalAvailable);
                return CustomResponseDto<StockStatusDto>.Success(200, stockStatus, "Stock status retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock status for ProductId: {ProductId}", productId);
                return CustomResponseDto<StockStatusDto>.InternalServerError("An error occurred while retrieving stock status");
            }
        }

        public async Task<CustomResponseDto<List<LowStockItemDto>>> ProcessGetLowStockItemsAsync(Guid? warehouseId = null, bool activeWarehousesOnly = true, int? maxItems = null)
        {
            try
            {
                _logger.LogInformation("Processing get low stock items for WarehouseId: {WarehouseId}",
                    warehouseId?.ToString() ?? "All");

                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);

                var query = inventoryQuery
                    .Include(i => i.Product)
                    .Include(i => i.Warehouse)
                    .Where(i => i.AvailableQuantity <= i.ReorderPoint && i.ReorderPoint > 0);

                if (warehouseId.HasValue)
                    query = query.Where(i => i.WarehouseId == warehouseId.Value);

                if (activeWarehousesOnly)
                    query = query.Where(i => i.Warehouse.IsActive);

                if (maxItems.HasValue)
                    query = query.Take(maxItems.Value);

                var lowStockItems = await query.ToListAsync();

                // Map to DTOs
                var lowStockDtos = lowStockItems.Select(item => new LowStockItemDto
                {
                    InventoryId = item.Id,
                    ProductId = item.ProductId,
                    ProductName = item.Product?.Name ?? "",
                    ProductModel = item.Product?.Model ?? "",
                    WarehouseId = item.WarehouseId,
                    WarehouseName = item.Warehouse?.Name ?? "",
                    AvailableQuantity = item.AvailableQuantity,
                    ReorderPoint = item.ReorderPoint,
                    ReorderQuantity = item.ReorderQuantity,
                    Status = item.Status,
                    LeadTimeDays = item.LeadTimeDays,
                    SupplierReference = item.SupplierReference
                }).ToList();

                _logger.LogInformation("Retrieved {Count} low stock items", lowStockDtos.Count);
                return CustomResponseDto<List<LowStockItemDto>>.Success(200, lowStockDtos, "Low stock items retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                return CustomResponseDto<List<LowStockItemDto>>.InternalServerError("An error occurred while retrieving low stock items");
            }
        }

        #endregion

        #region Stock Operations

        public async Task<bool> CheckStockAvailabilityAsync(Guid productId, int quantity, Guid? warehouseId = null)
        {
            try
            {
                _logger.LogInformation("Checking stock availability for ProductId: {ProductId}, Quantity: {Quantity}, WarehouseId: {WarehouseId}",
                    productId, quantity, warehouseId?.ToString() ?? "Any");

                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);

                var query = inventoryQuery
                    .Include(i => i.Warehouse)
                    .Where(i => i.ProductId == productId && i.Warehouse.IsActive);

                if (warehouseId.HasValue)
                    query = query.Where(i => i.WarehouseId == warehouseId.Value);

                var inventories = await query.ToListAsync();
                var totalAvailable = inventories.Sum(i => i.FreeQuantity);

                var isAvailable = totalAvailable >= quantity;
                _logger.LogInformation("Stock availability check result: {IsAvailable}, TotalAvailable: {TotalAvailable}, Required: {Required}",
                    isAvailable, totalAvailable, quantity);

                return isAvailable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking stock availability for ProductId: {ProductId}", productId);
                return false;
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessReserveStockAsync(Guid productId, Guid? warehouseId, int quantity, string reference, string? reason = null, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation("Processing reserve stock for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                    productId, quantity, reference);

                // Check availability
                var isAvailable = await CheckStockAvailabilityAsync(productId, quantity, warehouseId);
                if (!isAvailable)
                    return CustomResponseDto<bool>.BadRequest("Insufficient stock available for reservation");

                // Get inventory items
                var inventoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryReadRepo.GetAllAsync(tracking: false);

                var query = inventoryQuery
                    .Include(i => i.Warehouse)
                    .Where(i => i.ProductId == productId && i.Warehouse.IsActive &&
                               (i.AvailableQuantity - i.ReservedQuantity - i.AllocatedQuantity) > 0);

                if (warehouseId.HasValue)
                    query = query.Where(i => i.WarehouseId == warehouseId.Value);

                var inventories = await query.OrderByDescending(i => i.AvailableQuantity - i.ReservedQuantity - i.AllocatedQuantity).ToListAsync();

                // Reserve stock from available inventories
                var remainingQuantity = quantity;
                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();

                foreach (var inventoryItem in inventories)
                {
                    if (remainingQuantity <= 0) break;

                    var inventory = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == inventoryItem.Id);
                    if (inventory == null) continue;

                    var reserveQuantity = Math.Min(remainingQuantity, inventory.FreeQuantity);
                    if (reserveQuantity <= 0) continue;

                    // Reserve stock
                    inventory.ReserveStock(reserveQuantity, reference);

                    // Create transaction
                    var transaction = new InventoryTransaction(
                        inventory.Id,
                        InventoryTransactionType.Reserved,
                        reserveQuantity,
                        inventory.ReservedQuantity - reserveQuantity,
                        inventory.ReservedQuantity,
                        reference,
                        reason ?? "Stock reservation",
                        userId);

                    await transactionRepo.AddAsync(transaction);

                    remainingQuantity -= reserveQuantity;
                }

                if (remainingQuantity > 0)
                    return CustomResponseDto<bool>.BadRequest("Could not reserve full quantity");

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Stock reserved successfully for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                    productId, quantity, reference);
                return CustomResponseDto<bool>.Success(200, true, "Stock reserved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reserving stock for ProductId: {ProductId}, Reference: {Reference}", productId, reference);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while reserving stock");
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessAllocateStockAsync(Guid productId, Guid? warehouseId, int quantity, string reference, string? reason = null, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation("Processing allocate stock for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                    productId, quantity, reference);

                // Get inventory items with reservations for this reference
                var inventoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var transactionReadRepo = _unitOfWork.ReadRepository<InventoryTransaction>();
                var transactionQuery = await transactionReadRepo.GetAllAsync(tracking: false);

                // Find reserved transactions for this reference
                var reservedTransactions = await transactionQuery
                    .Include(t => t.Inventory)
                    .Where(t => t.Reference == reference && t.Type == InventoryTransactionType.Reserved)
                    .ToListAsync();

                if (!reservedTransactions.Any())
                    return CustomResponseDto<bool>.BadRequest("No reservations found for this reference");

                var totalReserved = reservedTransactions.Sum(t => t.Quantity);
                if (totalReserved < quantity)
                    return CustomResponseDto<bool>.BadRequest("Insufficient reserved stock for allocation");

                // Allocate stock from reservations
                var remainingQuantity = quantity;
                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();

                foreach (var reservedTransaction in reservedTransactions)
                {
                    if (remainingQuantity <= 0) break;

                    var inventory = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == reservedTransaction.InventoryId);
                    if (inventory == null) continue;

                    var allocateQuantity = Math.Min(remainingQuantity, reservedTransaction.Quantity);
                    if (allocateQuantity <= 0) continue;

                    // Allocate stock
                    inventory.AllocateStock(allocateQuantity, reference);

                    // Create transaction
                    var transaction = new InventoryTransaction(
                        inventory.Id,
                        InventoryTransactionType.Allocated,
                        allocateQuantity,
                        inventory.AllocatedQuantity - allocateQuantity,
                        inventory.AllocatedQuantity,
                        reference,
                        reason ?? "Stock allocation",
                        userId);

                    await transactionRepo.AddAsync(transaction);

                    remainingQuantity -= allocateQuantity;
                }

                if (remainingQuantity > 0)
                    return CustomResponseDto<bool>.BadRequest("Could not allocate full quantity");

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Stock allocated successfully for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                    productId, quantity, reference);
                return CustomResponseDto<bool>.Success(200, true, "Stock allocated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error allocating stock for ProductId: {ProductId}, Reference: {Reference}", productId, reference);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while allocating stock");
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessReleaseReservationAsync(string reference, string? reason = null, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation("Processing release reservation for Reference: {Reference}", reference);

                // Get reserved transactions for this reference
                var transactionReadRepo = _unitOfWork.ReadRepository<InventoryTransaction>();
                var transactionQuery = await transactionReadRepo.GetAllAsync(tracking: false);
                var reservedTransactions = await transactionQuery
                    .Where(t => t.Reference == reference && t.Type == InventoryTransactionType.Reserved)
                    .ToListAsync();

                if (!reservedTransactions.Any())
                    return CustomResponseDto<bool>.NotFound("No reservations found for this reference");

                // Release reservations
                var inventoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();

                foreach (var reservedTransaction in reservedTransactions)
                {
                    var inventory = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == reservedTransaction.InventoryId);
                    if (inventory == null) continue;

                    // Release reservation
                    inventory.ReleaseReservation(reservedTransaction.Quantity, reference);

                    // Create transaction
                    var transaction = new InventoryTransaction(
                        inventory.Id,
                        InventoryTransactionType.ReservationReleased,
                        reservedTransaction.Quantity,
                        inventory.ReservedQuantity + reservedTransaction.Quantity,
                        inventory.ReservedQuantity,
                        reference,
                        reason ?? "Reservation released",
                        userId);

                    await transactionRepo.AddAsync(transaction);
                }

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Reservation released successfully for Reference: {Reference}", reference);
                return CustomResponseDto<bool>.Success(200, true, "Reservation released successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error releasing reservation for Reference: {Reference}", reference);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while releasing reservation");
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessAdjustStockAsync(Guid inventoryId, int newQuantity, string reason, string? notes = null, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation("Processing adjust stock for InventoryId: {InventoryId}, NewQuantity: {NewQuantity}",
                    inventoryId, newQuantity);

                // Get inventory
                var inventoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var inventory = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == inventoryId);
                if (inventory == null)
                    return CustomResponseDto<bool>.NotFound("Inventory not found");

                var oldQuantity = inventory.AvailableQuantity;
                var quantityDiff = newQuantity - oldQuantity;

                // Update quantity
                inventory.UpdateAvailableQuantity(newQuantity, reason);

                // Create transaction
                var transactionType = quantityDiff >= 0 ? InventoryTransactionType.Adjustment : InventoryTransactionType.Adjustment;
                var transaction = new InventoryTransaction(
                    inventoryId,
                    transactionType,
                    Math.Abs(quantityDiff),
                    oldQuantity,
                    newQuantity,
                    "ADJUSTMENT",
                    reason,
                    userId);

                transaction.UpdateDetails(reason, notes);

                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();
                await transactionRepo.AddAsync(transaction);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Stock adjusted successfully for InventoryId: {InventoryId}, OldQuantity: {OldQuantity}, NewQuantity: {NewQuantity}",
                    inventoryId, oldQuantity, newQuantity);
                return CustomResponseDto<bool>.Success(200, true, "Stock adjusted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adjusting stock for InventoryId: {InventoryId}", inventoryId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while adjusting stock");
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessTransferStockAsync(Guid productId, Guid fromWarehouseId, Guid toWarehouseId, int quantity, string? reference = null, string? reason = null, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation("Processing transfer stock for ProductId: {ProductId}, From: {FromWarehouse}, To: {ToWarehouse}, Quantity: {Quantity}",
                    productId, fromWarehouseId, toWarehouseId, quantity);

                if (fromWarehouseId == toWarehouseId)
                    return CustomResponseDto<bool>.BadRequest("Source and destination warehouses cannot be the same");

                // Get source inventory
                var inventoryRepo = _unitOfWork.WriteRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryReadRepo.GetAllAsync(tracking: false);

                var sourceInventory = await inventoryQuery
                    .FirstOrDefaultAsync(i => i.ProductId == productId && i.WarehouseId == fromWarehouseId);
                if (sourceInventory == null)
                    return CustomResponseDto<bool>.NotFound("Source inventory not found");

                var sourceInventoryEntity = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == sourceInventory.Id);
                if (sourceInventoryEntity == null)
                    return CustomResponseDto<bool>.NotFound("Source inventory not found");

                if (sourceInventoryEntity.AvailableQuantity < quantity)
                    return CustomResponseDto<bool>.BadRequest("Insufficient stock in source warehouse");

                // Get or create destination inventory
                var destInventory = await inventoryQuery
                    .FirstOrDefaultAsync(i => i.ProductId == productId && i.WarehouseId == toWarehouseId);

                Domain.Entities.Inventory.Inventory destInventoryEntity;
                if (destInventory == null)
                {
                    // Create new inventory for destination warehouse
                    destInventoryEntity = new Domain.Entities.Inventory.Inventory(productId, toWarehouseId, 0);
                    await inventoryRepo.AddAsync(destInventoryEntity);
                }
                else
                {
                    destInventoryEntity = await inventoryRepo.Table.FirstOrDefaultAsync(i => i.Id == destInventory.Id);
                    if (destInventoryEntity == null)
                        return CustomResponseDto<bool>.NotFound("Destination inventory not found");
                }

                // Transfer stock
                sourceInventoryEntity.RemoveStock(quantity, reason ?? "Stock transfer");
                destInventoryEntity.AddStock(quantity, reason ?? "Stock transfer");

                // Create transactions
                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();
                var transferReference = reference ?? $"TRANSFER_{Guid.NewGuid():N}";

                var outTransaction = new InventoryTransaction(
                    sourceInventoryEntity.Id,
                    InventoryTransactionType.Transfer,
                    -quantity,
                    sourceInventoryEntity.AvailableQuantity + quantity,
                    sourceInventoryEntity.AvailableQuantity,
                    transferReference,
                    reason ?? "Stock transfer out",
                    userId);

                var inTransaction = new InventoryTransaction(
                    destInventoryEntity.Id,
                    InventoryTransactionType.Transfer,
                    quantity,
                    destInventoryEntity.AvailableQuantity - quantity,
                    destInventoryEntity.AvailableQuantity,
                    transferReference,
                    reason ?? "Stock transfer in",
                    userId);

                await transactionRepo.AddAsync(outTransaction);
                await transactionRepo.AddAsync(inTransaction);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Stock transferred successfully for ProductId: {ProductId}, Quantity: {Quantity}",
                    productId, quantity);
                return CustomResponseDto<bool>.Success(200, true, "Stock transferred successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring stock for ProductId: {ProductId}", productId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while transferring stock");
            }
        }

        #endregion

        #region Warehouse Management

        public async Task<CustomResponseDto<WarehouseDto>> ProcessCreateWarehouseAsync(CreateWarehouseDto createDto)
        {
            try
            {
                _logger.LogInformation("Processing create warehouse: {Name} ({Code})", createDto.Name, createDto.Code);

                // Check if warehouse code already exists
                var warehouseRepo = _unitOfWork.ReadRepository<Warehouse>();
                var warehouseQuery = await warehouseRepo.GetAllAsync(tracking: false);
                var existingWarehouse = await warehouseQuery.FirstOrDefaultAsync(w => w.Code == createDto.Code);

                if (existingWarehouse != null)
                    return CustomResponseDto<WarehouseDto>.BadRequest("Warehouse code already exists");

                // Create warehouse entity
                var warehouse = new Warehouse(
                    createDto.Name,
                    createDto.Code,
                    createDto.Description,
                    createDto.Address,
                    createDto.IsActive,
                    createDto.IsMain,
                    createDto.Type);

                warehouse.UpdateInfo(createDto.Name, createDto.Description, createDto.Address,
                    createDto.Phone, createDto.Email, createDto.ManagerName);
                warehouse.UpdateSortOrder(createDto.SortOrder);

                // Add to repository
                var warehouseWriteRepo = _unitOfWork.WriteRepository<Warehouse>();
                await warehouseWriteRepo.AddAsync(warehouse);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var warehouseDto = _mapper.Map<WarehouseDto>(warehouse);

                _logger.LogInformation("Warehouse created successfully with ID: {WarehouseId}", warehouse.Id);
                return CustomResponseDto<WarehouseDto>.Success(201, warehouseDto, "Warehouse created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating warehouse: {Name} ({Code})", createDto.Name, createDto.Code);
                return CustomResponseDto<WarehouseDto>.InternalServerError("An error occurred while creating warehouse");
            }
        }

        public async Task<CustomResponseDto<WarehouseDto>> ProcessUpdateWarehouseAsync(UpdateWarehouseDto updateDto)
        {
            try
            {
                _logger.LogInformation("Processing update warehouse with ID: {WarehouseId}", updateDto.Id);

                // Get warehouse
                var warehouseRepo = _unitOfWork.WriteRepository<Warehouse>();
                var warehouse = await warehouseRepo.Table.FirstOrDefaultAsync(w => w.Id == updateDto.Id);
                if (warehouse == null)
                    return CustomResponseDto<WarehouseDto>.NotFound("Warehouse not found");

                // Update warehouse
                warehouse.UpdateInfo(updateDto.Name, updateDto.Description, updateDto.Address,
                    updateDto.Phone, updateDto.Email, updateDto.ManagerName);
                warehouse.UpdateStatus(updateDto.IsActive, updateDto.IsMain);
                warehouse.UpdateSortOrder(updateDto.SortOrder);
                warehouse.Type = updateDto.Type;

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var warehouseDto = _mapper.Map<WarehouseDto>(warehouse);

                _logger.LogInformation("Warehouse updated successfully with ID: {WarehouseId}", updateDto.Id);
                return CustomResponseDto<WarehouseDto>.Success(200, warehouseDto, "Warehouse updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse with ID: {WarehouseId}", updateDto.Id);
                return CustomResponseDto<WarehouseDto>.InternalServerError("An error occurred while updating warehouse");
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessDeleteWarehouseAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Processing delete warehouse with ID: {WarehouseId}", id);

                // Check if warehouse has inventory items
                var inventoryRepo = _unitOfWork.ReadRepository<Domain.Entities.Inventory.Inventory>();
                var inventoryQuery = await inventoryRepo.GetAllAsync(tracking: false);
                var hasInventory = await inventoryQuery.AnyAsync(i => i.WarehouseId == id);

                if (hasInventory)
                    return CustomResponseDto<bool>.BadRequest("Cannot delete warehouse with existing inventory items");

                // Get warehouse
                var warehouseRepo = _unitOfWork.WriteRepository<Warehouse>();
                var warehouse = await warehouseRepo.Table.FirstOrDefaultAsync(w => w.Id == id);
                if (warehouse == null)
                    return CustomResponseDto<bool>.NotFound("Warehouse not found");

                // Delete warehouse (soft delete)
                await warehouseRepo.RemoveAsync(warehouse);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Warehouse deleted successfully with ID: {WarehouseId}", id);
                return CustomResponseDto<bool>.Success(200, true, "Warehouse deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting warehouse with ID: {WarehouseId}", id);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while deleting warehouse");
            }
        }

        public async Task<CustomResponseDto<PagedResult<WarehouseDto>>> ProcessGetAllWarehousesAsync(WarehouseFilterDto filterDto)
        {
            try
            {
                _logger.LogInformation("Processing get all warehouses with filter");

                var warehouseRepo = _unitOfWork.ReadRepository<Warehouse>();
                var warehouseQuery = await warehouseRepo.GetAllAsync(tracking: false);

                var query = warehouseQuery.AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(filterDto.SearchTerm))
                {
                    var searchTerm = filterDto.SearchTerm.ToLower();
                    query = query.Where(w => w.Name.ToLower().Contains(searchTerm) ||
                                           w.Code.ToLower().Contains(searchTerm) ||
                                           (w.Description != null && w.Description.ToLower().Contains(searchTerm)));
                }

                if (filterDto.IsActive.HasValue)
                    query = query.Where(w => w.IsActive == filterDto.IsActive.Value);

                if (filterDto.IsMain.HasValue)
                    query = query.Where(w => w.IsMain == filterDto.IsMain.Value);

                if (filterDto.Type.HasValue)
                    query = query.Where(w => w.Type == filterDto.Type.Value);

                // Apply sorting
                query = filterDto.SortBy?.ToLower() switch
                {
                    "code" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(w => w.Code) : query.OrderBy(w => w.Code),
                    "type" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(w => w.Type) : query.OrderBy(w => w.Type),
                    "isactive" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(w => w.IsActive) : query.OrderBy(w => w.IsActive),
                    "sortorder" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(w => w.SortOrder) : query.OrderBy(w => w.SortOrder),
                    _ => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(w => w.Name) : query.OrderBy(w => w.Name)
                };

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var warehouses = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .ToListAsync();

                // Map to DTOs
                var warehouseDtos = warehouses.Select(w => _mapper.Map<WarehouseDto>(w)).ToList();

                var pagedResult = PagedResult<WarehouseDto>.Create(
                    warehouseDtos,
                    totalCount,
                    filterDto.PageNumber,
                    filterDto.PageSize);

                _logger.LogInformation("Retrieved {Count} warehouses out of {Total}", warehouseDtos.Count, totalCount);
                return CustomResponseDto<PagedResult<WarehouseDto>>.Success(200, pagedResult, "Warehouses retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all warehouses");
                return CustomResponseDto<PagedResult<WarehouseDto>>.InternalServerError("An error occurred while retrieving warehouses");
            }
        }

        #endregion

        #region Transactions & Reporting

        public async Task<CustomResponseDto<PagedResult<InventoryTransactionDto>>> ProcessGetInventoryTransactionsAsync(InventoryTransactionFilterDto filterDto)
        {
            try
            {
                _logger.LogInformation("Processing get inventory transactions with filter");

                var transactionRepo = _unitOfWork.ReadRepository<InventoryTransaction>();
                var transactionQuery = await transactionRepo.GetAllAsync(tracking: false);

                var query = transactionQuery
                    .Include(t => t.Inventory)
                        .ThenInclude(i => i.Product)
                    .Include(t => t.Inventory)
                        .ThenInclude(i => i.Warehouse)
                    .Include(t => t.User)
                    .AsQueryable();

                // Apply filters
                if (filterDto.InventoryId.HasValue)
                    query = query.Where(t => t.InventoryId == filterDto.InventoryId.Value);

                if (filterDto.ProductId.HasValue)
                    query = query.Where(t => t.Inventory.ProductId == filterDto.ProductId.Value);

                if (filterDto.WarehouseId.HasValue)
                    query = query.Where(t => t.Inventory.WarehouseId == filterDto.WarehouseId.Value);

                if (filterDto.Type.HasValue)
                    query = query.Where(t => t.Type == filterDto.Type.Value);

                if (!string.IsNullOrEmpty(filterDto.Reference))
                    query = query.Where(t => t.Reference != null && t.Reference.Contains(filterDto.Reference));

                if (filterDto.TransactionDateFrom.HasValue)
                    query = query.Where(t => t.TransactionDate >= filterDto.TransactionDateFrom.Value);

                if (filterDto.TransactionDateTo.HasValue)
                    query = query.Where(t => t.TransactionDate <= filterDto.TransactionDateTo.Value);

                // Apply sorting
                query = filterDto.SortBy?.ToLower() switch
                {
                    "type" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(t => t.Type) : query.OrderBy(t => t.Type),
                    "quantity" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(t => t.Quantity) : query.OrderBy(t => t.Quantity),
                    "reference" => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(t => t.Reference) : query.OrderBy(t => t.Reference),
                    _ => filterDto.SortDirection?.ToLower() == "desc" ? query.OrderByDescending(t => t.TransactionDate) : query.OrderBy(t => t.TransactionDate)
                };

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var transactions = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .ToListAsync();

                // Map to DTOs
                var transactionDtos = transactions.Select(t =>
                {
                    var dto = _mapper.Map<InventoryTransactionDto>(t);
                    dto.ProductName = t.Inventory?.Product?.Name;
                    dto.ProductModel = t.Inventory?.Product?.Model;
                    dto.WarehouseName = t.Inventory?.Warehouse?.Name;
                    dto.UserName = t.User?.Username;
                    return dto;
                }).ToList();

                var pagedResult = PagedResult<InventoryTransactionDto>.Create(
                    transactionDtos,
                    totalCount,
                    filterDto.PageNumber,
                    filterDto.PageSize);

                _logger.LogInformation("Retrieved {Count} transactions out of {Total}", transactionDtos.Count, totalCount);
                return CustomResponseDto<PagedResult<InventoryTransactionDto>>.Success(200, pagedResult, "Transactions retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions");
                return CustomResponseDto<PagedResult<InventoryTransactionDto>>.InternalServerError("An error occurred while retrieving transactions");
            }
        }

        public async Task CreateInventoryTransactionAsync(CreateInventoryTransactionDto transactionDto)
        {
            try
            {
                _logger.LogInformation("Creating inventory transaction for InventoryId: {InventoryId}", transactionDto.InventoryId);

                var transaction = new InventoryTransaction(
                    transactionDto.InventoryId,
                    transactionDto.Type,
                    transactionDto.Quantity,
                    transactionDto.QuantityBefore,
                    transactionDto.QuantityAfter,
                    transactionDto.Reference,
                    transactionDto.Reason,
                    transactionDto.UserId);

                transaction.UpdateDetails(transactionDto.Reason, transactionDto.Notes,
                    transactionDto.UnitCost, transactionDto.ExternalReference,
                    transactionDto.BatchNumber, transactionDto.ExpiryDate);

                var transactionRepo = _unitOfWork.WriteRepository<InventoryTransaction>();
                await transactionRepo.AddAsync(transaction);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Inventory transaction created successfully with ID: {TransactionId}", transaction.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inventory transaction for InventoryId: {InventoryId}", transactionDto.InventoryId);
                throw;
            }
        }

        #endregion
    }
}
