﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Customer;

public class Customer : BaseEntity
{
    public int? StoreId { get; set; } = 0;
    public int? CustomerStoreId { get; set; } = 0;
    public int? CustomerGroupId { get; set; }
    public int? CustomerBazaarId { get; set; } = 0;
    public int? BazaarId { get; set; } = 0;
    [MaxLength(100)] public string? Firstname { get; set; }
    [MaxLength(100)] public string? Lastname { get; set; }
    [MaxLength(20)] public string? Telephone { get; set; }
    [MaxLength(100)] public string? Email { get; set; }
    public bool Status { get; set; } = true;
    public ICollection<Address>? Addresses { get; set; }
    [MaxLength(500)] public string? AccountCustomField { get; set; }
    public int OrderCount { get; set; }
    public Todo? Todo { get; set; }

}

