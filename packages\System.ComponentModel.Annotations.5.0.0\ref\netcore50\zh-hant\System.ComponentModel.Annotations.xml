﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.Annotations</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.DataAnnotations.AssociationAttribute">
      <summary>指定實體成員表示某種資料關聯性，例如外部索引鍵關聯性。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.AssociationAttribute.#ctor(System.String,System.String,System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.AssociationAttribute" /> 類別的新執行個體。</summary>
      <param name="name">關聯的名稱。</param>
      <param name="thisKey">關聯的 <paramref name="thisKey" /> 一端，索引鍵值之屬性名稱的逗號分隔清單。</param>
      <param name="otherKey">關聯的 <paramref name="otherKey" /> 一端，索引鍵值之屬性名稱的逗號分隔清單。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.IsForeignKey">
      <summary>取得或設定值，這個值表示關聯成員是否代表外部索引鍵。</summary>
      <returns>如果關聯表示外部索引鍵，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.Name">
      <summary>取得關聯的名稱。</summary>
      <returns>關聯的名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey">
      <summary>從關聯的 OtherKey 一端，取得索引鍵值的屬性名稱。</summary>
      <returns>屬性名稱的逗號分隔清單，表示關聯的 OtherKey 一端的索引鍵值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKeyMembers">
      <summary>取得 <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" /> 屬性中所指定個別索引鍵成員的集合。</summary>
      <returns>
        <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" /> 屬性中所指定個別索引鍵成員的集合。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey">
      <summary>從關聯的 ThisKey 一端，取得索引鍵值的屬性名稱。</summary>
      <returns>屬性名稱的逗號分隔清單，表示關聯的 ThisKey 一端的索引鍵值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKeyMembers">
      <summary>取得 <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" /> 屬性中所指定個別索引鍵成員的集合。</summary>
      <returns>
        <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" /> 屬性中所指定個別索引鍵成員的集合。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CompareAttribute">
      <summary>提供屬性 (Attribute)，來比較兩個屬性 (Property)。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.CompareAttribute" /> 類別的新執行個體。</summary>
      <param name="otherProperty">要與目前屬性比較的屬性。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.FormatErrorMessage(System.String)">
      <summary>根據發生錯誤所在的資料欄位，將格式套用至錯誤訊息。</summary>
      <returns>格式化的錯誤訊息。</returns>
      <param name="name">造成錯誤失敗之欄位的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>判斷指定的物件是否有效。</summary>
      <returns>如果 <paramref name="value" /> 有效則為 true，否則為 false。</returns>
      <param name="value">要驗證的物件。</param>
      <param name="validationContext">包含驗證要求相關資訊的物件。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherProperty">
      <summary>取得要與目前屬性比較的屬性。</summary>
      <returns>另一個屬性。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherPropertyDisplayName">
      <summary>取得其他屬性的顯示名稱。</summary>
      <returns>其他屬性的顯示名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.RequiresValidationContext">
      <summary>取得值，這個值表示屬性是否需要驗證內容。</summary>
      <returns>如果屬性需要驗證內容，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute">
      <summary>指定屬性參與開放式並行存取 (Optimistic Concurrency) 檢查。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CreditCardAttribute">
      <summary>指定資料欄位值為信用卡卡號。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.CreditCardAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.IsValid(System.Object)">
      <summary>判斷指定的信用卡號碼是否有效。</summary>
      <returns>如果信用卡號碼有效，則為 true，否則為 false。</returns>
      <param name="value">要驗證的值。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute">
      <summary>指定自訂驗證方法，此方法用來驗證屬性或類別執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.#ctor(System.Type,System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute" /> 類別的新執行個體。</summary>
      <param name="validatorType">包含會執行自訂驗證之方法的型別。</param>
      <param name="method">執行自訂驗證的方法。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.FormatErrorMessage(System.String)">
      <summary>格式化驗證錯誤訊息。</summary>
      <returns>格式化之錯誤訊息的執行個體。</returns>
      <param name="name">要包含在格式化訊息中的名稱。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.Method">
      <summary>取得驗證方法。</summary>
      <returns>驗證方法的名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.ValidatorType">
      <summary>取得會執行自訂驗證的型別。</summary>
      <returns>執行自訂驗證的型別。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataType">
      <summary>代表與資料欄位和參數相關聯之資料型別的列舉型別 (Enumeration)。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.CreditCard">
      <summary>表示信用卡卡號。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Currency">
      <summary>表示貨幣值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Custom">
      <summary>表示自訂資料型別。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Date">
      <summary>表示日期值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.DateTime">
      <summary>表示時間的瞬間，以一天的日期和時間表示。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Duration">
      <summary>表示物件存在的持續時間。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.EmailAddress">
      <summary>表示電子郵件地址。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Html">
      <summary>表示 HTML 檔。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.ImageUrl">
      <summary>表示影像的 URL。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.MultilineText">
      <summary>表示多行文字。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Password">
      <summary>表示密碼值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PhoneNumber">
      <summary>表示電話號碼值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PostalCode">
      <summary>表示郵遞區號。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Text">
      <summary>表示顯示的文字。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Time">
      <summary>表示時間值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Upload">
      <summary>表示檔案上傳資料型別。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Url">
      <summary>表示 URL 值。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataTypeAttribute">
      <summary>指定與資料欄位產生關聯的其他型別名稱。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.ComponentModel.DataAnnotations.DataType)">
      <summary>使用指定的型別名稱，初始化 <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="dataType">與資料欄位產生關聯的型別名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.String)">
      <summary>使用指定的欄位範本名稱，初始化 <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="customDataType">與資料欄位產生關聯的自訂欄位範本名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="customDataType" /> 為 null 或空字串 ("")。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.CustomDataType">
      <summary>取得與資料欄位相關聯的自訂欄位範本名稱。</summary>
      <returns>與資料欄位相關聯的自訂欄位範本名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DataType">
      <summary>取得與資料欄位相關聯的型別。</summary>
      <returns>其中一個 <see cref="T:System.ComponentModel.DataAnnotations.DataType" /> 值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DisplayFormat">
      <summary>取得資料欄位的顯示格式。</summary>
      <returns>資料欄位的顯示格式。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.GetDataTypeName">
      <summary>傳回與資料欄位相關聯的型別名稱。</summary>
      <returns>與資料欄位相關聯的型別名稱。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.IsValid(System.Object)">
      <summary>檢查資料欄位的值是否有效。</summary>
      <returns>一律為 true。</returns>
      <param name="value">要驗證的資料欄位值。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayAttribute">
      <summary>提供一般用途屬性，可讓您為實體部分類別的型別和成員指定可當地語系化的字串。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField">
      <summary>取得或設定值，這個值表示 UI 是否應該自動產生以顯示這個欄位。</summary>
      <returns>如果 UI 應該自動產生以顯示這個欄位，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在設定屬性值之前嘗試取得屬性值。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter">
      <summary>取得或設定值，這個值表示是否會針對此欄位自動顯示篩選 UI。</summary>
      <returns>如果 UI 應該自動產生以顯示這個欄位的篩選，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在設定屬性值之前嘗試取得屬性值。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description">
      <summary>取得或設定 UI 中用來顯示描述的值。</summary>
      <returns>UI 中用來顯示描述的值。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateField">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" /> 屬性值。</summary>
      <returns>如果 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" /> 屬性已初始化，則為屬性值，否則為 null。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateFilter">
      <summary>傳回值，這個值表示是否應該自動產生 UI 以顯示這個欄位的篩選。</summary>
      <returns>如果 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter" /> 屬性已初始化，則為屬性值，否則為 null。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetDescription">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 屬性值。</summary>
      <returns>如果已指定 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 而且 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 屬性表示資源索引鍵時，則為當地語系化的描述，否則為 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 屬性的非當地語系化值。</returns>
      <exception cref="T:System.InvalidOperationException">在 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性和 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 屬性都已初始化，但是在 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性中找不到名稱符合 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 值的公用靜態屬性。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetGroupName">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> 屬性值。</summary>
      <returns>如果 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> 已初始化，則為用來將 UI 欄位分組的值，否則為 null。如果已指定 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性而且 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> 屬性表示資源索引鍵時，則傳回當地語系化的字串，否則傳回非當地語系化的字串。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetName">
      <summary>傳回 UI 中用於欄位顯示的值。</summary>
      <returns>如果已指定 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性而且 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> 屬性表示資源索引鍵時，則為 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> 屬性的當地語系化字串，否則為 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> 屬性的非當地語系化值。</returns>
      <exception cref="T:System.InvalidOperationException">在 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性和 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> 屬性都已初始化，但是在 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性中找不到名稱符合 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> 值的公用靜態屬性。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetOrder">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" /> 屬性值。</summary>
      <returns>如果 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" /> 屬性已設定，則為此屬性的值，否則為 null。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetPrompt">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 屬性值。</summary>
      <returns>如果已指定 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性而且 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 屬性表示資源索引鍵時，則會取得 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 屬性的當地語系化字串，否則取得 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 屬性的非當地語系化值。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetShortName">
      <summary>傳回 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> 屬性值。</summary>
      <returns>如果已指定 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> 屬性而且 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> 屬性表示資源索引鍵時，則為 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> 屬性的當地語系化字串，否則為 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> 屬性的非當地語系化值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName">
      <summary>取得或設定用來將 UI 欄位分組的值。</summary>
      <returns>用來將 UI 欄位分組的值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name">
      <summary>取得或設定 UI 中用於顯示的值。</summary>
      <returns>UI 中用於顯示的值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order">
      <summary>取得或設定資料行的順序加權。</summary>
      <returns>資料行的順序加權。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt">
      <summary>取得或設定 UI 中用來設定提示浮水印的值。</summary>
      <returns>UI 中用來顯示浮水印的值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType">
      <summary>取得或設定型別，其中包含 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />、<see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />、<see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 和 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 等屬性的資源。</summary>
      <returns>包含 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />、<see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />、<see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> 和 <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> 屬性在內的資源型別。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName">
      <summary>取得或設定用於方格資料行標籤的值。</summary>
      <returns>用於方格資料行標籤的值。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute">
      <summary>指定所參考資料表中顯示的資料行為外部索引鍵資料行。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String)">
      <summary>使用指定的資料行，初始化 <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> 類別的新執行個體。</summary>
      <param name="displayColumn">做為顯示資料行的資料行名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String)">
      <summary>使用指定的顯示和排序資料行，初始化 <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> 類別的新執行個體。</summary>
      <param name="displayColumn">做為顯示資料行的資料行名稱。</param>
      <param name="sortColumn">用於排序的資料行名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String,System.Boolean)">
      <summary>使用指定的顯示資料行，以及指定的排序資料行和排序次序，初始化 <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> 類別的新執行個體。</summary>
      <param name="displayColumn">做為顯示資料行的資料行名稱。</param>
      <param name="sortColumn">用於排序的資料行名稱。</param>
      <param name="sortDescending">true 表示依遞減順序排序，否則為 false。預設為 false。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.DisplayColumn">
      <summary>取得用來做為顯示欄位的資料行名稱。</summary>
      <returns>顯示資料行的名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortColumn">
      <summary>取得用於排序的資料行名稱。</summary>
      <returns>排序資料行的名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortDescending">
      <summary>取得值，這個值指出要依遞減或遞增次序排序。</summary>
      <returns>如果資料行要依遞減次序排序，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute">
      <summary>指定 ASP.NET Dynamic Data 顯示和格式化資料欄位的方式。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ApplyFormatInEditMode">
      <summary>取得或設定值，這個值指出當資料欄位處於編輯模式時，<see cref="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString" /> 屬性指定的格式化字串是否套用至欄位值。</summary>
      <returns>如果格式化字串會套用至編輯模式下的欄位值，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ConvertEmptyStringToNull">
      <summary>取得或設定值，這個值指出在資料來源中更新資料欄位時，是否將空字串值 ("") 自動轉換為 null。</summary>
      <returns>如果空字串值會自動轉換為 null，則為 true，否則為 false。預設為 true。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString">
      <summary>取得或設定欄位值的顯示格式。</summary>
      <returns>格式化字串，指定資料欄位值的顯示格式。預設為空字串 ("")，表示未將特殊格式套用至該欄位值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.HtmlEncode">
      <summary>取得或設定值，這個值指出欄位是否應經過 HTML 編碼。</summary>
      <returns>如果欄位應該先經過 HTML 編碼則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.NullDisplayText">
      <summary>取得或設定欄位值為 null 時為欄位顯示的文字。</summary>
      <returns>文字，會在欄位值為 null 時為欄位顯示。預設為空字串 ("")，表示這個屬性未設定。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EditableAttribute">
      <summary>指出資料欄位是否可以編輯。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EditableAttribute.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.EditableAttribute" /> 類別的新執行個體。</summary>
      <param name="allowEdit">true 表示指定該欄位可以編輯，否則為 false。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowEdit">
      <summary>取得值，這個值指出欄位是否可以編輯。</summary>
      <returns>如果欄位可以編輯則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowInitialValue">
      <summary>取得或設定值，這個值指出初始值是否已啟用。</summary>
      <returns>如果初始值已啟用則為 true ，否則為 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute">
      <summary>驗證電子郵件地址。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.IsValid(System.Object)">
      <summary>判斷指定的值是否符合有效的電子郵件地址模式。</summary>
      <returns>如果指定的值有效或為 null，則為 true，否則為 false。</returns>
      <param name="value">要驗證的值。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute">
      <summary>讓 .NET Framework 列舉型別對應至資料行。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="enumType">列舉的型別。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.EnumType">
      <summary>取得或設定列舉型別。</summary>
      <returns>列舉型別。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.IsValid(System.Object)">
      <summary>檢查資料欄位的值是否有效。</summary>
      <returns>如果資料欄位值是有效的，則為 true，否則為 false。</returns>
      <param name="value">要驗證的資料欄位值。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute">
      <summary>驗證副檔名。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.Extensions">
      <summary>取得或設定副檔名。</summary>
      <returns>副檔名或預設副檔名 (".png"、".jpg"、".jpeg" 和 ".gif") （如果未設定屬性）。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.FormatErrorMessage(System.String)">
      <summary>根據發生錯誤所在的資料欄位，將格式套用至錯誤訊息。</summary>
      <returns>格式化的錯誤訊息。</returns>
      <param name="name">造成錯誤失敗之欄位的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.IsValid(System.Object)">
      <summary>檢查指定的檔案副檔名是否有效。</summary>
      <returns>如果副檔名有效，則為 true，否則為 false。</returns>
      <param name="value">有效副檔名的以逗號分隔的清單。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute">
      <summary>表示用來指定資料行篩選行為的屬性。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String)">
      <summary>使用篩選 UI 提示，初始化 <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="filterUIHint">用於篩選的控制項名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String)">
      <summary>使用篩選 UI 提示和展示層名稱，初始化 <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="filterUIHint">用於篩選的控制項名稱。</param>
      <param name="presentationLayer">支援此控制項的展示層名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>使用篩選 UI 提示、展示層名稱和控制項參數，初始化 <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="filterUIHint">用於篩選的控制項名稱。</param>
      <param name="presentationLayer">支援此控制項的展示層名稱。</param>
      <param name="controlParameters">控制項的參數清單。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.ControlParameters">
      <summary>取得控制項的建構函式中做為參數的名稱/值組。</summary>
      <returns>控制項的建構函式中做為參數的名稱/值組。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.Equals(System.Object)">
      <summary>傳回值，這個值指出這個屬性執行個體是否等於指定的物件。</summary>
      <returns>如果傳遞的物件與這個屬性執行個體相等則為 True，否則 false。</returns>
      <param name="obj">要與這個屬性執行個體比較的物件。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.FilterUIHint">
      <summary>取得用於篩選的控制項名稱。</summary>
      <returns>用於篩選的控制項名稱。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.GetHashCode">
      <summary>傳回這個屬性執行個體的雜湊程式碼。</summary>
      <returns>這個屬性執行個體的雜湊程式碼。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.PresentationLayer">
      <summary>取得支援此控制項之展示層的名稱。</summary>
      <returns>支援此控制項的展示層名稱。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.IValidatableObject">
      <summary>提供讓物件失效的方式。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.IValidatableObject.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>判斷指定的物件是否有效。</summary>
      <returns>存放驗證失敗之資訊的集合。</returns>
      <param name="validationContext">驗證內容。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.KeyAttribute">
      <summary>表示唯一識別實體的一個或多個屬性。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.KeyAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.KeyAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute">
      <summary>指定屬性中所允許之陣列或字串資料的最大長度。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor(System.Int32)">
      <summary>根據 <paramref name="length" /> 參數初始化 <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> 類別的新執行個體。</summary>
      <param name="length">陣列或字串資料所容許的最大長度。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.FormatErrorMessage(System.String)">
      <summary>套用格式至指定的錯誤訊息。</summary>
      <returns>描述可接受之最大長度的當地語系化字串。</returns>
      <param name="name">要包含在格式化字串中的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.IsValid(System.Object)">
      <summary>判斷指定的物件是否有效。</summary>
      <returns>如果此值為 null 或是小於或等於指定的最大長度，則為 true，否則為 false。</returns>
      <param name="value">要驗證的物件。</param>
      <exception cref="Sytem.InvalidOperationException">長度為零或小於負一。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MaxLengthAttribute.Length">
      <summary>取得陣列或字串資料所容許的最大長度。</summary>
      <returns>陣列或字串資料所容許的最大長度。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MinLengthAttribute">
      <summary>指定屬性中所允許之陣列或字串資料的最小長度。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.MinLengthAttribute" /> 類別的新執行個體。</summary>
      <param name="length">陣列或字串資料的長度。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.FormatErrorMessage(System.String)">
      <summary>套用格式至指定的錯誤訊息。</summary>
      <returns>描述可接受之最小長度的當地語系化字串。</returns>
      <param name="name">要包含在格式化字串中的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.IsValid(System.Object)">
      <summary>判斷指定的物件是否有效。</summary>
      <returns>如果指定的物件有效，則為 true，否則為 false。</returns>
      <param name="value">要驗證的物件。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MinLengthAttribute.Length">
      <summary>取得或設定陣列或字串資料允許的最小長度。</summary>
      <returns>陣列或字串資料所容許的最小長度。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.PhoneAttribute">
      <summary>電話號碼使用規則運算式，指定資料欄位值為語式正確的電話號碼。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.PhoneAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.IsValid(System.Object)">
      <summary>判斷指定的電話號碼是否為有效的電話號碼格式。</summary>
      <returns>如果電話號碼有效，則為 true，否則為 false。</returns>
      <param name="value">要驗證的值。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RangeAttribute">
      <summary>指定資料欄位值的數值範圍條件約束。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Double,System.Double)">
      <summary>使用指定的最大值和最小值，初始化 <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> 類別的新執行個體。</summary>
      <param name="minimum">指定資料欄位值允許的最小值。</param>
      <param name="maximum">指定資料欄位值允許的最大值。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Int32,System.Int32)">
      <summary>使用指定的最大值和最小值，初始化 <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> 類別的新執行個體。</summary>
      <param name="minimum">指定資料欄位值允許的最小值。</param>
      <param name="maximum">指定資料欄位值允許的最大值。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Type,System.String,System.String)">
      <summary>使用指定的最大值、最小值和特定型別，初始化 <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> 類別的新執行個體。</summary>
      <param name="type">指定要測試的物件型別。</param>
      <param name="minimum">指定資料欄位值允許的最小值。</param>
      <param name="maximum">指定資料欄位值允許的最大值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.FormatErrorMessage(System.String)">
      <summary>格式化在範圍驗證失敗時所顯示的錯誤訊息。</summary>
      <returns>格式化的錯誤訊息。</returns>
      <param name="name">造成錯誤失敗之欄位的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.IsValid(System.Object)">
      <summary>檢查資料欄位的值是否在指定的範圍內。</summary>
      <returns>如果指定的值在範圍內，則為 true，否則為 false。</returns>
      <param name="value">要驗證的資料欄位值。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">資料欄位值超出允許的範圍。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Maximum">
      <summary>取得允許的最大欄位值。</summary>
      <returns>資料欄位允許的最大值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Minimum">
      <summary>取得允許的最小欄位值。</summary>
      <returns>資料欄位允許的最小值。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.OperandType">
      <summary>取得必須驗證其值的資料欄位型別。</summary>
      <returns>必須驗證其值的資料欄位型別。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute">
      <summary>指定 ASP.NET Dynamic Data 中的資料欄位值必須符合指定的規則運算式 (Regular Expression)。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute" /> 類別的新執行個體。</summary>
      <param name="pattern">用來驗證資料欄位值的規則運算式。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.FormatErrorMessage(System.String)">
      <summary>格式化要在規則運算式驗證失敗時顯示的錯誤訊息。</summary>
      <returns>格式化的錯誤訊息。</returns>
      <param name="name">造成錯誤失敗之欄位的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.IsValid(System.Object)">
      <summary>檢查使用者輸入的值是否符合規則運算式模式。</summary>
      <returns>如果驗證成功，則為 true，否則為 false。</returns>
      <param name="value">要驗證的資料欄位值。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">資料欄位值不符合規則運算式模式。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.Pattern">
      <summary>取得規則運算式模式。</summary>
      <returns>須符合的模式。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RequiredAttribute">
      <summary>指出需要使用資料欄位值。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.RequiredAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RequiredAttribute.AllowEmptyStrings">
      <summary>取得或設定值，這個值指出是否允許空字串。</summary>
      <returns>如果允許空字串則為 true，否則為 false。預設值是 false。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.IsValid(System.Object)">
      <summary>檢查必要資料欄位的值是否不為空白。</summary>
      <returns>如果驗證成功，則為 true，否則為 false。</returns>
      <param name="value">要驗證的資料欄位值。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">資料欄位值為 null。</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute">
      <summary>指定類別或資料行是否使用 Scaffolding。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.#ctor(System.Boolean)">
      <summary>使用 <see cref="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold" /> 屬性，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute" /> 的新執行個體。</summary>
      <param name="scaffold">指定是否啟用 Scaffolding 的值。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold">
      <summary>取得或設定值，這個值指定是否啟用 Scaffolding。</summary>
      <returns>如果啟用 Scaffolding，則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.StringLengthAttribute">
      <summary>指定資料欄位中允許的最小和最大字元長度。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.#ctor(System.Int32)">
      <summary>使用指定的最大長度，初始化 <see cref="T:System.ComponentModel.DataAnnotations.StringLengthAttribute" /> 類別的新執行個體。</summary>
      <param name="maximumLength">字串的長度上限。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.FormatErrorMessage(System.String)">
      <summary>套用格式至指定的錯誤訊息。</summary>
      <returns>格式化的錯誤訊息。</returns>
      <param name="name">造成錯誤失敗之欄位的名稱。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> 為負值。-或-<paramref name="maximumLength" /> 小於 <paramref name="minimumLength" />。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(System.Object)">
      <summary>判斷指定的物件是否有效。</summary>
      <returns>如果指定的物件有效，則為 true，否則為 false。</returns>
      <param name="value">要驗證的物件。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> 為負值。-或-<paramref name="maximumLength" /> 小於 <see cref="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength" />。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MaximumLength">
      <summary>取得或設定字串的最大長度。</summary>
      <returns>字串的最大長度。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength">
      <summary>取得或設定字串的長度下限。</summary>
      <returns>字串的最小長度。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.TimestampAttribute">
      <summary>將資料行的資料型別指定為資料列版本。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.TimestampAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.TimestampAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UIHintAttribute">
      <summary>指定 Dynamic Data 用來顯示資料欄位的範本或使用者控制項。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String)">
      <summary>使用指定的使用者控制項，初始化 <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="uiHint">用來顯示資料欄位的使用者控制項。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String)">
      <summary>使用指定的使用者控制項和指定的展示層，初始化 <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="uiHint">用來顯示資料欄位的使用者控制項 (欄位範本)。</param>
      <param name="presentationLayer">使用此類別的展示層。可以設定為 "HTML"、"Silverlight"、"WPF" 或 "WinForms"。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>使用指定的使用者控制項、展示層和控制項參數，初始化 <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> 類別的新執行個體。</summary>
      <param name="uiHint">用來顯示資料欄位的使用者控制項 (欄位範本)。</param>
      <param name="presentationLayer">使用此類別的展示層。可以設定為 "HTML"、"Silverlight"、"WPF" 或 "WinForms"。</param>
      <param name="controlParameters">用來從任何資料來源擷取值的物件。</param>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> 為 null，否則就是條件約束索引鍵。-或-<see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> 的值不是字串。</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters">
      <summary>取得或設定用來從任何資料來源擷取值的 <see cref="T:System.Web.DynamicData.DynamicControlParameter" /> 物件。</summary>
      <returns>索引鍵/值組的集合。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.Equals(System.Object)">
      <summary>取得值，這個值表示這個執行個體是否等於指定的物件。</summary>
      <returns>如果指定的物件等於這個執行個體則為 true，否則為 false。</returns>
      <param name="obj">要與這個執行個體進行比較的物件，或者 null 參考。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.GetHashCode">
      <summary>取得目前屬性之執行個體的雜湊程式碼。</summary>
      <returns>這個屬性執行個體的雜湊程式碼。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.PresentationLayer">
      <summary>取得或設定使用 <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> 類別的展示層。</summary>
      <returns>此類別所使用的展示層。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.UIHint">
      <summary>取得或設定用來顯示資料欄位的欄位範本名稱。</summary>
      <returns>顯示資料欄位的欄位範本名稱。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UrlAttribute">
      <summary>提供 URL 驗證。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.#ctor">
      <summary>會初始化 <see cref="T:System.ComponentModel.DataAnnotations.UrlAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.IsValid(System.Object)">
      <summary>驗證所指定 URL 的格式。</summary>
      <returns>如果 URL 格式有效或為 null 則為 true，否則為 false。</returns>
      <param name="value">要驗證的 URL。</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationAttribute">
      <summary>做為所有驗證屬性的基底類別 (Base Class)。</summary>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">已當地語系化錯誤訊息的 <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> 和 <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName" /> 屬性會在設定未當地語系化的 <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage" /> 屬性錯誤訊息時同時設定。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.Func{System.String})">
      <summary>使用會啟用驗證資源存取的函式，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別的新執行個體。</summary>
      <param name="errorMessageAccessor">啟用驗證資源存取的函式。</param>
      <exception cref="T:System:ArgumentNullException">
        <paramref name="errorMessageAccessor" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.String)">
      <summary>使用要與驗證控制項關聯的錯誤訊息，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別的新執行個體。</summary>
      <param name="errorMessage">要與驗證控制項關聯的錯誤訊息。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage">
      <summary>取得或設定錯誤訊息，此錯誤訊息會在驗證失敗時與驗證控制項產生關聯。</summary>
      <returns>與驗證控制項相關聯的錯誤訊息。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName">
      <summary>取得或設定要在驗證失敗時用來查閱 <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> 屬性值的錯誤訊息資源名稱。</summary>
      <returns>與驗證控制項相關聯的錯誤訊息資源。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType">
      <summary>取得或設定資源類型，此類型可在驗證失敗時用於查閱錯誤訊息。</summary>
      <returns>與驗證控制項相關聯的錯誤訊息類型。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageString">
      <summary>取得當地語系化的驗證錯誤訊息。</summary>
      <returns>當地語系化的驗證錯誤訊息。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.FormatErrorMessage(System.String)">
      <summary>根據發生錯誤所在的資料欄位，將格式套用至錯誤訊息。</summary>
      <returns>格式化之錯誤訊息的執行個體。</returns>
      <param name="name">要包含在格式化訊息中的名稱。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>檢查指定的值在目前的驗證屬性方面是否有效。</summary>
      <returns>
        <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 類別的執行個體。</returns>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">有關驗證作業的內容資訊。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object)">
      <summary>判斷指定的物件值是否有效。</summary>
      <returns>如果指定的值有效，則為 true，否則為 false。</returns>
      <param name="value">要驗證的物件值。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>根據目前的驗證屬性，驗證指定的值。</summary>
      <returns>
        <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 類別的執行個體。</returns>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">有關驗證作業的內容資訊。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.RequiresValidationContext">
      <summary>取得值，這個值表示屬性是否需要驗證內容。</summary>
      <returns>如果屬性需要驗證內容，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>驗證指定的物件。</summary>
      <param name="value">要驗證的物件。</param>
      <param name="validationContext">
        <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> 物件，該物件描述會在其中執行驗證檢查的內容。這個參數不可以是 null。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">驗證失敗。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.String)">
      <summary>驗證指定的物件。</summary>
      <param name="value">要驗證的物件值。</param>
      <param name="name">要包含在錯誤訊息中的名稱。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="value" /> 無效。</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationContext">
      <summary>描述要在其中執行驗證檢查的內容。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object)">
      <summary>使用指定的物件執行個體，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> 類別的新執行個體</summary>
      <param name="instance">要驗證的物件執行個體。不可為 null。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>使用指定的物件和選擇性屬性包，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> 類別的新執行個體。</summary>
      <param name="instance">要驗證的物件執行個體。不可為 null</param>
      <param name="items">要提供給取用者的選擇性索引鍵/值組集合。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.IServiceProvider,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>使用服務提供者和服務取用者的字典，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> 類別的新執行個體。</summary>
      <param name="instance">要驗證的物件。這是必要參數。</param>
      <param name="serviceProvider">實作 <see cref="T:System.IServiceProvider" /> 介面的物件。這是選擇性參數。</param>
      <param name="items">要提供給服務取用者之索引鍵/值組的字典。這是選擇性參數。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.DisplayName">
      <summary>取得或設定要驗證之成員的名稱。</summary>
      <returns>要驗證之成員的名稱。</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.GetService(System.Type)">
      <summary>傳回提供自訂驗證的服務。</summary>
      <returns>服務的執行個體；如果無法使用服務，則為 null。</returns>
      <param name="serviceType">要用於驗證的服務類型。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.InitializeServiceProvider(System.Func{System.Type,System.Object})">
      <summary>使用服務提供者初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" />；呼叫 GetService 時，這個服務提供者會依類型傳回服務執行個體。</summary>
      <param name="serviceProvider">服務提供者。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.Items">
      <summary>取得與這個內容關聯之索引鍵/值組的字典。</summary>
      <returns>這個內容之索引鍵/值組的字典。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName">
      <summary>取得或設定要驗證之成員的名稱。</summary>
      <returns>要驗證之成員的名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectInstance">
      <summary>取得要驗證的物件。</summary>
      <returns>要驗證的物件。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectType">
      <summary>取得要驗證之物件的類型。</summary>
      <returns>要驗證之物件的型別。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationException">
      <summary>表示使用 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別驗證資料欄位時發生的例外狀況 (Exception)。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor">
      <summary>使用系統產生的錯誤訊息，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.ComponentModel.DataAnnotations.ValidationResult,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>使用驗證結果、驗證屬性以及目前例外狀況的值，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> 類別的新執行個體。</summary>
      <param name="validationResult">驗證結果的清單。</param>
      <param name="validatingAttribute">造成目前例外狀況的屬性。</param>
      <param name="value">造成此屬性觸發驗證錯誤的物件值。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> 類別的新執行個體。</summary>
      <param name="message">陳述錯誤的指定訊息。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>使用指定的錯誤訊息、驗證屬性 (Attribute) 以及目前例外狀況的值，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> 類別的新執行個體。</summary>
      <param name="errorMessage">陳述錯誤的訊息。</param>
      <param name="validatingAttribute">造成目前例外狀況的屬性。</param>
      <param name="value">造成此屬性觸發驗證錯誤的物件值。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和內部例外狀況執行個體集合，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息。</param>
      <param name="innerException">驗證例外狀況的集合。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationAttribute">
      <summary>取得觸發此例外狀況之 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別的執行個體。</summary>
      <returns>觸發此例外狀況之驗證屬性型別的執行個體。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult">
      <summary>取得描述驗證錯誤的 <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> 執行個體。</summary>
      <returns>描述驗證錯誤的 <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> 執行個體。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.Value">
      <summary>取得造成 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別觸發此例外狀況之物件的值。</summary>
      <returns>造成 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 類別觸發驗證錯誤之物件的值。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationResult">
      <summary>表示驗證要求結果的容器。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.ComponentModel.DataAnnotations.ValidationResult)">
      <summary>使用 <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 物件，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 類別的新執行個體。</summary>
      <param name="validationResult">驗證結果物件。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String)">
      <summary>使用錯誤訊息，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 類別的新執行個體。</summary>
      <param name="errorMessage">錯誤訊息。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>使用錯誤訊息以及有驗證錯誤的成員清單，初始化 <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> 類別的新執行個體。</summary>
      <param name="errorMessage">錯誤訊息。</param>
      <param name="memberNames">有驗證錯誤的成員名稱清單。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.ErrorMessage">
      <summary>取得驗證的錯誤訊息。</summary>
      <returns>驗證的錯誤訊息。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.MemberNames">
      <summary>取得成員名稱集合，這些成員表示哪些欄位有驗證錯誤。</summary>
      <returns>表示哪些欄位有驗證錯誤的成員名稱集合。</returns>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.ValidationResult.Success">
      <summary>表示驗證成功 (若驗證成功則為 true，否則為  false)。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.ToString">
      <summary>傳回目前驗證結果的字串表示。</summary>
      <returns>目前的驗證結果。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Validator">
      <summary>定義 Helper 類別，包含在相關聯的 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 屬性內時，可用來驗證物件、屬性和方法。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>使用驗證內容和驗證結果集合，判斷指定的物件是否有效。</summary>
      <returns>如果物件有效則為 true，否則為 false。</returns>
      <param name="instance">要驗證的物件。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <param name="validationResults">用來存放每一個失敗驗證的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Boolean)">
      <summary>使用驗證內容、驗證結果集合以及指定是否驗證所有屬性的值，判斷指定的物件是否有效。</summary>
      <returns>如果物件有效則為 true，否則為 false。</returns>
      <param name="instance">要驗證的物件。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <param name="validationResults">用來存放每一個失敗驗證的集合。</param>
      <param name="validateAllProperties">true 表示要驗證所有的屬性，如果為 false 則只驗證必要的屬性。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>驗證屬性。</summary>
      <returns>如果屬性有效則為 true，否則為 false。</returns>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">描述要驗證之屬性的內容。</param>
      <param name="validationResults">用來存放每一個失敗驗證的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 無法指派給屬性。-或-<paramref name="value " />為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>傳回值，這個值指出包含指定屬性的指定值是否有效。</summary>
      <returns>如果物件有效則為 true，否則為 false。</returns>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <param name="validationResults">存放失敗驗證的集合。</param>
      <param name="validationAttributes">驗證屬性。</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>使用驗證內容，判斷指定的物件是否有效。</summary>
      <param name="instance">要驗證的物件。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">物件不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Boolean)">
      <summary>使用驗證內容以及指定是否驗證所有屬性的值，判斷指定的物件是否有效。</summary>
      <param name="instance">要驗證的物件。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <param name="validateAllProperties">true 表示驗證所有屬性，否則為 false。</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="instance" /> 無效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>驗證屬性。</summary>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">描述要驗證之屬性的內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 無法指派給屬性。</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="value" /> 參數無效。</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>驗證指定的屬性。</summary>
      <param name="value">要驗證的值。</param>
      <param name="validationContext">內容，可描述要驗證的物件。</param>
      <param name="validationAttributes">驗證屬性。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validationContext" /> 參數為 null。</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="value" /> 參數不會以 <paramref name="validationAttributes" /> 參數驗證。</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute">
      <summary>表示資料庫資料行屬性對應。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" /> 類別的新執行個體。</summary>
      <param name="name">此屬性所對應的資料行名稱。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Name">
      <summary>取得屬性對應資料行名稱。</summary>
      <returns>此屬性所對應的資料行名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Order">
      <summary>取得或設定資料行的以零起始的命令屬性對應。</summary>
      <returns>資料行的順序。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.TypeName">
      <summary>取得或設定資料行的資料庫提供者特定資料型別的屬性對應。</summary>
      <returns>此屬性所對應之資料行的資料庫提供者特有資料型別。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute">
      <summary>表示此類別為複雜型別。複雜型別是實體型別的非純量屬性，可讓純量屬性得以在實體內組織。複雜型別沒有索引鍵而且無法由 Entity Framework 所管理 (除了父物件以外)。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute">
      <summary>指定資料庫如何產生屬性的值。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.#ctor(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute" /> 類別的新執行個體。</summary>
      <param name="databaseGeneratedOption">資料庫產生的選項。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.DatabaseGeneratedOption">
      <summary>取得或設定用於的樣式產生屬性值在資料庫。</summary>
      <returns>資料庫產生的選項。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption">
      <summary>表示用於的樣式建立一個屬性的值是在資料庫中。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Computed">
      <summary>當插入或更新資料列時，資料庫會產生值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity">
      <summary>當插入資料列時，資料庫會產生值。</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.None">
      <summary>資料庫不會產生值。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute">
      <summary>表示在關聯性中當做外部索引鍵使用的屬性。此註釋可能會放在外部索引鍵屬性上並指定關聯的導覽屬性名稱，或是放在導覽屬性上並指定關聯的外部索引鍵名稱。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute" /> 類別的新執行個體。</summary>
      <param name="name">如果您將 ForeigKey 屬性加入至外部索引鍵屬性，您應該指定相關聯的導覽屬性名稱。如果您將 ForeigKey 屬性加入至導覽屬性，您應該指定相關聯的外部索引鍵名稱。如果導覽屬性有多個外部索引鍵，請使用逗號來分隔外部索引鍵名稱清單。如需詳細資訊，請參閱 Code First 資料註解。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.Name">
      <summary>如果您將 ForeigKey 屬性加入至外部索引鍵屬性，您應該指定相關聯的導覽屬性名稱。如果您將 ForeigKey 屬性加入至導覽屬性，您應該指定相關聯的外部索引鍵名稱。如果導覽屬性有多個外部索引鍵，請使用逗號來分隔外部索引鍵名稱清單。如需詳細資訊，請參閱 Code First 資料註解。</summary>
      <returns>關聯的導覽屬性或關聯的外部索引鍵屬性名稱。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute">
      <summary>指定導覽屬性的反向，表示相同關聯性的另一端。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.#ctor(System.String)">
      <summary>使用指定的屬性，初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute" /> 類別的新執行個體。</summary>
      <param name="property">表示相同關聯性之另一端的導覽屬性。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.Property">
      <summary>取得表示相同關聯性另一端的巡覽屬性。</summary>
      <returns>屬性 (Attribute) 的屬性 (Property)。</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute">
      <summary>表示應該從資料庫對應中排除屬性或類別。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute">
      <summary>指定類別所對應的資料庫資料表。</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.TableAttribute.#ctor(System.String)">
      <summary>使用指定的資料表名稱，初始化 <see cref="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute" /> 類別的新執行個體。</summary>
      <param name="name">此類別所對應的資料表名稱。</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Name">
      <summary>取得類別所對應的資料表名稱。</summary>
      <returns>此類別所對應的資料表名稱。</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Schema">
      <summary>取得或設定類別所對應之資料表的結構描述。</summary>
      <returns>此類別所對應之資料表的結構描述。</returns>
    </member>
  </members>
</doc>