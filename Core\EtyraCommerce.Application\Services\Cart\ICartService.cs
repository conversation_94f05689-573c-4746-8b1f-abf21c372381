using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;

namespace EtyraCommerce.Application.Services.Cart
{
    /// <summary>
    /// Cart service interface for MediatR command/query dispatch
    /// </summary>
    public interface ICartService
    {
        #region Cart Management

        /// <summary>
        /// Adds an item to cart
        /// </summary>
        Task<CustomResponseDto<CartDto>> AddToCartAsync(AddToCartDto addToCartDto, Guid? customerId = null);

        /// <summary>
        /// Updates cart item quantity
        /// </summary>
        Task<CustomResponseDto<CartDto>> UpdateCartItemAsync(UpdateCartItemDto updateCartItemDto, Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Removes an item from cart
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> RemoveFromCartAsync(RemoveFromCartDto removeFromCartDto, Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Clears all items from cart
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ClearCartAsync(Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Merges guest cart to user cart
        /// </summary>
        Task<CustomResponseDto<CartDto>> MergeCartAsync(MergeCartDto mergeCartDto);

        #endregion

        #region Cart Retrieval

        /// <summary>
        /// Gets cart with all items
        /// </summary>
        Task<CustomResponseDto<CartDto>> GetCartAsync(Guid? customerId = null, string? sessionId = null, bool includeInactive = false, bool includeExpired = false);

        /// <summary>
        /// Gets cart summary without detailed items
        /// </summary>
        Task<CustomResponseDto<CartSummaryDto>> GetCartSummaryAsync(Guid? customerId = null, string? sessionId = null);

        #endregion
    }
}
