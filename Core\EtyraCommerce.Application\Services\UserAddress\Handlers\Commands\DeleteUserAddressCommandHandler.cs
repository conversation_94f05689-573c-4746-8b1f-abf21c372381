using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Commands
{
    /// <summary>
    /// Handler for deleting a user address
    /// </summary>
    public class DeleteUserAddressCommandHandler : IRequestHandler<DeleteUserAddressCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<DeleteUserAddressCommandHandler> _logger;

        public DeleteUserAddressCommandHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<DeleteUserAddressCommandHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(DeleteUserAddressCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling DeleteUserAddressCommand for AddressId: {AddressId}, UserId: {UserId}, HardDelete: {HardDelete}",
                    request.AddressId, request.UserId, request.HardDelete);

                // Validate request
                if (request.AddressId == Guid.Empty)
                {
                    _logger.LogWarning("DeleteUserAddressCommand received with empty AddressId for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<NoContentDto>.BadRequest("Address ID is required");
                }

                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("DeleteUserAddressCommand received with empty UserId for AddressId: {AddressId}", request.AddressId);
                    return CustomResponseDto<NoContentDto>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessDeleteUserAddressAsync(request.AddressId, request.UserId, request.HardDelete);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("DeleteUserAddressCommand handled successfully for AddressId: {AddressId}, UserId: {UserId}",
                        request.AddressId, request.UserId);
                }
                else
                {
                    _logger.LogWarning("DeleteUserAddressCommand failed for AddressId: {AddressId}, UserId: {UserId}, Error: {Error}",
                        request.AddressId, request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling DeleteUserAddressCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the address");
            }
        }
    }
}
