using EtyraCommerce.Domain.Entities.Category;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Category entity
    /// </summary>
    public class CategoryConfiguration : AuditableBaseEntityConfiguration<Category>
    {
        public override void Configure(EntityTypeBuilder<Category> builder)
        {
            // Apply base configuration (BaseEntity + AuditableBaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("categories", "etyra_core");

            #region Properties

            // Name
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasColumnType("text")
                .IsRequired(false);

            // Slug
            builder.Property(x => x.Slug)
                .HasColumnName("slug")
                .HasMaxLength(200)
                .IsRequired();

            // Parent Category ID
            builder.Property(x => x.ParentCategoryId)
                .HasColumnName("parent_category_id")
                .IsRequired(false);

            // Image URL
            builder.Property(x => x.ImageUrl)
                .HasColumnName("image_url")
                .HasMaxLength(500)
                .IsRequired(false);

            // Icon
            builder.Property(x => x.Icon)
                .HasColumnName("icon")
                .HasMaxLength(100)
                .IsRequired(false);

            // Sort Order
            builder.Property(x => x.SortOrder)
                .HasColumnName("sort_order")
                .HasDefaultValue(0)
                .IsRequired();

            // Is Active
            builder.Property(x => x.IsActive)
                .HasColumnName("is_active")
                .HasDefaultValue(true)
                .IsRequired();

            // Show In Menu
            builder.Property(x => x.ShowInMenu)
                .HasColumnName("show_in_menu")
                .HasDefaultValue(true)
                .IsRequired();

            // Meta Title
            builder.Property(x => x.MetaTitle)
                .HasColumnName("meta_title")
                .HasMaxLength(120)
                .IsRequired(false);

            // Meta Description
            builder.Property(x => x.MetaDescription)
                .HasColumnName("meta_description")
                .HasMaxLength(350)
                .IsRequired(false);

            // Meta Keywords
            builder.Property(x => x.MetaKeywords)
                .HasColumnName("meta_keywords")
                .HasMaxLength(200)
                .IsRequired(false);

            // Product Count
            builder.Property(x => x.ProductCount)
                .HasColumnName("product_count")
                .HasDefaultValue(0)
                .IsRequired();

            #endregion

            #region Navigation Properties

            // Parent Category (Self-referencing)
            builder.HasOne(x => x.ParentCategory)
                .WithMany(x => x.ChildCategories)
                .HasForeignKey(x => x.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete for hierarchy

            // Child Categories (One-to-Many)
            builder.HasMany(x => x.ChildCategories)
                .WithOne(x => x.ParentCategory)
                .HasForeignKey(x => x.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product Categories (One-to-Many)
            builder.HasMany(x => x.ProductCategories)
                .WithOne(x => x.Category)
                .HasForeignKey(x => x.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Category Descriptions (One-to-Many)
            builder.HasMany(x => x.Descriptions)
                .WithOne(x => x.Category)
                .HasForeignKey(x => x.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Unique Indexes
            builder.HasIndex(x => x.Slug)
                .IsUnique()
                .HasDatabaseName("ix_categories_slug_unique");

            // Performance Indexes
            builder.HasIndex(x => x.Name)
                .HasDatabaseName("ix_categories_name");

            builder.HasIndex(x => x.ParentCategoryId)
                .HasDatabaseName("ix_categories_parent_category_id");

            builder.HasIndex(x => x.IsActive)
                .HasDatabaseName("ix_categories_is_active");

            builder.HasIndex(x => x.ShowInMenu)
                .HasDatabaseName("ix_categories_show_in_menu");

            builder.HasIndex(x => x.SortOrder)
                .HasDatabaseName("ix_categories_sort_order");

            // Composite Indexes
            builder.HasIndex(x => new { x.ParentCategoryId, x.SortOrder })
                .HasDatabaseName("ix_categories_parent_sort");

            builder.HasIndex(x => new { x.IsActive, x.ShowInMenu })
                .HasDatabaseName("ix_categories_active_menu");

            builder.HasIndex(x => new { x.ParentCategoryId, x.IsActive })
                .HasDatabaseName("ix_categories_parent_active");

            // Hierarchical Queries
            builder.HasIndex(x => new { x.ParentCategoryId, x.IsActive, x.SortOrder })
                .HasDatabaseName("ix_categories_hierarchy");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_Categories_Name_NotEmpty",
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("CK_Categories_Slug_NotEmpty",
                "LENGTH(TRIM(slug)) > 0");

            builder.HasCheckConstraint("CK_Categories_SortOrder_NonNegative",
                "sort_order >= 0");

            builder.HasCheckConstraint("CK_Categories_ProductCount_NonNegative",
                "product_count >= 0");

            // Prevent self-referencing (category cannot be its own parent)
            builder.HasCheckConstraint("CK_Categories_NoSelfReference",
                "parent_category_id != id");

            #endregion
        }

        protected override string GetTableName() => "categories";
    }
}
