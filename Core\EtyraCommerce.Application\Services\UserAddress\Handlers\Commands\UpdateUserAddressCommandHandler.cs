using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Commands
{
    /// <summary>
    /// Handler for updating an existing user address
    /// </summary>
    public class UpdateUserAddressCommandHandler : IRequestHandler<UpdateUserAddressCommand, CustomResponseDto<UserAddressDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<UpdateUserAddressCommandHandler> _logger;

        public UpdateUserAddressCommandHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<UpdateUserAddressCommandHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserAddressDto>> Handle(UpdateUserAddressCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling UpdateUserAddressCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);

                // Validate request
                if (request.AddressId == Guid.Empty)
                {
                    _logger.LogWarning("UpdateUserAddressCommand received with empty AddressId for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<UserAddressDto>.BadRequest("Address ID is required");
                }

                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("UpdateUserAddressCommand received with empty UserId for AddressId: {AddressId}", request.AddressId);
                    return CustomResponseDto<UserAddressDto>.BadRequest("User ID is required");
                }

                // Create DTO from command properties
                var updateDto = new UpdateUserAddressDto
                {
                    Type = request.Type,
                    IsDefault = request.IsDefault,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    PhoneNumber = request.PhoneNumber,
                    Label = request.Label,
                    DeliveryInstructions = request.DeliveryInstructions,
                    IsActive = request.IsActive,
                    Street = request.Street,
                    AddressLine2 = request.AddressLine2,
                    City = request.City,
                    State = request.State,
                    PostalCode = request.PostalCode,
                    Country = request.Country,
                    CompanyName = request.CompanyName,
                    TaxNumber = request.TaxNumber,
                    TaxOffice = request.TaxOffice,
                    CompanyTitle = request.CompanyTitle,
                    IsCompanyAddress = request.IsCompanyAddress
                };

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessUpdateUserAddressAsync(request.AddressId, request.UserId, updateDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("UpdateUserAddressCommand handled successfully for AddressId: {AddressId}, UserId: {UserId}",
                        request.AddressId, request.UserId);
                }
                else
                {
                    _logger.LogWarning("UpdateUserAddressCommand failed for AddressId: {AddressId}, UserId: {UserId}, Error: {Error}",
                        request.AddressId, request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling UpdateUserAddressCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while updating the address");
            }
        }
    }
}
