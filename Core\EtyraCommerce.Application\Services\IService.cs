﻿using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Domain.Entities;
using System.Linq.Expressions;

namespace EtyraCommerce.Application.Services
{
    /// <summary>
    /// Generic service interface for CRUD operations
    /// </summary>
    /// <typeparam name="TEntity">Entity type that inherits from BaseEntity</typeparam>
    /// <typeparam name="TDto">DTO type for data transfer</typeparam>
    public interface IService<TEntity, TDto> where TEntity : BaseEntity where TDto : class
    {
        #region Read Operations

        /// <summary>
        /// Gets all entities
        /// </summary>
        Task<CustomResponseDto<IEnumerable<TDto>>> GetAllAsync(bool tracking = true);

        /// <summary>
        /// Gets entities matching the expression
        /// </summary>
        Task<CustomResponseDto<IEnumerable<TDto>>> GetWhereAsync(Expression<Func<TEntity, bool>> expression, bool tracking = true);

        /// <summary>
        /// Gets first entity matching the expression or null
        /// </summary>
        Task<CustomResponseDto<TDto?>> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> expression, bool tracking = true);

        /// <summary>
        /// Gets entity by ID or null if not found
        /// </summary>
        Task<CustomResponseDto<TDto?>> GetByIdAsync(Guid id, bool tracking = true);

        /// <summary>
        /// Checks if any entity matches the expression
        /// </summary>
        Task<CustomResponseDto<bool>> AnyAsync(Expression<Func<TEntity, bool>> expression, bool tracking = false);

        /// <summary>
        /// Gets count of entities matching the expression
        /// </summary>
        Task<CustomResponseDto<int>> CountAsync(Expression<Func<TEntity, bool>>? expression = null);

        /// <summary>
        /// Gets paginated results
        /// </summary>
        Task<CustomResponseDto<PagedResult<TDto>>> GetPagedAsync(int pageNumber, int pageSize, bool tracking = true);

        /// <summary>
        /// Gets paginated results matching the expression
        /// </summary>
        Task<CustomResponseDto<PagedResult<TDto>>> GetPagedWhereAsync(Expression<Func<TEntity, bool>> expression, int pageNumber, int pageSize, bool tracking = true);

        /// <summary>
        /// Searches entities with pagination
        /// </summary>
        Task<CustomResponseDto<PagedResult<TDto>>> SearchAsync(SearchRequest searchRequest);

        #endregion

        #region Write Operations

        /// <summary>
        /// Adds a new entity
        /// </summary>
        Task<CustomResponseDto<TDto>> AddAsync(TDto dto);

        /// <summary>
        /// Adds multiple entities
        /// </summary>
        Task<CustomResponseDto<IEnumerable<TDto>>> AddRangeAsync(IEnumerable<TDto> dtoList);

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        Task<CustomResponseDto<TDto>> UpdateAsync(TDto dto);

        /// <summary>
        /// Updates multiple entities
        /// </summary>
        Task<CustomResponseDto<IEnumerable<TDto>>> UpdateRangeAsync(IEnumerable<TDto> dtoList);

        /// <summary>
        /// Removes entity by ID (hard delete)
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> RemoveByIdAsync(Guid id);

        /// <summary>
        /// Removes entity (hard delete)
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> RemoveAsync(TDto dto);

        /// <summary>
        /// Removes multiple entities by IDs (hard delete)
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> RemoveRangeAsync(IEnumerable<Guid> ids);

        /// <summary>
        /// Soft deletes entity by ID
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> SoftDeleteByIdAsync(Guid id);

        /// <summary>
        /// Soft deletes entity
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> SoftDeleteAsync(TDto dto);

        /// <summary>
        /// Restores soft deleted entity by ID
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> RestoreByIdAsync(Guid id);

        #endregion

        #region Validation

        /// <summary>
        /// Validates DTO before operations
        /// </summary>
        Task<CustomResponseDto<ValidationResult>> ValidateAsync(TDto dto);

        /// <summary>
        /// Validates multiple DTOs
        /// </summary>
        Task<CustomResponseDto<ValidationResult>> ValidateRangeAsync(IEnumerable<TDto> dtoList);

        #endregion
    }
}