using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using MediatR;

namespace EtyraCommerce.Application.Services.Payment.Queries;

/// <summary>
/// Query to get a payment method by ID
/// </summary>
public class GetPaymentMethodByIdQuery : IRequest<CustomResponseDto<PaymentMethodDto>>
{
    /// <summary>
    /// Payment method ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to track changes for the entity
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetPaymentMethodByIdQuery(Guid id, bool tracking = false)
    {
        Id = id;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetPaymentMethodByIdQuery() { }
}
