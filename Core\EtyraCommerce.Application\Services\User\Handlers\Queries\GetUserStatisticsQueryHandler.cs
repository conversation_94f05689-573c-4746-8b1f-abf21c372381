using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserStatisticsQuery
    /// </summary>
    public class GetUserStatisticsQueryHandler : IRequestHandler<GetUserStatisticsQuery, CustomResponseDto<UserStatisticsDto>>
    {
        private readonly IUserService _userService;
        private readonly ILogger<GetUserStatisticsQueryHandler> _logger;

        public GetUserStatisticsQueryHandler(
            IUserService userService,
            ILogger<GetUserStatisticsQueryHandler> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserStatisticsDto>> Handle(GetUserStatisticsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get user statistics query with FromDate: {FromDate}, ToDate: {ToDate}",
                    request.FromDate, request.ToDate);

                // Validate date range
                if (request.FromDate.HasValue && request.ToDate.HasValue && request.FromDate > request.ToDate)
                {
                    _logger.LogWarning("Get user statistics query failed: Invalid date range - FromDate {FromDate} is after ToDate {ToDate}",
                        request.FromDate, request.ToDate);
                    return CustomResponseDto<UserStatisticsDto>.BadRequest("From date cannot be after to date");
                }

                // Use user service method for statistics
                var result = await _userService.GetUserStatisticsAsync();

                if (result.IsSuccess)
                {
                    _logger.LogDebug("Get user statistics query successful. Total users: {TotalUsers}, Active users: {ActiveUsers}",
                        result.Data?.TotalUsers, result.Data?.ActiveUsers);

                    // TODO: Apply date range filtering if specified
                    // TODO: Add detailed breakdown if requested
                }
                else
                {
                    _logger.LogWarning("Get user statistics query failed. Reason: {Message}", result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get user statistics query");
                return CustomResponseDto<UserStatisticsDto>.InternalServerError("An error occurred while retrieving user statistics");
            }
        }
    }
}
