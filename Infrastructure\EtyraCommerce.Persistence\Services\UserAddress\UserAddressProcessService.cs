using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Repositories.User;
using EtyraCommerce.Application.Repositories.UserAddress;
using EtyraCommerce.Application.Services.UserAddress;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.UserAddress
{
    /// <summary>
    /// Process service implementation for UserAddress operations
    /// Handles business logic and coordinates between handlers and repositories
    /// </summary>
    public class UserAddressProcessService : IUserAddressProcessService
    {
        private readonly IUserAddressReadRepository _userAddressReadRepository;
        private readonly IUserAddressWriteRepository _userAddressWriteRepository;
        private readonly IUserReadRepository _userReadRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<UserAddressProcessService> _logger;

        public UserAddressProcessService(
            IUserAddressReadRepository userAddressReadRepository,
            IUserAddressWriteRepository userAddressWriteRepository,
            IUserReadRepository userReadRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<UserAddressProcessService> logger)
        {
            _userAddressReadRepository = userAddressReadRepository;
            _userAddressWriteRepository = userAddressWriteRepository;
            _userReadRepository = userReadRepository;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        #region Command Operations

        public async Task<CustomResponseDto<UserAddressDto>> ProcessCreateUserAddressAsync(Guid userId, CreateUserAddressDto createDto)
        {
            try
            {
                _logger.LogInformation("Processing create user address for UserId: {UserId}", userId);

                // Validate user exists
                var userExists = await _userReadRepository.GetByIdAsync(userId);
                if (userExists == null)
                {
                    _logger.LogWarning("User not found for UserId: {UserId}", userId);
                    return CustomResponseDto<UserAddressDto>.NotFound("User not found");
                }

                // Create Address Value Object
                var address = new Address(
                    createDto.Street,
                    createDto.City,
                    createDto.State,
                    createDto.PostalCode,
                    createDto.Country,
                    createDto.AddressLine2);

                // Create PhoneNumber Value Object if provided
                PhoneNumber? phoneNumber = null;
                if (!string.IsNullOrWhiteSpace(createDto.PhoneNumber))
                {
                    try
                    {
                        phoneNumber = new PhoneNumber(createDto.PhoneNumber);
                    }
                    catch (ArgumentException ex)
                    {
                        _logger.LogWarning("Invalid phone number provided: {PhoneNumber}, Error: {Error}", createDto.PhoneNumber, ex.Message);
                        return CustomResponseDto<UserAddressDto>.BadRequest($"Invalid phone number: {ex.Message}");
                    }
                }

                // Create UserAddress entity
                var userAddress = new Domain.Entities.User.UserAddress(
                    userId,
                    address,
                    createDto.FirstName,
                    createDto.LastName,
                    (AddressType)createDto.Type,
                    createDto.Label,
                    createDto.IsDefault,
                    createDto.CompanyName,
                    createDto.TaxNumber,
                    createDto.TaxOffice,
                    createDto.CompanyTitle,
                    createDto.IsCompanyAddress);

                // Set optional properties
                if (phoneNumber != null)
                    userAddress.UpdatePhoneNumber(phoneNumber);

                if (!string.IsNullOrWhiteSpace(createDto.DeliveryInstructions))
                    userAddress.UpdateDeliveryInstructions(createDto.DeliveryInstructions);

                // Handle default address logic
                if (createDto.IsDefault)
                {
                    await HandleDefaultAddressLogicAsync(userId, null);
                }

                // Save to database
                await _userAddressWriteRepository.AddAsync(userAddress);
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var resultDto = _mapper.Map<UserAddressDto>(userAddress);

                _logger.LogInformation("User address created successfully for UserId: {UserId}, AddressId: {AddressId}", userId, userAddress.Id);
                return CustomResponseDto<UserAddressDto>.Success(resultDto, "Address created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user address for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while creating the address");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> ProcessUpdateUserAddressAsync(Guid addressId, Guid userId, UpdateUserAddressDto updateDto)
        {
            try
            {
                _logger.LogInformation("Processing update user address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                // Get existing address
                var existingAddress = await _userAddressReadRepository.GetByIdAsync(addressId);
                if (existingAddress == null)
                {
                    _logger.LogWarning("Address not found for AddressId: {AddressId}", addressId);
                    return CustomResponseDto<UserAddressDto>.NotFound("Address not found");
                }

                // Validate ownership
                if (existingAddress.UserId != userId)
                {
                    _logger.LogWarning("Address ownership validation failed for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                    return CustomResponseDto<UserAddressDto>.Forbidden("You don't have permission to update this address");
                }

                // Create new Address Value Object
                var newAddress = new Address(
                    updateDto.Street,
                    updateDto.City,
                    updateDto.State,
                    updateDto.PostalCode,
                    updateDto.Country,
                    updateDto.AddressLine2);

                // Update address information
                existingAddress.UpdateAddress(newAddress, updateDto.FirstName, updateDto.LastName, updateDto.Label);

                // Update phone number
                PhoneNumber? phoneNumber = null;
                if (!string.IsNullOrWhiteSpace(updateDto.PhoneNumber))
                {
                    try
                    {
                        phoneNumber = new PhoneNumber(updateDto.PhoneNumber);
                    }
                    catch (ArgumentException ex)
                    {
                        _logger.LogWarning("Invalid phone number provided: {PhoneNumber}, Error: {Error}", updateDto.PhoneNumber, ex.Message);
                        return CustomResponseDto<UserAddressDto>.BadRequest($"Invalid phone number: {ex.Message}");
                    }
                }
                existingAddress.UpdatePhoneNumber(phoneNumber);

                // Update delivery instructions
                existingAddress.UpdateDeliveryInstructions(updateDto.DeliveryInstructions);

                // Update address type
                existingAddress.Type = (AddressType)updateDto.Type;

                // Handle active status
                if (updateDto.IsActive != existingAddress.IsActive)
                {
                    if (updateDto.IsActive)
                        existingAddress.Activate();
                    else
                        existingAddress.Deactivate();
                }

                // Handle default address logic
                if (updateDto.IsDefault && !existingAddress.IsDefault)
                {
                    await HandleDefaultAddressLogicAsync(userId, addressId);
                    existingAddress.SetAsDefault();
                }
                else if (!updateDto.IsDefault && existingAddress.IsDefault)
                {
                    existingAddress.RemoveDefaultStatus();
                }

                // Save changes
                await _userAddressWriteRepository.UpdateAsync(existingAddress);
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO
                var resultDto = _mapper.Map<UserAddressDto>(existingAddress);

                _logger.LogInformation("User address updated successfully for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.Success(resultDto, "Address updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while updating the address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteUserAddressAsync(Guid addressId, Guid userId, bool hardDelete = false)
        {
            try
            {
                _logger.LogInformation("Processing delete user address for AddressId: {AddressId}, UserId: {UserId}, HardDelete: {HardDelete}",
                    addressId, userId, hardDelete);

                // Get existing address
                var existingAddress = await _userAddressReadRepository.GetByIdAsync(addressId);
                if (existingAddress == null)
                {
                    _logger.LogWarning("Address not found for AddressId: {AddressId}", addressId);
                    return CustomResponseDto<NoContentDto>.NotFound("Address not found");
                }

                // Validate ownership
                if (existingAddress.UserId != userId)
                {
                    _logger.LogWarning("Address ownership validation failed for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                    return CustomResponseDto<NoContentDto>.Forbidden("You don't have permission to delete this address");
                }

                // Check if this is the only address (business rule: user should have at least one address)
                var userAddressCount = await _userAddressReadRepository.GetUserAddressCountAsync(userId, includeInactive: true);

                if (userAddressCount <= 1)
                {
                    _logger.LogWarning("Cannot delete the only address for UserId: {UserId}", userId);
                    return CustomResponseDto<NoContentDto>.BadRequest("Cannot delete the only address. User must have at least one address.");
                }

                // Perform deletion
                if (hardDelete)
                {
                    await _userAddressWriteRepository.RemoveAsync(existingAddress);
                }
                else
                {
                    await _userAddressWriteRepository.SoftDeleteAsync(existingAddress);
                }

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User address deleted successfully for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "Address deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessSetDefaultAddressAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Processing set default address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                // Get existing address
                var existingAddress = await _userAddressReadRepository.GetByIdAsync(addressId);
                if (existingAddress == null)
                {
                    _logger.LogWarning("Address not found for AddressId: {AddressId}", addressId);
                    return CustomResponseDto<NoContentDto>.NotFound("Address not found");
                }

                // Validate ownership
                if (existingAddress.UserId != userId)
                {
                    _logger.LogWarning("Address ownership validation failed for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                    return CustomResponseDto<NoContentDto>.Forbidden("You don't have permission to modify this address");
                }

                // Check if address is active
                if (!existingAddress.IsActive)
                {
                    _logger.LogWarning("Cannot set inactive address as default for AddressId: {AddressId}", addressId);
                    return CustomResponseDto<NoContentDto>.BadRequest("Cannot set inactive address as default");
                }

                // Handle default address logic
                await HandleDefaultAddressLogicAsync(userId, addressId);
                existingAddress.SetAsDefault();

                // Save changes
                await _userAddressWriteRepository.UpdateAsync(existingAddress);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Default address set successfully for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "Default address set successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while setting the default address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessToggleAddressStatusAsync(Guid addressId, Guid userId, bool isActive)
        {
            try
            {
                _logger.LogInformation("Processing toggle address status for AddressId: {AddressId}, UserId: {UserId}, IsActive: {IsActive}",
                    addressId, userId, isActive);

                // Get existing address
                var existingAddress = await _userAddressReadRepository.GetByIdAsync(addressId);
                if (existingAddress == null)
                {
                    _logger.LogWarning("Address not found for AddressId: {AddressId}", addressId);
                    return CustomResponseDto<NoContentDto>.NotFound("Address not found");
                }

                // Validate ownership
                if (existingAddress.UserId != userId)
                {
                    _logger.LogWarning("Address ownership validation failed for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                    return CustomResponseDto<NoContentDto>.Forbidden("You don't have permission to modify this address");
                }

                // Toggle status
                if (isActive)
                    existingAddress.Activate();
                else
                    existingAddress.Deactivate();

                // Save changes
                await _userAddressWriteRepository.UpdateAsync(existingAddress);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Address status toggled successfully for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), $"Address {(isActive ? "activated" : "deactivated")} successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling address status for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while toggling the address status");
            }
        }

        #endregion

        #region Query Operations

        public async Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetUserAddressesAsync(Guid userId, int? addressType = null, bool? isActive = null, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Processing get user addresses for UserId: {UserId}, AddressType: {AddressType}, IsActive: {IsActive}",
                    userId, addressType, isActive);

                var addresses = await _userAddressReadRepository.GetUserAddressesAsync(userId, includeInactive);

                // Apply additional filters in memory (since repository method doesn't support all filters)
                if (addressType.HasValue)
                {
                    var targetType = (AddressType)addressType.Value;
                    addresses = addresses.Where(a => a.Type == targetType || a.Type == AddressType.Both).ToList();
                }

                if (isActive.HasValue)
                {
                    addresses = addresses.Where(a => a.IsActive == isActive.Value).ToList();
                }

                // Order by default first, then by creation date
                addresses = addresses.OrderByDescending(a => a.IsDefault).ThenByDescending(a => a.CreatedAt).ToList();
                var addressDtos = _mapper.Map<List<UserAddressDto>>(addresses);

                _logger.LogInformation("Retrieved {Count} addresses for UserId: {UserId}", addressDtos.Count, userId);
                return CustomResponseDto<List<UserAddressDto>>.Success(addressDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user addresses for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving addresses");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> ProcessGetUserAddressByIdAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Processing get user address by ID for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                var address = await _userAddressReadRepository.GetUserAddressByIdAsync(addressId, userId);

                if (address == null)
                {
                    _logger.LogWarning("Address not found or access denied for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                    return CustomResponseDto<UserAddressDto>.NotFound("Address not found");
                }

                var addressDto = _mapper.Map<UserAddressDto>(address);

                _logger.LogInformation("Retrieved address successfully for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.Success(addressDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user address by ID for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the address");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> ProcessGetDefaultAddressAsync(Guid userId, int? addressType = null)
        {
            try
            {
                _logger.LogInformation("Processing get default address for UserId: {UserId}, AddressType: {AddressType}", userId, addressType);

                var defaultAddress = await _userAddressReadRepository.GetDefaultAddressAsync(userId,
                    addressType.HasValue ? (AddressType)addressType.Value : null);

                if (defaultAddress == null)
                {
                    _logger.LogInformation("No default address found for UserId: {UserId}, AddressType: {AddressType}", userId, addressType);
                    return CustomResponseDto<UserAddressDto>.NotFound("No default address found");
                }

                var addressDto = _mapper.Map<UserAddressDto>(defaultAddress);

                _logger.LogInformation("Retrieved default address successfully for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressDto>.Success(addressDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting default address for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the default address");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetBillingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Processing get billing addresses for UserId: {UserId}, IncludeInactive: {IncludeInactive}", userId, includeInactive);

                var query = _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .Where(a => a.Type == AddressType.Billing || a.Type == AddressType.Both);

                if (!includeInactive)
                    query = query.Where(a => a.IsActive);

                // Order by default first, then by creation date
                query = query.OrderByDescending(a => a.IsDefault).ThenByDescending(a => a.CreatedAt);

                var addresses = await query.ToListAsync();
                var addressDtos = _mapper.Map<List<UserAddressDto>>(addresses);

                _logger.LogInformation("Retrieved {Count} billing addresses for UserId: {UserId}", addressDtos.Count, userId);
                return CustomResponseDto<List<UserAddressDto>>.Success(addressDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting billing addresses for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving billing addresses");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetShippingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Processing get shipping addresses for UserId: {UserId}, IncludeInactive: {IncludeInactive}", userId, includeInactive);

                var query = _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .Where(a => a.Type == AddressType.Shipping || a.Type == AddressType.Both);

                if (!includeInactive)
                    query = query.Where(a => a.IsActive);

                // Order by default first, then by creation date
                query = query.OrderByDescending(a => a.IsDefault).ThenByDescending(a => a.CreatedAt);

                var addresses = await query.ToListAsync();
                var addressDtos = _mapper.Map<List<UserAddressDto>>(addresses);

                _logger.LogInformation("Retrieved {Count} shipping addresses for UserId: {UserId}", addressDtos.Count, userId);
                return CustomResponseDto<List<UserAddressDto>>.Success(addressDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shipping addresses for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving shipping addresses");
            }
        }

        #endregion

        #region Validation Operations

        public async Task<CustomResponseDto<bool>> ValidateUserCanHaveNewAddressAsync(Guid userId)
        {
            try
            {
                // Business rule: Users can have maximum 10 addresses
                const int maxAddressesPerUser = 10;

                var addressCount = await _userAddressReadRepository.GetUserAddressCountAsync(userId, includeInactive: true);

                var canHaveNewAddress = addressCount < maxAddressesPerUser;

                if (!canHaveNewAddress)
                {
                    _logger.LogWarning("User has reached maximum address limit. UserId: {UserId}, Count: {Count}", userId, addressCount);
                    return CustomResponseDto<bool>.BadRequest($"Maximum {maxAddressesPerUser} addresses allowed per user");
                }

                return CustomResponseDto<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating if user can have new address for UserId: {UserId}", userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address limit");
            }
        }

        public async Task<CustomResponseDto<bool>> ValidateAddressBelongsToUserAsync(Guid addressId, Guid userId)
        {
            try
            {
                var addressExists = await _userAddressReadRepository.Table
                    .Where(a => a.Id == addressId && a.UserId == userId && !a.IsDeleted)
                    .AnyAsync();

                return CustomResponseDto<bool>.Success(addressExists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating address ownership for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address ownership");
            }
        }

        public async Task<CustomResponseDto<bool>> ValidateAddressCanBeDeletedAsync(Guid addressId, Guid userId)
        {
            try
            {
                // Check if this is the only address
                var userAddressCount = await _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .CountAsync();

                if (userAddressCount <= 1)
                {
                    return CustomResponseDto<bool>.BadRequest("Cannot delete the only address. User must have at least one address.");
                }

                // TODO: Add additional business rules if needed
                // For example: Check if address is being used in pending orders

                return CustomResponseDto<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating if address can be deleted for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address deletion");
            }
        }

        #endregion

        #region Business Logic Operations

        public async Task<CustomResponseDto<int>> GetUserAddressCountAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                var query = _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && !a.IsDeleted);

                if (!includeInactive)
                    query = query.Where(a => a.IsActive);

                var count = await query.CountAsync();

                return CustomResponseDto<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user address count for UserId: {UserId}", userId);
                return CustomResponseDto<int>.InternalServerError("An error occurred while getting address count");
            }
        }

        public async Task<CustomResponseDto<bool>> UserHasAddressesAsync(Guid userId)
        {
            try
            {
                var hasAddresses = await _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && a.IsActive && !a.IsDeleted)
                    .AnyAsync();

                return CustomResponseDto<bool>.Success(hasAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user has addresses for UserId: {UserId}", userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while checking user addresses");
            }
        }

        public async Task<CustomResponseDto<UserAddressStatsDto>> GetUserAddressStatsAsync(Guid userId)
        {
            try
            {
                var addresses = await _userAddressReadRepository.Table
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .ToListAsync();

                var stats = new UserAddressStatsDto
                {
                    TotalAddresses = addresses.Count,
                    ActiveAddresses = addresses.Count(a => a.IsActive),
                    InactiveAddresses = addresses.Count(a => !a.IsActive),
                    BillingAddresses = addresses.Count(a => a.Type == AddressType.Billing || a.Type == AddressType.Both),
                    ShippingAddresses = addresses.Count(a => a.Type == AddressType.Shipping || a.Type == AddressType.Both),
                    BothTypeAddresses = addresses.Count(a => a.Type == AddressType.Both),
                    HasDefaultAddress = addresses.Any(a => a.IsDefault),
                    DefaultAddressId = addresses.FirstOrDefault(a => a.IsDefault)?.Id
                };

                return CustomResponseDto<UserAddressStatsDto>.Success(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user address stats for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressStatsDto>.InternalServerError("An error occurred while getting address statistics");
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Handles the logic for setting a new default address
        /// Removes default status from other addresses of the same user
        /// </summary>
        private async Task HandleDefaultAddressLogicAsync(Guid userId, Guid? excludeAddressId)
        {
            // Get all user addresses (including inactive ones for default logic)
            var allUserAddresses = await _userAddressReadRepository.GetUserAddressesAsync(userId, includeInactive: true);

            // Filter for default addresses, excluding the one we're setting as default
            var existingDefaultAddresses = allUserAddresses
                .Where(a => a.IsDefault && (excludeAddressId == null || a.Id != excludeAddressId))
                .ToList();

            foreach (var address in existingDefaultAddresses)
            {
                address.RemoveDefaultStatus();
                await _userAddressWriteRepository.UpdateAsync(address);
            }
        }

        #endregion
    }
}
