﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class mig001 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "orders",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    customer_phone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    customer_first_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    customer_last_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    payment_status = table.Column<int>(type: "integer", nullable: false),
                    shipping_status = table.Column<int>(type: "integer", nullable: false),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_line2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_line2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    subtotal_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    subtotal_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    tax_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    tax_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    shipping_cost_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    shipping_cost_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    discount_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    discount_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    total_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    internal_notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    shipping_method = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    payment_method = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    tracking_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    expected_delivery_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    actual_delivery_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_orders", x => x.id);
                    table.CheckConstraint("ck_orders_discount_positive", "discount_amount >= 0");
                    table.CheckConstraint("ck_orders_payment_status_valid", "payment_status BETWEEN 0 AND 6");
                    table.CheckConstraint("ck_orders_shipping_positive", "shipping_cost_amount >= 0");
                    table.CheckConstraint("ck_orders_shipping_status_valid", "shipping_status BETWEEN 0 AND 6");
                    table.CheckConstraint("ck_orders_status_valid", "status BETWEEN 0 AND 9");
                    table.CheckConstraint("ck_orders_subtotal_positive", "subtotal_amount >= 0");
                    table.CheckConstraint("ck_orders_tax_positive", "tax_amount >= 0");
                    table.CheckConstraint("ck_orders_total_positive", "total_amount >= 0");
                    table.ForeignKey(
                        name: "FK_orders_users_customer_id",
                        column: x => x.customer_id,
                        principalSchema: "etyra_core",
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "order_items",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    product_sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    quantity = table.Column<int>(type: "integer", nullable: false),
                    total_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    total_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    variant_info = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    special_instructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    discount_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    discount_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    tax_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    tax_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_order_items", x => x.id);
                    table.CheckConstraint("ck_order_items_discount_positive", "discount_amount IS NULL OR discount_amount >= 0");
                    table.CheckConstraint("ck_order_items_quantity_positive", "quantity > 0");
                    table.CheckConstraint("ck_order_items_tax_positive", "tax_amount IS NULL OR tax_amount >= 0");
                    table.CheckConstraint("ck_order_items_total_price_positive", "total_price_amount > 0");
                    table.CheckConstraint("ck_order_items_unit_price_positive", "unit_price_amount > 0");
                    table.ForeignKey(
                        name: "FK_order_items_orders_order_id",
                        column: x => x.order_id,
                        principalSchema: "etyra_core",
                        principalTable: "orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_order_items_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_order_items_order_id",
                schema: "etyra_core",
                table: "order_items",
                column: "order_id");

            migrationBuilder.CreateIndex(
                name: "ix_order_items_order_product",
                schema: "etyra_core",
                table: "order_items",
                columns: new[] { "order_id", "product_id" });

            migrationBuilder.CreateIndex(
                name: "ix_order_items_product_id",
                schema: "etyra_core",
                table: "order_items",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_order_items_product_sku",
                schema: "etyra_core",
                table: "order_items",
                column: "product_sku");

            migrationBuilder.CreateIndex(
                name: "ix_orderitem_created_at",
                schema: "etyra_core",
                table: "order_items",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_orderitem_is_deleted",
                schema: "etyra_core",
                table: "order_items",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_orderitem_is_deleted_created_at",
                schema: "etyra_core",
                table: "order_items",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_order_created_by",
                schema: "etyra_core",
                table: "orders",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_order_created_by_created_at",
                schema: "etyra_core",
                table: "orders",
                columns: new[] { "created_by", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_order_deleted_by",
                schema: "etyra_core",
                table: "orders",
                column: "deleted_by");

            migrationBuilder.CreateIndex(
                name: "ix_order_is_deleted",
                schema: "etyra_core",
                table: "orders",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_order_is_deleted_created_at",
                schema: "etyra_core",
                table: "orders",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_order_updated_by",
                schema: "etyra_core",
                table: "orders",
                column: "updated_by");

            migrationBuilder.CreateIndex(
                name: "ix_order_updated_by_updated_at",
                schema: "etyra_core",
                table: "orders",
                columns: new[] { "updated_by", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_orders_created_at",
                schema: "etyra_core",
                table: "orders",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_orders_customer_id",
                schema: "etyra_core",
                table: "orders",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "ix_orders_customer_status",
                schema: "etyra_core",
                table: "orders",
                columns: new[] { "customer_id", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_orders_order_number",
                schema: "etyra_core",
                table: "orders",
                column: "order_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_orders_payment_status",
                schema: "etyra_core",
                table: "orders",
                column: "payment_status");

            migrationBuilder.CreateIndex(
                name: "ix_orders_status",
                schema: "etyra_core",
                table: "orders",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_orders_status_created",
                schema: "etyra_core",
                table: "orders",
                columns: new[] { "status", "created_at" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "order_items",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "orders",
                schema: "etyra_core");
        }
    }
}
