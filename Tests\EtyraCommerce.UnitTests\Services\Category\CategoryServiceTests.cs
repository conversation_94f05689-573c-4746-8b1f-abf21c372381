using AutoMapper;
using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category;
using EtyraCommerce.Application.Services.Category.Commands;
using EtyraCommerce.Application.Services.Category.Queries;
using EtyraCommerce.Persistence.Services.Category;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EtyraCommerce.UnitTests.Services.Category
{
    /// <summary>
    /// Unit tests for CategoryService
    /// </summary>
    public class CategoryServiceTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<ILogger<CategoryService>> _mockLogger;
        private readonly CategoryService _categoryService;

        public CategoryServiceTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockLogger = new Mock<ILogger<CategoryService>>();
            _categoryService = new CategoryService(_mockMediator.Object, _mockLogger.Object);
        }

        #region CreateCategoryAsync Tests

        [Fact]
        public async Task CreateCategoryAsync_ValidDto_ReturnsSuccessResponse()
        {
            // Arrange
            var createDto = new CreateCategoryDto
            {
                Name = "Test Category",
                Description = "Test Description",
                IsActive = true,
                ShowInMenu = true
            };

            var expectedResponse = CustomResponseDto<CategoryDto>.Success(201, new CategoryDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Category",
                Description = "Test Description",
                IsActive = true,
                ShowInMenu = true
            }, "Category created successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<CreateCategoryCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.CreateCategoryAsync(createDto);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(201, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal("Test Category", result.Data.Name);

            _mockMediator.Verify(m => m.Send(It.IsAny<CreateCategoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateCategoryAsync_MediatorThrowsException_ReturnsErrorResponse()
        {
            // Arrange
            var createDto = new CreateCategoryDto { Name = "Test Category" };

            _mockMediator.Setup(m => m.Send(It.IsAny<CreateCategoryCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _categoryService.CreateCategoryAsync(createDto));

            _mockMediator.Verify(m => m.Send(It.IsAny<CreateCategoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region UpdateCategoryAsync Tests

        [Fact]
        public async Task UpdateCategoryAsync_ValidDto_ReturnsSuccessResponse()
        {
            // Arrange
            var categoryId = Guid.NewGuid();
            var updateDto = new UpdateCategoryDto
            {
                Name = "Updated Category",
                Description = "Updated Description",
                IsActive = true
            };

            var expectedResponse = CustomResponseDto<CategoryDto>.Success(200, new CategoryDto
            {
                Id = categoryId,
                Name = "Updated Category",
                Description = "Updated Description",
                IsActive = true
            }, "Category updated successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<UpdateCategoryCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.UpdateCategoryAsync(categoryId, updateDto);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal("Updated Category", result.Data.Name);

            _mockMediator.Verify(m => m.Send(It.IsAny<UpdateCategoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region DeleteCategoryAsync Tests

        [Fact]
        public async Task DeleteCategoryAsync_ValidId_ReturnsSuccessResponse()
        {
            // Arrange
            var categoryId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<NoContentDto>.Success(204, new NoContentDto(), "Category deleted successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<DeleteCategoryCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.DeleteCategoryAsync(categoryId);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(204, result.StatusCode);

            _mockMediator.Verify(m => m.Send(It.IsAny<DeleteCategoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task DeleteCategoryAsync_WithForceDeleteAndDeleteChildren_ReturnsSuccessResponse()
        {
            // Arrange
            var categoryId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<NoContentDto>.Success(204, new NoContentDto(), "Category deleted successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<DeleteCategoryCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.DeleteCategoryAsync(categoryId, forceDelete: true, deleteChildren: true);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(204, result.StatusCode);

            _mockMediator.Verify(m => m.Send(It.Is<DeleteCategoryCommand>(cmd => 
                cmd.CategoryId == categoryId && 
                cmd.ForceDelete == true && 
                cmd.DeleteChildren == true), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetCategoryByIdAsync Tests

        [Fact]
        public async Task GetCategoryByIdAsync_ValidId_ReturnsSuccessResponse()
        {
            // Arrange
            var categoryId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<CategoryDto>.Success(200, new CategoryDto
            {
                Id = categoryId,
                Name = "Test Category",
                IsActive = true
            }, "Category retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoryByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoryByIdAsync(categoryId);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(categoryId, result.Data.Id);

            _mockMediator.Verify(m => m.Send(It.IsAny<GetCategoryByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetCategoryByIdAsync_WithIncludeOptions_ReturnsSuccessResponse()
        {
            // Arrange
            var categoryId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<CategoryDto>.Success(200, new CategoryDto
            {
                Id = categoryId,
                Name = "Test Category",
                ChildCategories = new List<CategoryDto>(),
                ParentCategory = new CategoryDto(),
                Descriptions = new List<CategoryDescriptionDto>()
            }, "Category retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoryByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoryByIdAsync(categoryId, includeChildren: true, includeParent: true, includeDescriptions: true, languageCode: "en-US");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.NotNull(result.Data.ChildCategories);
            Assert.NotNull(result.Data.ParentCategory);
            Assert.NotNull(result.Data.Descriptions);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoryByIdQuery>(q => 
                q.CategoryId == categoryId &&
                q.IncludeChildren == true &&
                q.IncludeParent == true &&
                q.IncludeDescriptions == true &&
                q.LanguageCode == "en-US"), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetAllCategoriesAsync Tests

        [Fact]
        public async Task GetAllCategoriesAsync_ValidSearchDto_ReturnsSuccessResponse()
        {
            // Arrange
            var searchDto = new CategorySearchDto
            {
                SearchTerm = "test",
                IsActive = true,
                PageNumber = 1,
                PageSize = 10
            };

            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Test Category 1" },
                new CategoryDto { Id = Guid.NewGuid(), Name = "Test Category 2" }
            };

            var pagedResult = new PagedResult<CategoryDto>
            {
                Items = categories,
                TotalCount = 2,
                PageNumber = 1,
                PageSize = 10
            };

            var expectedResponse = CustomResponseDto<PagedResult<CategoryDto>>.Success(200, pagedResult, "Categories retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetAllCategoriesQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetAllCategoriesAsync(searchDto);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(2, result.Data.Items.Count);
            Assert.Equal(2, result.Data.TotalCount);

            _mockMediator.Verify(m => m.Send(It.IsAny<GetAllCategoriesQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetCategoriesByParentAsync Tests

        [Fact]
        public async Task GetCategoriesByParentAsync_RootCategories_ReturnsSuccessResponse()
        {
            // Arrange
            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Root Category 1", ParentCategoryId = null },
                new CategoryDto { Id = Guid.NewGuid(), Name = "Root Category 2", ParentCategoryId = null }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Categories retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoriesByParentQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoriesByParentAsync(null);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(2, result.Data.Count);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoriesByParentQuery>(q => 
                q.ParentCategoryId == null), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetCategoriesByParentAsync_WithParentId_ReturnsSuccessResponse()
        {
            // Arrange
            var parentId = Guid.NewGuid();
            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Child Category 1", ParentCategoryId = parentId },
                new CategoryDto { Id = Guid.NewGuid(), Name = "Child Category 2", ParentCategoryId = parentId }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Categories retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoriesByParentQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoriesByParentAsync(parentId, activeOnly: true, includeChildren: false, includeDescriptions: true, languageCode: "tr-TR");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(2, result.Data.Count);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoriesByParentQuery>(q => 
                q.ParentCategoryId == parentId &&
                q.ActiveOnly == true &&
                q.IncludeChildren == false &&
                q.IncludeDescriptions == true &&
                q.LanguageCode == "tr-TR"), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetCategoryTreeAsync Tests

        [Fact]
        public async Task GetCategoryTreeAsync_FullTree_ReturnsSuccessResponse()
        {
            // Arrange
            var categories = new List<CategoryDto>
            {
                new CategoryDto 
                { 
                    Id = Guid.NewGuid(), 
                    Name = "Root Category",
                    ChildCategories = new List<CategoryDto>
                    {
                        new CategoryDto { Id = Guid.NewGuid(), Name = "Child Category" }
                    }
                }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Category tree retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoryTreeQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoryTreeAsync();

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Single(result.Data);
            Assert.NotNull(result.Data[0].ChildCategories);
            Assert.Single(result.Data[0].ChildCategories);

            _mockMediator.Verify(m => m.Send(It.IsAny<GetCategoryTreeQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetCategoryTreeAsync_WithParameters_ReturnsSuccessResponse()
        {
            // Arrange
            var rootId = Guid.NewGuid();
            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Sub Category" }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Category tree retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoryTreeQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetCategoryTreeAsync(rootId, maxDepth: 3, activeOnly: true, menuOnly: true, languageCode: "en-US");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.Equal(200, result.StatusCode);
            Assert.NotNull(result.Data);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoryTreeQuery>(q => 
                q.RootCategoryId == rootId &&
                q.MaxDepth == 3 &&
                q.ActiveOnly == true &&
                q.MenuOnly == true &&
                q.LanguageCode == "en-US"), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Utility Methods Tests

        [Fact]
        public async Task GetRootCategoriesAsync_CallsGetCategoriesByParentWithNull()
        {
            // Arrange
            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Root Category" }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Categories retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoriesByParentQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetRootCategoriesAsync(activeOnly: true, includeChildren: true, languageCode: "en-US");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoriesByParentQuery>(q => 
                q.ParentCategoryId == null &&
                q.ActiveOnly == true &&
                q.IncludeChildren == true &&
                q.LanguageCode == "en-US"), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetMenuCategoriesAsync_CallsGetCategoryTreeWithMenuOnly()
        {
            // Arrange
            var categories = new List<CategoryDto>
            {
                new CategoryDto { Id = Guid.NewGuid(), Name = "Menu Category", ShowInMenu = true }
            };

            var expectedResponse = CustomResponseDto<List<CategoryDto>>.Success(200, categories, "Menu categories retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCategoryTreeQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _categoryService.GetMenuCategoriesAsync(maxDepth: 2, languageCode: "tr-TR");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);

            _mockMediator.Verify(m => m.Send(It.Is<GetCategoryTreeQuery>(q => 
                q.RootCategoryId == null &&
                q.MaxDepth == 2 &&
                q.ActiveOnly == true &&
                q.MenuOnly == true &&
                q.LanguageCode == "tr-TR"), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion
    }
}
