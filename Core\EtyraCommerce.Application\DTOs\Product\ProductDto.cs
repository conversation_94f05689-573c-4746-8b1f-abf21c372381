using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Domain.Entities.Product;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// Product Data Transfer Object for API responses
    /// </summary>
    public class ProductDto : AuditableDto
    {
        /// <summary>
        /// Product model/code
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// Product name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product rating (1-5 stars)
        /// </summary>
        public int? Stars { get; set; }

        /// <summary>
        /// AI Platform ID for integration
        /// </summary>
        public int? AiPlatformId { get; set; }

        #region Product Codes & Identifiers

        /// <summary>
        /// European Article Number (EAN-13)
        /// </summary>
        public string? EAN { get; set; }

        /// <summary>
        /// Manufacturer Part Number
        /// </summary>
        public string? MPN { get; set; }

        /// <summary>
        /// Product barcode
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// Product brand
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Universal Product Code
        /// </summary>
        public string? UPC { get; set; }

        /// <summary>
        /// Stock Keeping Unit (internal SKU)
        /// </summary>
        public string SKU { get; set; } = string.Empty;

        #endregion

        #region Pricing & Currency

        /// <summary>
        /// Base price amount
        /// </summary>
        public decimal BasePrice { get; set; }

        /// <summary>
        /// Base price currency
        /// </summary>
        public string BasePriceCurrency { get; set; } = string.Empty;

        /// <summary>
        /// Cost price amount (optional)
        /// </summary>
        public decimal? Cost { get; set; }

        /// <summary>
        /// Cost price currency (optional)
        /// </summary>
        public string? CostCurrency { get; set; }

        /// <summary>
        /// Sale price amount (optional)
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// Sale price currency (optional)
        /// </summary>
        public string? SalePriceCurrency { get; set; }

        /// <summary>
        /// Default currency for this product
        /// </summary>
        public string DefaultCurrency { get; set; } = string.Empty;

        /// <summary>
        /// Effective price (sale price if on sale, otherwise base price)
        /// </summary>
        public decimal EffectivePrice { get; set; }

        /// <summary>
        /// Effective price currency
        /// </summary>
        public string EffectivePriceCurrency { get; set; } = string.Empty;

        #endregion

        #region Dimensions

        /// <summary>
        /// Product dimensions (JSON)
        /// </summary>
        public ProductDimensionsDto? Dimensions { get; set; }

        #endregion

        #region Images

        /// <summary>
        /// Main product image URL
        /// </summary>
        public string? MainImage { get; set; }

        /// <summary>
        /// Thumbnail image URL
        /// </summary>
        public string? ThumbnailImage { get; set; }

        /// <summary>
        /// Product images
        /// </summary>
        public List<ProductImageDto> Images { get; set; } = new();

        #endregion

        #region Status & Type

        /// <summary>
        /// Product status
        /// </summary>
        public ProductStatus Status { get; set; }

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Type { get; set; }

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool IsFeatured { get; set; }

        /// <summary>
        /// Whether product is digital (no shipping required)
        /// </summary>
        public bool IsDigital { get; set; }

        #endregion

        #region SEO & Marketing

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags (comma separated)
        /// </summary>
        public string? Tags { get; set; }

        #endregion

        #region Dates

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Product launch date
        /// </summary>
        public DateTime? LaunchDate { get; set; }

        /// <summary>
        /// Product discontinue date
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Product descriptions in different languages
        /// </summary>
        public List<ProductDescriptionDto> Descriptions { get; set; } = new();

        /// <summary>
        /// Product categories
        /// </summary>
        public List<CategoryDto> Categories { get; set; } = new();

        /// <summary>
        /// Primary category
        /// </summary>
        public CategoryDto? PrimaryCategory { get; set; }

        /// <summary>
        /// Product discounts
        /// </summary>
        public List<ProductDiscountDto> Discounts { get; set; } = new();

        /// <summary>
        /// Product attributes
        /// </summary>
        public List<ProductAttributeDto> Attributes { get; set; } = new();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Checks if product is currently on sale
        /// </summary>
        public bool IsOnSale { get; set; }



        /// <summary>
        /// Gets the discount percentage if on sale
        /// </summary>
        public decimal? DiscountPercentage { get; set; }

        /// <summary>
        /// Gets the profit margin if cost is set
        /// </summary>
        public decimal? ProfitMargin { get; set; }

        /// <summary>
        /// Gets display name (Name or Model if Name is empty)
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Formatted price with currency
        /// </summary>
        public string FormattedPrice { get; set; } = string.Empty;

        /// <summary>
        /// Formatted sale price with currency (if on sale)
        /// </summary>
        public string? FormattedSalePrice { get; set; }

        #endregion
    }

    /// <summary>
    /// Product dimensions DTO
    /// </summary>
    public class ProductDimensionsDto
    {
        public decimal Length { get; set; }
        public decimal Width { get; set; }
        public decimal Height { get; set; }
        public decimal Weight { get; set; }
        public string Unit { get; set; } = "cm";
        public string WeightUnit { get; set; } = "kg";
        public decimal Volume { get; set; }
        public decimal VolumetricWeight { get; set; }
        public string FormattedDimensions { get; set; } = string.Empty;
    }


}
