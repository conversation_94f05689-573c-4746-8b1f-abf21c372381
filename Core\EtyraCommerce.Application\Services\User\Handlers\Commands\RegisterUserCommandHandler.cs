using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Commands
{
    /// <summary>
    /// Handler for RegisterUserCommand - Validates and delegates to UserProcessService
    /// </summary>
    public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, CustomResponseDto<UserDto>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<RegisterUserCommandHandler> _logger;

        public RegisterUserCommandHandler(
            IUserProcessService userProcessService,
            ILogger<RegisterUserCommandHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto>> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing registration request for email: {Email}", request.Email);

                // Validate passwords match
                if (request.Password != request.ConfirmPassword)
                {
                    _logger.LogWarning("Registration failed for {Email}: Password confirmation mismatch", request.Email);
                    return CustomResponseDto<UserDto>.BadRequest("Password and confirmation password do not match");
                }

                // Validate terms acceptance
                if (!request.AcceptTerms)
                {
                    _logger.LogWarning("Registration failed for {Email}: Terms not accepted", request.Email);
                    return CustomResponseDto<UserDto>.BadRequest("You must accept the terms and conditions");
                }

                // Create registration DTO from command
                var createUserDto = new CreateUserDto
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber,
                    Username = request.Username,
                    Password = request.Password,
                    ConfirmPassword = request.ConfirmPassword,
                    Culture = request.Culture,
                    TimeZone = request.TimeZone,
                    AcceptTerms = request.AcceptTerms,
                    SubscribeToNewsletter = request.SubscribeToNewsletter
                };

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessRegisterAsync(createUserDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Registration successful for email: {Email}, UserId: {UserId}",
                        request.Email, result.Data?.Id);

                    // TODO: Send welcome email
                    // TODO: Send email confirmation
                    // TODO: Log registration with IP and User Agent
                    // TODO: Subscribe to newsletter if opted in
                }
                else
                {
                    _logger.LogWarning("Registration failed for email: {Email}. Reason: {Message}",
                        request.Email, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing registration command for email: {Email}", request.Email);
                return CustomResponseDto<UserDto>.InternalServerError("An error occurred during registration");
            }
        }
    }
}
