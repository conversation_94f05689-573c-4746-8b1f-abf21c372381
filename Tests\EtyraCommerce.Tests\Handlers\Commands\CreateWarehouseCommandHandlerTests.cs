using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using EtyraCommerce.Application.Services.Inventory.Handlers.Commands;
using EtyraCommerce.Domain.ValueObjects;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class CreateWarehouseCommandHandlerTests
    {
        private readonly Mock<IInventoryProcessService> _mockInventoryProcessService;
        private readonly Mock<ILogger<CreateWarehouseCommandHandler>> _mockLogger;
        private readonly CreateWarehouseCommandHandler _handler;

        public CreateWarehouseCommandHandlerTests()
        {
            _mockInventoryProcessService = new Mock<IInventoryProcessService>();
            _mockLogger = new Mock<ILogger<CreateWarehouseCommandHandler>>();
            _handler = new CreateWarehouseCommandHandler(_mockInventoryProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessWithWarehouseDto()
        {
            // Arrange
            var command = new CreateWarehouseCommand
            {
                Name = "Main Warehouse",
                Code = "WH-001",
                Description = "Primary warehouse for main products",
                Address = new Address("123 Warehouse St", "Industrial Zone", "Istanbul", "Istanbul", "34000", "Turkey"),
                Phone = "+90 ************",
                Email = "<EMAIL>",
                ManagerName = "John Doe",
                IsActive = true
            };

            var expectedDto = new WarehouseDto
            {
                Id = Guid.NewGuid(),
                Name = command.Name,
                Code = command.Code,
                Description = command.Description,
                Address = command.Address,
                Phone = command.Phone,
                Email = command.Email,
                ManagerName = command.ManagerName,
                IsActive = command.IsActive,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var expectedResponse = CustomResponseDto<WarehouseDto>.Success(expectedDto, "Warehouse created successfully");

            _mockInventoryProcessService
                .Setup(x => x.ProcessCreateWarehouseAsync(It.IsAny<CreateWarehouseDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Name.Should().Be(command.Name);
            result.Data.Code.Should().Be(command.Code);
            result.Data.Description.Should().Be(command.Description);
            result.Data.Address.Should().Be(command.Address);
            result.Data.Phone.Should().Be(command.Phone);
            result.Data.Email.Should().Be(command.Email);
            result.Data.ManagerName.Should().Be(command.ManagerName);
            result.Data.IsActive.Should().Be(command.IsActive);

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateWarehouseAsync(It.Is<CreateWarehouseDto>(dto =>
                    dto.Name == command.Name &&
                    dto.Code == command.Code &&
                    dto.Description == command.Description &&
                    dto.Address == command.Address &&
                    dto.Phone == command.Phone &&
                    dto.Email == command.Email &&
                    dto.ManagerName == command.ManagerName &&
                    dto.IsActive == command.IsActive
                )), Times.Once);
        }

        #endregion
    }
}
