using AutoMapper;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Order;
using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Services.Order;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Services
{
    public class OrderProcessServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<OrderProcessService>> _mockLogger;
        private readonly Mock<ICartProcessService> _mockCartProcessService; 
        private readonly Mock<IReadRepository<User>> _mockUserReadRepository;
        private readonly Mock<IReadRepository<Product>> _mockProductReadRepository;
        private readonly Mock<IReadRepository<Order>> _mockOrderReadRepository;
        private readonly Mock<IWriteRepository<Order>> _mockOrderWriteRepository;
        private readonly OrderProcessService _orderProcessService;

        public OrderProcessServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<OrderProcessService>>();
            _mockCartProcessService = new Mock<ICartProcessService>();
            _mockUserReadRepository = new Mock<IReadRepository<User>>();
            _mockProductReadRepository = new Mock<IReadRepository<Product>>();
            _mockOrderReadRepository = new Mock<IReadRepository<Order>>();
            _mockOrderWriteRepository = new Mock<IWriteRepository<Order>>();

            _mockUnitOfWork.Setup(x => x.ReadRepository<User>()).Returns(_mockUserReadRepository.Object);
            _mockUnitOfWork.Setup(x => x.ReadRepository<Product>()).Returns(_mockProductReadRepository.Object);
            _mockUnitOfWork.Setup(x => x.ReadRepository<Order>()).Returns(_mockOrderReadRepository.Object);
            _mockUnitOfWork.Setup(x => x.WriteRepository<Order>()).Returns(_mockOrderWriteRepository.Object);

            _orderProcessService = new OrderProcessService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object, _mockCartProcessService.Object);
        }

        #region ProcessCreateOrderAsync Tests

        [Fact(Skip = "EF Core ToListAsync mock issue - needs MockQueryable.Moq")]
        public async Task ProcessCreateOrderAsync_ValidDto_ReturnsSuccessWithOrderDto()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var createOrderDto = new CreateOrderDto
            {
                CustomerId = customerId,
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                ShippingAddress = new CreateAddressDto
                {
                    Street = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA"
                },
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto
                    {
                        ProductId = productId,
                        Quantity = 2
                    }
                },
                Currency = "USD"
            };

            // Setup customer validation
            var user = new User
            {
                Id = customerId,
                Email = new Email("<EMAIL>"),
                IsDeleted = false
            };
            _mockUserReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync(user);

            // Setup product validation
            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(50.00m, Currency.FromCode("USD")),
                DefaultCurrency = Currency.FromCode("USD"),
                IsDeleted = false
            };

            var products = new List<Product> { product };
            var mockQueryable = products.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(It.IsAny<bool>())).ReturnsAsync(mockQueryable);

            // Setup mapper
            var billingAddress = new Address("123 Main St", "New York", "NY", "10001", "USA");
            var shippingAddress = new Address("123 Main St", "New York", "NY", "10001", "USA");
            _mockMapper.Setup(x => x.Map<Address>(createOrderDto.BillingAddress)).Returns(billingAddress);
            _mockMapper.Setup(x => x.Map<Address>(createOrderDto.ShippingAddress)).Returns(shippingAddress);

            var expectedOrderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                OrderNumber = "ORD-20241201-ABC123",
                CustomerId = customerId,
                Total = 100.00m,
                Currency = "USD"
            };
            _mockMapper.Setup(x => x.Map<OrderDto>(It.IsAny<Order>())).Returns(expectedOrderDto);

            // Setup unit of work
            _mockUnitOfWork.Setup(x => x.SaveChangesAsync()).ReturnsAsync(1);

            // Act
            var result = await _orderProcessService.ProcessCreateOrderAsync(createOrderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(201);
            result.Data.Should().NotBeNull();
            result.Data!.CustomerId.Should().Be(customerId);

            _mockOrderWriteRepository.Verify(x => x.AddAsync(It.IsAny<Order>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact(Skip = "EF Core ToListAsync mock issue - needs MockQueryable.Moq")]
        public async Task ProcessCreateOrderAsync_CustomerNotFound_ReturnsBadRequest()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var createOrderDto = new CreateOrderDto
            {
                CustomerId = customerId,
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto { ProductId = Guid.NewGuid(), Quantity = 1 }
                },
                Currency = "USD"
            };

            _mockUserReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync((User?)null);

            // Act
            var result = await _orderProcessService.ProcessCreateOrderAsync(createOrderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Customer not found");

            _mockOrderWriteRepository.Verify(x => x.AddAsync(It.IsAny<Order>()), Times.Never);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        }

        [Fact(Skip = "EF Core ToListAsync mock issue - needs MockQueryable.Moq")]
        public async Task ProcessCreateOrderAsync_InsufficientStock_ReturnsBadRequest()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var createOrderDto = new CreateOrderDto
            {
                CustomerId = customerId,
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto
                    {
                        ProductId = productId,
                        Quantity = 10 // More than available stock
                    }
                },
                Currency = "USD"
            };

            // Setup customer validation
            var user = new User
            {
                Id = customerId,
                Email = new Email("<EMAIL>"),
                IsDeleted = false
            };
            _mockUserReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync(user);

            // Setup product with insufficient stock
            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(50.00m, Currency.FromCode("USD")),
                DefaultCurrency = Currency.FromCode("USD"),
                IsDeleted = false
            };

            var products = new List<Product> { product };
            var mockQueryable = products.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(It.IsAny<bool>())).ReturnsAsync(mockQueryable);

            // Act
            var result = await _orderProcessService.ProcessCreateOrderAsync(createOrderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Insufficient stock");

            _mockOrderWriteRepository.Verify(x => x.AddAsync(It.IsAny<Order>()), Times.Never);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        }

        [Fact(Skip = "EF Core ToListAsync mock issue - needs MockQueryable.Moq")]
        public async Task ProcessCreateOrderAsync_ProductNotFound_ReturnsBadRequest()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var createOrderDto = new CreateOrderDto
            {
                CustomerId = customerId,
                CustomerEmail = "<EMAIL>",
                CustomerFirstName = "John",
                CustomerLastName = "Doe",
                BillingAddress = new CreateAddressDto(),
                ShippingAddress = new CreateAddressDto(),
                OrderItems = new List<CreateOrderItemDto>
                {
                    new CreateOrderItemDto
                    {
                        ProductId = productId,
                        Quantity = 1
                    }
                },
                Currency = "USD"
            };

            // Setup customer validation
            var user = new User
            {
                Id = customerId,
                Email = new Email("<EMAIL>"),
                IsDeleted = false
            };
            _mockUserReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync(user);

            // Setup empty product list (product not found)
            var products = new List<Product>();
            var mockQueryable = products.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(It.IsAny<bool>())).ReturnsAsync(mockQueryable);

            // Act
            var result = await _orderProcessService.ProcessCreateOrderAsync(createOrderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Products not found");

            _mockOrderWriteRepository.Verify(x => x.AddAsync(It.IsAny<Order>()), Times.Never);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        }

        #endregion

        #region ProcessUpdateOrderStatusAsync Tests

        [Fact(Skip = "Order entity mock issue - needs proper entity setup")]
        public async Task ProcessUpdateOrderStatusAsync_ValidOrder_ReturnsSuccess()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var order = new Order(
                Guid.NewGuid(),
                new Email("<EMAIL>"),
                "John",
                "Doe",
                new Address("123 Main St", "New York", "NY", "10001", "USA"),
                new Address("123 Main St", "New York", "NY", "10001", "USA"),
                Currency.FromCode("USD")
            );

            _mockOrderReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync(order);
            _mockUnitOfWork.Setup(x => x.SaveChangesAsync()).ReturnsAsync(1);

            // Act
            var result = await _orderProcessService.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Confirmed);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Message.Should().Be("Order status updated successfully");

            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact(Skip = "Order entity mock issue - needs proper entity setup")]
        public async Task ProcessUpdateOrderStatusAsync_OrderNotFound_ReturnsNotFound()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            _mockOrderReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync((Order?)null);

            // Act
            var result = await _orderProcessService.ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Confirmed);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        }

        #endregion

        #region ProcessCancelOrderAsync Tests

        [Fact(Skip = "Order entity mock issue - needs proper entity setup")]
        public async Task ProcessCancelOrderAsync_ValidOrder_ReturnsSuccess()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var order = new Order(
                Guid.NewGuid(),
                new Email("<EMAIL>"),
                "John",
                "Doe",
                new Address("123 Main St", "New York", "NY", "10001", "USA"),
                new Address("123 Main St", "New York", "NY", "10001", "USA"),
                Currency.FromCode("USD")
            );

            _mockOrderReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync(order);
            _mockUnitOfWork.Setup(x => x.SaveChangesAsync()).ReturnsAsync(1);

            // Act
            var result = await _orderProcessService.ProcessCancelOrderAsync(orderId, "Customer request");

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Message.Should().Be("Order cancelled successfully");

            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact(Skip = "Order entity mock issue - needs proper entity setup")]
        public async Task ProcessCancelOrderAsync_OrderNotFound_ReturnsNotFound()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            _mockOrderReadRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>())).ReturnsAsync((Order?)null);

            // Act
            var result = await _orderProcessService.ProcessCancelOrderAsync(orderId, "Customer request");

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
        }

        #endregion
    }
}
