using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations.Examples
{
    /// <summary>
    /// Example entity configuration showing how to use Value Objects
    /// This is a demonstration class - remove when creating real entities
    /// </summary>
    public class ExampleEntity : AuditableBaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public Email Email { get; set; } = null!;
        public PhoneNumber? PhoneNumber { get; set; }
        public Money Price { get; set; } = null!;
        public Money? OptionalPrice { get; set; }
        public Currency PreferredCurrency { get; set; } = null!;
    }

    /// <summary>
    /// Example configuration showing all Value Object usage patterns
    /// </summary>
    public class ExampleEntityConfiguration : AuditableBaseEntityConfiguration<ExampleEntity>
    {
        public override void Configure(EntityTypeBuilder<ExampleEntity> builder)
        {
            // Apply base configuration (BaseEntity + AuditableBaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("example_entities", "etyra_core");

            // Regular Properties
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Email Value Object (Required)
            ValueObjectConversions.ConfigureEmail<ExampleEntity>(
                builder.Property(x => x.Email),
                "email");

            // PhoneNumber Value Object (Optional)
            ValueObjectConversions.ConfigureNullablePhoneNumber<ExampleEntity>(
                builder.Property(x => x.PhoneNumber),
                "phone_number");

            // Money Value Object (Required) - Separate Columns Approach
            builder.OwnsOne(x => x.Price, price =>
            {
                price.Property(p => p.Amount)
                    .HasColumnName("price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                price.Property(p => p.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Money Value Object (Optional) - JSON Approach
            ValueObjectConversions.ConfigureNullableMoneyAsJson<ExampleEntity>(
                builder.Property(x => x.OptionalPrice),
                "optional_price_json");

            // Currency Value Object (Required)
            ValueObjectConversions.ConfigureCurrency<ExampleEntity>(
                builder.Property(x => x.PreferredCurrency),
                "preferred_currency");

            // Indexes
            builder.HasIndex(x => x.Name)
                .HasDatabaseName("ix_example_entities_name");

            builder.HasIndex(x => x.Email)
                .IsUnique()
                .HasDatabaseName("ix_example_entities_email");

            // Composite Index with Value Object
            builder.HasIndex(x => new { x.Name, x.PreferredCurrency })
                .HasDatabaseName("ix_example_entities_name_currency");
        }

        protected override string GetTableName() => "example_entities";
    }
}
