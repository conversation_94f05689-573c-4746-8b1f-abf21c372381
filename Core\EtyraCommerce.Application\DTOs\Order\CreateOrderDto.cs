using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Create order data transfer object
    /// </summary>
    public class CreateOrderDto
    {
        /// <summary>
        /// Customer ID
        /// </summary>
        [Required]
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Customer email
        /// </summary>
        [Required]
        [EmailAddress]
        [MaxLength(254)]
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number (optional)
        /// </summary>
        [Phone]
        [MaxLength(20)]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer last name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerLastName { get; set; } = string.Empty;

        /// <summary>
        /// Billing address
        /// </summary>
        [Required]
        public CreateAddressDto BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address (if different from billing)
        /// </summary>
        [Required]
        public CreateAddressDto ShippingAddress { get; set; } = null!;

        /// <summary>
        /// Order items
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "Order must contain at least one item")]
        public List<CreateOrderItemDto> OrderItems { get; set; } = new();

        /// <summary>
        /// Currency code
        /// </summary>
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Customer notes (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Shipping method (optional)
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Payment method (optional)
        /// </summary>
        [MaxLength(100)]
        public string? PaymentMethod { get; set; }
    }

    /// <summary>
    /// Create address DTO
    /// </summary>
    public class CreateAddressDto
    {
        /// <summary>
        /// Street address
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Address line 2 (optional)
        /// </summary>
        [MaxLength(200)]
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State/Province
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal/ZIP code
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Country { get; set; } = string.Empty;
    }
}
