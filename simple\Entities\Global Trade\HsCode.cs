﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Global_Trade;

public class HsCode : BaseEntity
{

    [MaxLength(12)]
    public string? Code { get; set; }
    [MaxLength(100)]
    public string? HsCodeName { get; set; }
    [MaxLength(250)]
    public string? Description { get; set; }
    public ICollection<Category> Category { get; set; }
    public ICollection<ImportProduct>? ImportProducts { get; set; }
    public decimal? PercentCustomsDuty { get; set; }
    public bool Status { get; set; }
}