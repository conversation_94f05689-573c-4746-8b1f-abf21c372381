using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// Product description DTO
    /// </summary>
    public class ProductDescriptionDto : BaseDto
    {
        /// <summary>
        /// Product name in specific language
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product description in specific language
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Short description/summary
        /// </summary>
        public string? ShortDescription { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags in specific language
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// SEO-friendly URL slug
        /// </summary>
        public string? Slug { get; set; }

        /// <summary>
        /// Language code (e.g., "en-US", "tr-TR")
        /// </summary>
        public string LanguageCode { get; set; } = "en-US";

        /// <summary>
        /// Store ID (for multi-store support)
        /// </summary>
        public Guid? StoreId { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }
    }

    /// <summary>
    /// DTO for creating product description
    /// </summary>
    public class CreateProductDescriptionDto
    {
        /// <summary>
        /// Product name in specific language
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product description in specific language
        /// </summary>
        [Required(ErrorMessage = "Description is required")]
        [StringLength(10000, MinimumLength = 1, ErrorMessage = "Description must be between 1 and 10000 characters")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Short description/summary
        /// </summary>
        [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
        public string? ShortDescription { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        [StringLength(120, ErrorMessage = "Meta title cannot exceed 120 characters")]
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        [StringLength(350, ErrorMessage = "Meta description cannot exceed 350 characters")]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags in specific language
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// SEO-friendly URL slug (will be auto-generated if not provided)
        /// </summary>
        [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
        public string? Slug { get; set; }

        /// <summary>
        /// Language code (e.g., "en-US", "tr-TR")
        /// </summary>
        [Required(ErrorMessage = "Language code is required")]
        [StringLength(10, MinimumLength = 2, ErrorMessage = "Language code must be between 2 and 10 characters")]
        public string LanguageCode { get; set; } = "en-US";

        /// <summary>
        /// Store ID (for multi-store support)
        /// </summary>
        public Guid? StoreId { get; set; }
    }

    /// <summary>
    /// DTO for updating product description
    /// </summary>
    public class UpdateProductDescriptionDto
    {
        /// <summary>
        /// Product name in specific language
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product description in specific language
        /// </summary>
        [Required(ErrorMessage = "Description is required")]
        [StringLength(10000, MinimumLength = 1, ErrorMessage = "Description must be between 1 and 10000 characters")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Short description/summary
        /// </summary>
        [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
        public string? ShortDescription { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        [StringLength(120, ErrorMessage = "Meta title cannot exceed 120 characters")]
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        [StringLength(350, ErrorMessage = "Meta description cannot exceed 350 characters")]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags in specific language
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// SEO-friendly URL slug
        /// </summary>
        [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
        public string? Slug { get; set; }
    }
}
