using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Commands
{
    /// <summary>
    /// Handler for creating an order from shopping cart
    /// </summary>
    public class CreateOrderFromCartCommandHandler : IRequestHandler<CreateOrderFromCartCommand, CustomResponseDto<OrderDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<CreateOrderFromCartCommandHandler> _logger;

        public CreateOrderFromCartCommandHandler(
            IOrderProcessService orderProcessService,
            ILogger<CreateOrderFromCartCommandHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<OrderDto>> Handle(CreateOrderFromCartCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing create order from cart command for CustomerId: {CustomerId}, SessionId: {SessionId}, PaymentMethod: {PaymentMethod}",
                    request.CustomerId, request.SessionId, request.PaymentMethod);

                // Validation
                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                {
                    _logger.LogWarning("Create order from cart command failed: Either Customer ID or Session ID is required");
                    return CustomResponseDto<OrderDto>.BadRequest("Either Customer ID or Session ID is required");
                }

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                {
                    _logger.LogWarning("Create order from cart command failed: Cannot specify both Customer ID and Session ID");
                    return CustomResponseDto<OrderDto>.BadRequest("Cannot specify both Customer ID and Session ID");
                }

                // Delegate to process service
                var result = await _orderProcessService.CreateOrderFromCartAsync(request);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Create order from cart command processed successfully for CustomerId: {CustomerId}, SessionId: {SessionId}, OrderId: {OrderId}",
                        request.CustomerId, request.SessionId, result.Data?.Id);
                }
                else
                {
                    _logger.LogWarning("Create order from cart command failed for CustomerId: {CustomerId}, SessionId: {SessionId}, Errors: {Errors}",
                        request.CustomerId, request.SessionId, string.Join(", ", result.ErrorList ?? new List<string>()));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing create order from cart command for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating order from cart");
            }
        }
    }
}
