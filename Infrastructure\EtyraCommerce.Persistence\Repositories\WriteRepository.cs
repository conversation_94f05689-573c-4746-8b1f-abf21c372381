﻿using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories
{
    /// <summary>
    /// Generic write repository implementation
    /// </summary>
    /// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
    public class WriteRepository<T> : IWriteRepository<T> where T : BaseEntity
    {
        protected readonly EtyraCommerceDbContext _context;

        public WriteRepository(EtyraCommerceDbContext context)
        {
            _context = context;
        }
        public DbSet<T> Table => _context.Set<T>();
        /// <summary>
        /// Adds a new entity
        /// </summary>
        public async Task<T> AddAsync(T model)
        {
            var entry = await Table.AddAsync(model);
            return entry.Entity;
        }

        /// <summary>
        /// Adds multiple entities
        /// </summary>
        public async Task AddRangeAsync(IEnumerable<T> models)
        {
            await Table.AddRangeAsync(models);
        }

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        public async Task<T> UpdateAsync(T model)
        {
            model.MarkAsUpdated(); // BaseEntity method
            var entry = Table.Update(model);
            return await Task.FromResult(entry.Entity);
        }

        /// <summary>
        /// Removes entity by ID (hard delete)
        /// </summary>
        public async Task<bool> RemoveByIdAsync(Guid id)
        {
            var entity = await Table.FindAsync(id);
            if (entity == null)
                return false;

            Table.Remove(entity);
            return true;
        }

        /// <summary>
        /// Removes an entity (hard delete)
        /// </summary>
        public async Task RemoveAsync(T model)
        {
            Table.Remove(model);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Removes multiple entities (hard delete)
        /// </summary>
        public async Task RemoveRangeAsync(IEnumerable<T> models)
        {
            Table.RemoveRange(models);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Marks entity as deleted (soft delete)
        /// </summary>
        public async Task<bool> SoftDeleteByIdAsync(Guid id)
        {
            var entity = await Table.FindAsync(id);
            if (entity == null)
                return false;

            entity.MarkAsDeleted(); // BaseEntity method
            Table.Update(entity);
            return true;
        }

        /// <summary>
        /// Marks entity as deleted (soft delete)
        /// </summary>
        public async Task SoftDeleteAsync(T model)
        {
            model.MarkAsDeleted(); // BaseEntity method
            Table.Update(model);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Restores a soft deleted entity
        /// </summary>
        public async Task<bool> RestoreByIdAsync(Guid id)
        {
            var entity = await Table.IgnoreQueryFilters()
                .FirstOrDefaultAsync(e => e.Id == id && e.IsDeleted);

            if (entity == null)
                return false;

            entity.Restore(); // BaseEntity method
            Table.Update(entity);
            return true;
        }
    }
}