﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Company;

public class Manufacturer : BaseEntity
{
    [MaxLength(50)]
    public string? Name { get; set; }

    [MaxLength(200)]
    public string? Address { get; set; }

    [MaxLength(50)]
    public string? WebSite { get; set; }

    [MaxLength(200)]
    public string? Comment { get; set; }

    [MaxLength(200)]
    public string? Image { get; set; }

    public ICollection<Person>? Persons { get; set; }

    public ICollection<ManufacturerProduct> ManufacturerProducts { get; set; }
}

