{
  "ConnectionStrings": {
    //"DefaultConnection": "Host=etyra-postgres;Port=5432;Database=etyracommerce;Username=etyra;Password=******************"
    "DefaultConnection": "Host=localhost;Port=5432;Database=etyracommerce;Username=etyra;Password=******************"
  },
  "JwtSettings": {
    "SecretKey": "EtyraCommerce-Super-Secret-Key-For-JWT-Token-Generation-2024-Must-Be-At-Least-32-Characters-Long",
    "Issuer": "EtyraCommerce",
    "Audience": "EtyraCommerce",
    "AccessTokenExpirationMinutes": 15,
    "RefreshTokenExpirationDays": 30
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "AllowedHosts": "*"
}
