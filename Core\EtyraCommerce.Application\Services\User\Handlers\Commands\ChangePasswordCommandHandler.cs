using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.User.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Commands
{
    /// <summary>
    /// Handler for ChangePasswordCommand - Validates and delegates to UserProcessService
    /// </summary>
    public class ChangePasswordCommandHandler : IRequestHandler<ChangePasswordCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<ChangePasswordCommandHandler> _logger;

        public ChangePasswordCommandHandler(
            IUserProcessService userProcessService,
            ILogger<ChangePasswordCommandHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing password change request for UserId: {UserId}", request.UserId);

                // Validation
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("Password change failed: UserId is required");
                    return CustomResponseDto<NoContentDto>.BadRequest("UserId is required");
                }

                if (string.IsNullOrWhiteSpace(request.CurrentPassword))
                {
                    _logger.LogWarning("Password change failed: Current password is required");
                    return CustomResponseDto<NoContentDto>.BadRequest("Current password is required");
                }

                if (string.IsNullOrWhiteSpace(request.NewPassword))
                {
                    _logger.LogWarning("Password change failed: New password is required");
                    return CustomResponseDto<NoContentDto>.BadRequest("New password is required");
                }

                // Validate passwords match
                if (request.NewPassword != request.ConfirmNewPassword)
                {
                    _logger.LogWarning("Password change failed for UserId: {UserId}: New password confirmation mismatch", request.UserId);
                    return CustomResponseDto<NoContentDto>.BadRequest("New password and confirmation password do not match");
                }

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessChangePasswordAsync(
                    request.UserId,
                    request.CurrentPassword,
                    request.NewPassword);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Password change successful for UserId: {UserId}", request.UserId);

                    // TODO: Send password change notification email
                    // TODO: Log security event
                    // TODO: Invalidate existing sessions/tokens
                }
                else
                {
                    _logger.LogWarning("Password change failed for UserId: {UserId}. Reason: {Message}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing password change command for UserId: {UserId}", request.UserId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred during password change");
            }
        }
    }
}
