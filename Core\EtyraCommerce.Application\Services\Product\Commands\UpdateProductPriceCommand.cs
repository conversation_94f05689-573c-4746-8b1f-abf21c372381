using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Commands
{
    /// <summary>
    /// Command for updating product price
    /// </summary>
    public class UpdateProductPriceCommand : IRequest<CustomResponseDto<ProductDto>>
    {
        /// <summary>
        /// Product ID to update price for
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// New base price amount
        /// </summary>
        public decimal? BasePrice { get; set; }

        /// <summary>
        /// Base price currency
        /// </summary>
        public string? BasePriceCurrency { get; set; }

        /// <summary>
        /// New cost price amount
        /// </summary>
        public decimal? Cost { get; set; }

        /// <summary>
        /// Cost price currency
        /// </summary>
        public string? CostCurrency { get; set; }

        /// <summary>
        /// New sale price amount
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// Sale price currency
        /// </summary>
        public string? SalePriceCurrency { get; set; }

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Reason for price change
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Price adjustment type
        /// </summary>
        public PriceAdjustmentType AdjustmentType { get; set; } = PriceAdjustmentType.Set;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateProductPriceCommand()
        {
        }

        /// <summary>
        /// Constructor with product ID and base price
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="basePrice">Base price</param>
        /// <param name="currency">Currency</param>
        public UpdateProductPriceCommand(Guid productId, decimal basePrice, string currency = "USD")
        {
            ProductId = productId;
            BasePrice = basePrice;
            BasePriceCurrency = currency;
        }

        /// <summary>
        /// Constructor with full parameters
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="basePrice">Base price</param>
        /// <param name="currency">Currency</param>
        /// <param name="adjustmentType">Adjustment type</param>
        /// <param name="reason">Reason for change</param>
        public UpdateProductPriceCommand(Guid productId, decimal basePrice, string currency, PriceAdjustmentType adjustmentType, string? reason = null)
        {
            ProductId = productId;
            BasePrice = basePrice;
            BasePriceCurrency = currency;
            AdjustmentType = adjustmentType;
            Reason = reason;
        }
    }
}
