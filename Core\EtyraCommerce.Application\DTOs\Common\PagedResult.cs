namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Generic paginated result wrapper
    /// </summary>
    /// <typeparam name="T">Type of items in the result</typeparam>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
        public int StartIndex => (PageNumber - 1) * PageSize + 1;
        public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);
        public bool IsEmpty => !Items.Any();
        public int ItemCount => Items.Count;

        /// <summary>
        /// Creates an empty paged result
        /// </summary>
        public static PagedResult<T> Empty(int pageNumber = 1, int pageSize = 10)
        {
            return new PagedResult<T>
            {
                Items = new List<T>(),
                TotalCount = 0,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Creates a paged result from a list and pagination info
        /// </summary>
        public static PagedResult<T> Create(List<T> items, int totalCount, int pageNumber, int pageSize)
        {
            return new PagedResult<T>
            {
                Items = items ?? new List<T>(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Creates a paged result from IQueryable with automatic count
        /// </summary>
        public static async Task<PagedResult<T>> CreateAsync<TSource>(
            IQueryable<TSource> source,
            int pageNumber,
            int pageSize,
            Func<TSource, T> selector)
        {
            var totalCount = source.Count();
            var items = source
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(selector)
                .ToList();

            return Create(items, totalCount, pageNumber, pageSize);
        }

        /// <summary>
        /// Maps the items to a different type
        /// </summary>
        public PagedResult<TResult> Map<TResult>(Func<T, TResult> mapper)
        {
            return new PagedResult<TResult>
            {
                Items = Items.Select(mapper).ToList(),
                TotalCount = TotalCount,
                PageNumber = PageNumber,
                PageSize = PageSize
            };
        }

        /// <summary>
        /// Gets pagination metadata
        /// </summary>
        public PaginationMetadata GetMetadata()
        {
            return new PaginationMetadata
            {
                CurrentPage = PageNumber,
                PageSize = PageSize,
                TotalCount = TotalCount,
                TotalPages = TotalPages,
                HasPrevious = HasPreviousPage,
                HasNext = HasNextPage,
                StartIndex = StartIndex,
                EndIndex = EndIndex
            };
        }

        /// <summary>
        /// Gets page navigation info
        /// </summary>
        public PageNavigation GetNavigation()
        {
            return new PageNavigation
            {
                FirstPage = 1,
                LastPage = TotalPages,
                PreviousPage = HasPreviousPage ? PageNumber - 1 : null,
                NextPage = HasNextPage ? PageNumber + 1 : null,
                CurrentPage = PageNumber
            };
        }

        /// <summary>
        /// Validates pagination parameters
        /// </summary>
        public static (int pageNumber, int pageSize) ValidatePagination(int pageNumber, int pageSize, int maxPageSize = 100)
        {
            pageNumber = Math.Max(1, pageNumber);
            pageSize = Math.Max(1, Math.Min(pageSize, maxPageSize));
            return (pageNumber, pageSize);
        }

        /// <summary>
        /// Creates page range for pagination UI
        /// </summary>
        public List<int> GetPageRange(int rangeSize = 5)
        {
            var pages = new List<int>();
            var start = Math.Max(1, PageNumber - rangeSize / 2);
            var end = Math.Min(TotalPages, start + rangeSize - 1);

            // Adjust start if we're near the end
            start = Math.Max(1, end - rangeSize + 1);

            for (int i = start; i <= end; i++)
            {
                pages.Add(i);
            }

            return pages;
        }

        /// <summary>
        /// Gets summary text for display
        /// </summary>
        public string GetSummary()
        {
            if (TotalCount == 0)
                return "No items found";

            if (TotalCount == 1)
                return "1 item";

            if (TotalPages == 1)
                return $"{TotalCount} items";

            return $"Showing {StartIndex}-{EndIndex} of {TotalCount} items (Page {PageNumber} of {TotalPages})";
        }
    }

    /// <summary>
    /// Pagination metadata
    /// </summary>
    public class PaginationMetadata
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPrevious { get; set; }
        public bool HasNext { get; set; }
        public int StartIndex { get; set; }
        public int EndIndex { get; set; }
    }

    /// <summary>
    /// Page navigation info
    /// </summary>
    public class PageNavigation
    {
        public int FirstPage { get; set; }
        public int LastPage { get; set; }
        public int? PreviousPage { get; set; }
        public int? NextPage { get; set; }
        public int CurrentPage { get; set; }
    }
}
