using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class InvoicePayment : BaseEntity
{
    public int InvoiceId { get; set; }
    public Invoice Invoice { get; set; }
    public int AccountId { get; set; }

    public DateTime PaymentDate { get; set; }
    
    public decimal Amount { get; set; }
    
    public int CurrencyId { get; set; }
    public Currency Currency { get; set; }
    
    public PaymentMethod PaymentMethod { get; set; }
    
    [MaxLength(100)]
    public string ReferenceNumber { get; set; }
    
    [MaxLength(500)]
    public string Notes { get; set; }
    
    [MaxLength(100)]
    public string CreatedBy { get; set; }
} 