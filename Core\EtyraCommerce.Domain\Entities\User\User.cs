using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.User
{
    /// <summary>
    /// User entity representing a system user
    /// </summary>
    public class User : AuditableBaseEntity
    {
        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's email address (Value Object)
        /// </summary>
        public Email Email { get; set; } = null!;

        /// <summary>
        /// User's phone number (Value Object - Optional)
        /// </summary>
        public PhoneNumber? PhoneNumber { get; set; }

        /// <summary>
        /// Unique username for login
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Hashed password for authentication
        /// </summary>
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// Salt used for password hashing
        /// </summary>
        public string PasswordSalt { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the user account is enabled (different from IsActive which checks !IsDeleted)
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Indicates if the email address has been confirmed
        /// </summary>
        public bool IsEmailConfirmed { get; set; } = false;

        /// <summary>
        /// Indicates if the phone number has been confirmed
        /// </summary>
        public bool IsPhoneConfirmed { get; set; } = false;

        /// <summary>
        /// Date and time of the last login
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Number of failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Date and time when the account was locked (if applicable)
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// Email confirmation token
        /// </summary>
        public string? EmailConfirmationToken { get; set; }

        /// <summary>
        /// Password reset token
        /// </summary>
        public string? PasswordResetToken { get; set; }

        /// <summary>
        /// Password reset token expiry date
        /// </summary>
        public DateTime? PasswordResetTokenExpiry { get; set; }

        /// <summary>
        /// Date and time when the password was last changed
        /// </summary>
        public DateTime? PasswordChangedAt { get; set; }

        /// <summary>
        /// Email confirmation token expiry date
        /// </summary>
        public DateTime? EmailConfirmationTokenExpiry { get; set; }

        /// <summary>
        /// Phone confirmation token
        /// </summary>
        public string? PhoneConfirmationToken { get; set; }

        /// <summary>
        /// Phone confirmation token expiry date
        /// </summary>
        public DateTime? PhoneConfirmationTokenExpiry { get; set; }

        /// <summary>
        /// User's preferred language/culture
        /// </summary>
        public string Culture { get; set; } = "en-US";

        /// <summary>
        /// User's timezone
        /// </summary>
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// User's profile picture URL or path
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// Additional notes about the user (admin use)
        /// </summary>
        public string? Notes { get; set; }

        #region JWT Authentication

        /// <summary>
        /// Current refresh token for JWT authentication
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// Expiration date and time for the refresh token
        /// </summary>
        public DateTime? RefreshTokenExpiresAt { get; set; }

        /// <summary>
        /// Date and time when the refresh token was created
        /// </summary>
        public DateTime? RefreshTokenCreatedAt { get; set; }

        /// <summary>
        /// IP address from which the refresh token was created
        /// </summary>
        public string? RefreshTokenCreatedByIp { get; set; }

        /// <summary>
        /// Date and time when the refresh token was revoked (if revoked)
        /// </summary>
        public DateTime? RefreshTokenRevokedAt { get; set; }

        /// <summary>
        /// IP address from which the refresh token was revoked
        /// </summary>
        public string? RefreshTokenRevokedByIp { get; set; }

        /// <summary>
        /// Reason for refresh token revocation
        /// </summary>
        public string? RefreshTokenRevokedReason { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the user's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Checks if the user account is locked
        /// </summary>
        public bool IsLocked => LockedUntil.HasValue && LockedUntil.Value > DateTime.UtcNow;

        /// <summary>
        /// Checks if the user can login (enabled, not locked, email confirmed, not deleted)
        /// </summary>
        public bool CanLogin => IsEnabled && !IsLocked && IsEmailConfirmed && !IsDeleted;

        /// <summary>
        /// Checks if the refresh token is active (not null, not expired, not revoked)
        /// </summary>
        public bool IsRefreshTokenActive =>
            !string.IsNullOrEmpty(RefreshToken) &&
            RefreshTokenExpiresAt.HasValue &&
            RefreshTokenExpiresAt.Value > DateTime.UtcNow &&
            !RefreshTokenRevokedAt.HasValue;

        /// <summary>
        /// Gets the display name (FirstName or Username if FirstName is empty)
        /// </summary>
        public string DisplayName => !string.IsNullOrWhiteSpace(FirstName) ? FirstName : Username;

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Collection of addresses associated with this user
        /// </summary>
        public virtual ICollection<UserAddress> Addresses { get; set; } = new List<UserAddress>();

        #endregion

        #region Business Methods

        /// <summary>
        /// Confirms the user's email address
        /// </summary>
        public void ConfirmEmail()
        {
            IsEmailConfirmed = true;
            EmailConfirmationToken = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Confirms the user's phone number
        /// </summary>
        public void ConfirmPhone()
        {
            IsPhoneConfirmed = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Records a successful login
        /// </summary>
        public void RecordSuccessfulLogin()
        {
            LastLoginAt = DateTime.UtcNow;
            FailedLoginAttempts = 0;
            LockedUntil = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Records a failed login attempt
        /// </summary>
        /// <param name="maxAttempts">Maximum allowed failed attempts before locking</param>
        /// <param name="lockoutDurationMinutes">Lockout duration in minutes</param>
        public void RecordFailedLogin(int maxAttempts = 5, int lockoutDurationMinutes = 30)
        {
            FailedLoginAttempts++;

            if (FailedLoginAttempts >= maxAttempts)
            {
                LockedUntil = DateTime.UtcNow.AddMinutes(lockoutDurationMinutes);
            }

            MarkAsUpdated();
        }

        /// <summary>
        /// Unlocks the user account
        /// </summary>
        public void Unlock()
        {
            FailedLoginAttempts = 0;
            LockedUntil = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Activates the user account
        /// </summary>
        public void Activate()
        {
            IsEnabled = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the user account
        /// </summary>
        public void Deactivate()
        {
            IsEnabled = false;
            MarkAsUpdated();
        }

        #region JWT Authentication Methods

        /// <summary>
        /// Sets a new refresh token for the user
        /// </summary>
        /// <param name="token">The refresh token</param>
        /// <param name="expiresAt">Token expiration date</param>
        /// <param name="createdByIp">IP address that created the token</param>
        public void SetRefreshToken(string token, DateTime expiresAt, string? createdByIp = null)
        {
            RefreshToken = token;
            RefreshTokenExpiresAt = expiresAt;
            RefreshTokenCreatedAt = DateTime.UtcNow;
            RefreshTokenCreatedByIp = createdByIp;

            // Clear any previous revocation data
            RefreshTokenRevokedAt = null;
            RefreshTokenRevokedByIp = null;
            RefreshTokenRevokedReason = null;

            MarkAsUpdated();
        }

        /// <summary>
        /// Revokes the current refresh token
        /// </summary>
        /// <param name="revokedByIp">IP address that revoked the token</param>
        /// <param name="reason">Reason for revocation</param>
        public void RevokeRefreshToken(string? revokedByIp = null, string? reason = null)
        {
            RefreshTokenRevokedAt = DateTime.UtcNow;
            RefreshTokenRevokedByIp = revokedByIp;
            RefreshTokenRevokedReason = reason ?? "Token revoked";
            MarkAsUpdated();
        }

        /// <summary>
        /// Clears all refresh token data
        /// </summary>
        public void ClearRefreshToken()
        {
            RefreshToken = null;
            RefreshTokenExpiresAt = null;
            RefreshTokenCreatedAt = null;
            RefreshTokenCreatedByIp = null;
            RefreshTokenRevokedAt = null;
            RefreshTokenRevokedByIp = null;
            RefreshTokenRevokedReason = null;
            MarkAsUpdated();
        }

        #endregion

        /// <summary>
        /// Sets a new password reset token
        /// </summary>
        /// <param name="token">Reset token</param>
        /// <param name="expiryHours">Token expiry in hours (default: 24)</param>
        public void SetPasswordResetToken(string token, int expiryHours = 24)
        {
            PasswordResetToken = token;
            PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(expiryHours);
            MarkAsUpdated();
        }

        /// <summary>
        /// Clears the password reset token
        /// </summary>
        public void ClearPasswordResetToken()
        {
            PasswordResetToken = null;
            PasswordResetTokenExpiry = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Checks if the password reset token is valid
        /// </summary>
        /// <param name="token">Token to validate</param>
        /// <returns>True if token is valid and not expired</returns>
        public bool IsPasswordResetTokenValid(string token)
        {
            return !string.IsNullOrEmpty(PasswordResetToken) &&
                   PasswordResetToken == token &&
                   PasswordResetTokenExpiry.HasValue &&
                   PasswordResetTokenExpiry.Value > DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the user's profile information
        /// </summary>
        /// <param name="firstName">First name</param>
        /// <param name="lastName">Last name</param>
        /// <param name="phoneNumber">Phone number</param>
        public void UpdateProfile(string firstName, string lastName, PhoneNumber? phoneNumber = null)
        {
            FirstName = firstName?.Trim() ?? string.Empty;
            LastName = lastName?.Trim() ?? string.Empty;
            PhoneNumber = phoneNumber;

            // Reset phone confirmation if phone number changed
            if (phoneNumber != PhoneNumber)
            {
                IsPhoneConfirmed = false;
            }

            MarkAsUpdated();
        }

        /// <summary>
        /// Updates last login timestamp
        /// </summary>
        public void UpdateLastLogin()
        {
            LastLoginAt = DateTime.UtcNow;
            MarkAsUpdated();
        }

        /// <summary>
        /// Changes user password
        /// </summary>
        /// <param name="passwordHash">New password hash</param>
        /// <param name="passwordSalt">New password salt</param>
        public void ChangePassword(string passwordHash, string passwordSalt)
        {
            PasswordHash = passwordHash;
            PasswordSalt = passwordSalt;
            PasswordChangedAt = DateTime.UtcNow;

            // Clear any existing reset tokens
            ClearPasswordResetToken();

            MarkAsUpdated();
        }

        /// <summary>
        /// Resets password with new hash and salt
        /// </summary>
        /// <param name="passwordHash">New password hash</param>
        /// <param name="passwordSalt">New password salt</param>
        public void ResetPassword(string passwordHash, string passwordSalt)
        {
            PasswordHash = passwordHash;
            PasswordSalt = passwordSalt;
            PasswordChangedAt = DateTime.UtcNow;

            // Clear reset token after successful reset
            ClearPasswordResetToken();

            // Reset failed login attempts
            FailedLoginAttempts = 0;
            LockedUntil = null;

            MarkAsUpdated();
        }

        /// <summary>
        /// Generates password reset token
        /// </summary>
        /// <param name="token">Reset token</param>
        /// <param name="expiry">Token expiry date</param>
        public void GeneratePasswordResetToken(string token, DateTime expiry)
        {
            PasswordResetToken = token;
            PasswordResetTokenExpiry = expiry;
            MarkAsUpdated();
        }

        /// <summary>
        /// Locks user account
        /// </summary>
        /// <param name="lockUntil">Lock until date</param>
        /// <param name="reason">Lock reason</param>
        public void Lock(DateTime lockUntil, string reason)
        {
            LockedUntil = lockUntil;
            MarkAsUpdated();
        }

        /// <summary>
        /// Increments failed login attempts
        /// </summary>
        public void IncrementFailedLoginAttempts()
        {
            FailedLoginAttempts++;
            MarkAsUpdated();
        }

        /// <summary>
        /// Resets failed login attempts to zero
        /// </summary>
        public void ResetFailedLoginAttempts()
        {
            FailedLoginAttempts = 0;
            MarkAsUpdated();
        }

        #endregion

        /// <summary>
        /// String representation of the user
        /// </summary>
        public override string ToString()
        {
            return $"User [Id: {Id}, Username: {Username}, Email: {Email}, FullName: {FullName}]";
        }
    }
}
