using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Queries
{
    /// <summary>
    /// Handler for getting a specific user address by ID
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class GetUserAddressByIdQueryHandler : IRequestHandler<GetUserAddressByIdQuery, CustomResponseDto<UserAddressDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<GetUserAddressByIdQueryHandler> _logger;

        public GetUserAddressByIdQueryHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<GetUserAddressByIdQueryHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserAddressDto>> Handle(GetUserAddressByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling GetUserAddressByIdQuery for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);

                // Validate request
                if (request.AddressId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddressByIdQuery received with empty AddressId for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<UserAddressDto>.BadRequest("Address ID is required");
                }

                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddressByIdQuery received with empty UserId for AddressId: {AddressId}", request.AddressId);
                    return CustomResponseDto<UserAddressDto>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessGetUserAddressByIdAsync(request.AddressId, request.UserId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("GetUserAddressByIdQuery handled successfully for AddressId: {AddressId}, UserId: {UserId}",
                        request.AddressId, request.UserId);
                }
                else
                {
                    _logger.LogWarning("GetUserAddressByIdQuery failed for AddressId: {AddressId}, UserId: {UserId}, Error: {Error}",
                        request.AddressId, request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling GetUserAddressByIdQuery for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the address");
            }
        }
    }
}
