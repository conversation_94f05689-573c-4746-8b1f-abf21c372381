namespace EtyraApp.Domain.Entities.Accounting;

/// <summary>
/// Hesap türlerini tanımlar
/// </summary>
public enum AccountType
{
    /// <summary>
    /// Nakit hesabı
    /// </summary>
    Cash = 1,
    
    /// <summary>
    /// Banka hesabı
    /// </summary>
    Bank = 2,
    
    /// <summary>
    /// Kredi kartı hesabı
    /// </summary>
    CreditCard = 3,
    
    /// <summary>
    /// Çek hesabı
    /// </summary>
    Check = 4,
    
    /// <summary>
    /// Alacak hesabı
    /// </summary>
    Receivable = 5,
    
    /// <summary>
    /// Bor<PERSON> hesabı
    /// </summary>
    Payable = 6,
    
    /// <summary>
    /// Diğer hesap türü
    /// </summary>
    Other = 7
}
