using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Queries
{
    /// <summary>
    /// Handler for GetOrderByIdQuery
    /// </summary>
    public class GetOrderByIdQueryHandler : IRequestHandler<GetOrderByIdQuery, CustomResponseDto<OrderDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<GetOrderByIdQueryHandler> _logger;

        public GetOrderByIdQueryHandler(
            IOrderProcessService orderProcessService,
            ILogger<GetOrderByIdQueryHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<OrderDto>> Handle(GetOrderByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get order by ID query for order: {OrderId}", request.OrderId);

                // Validation
                if (request.OrderId == Guid.Empty)
                    return CustomResponseDto<OrderDto>.BadRequest("Order ID is required");

                // Delegate to service
                var result = await _orderProcessService.GetOrderByIdAsync(
                    request.OrderId,
                    request.IncludeItems,
                    request.IncludeCustomer,
                    request.LanguageCode);

                _logger.LogInformation("Get order by ID query processed successfully for order: {OrderId}", request.OrderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get order by ID query for order: {OrderId}", request.OrderId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order");
            }
        }
    }
}
