namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Money value object representing an amount with currency
    /// </summary>
    public class Money : IEquatable<Money>, IComparable<Money>
    {
        public decimal Amount { get; private set; }
        public Currency Currency { get; private set; }

        // Parameterless constructor for EF Core
        private Money()
        {
            Amount = 0;
            Currency = Currency.USD; // Default currency
        }

        public Money(decimal amount, Currency currency)
        {
            if (amount < 0)
                throw new ArgumentException("Amount cannot be negative", nameof(amount));

            Amount = Math.Round(amount, currency.DecimalPlaces);
            Currency = currency ?? throw new ArgumentNullException(nameof(currency));
        }

        /// <summary>
        /// Creates a zero money instance with specified currency
        /// </summary>
        public static Money Zero(Currency currency) => new(0, currency);

        /// <summary>
        /// Creates money from amount and currency code
        /// </summary>
        public static Money Create(decimal amount, string currencyCode)
        {
            var currency = Currency.FromCode(currencyCode);
            return new Money(amount, currency);
        }

        /// <summary>
        /// Adds two money amounts (must be same currency)
        /// </summary>
        public Money Add(Money other)
        {
            EnsureSameCurrency(other);
            return new Money(Amount + other.Amount, Currency);
        }

        /// <summary>
        /// Subtracts two money amounts (must be same currency)
        /// </summary>
        public Money Subtract(Money other)
        {
            EnsureSameCurrency(other);
            return new Money(Amount - other.Amount, Currency);
        }

        /// <summary>
        /// Multiplies money by a factor
        /// </summary>
        public Money Multiply(decimal factor)
        {
            if (factor < 0)
                throw new ArgumentException("Factor cannot be negative", nameof(factor));

            return new Money(Amount * factor, Currency);
        }

        /// <summary>
        /// Divides money by a divisor
        /// </summary>
        public Money Divide(decimal divisor)
        {
            if (divisor <= 0)
                throw new ArgumentException("Divisor must be positive", nameof(divisor));

            return new Money(Amount / divisor, Currency);
        }

        /// <summary>
        /// Applies a percentage discount
        /// </summary>
        public Money ApplyDiscount(decimal discountPercentage)
        {
            if (discountPercentage < 0 || discountPercentage > 1)
                throw new ArgumentException("Discount percentage must be between 0 and 1", nameof(discountPercentage));

            var discountAmount = Amount * discountPercentage;
            return new Money(Amount - discountAmount, Currency);
        }

        /// <summary>
        /// Applies a percentage tax
        /// </summary>
        public Money ApplyTax(decimal taxPercentage)
        {
            if (taxPercentage < 0)
                throw new ArgumentException("Tax percentage cannot be negative", nameof(taxPercentage));

            var taxAmount = Amount * taxPercentage;
            return new Money(Amount + taxAmount, Currency);
        }

        /// <summary>
        /// Checks if this money amount is zero
        /// </summary>
        public bool IsZero => Amount == 0;

        /// <summary>
        /// Checks if this money amount is positive
        /// </summary>
        public bool IsPositive => Amount > 0;

        /// <summary>
        /// Formats money as string with currency symbol
        /// </summary>
        public string Format() => Currency.Format(Amount);

        private void EnsureSameCurrency(Money other)
        {
            if (!Currency.Equals(other.Currency))
                throw new InvalidOperationException($"Cannot perform operation on different currencies: {Currency.Code} and {other.Currency.Code}");
        }

        #region Equality and Comparison

        public bool Equals(Money? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Amount == other.Amount && Currency.Equals(other.Currency);
        }

        public override bool Equals(object? obj)
        {
            return obj is Money other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Amount, Currency);
        }

        public int CompareTo(Money? other)
        {
            if (other is null) return 1;
            EnsureSameCurrency(other);
            return Amount.CompareTo(other.Amount);
        }

        #endregion

        #region Operators

        public static Money operator +(Money left, Money right) => left.Add(right);
        public static Money operator -(Money left, Money right) => left.Subtract(right);
        public static Money operator *(Money money, decimal factor) => money.Multiply(factor);
        public static Money operator *(decimal factor, Money money) => money.Multiply(factor);
        public static Money operator /(Money money, decimal divisor) => money.Divide(divisor);

        public static bool operator ==(Money? left, Money? right)
        {
            if (left is null && right is null) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(Money? left, Money? right) => !(left == right);
        public static bool operator <(Money left, Money right) => left.CompareTo(right) < 0;
        public static bool operator <=(Money left, Money right) => left.CompareTo(right) <= 0;
        public static bool operator >(Money left, Money right) => left.CompareTo(right) > 0;
        public static bool operator >=(Money left, Money right) => left.CompareTo(right) >= 0;

        #endregion

        public override string ToString() => Format();
    }
}
