using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Queries
{
    /// <summary>
    /// Handler for GetCartSummaryQuery
    /// </summary>
    public class GetCartSummaryQueryHandler : IRequestHandler<GetCartSummaryQuery, CustomResponseDto<CartSummaryDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<GetCartSummaryQueryHandler> _logger;

        public GetCartSummaryQueryHandler(
            ICartProcessService cartProcessService,
            ILogger<GetCartSummaryQueryHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CartSummaryDto>> Handle(GetCartSummaryQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get cart summary query for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);

                // Validation
                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartSummaryDto>.BadRequest("Either Customer ID or Session ID is required");

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartSummaryDto>.BadRequest("Cannot specify both Customer ID and Session ID");

                // Delegate to process service
                var result = await _cartProcessService.GetCartSummaryAsync(
                    request.CustomerId,
                    request.SessionId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Get cart summary query processed successfully for CustomerId: {CustomerId}, SessionId: {SessionId}, CartId: {CartId}",
                        request.CustomerId, request.SessionId, result.Data?.Id);
                }
                else
                {
                    _logger.LogInformation("Get cart summary query completed with no cart found for CustomerId: {CustomerId}, SessionId: {SessionId}",
                        request.CustomerId, request.SessionId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get cart summary query for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);
                return CustomResponseDto<CartSummaryDto>.InternalServerError("An error occurred while retrieving cart summary");
            }
        }
    }
}
