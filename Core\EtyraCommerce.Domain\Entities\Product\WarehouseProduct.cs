using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product stock information per warehouse
    /// </summary>
    public class WarehouseProduct : BaseEntity
    {
        /// <summary>
        /// Selling price in this warehouse
        /// </summary>
        public Money Price { get; set; } = null!;

        /// <summary>
        /// Cost price in this warehouse
        /// </summary>
        public Money Cost { get; set; } = null!;

        /// <summary>
        /// Current stock quantity
        /// </summary>
        public int StockQuantity { get; set; } = 0;

        /// <summary>
        /// Minimum stock alert level
        /// </summary>
        public int MinStockAlert { get; set; } = 0;

        /// <summary>
        /// Maximum stock capacity
        /// </summary>
        public int? MaxStockCapacity { get; set; }

        /// <summary>
        /// Reserved stock (for pending orders)
        /// </summary>
        public int ReservedStock { get; set; } = 0;

        /// <summary>
        /// Stock status
        /// </summary>
        public StockStatus Status { get; set; } = StockStatus.InStock;

        /// <summary>
        /// Warehouse location/bin code
        /// </summary>
        public string? LocationCode { get; set; }

        /// <summary>
        /// Supplier reference for this warehouse
        /// </summary>
        public string? SupplierReference { get; set; }

        /// <summary>
        /// Lead time in days for restocking
        /// </summary>
        public int? LeadTimeDays { get; set; }

        /// <summary>
        /// Last stock update date
        /// </summary>
        public DateTime? LastStockUpdate { get; set; }

        /// <summary>
        /// Last stock count date
        /// </summary>
        public DateTime? LastStockCount { get; set; }

        /// <summary>
        /// Whether this warehouse is the primary source for this product
        /// </summary>
        public bool IsPrimaryWarehouse { get; set; } = false;

        /// <summary>
        /// Whether stock is managed for this product in this warehouse
        /// </summary>
        public bool ManageStock { get; set; } = true;

        /// <summary>
        /// Whether to allow backorders when out of stock
        /// </summary>
        public bool AllowBackorders { get; set; } = false;

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Warehouse ID
        /// </summary>
        public Guid WarehouseId { get; set; }

        // Note: Warehouse entity will be created later
        // public Warehouse Warehouse { get; set; } = null!;

        #endregion

        #region Computed Properties

        /// <summary>
        /// Available stock (total - reserved)
        /// </summary>
        public int AvailableStock => Math.Max(0, StockQuantity - ReservedStock);

        /// <summary>
        /// Checks if product is in stock
        /// </summary>
        public bool IsInStock => AvailableStock > 0 || !ManageStock || AllowBackorders;

        /// <summary>
        /// Checks if stock is low
        /// </summary>
        public bool IsLowStock => ManageStock && StockQuantity <= MinStockAlert && StockQuantity > 0;

        /// <summary>
        /// Checks if out of stock
        /// </summary>
        public bool IsOutOfStock => ManageStock && StockQuantity <= 0;

        /// <summary>
        /// Checks if stock is over capacity
        /// </summary>
        public bool IsOverCapacity => MaxStockCapacity.HasValue && StockQuantity > MaxStockCapacity;

        /// <summary>
        /// Gets profit margin
        /// </summary>
        public decimal? ProfitMargin
        {
            get
            {
                if (Price.Amount == 0) return null;
                var profit = Price.Amount - Cost.Amount;
                return Math.Round((profit / Price.Amount) * 100, 2);
            }
        }

        /// <summary>
        /// Gets stock value (quantity × cost)
        /// </summary>
        public Money StockValue => new(StockQuantity * Cost.Amount, Cost.Currency);

        /// <summary>
        /// Gets expected restock date
        /// </summary>
        public DateTime? ExpectedRestockDate
        {
            get
            {
                if (!LeadTimeDays.HasValue) return null;
                return DateTime.UtcNow.AddDays(LeadTimeDays.Value);
            }
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates stock quantity
        /// </summary>
        public void UpdateStock(int newQuantity, string? reason = null)
        {
            if (newQuantity < 0)
                throw new ArgumentException("Stock quantity cannot be negative");

            var oldQuantity = StockQuantity;
            StockQuantity = newQuantity;
            LastStockUpdate = DateTime.UtcNow;

            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Adds stock
        /// </summary>
        public void AddStock(int quantity, string? reason = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            StockQuantity += quantity;
            LastStockUpdate = DateTime.UtcNow;

            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes stock
        /// </summary>
        public void RemoveStock(int quantity, string? reason = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (ManageStock && AvailableStock < quantity)
                throw new InvalidOperationException("Insufficient available stock");

            StockQuantity -= quantity;
            LastStockUpdate = DateTime.UtcNow;

            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Reserves stock for an order
        /// </summary>
        public void ReserveStock(int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (ManageStock && AvailableStock < quantity)
                throw new InvalidOperationException("Insufficient available stock to reserve");

            ReservedStock += quantity;
            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Releases reserved stock
        /// </summary>
        public void ReleaseReservedStock(int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (quantity > ReservedStock)
                throw new InvalidOperationException("Cannot release more than reserved stock");

            ReservedStock -= quantity;
            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Confirms reserved stock (converts to actual sale)
        /// </summary>
        public void ConfirmReservedStock(int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (quantity > ReservedStock)
                throw new InvalidOperationException("Cannot confirm more than reserved stock");

            ReservedStock -= quantity;
            StockQuantity -= quantity;
            LastStockUpdate = DateTime.UtcNow;

            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates pricing
        /// </summary>
        public void UpdatePricing(Money? newPrice = null, Money? newCost = null)
        {
            if (newPrice != null)
            {
                if (newPrice.Currency.Code != Price.Currency.Code)
                    throw new ArgumentException("Price currency must match existing currency");
                Price = newPrice;
            }

            if (newCost != null)
            {
                if (newCost.Currency.Code != Cost.Currency.Code)
                    throw new ArgumentException("Cost currency must match existing currency");
                Cost = newCost;
            }

            MarkAsUpdated();
        }

        /// <summary>
        /// Performs stock count
        /// </summary>
        public void PerformStockCount(int countedQuantity, string? notes = null)
        {
            var difference = countedQuantity - StockQuantity;
            StockQuantity = countedQuantity;
            LastStockCount = DateTime.UtcNow;
            LastStockUpdate = DateTime.UtcNow;

            UpdateStockStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets as primary warehouse
        /// </summary>
        public void SetAsPrimary()
        {
            IsPrimaryWarehouse = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes primary status
        /// </summary>
        public void RemovePrimary()
        {
            IsPrimaryWarehouse = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates stock status based on current quantity
        /// </summary>
        private void UpdateStockStatus()
        {
            if (!ManageStock)
            {
                Status = StockStatus.InStock;
            }
            else if (StockQuantity <= 0)
            {
                Status = AllowBackorders ? StockStatus.Backorder : StockStatus.OutOfStock;
            }
            else if (IsLowStock)
            {
                Status = StockStatus.LowStock;
            }
            else
            {
                Status = StockStatus.InStock;
            }
        }

        #endregion

        public override string ToString()
        {
            return $"WarehouseProduct [ProductId: {ProductId}, WarehouseId: {WarehouseId}, Stock: {StockQuantity}, Status: {Status}]";
        }
    }

    /// <summary>
    /// Stock status enumeration
    /// </summary>
    public enum StockStatus
    {
        InStock = 0,
        LowStock = 1,
        OutOfStock = 2,
        Backorder = 3,
        Discontinued = 4
    }
}
