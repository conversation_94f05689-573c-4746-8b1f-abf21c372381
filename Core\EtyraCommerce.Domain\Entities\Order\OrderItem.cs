using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Order
{
    /// <summary>
    /// Order item entity representing a product within an order
    /// </summary>
    public class OrderItem : BaseEntity
    {
        #region Basic Information

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Product name at the time of order (snapshot)
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// Product SKU at the time of order (snapshot)
        /// </summary>
        public string ProductSku { get; set; } = string.Empty;

        /// <summary>
        /// Unit price at the time of order (snapshot)
        /// </summary>
        public Money UnitPrice { get; set; } = null!;

        /// <summary>
        /// Quantity ordered
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Total price for this line item (UnitPrice * Quantity)
        /// </summary>
        public Money TotalPrice { get; set; } = null!;

        #endregion

        #region Order Relationship

        /// <summary>
        /// Order ID this item belongs to
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// Navigation property to Order
        /// </summary>
        public Order Order { get; set; } = null!;

        #endregion

        #region Product Relationship

        /// <summary>
        /// Navigation property to Product
        /// </summary>
        public Product.Product Product { get; set; } = null!;

        #endregion

        #region Additional Information

        /// <summary>
        /// Product variant information (size, color, etc.)
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special instructions for this item
        /// </summary>
        public string? SpecialInstructions { get; set; }

        /// <summary>
        /// Discount amount applied to this item
        /// </summary>
        public Money? DiscountAmount { get; set; }

        /// <summary>
        /// Tax amount for this item
        /// </summary>
        public Money? TaxAmount { get; set; }

        #endregion

        #region Constructors

        // Parameterless constructor for EF Core
        public OrderItem() { }

        /// <summary>
        /// Creates a new order item
        /// </summary>
        public OrderItem(Guid productId, string productName, string productSku, Money unitPrice, int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be greater than zero", nameof(quantity));

            ProductId = productId;
            ProductName = productName ?? throw new ArgumentNullException(nameof(productName));
            ProductSku = productSku ?? throw new ArgumentNullException(nameof(productSku));
            UnitPrice = unitPrice ?? throw new ArgumentNullException(nameof(unitPrice));
            Quantity = quantity;

            CalculateTotalPrice();
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates the quantity and recalculates total price
        /// </summary>
        public void UpdateQuantity(int newQuantity)
        {
            if (newQuantity <= 0)
                throw new ArgumentException("Quantity must be greater than zero", nameof(newQuantity));

            Quantity = newQuantity;
            CalculateTotalPrice();
        }

        /// <summary>
        /// Updates the unit price and recalculates total price
        /// </summary>
        public void UpdateUnitPrice(Money newUnitPrice)
        {
            UnitPrice = newUnitPrice ?? throw new ArgumentNullException(nameof(newUnitPrice));
            CalculateTotalPrice();
        }

        /// <summary>
        /// Calculates the total price for this line item
        /// </summary>
        private void CalculateTotalPrice()
        {
            var totalAmount = UnitPrice.Amount * Quantity;
            TotalPrice = new Money(totalAmount, UnitPrice.Currency);
        }

        /// <summary>
        /// Applies discount to this item
        /// </summary>
        public void ApplyDiscount(Money discountAmount)
        {
            if (discountAmount == null)
                throw new ArgumentNullException(nameof(discountAmount));

            if (!discountAmount.Currency.Equals(UnitPrice.Currency))
                throw new ArgumentException("Discount currency must match item currency");

            if (discountAmount.Amount > TotalPrice.Amount)
                throw new ArgumentException("Discount amount cannot exceed total price");

            DiscountAmount = discountAmount;
        }

        /// <summary>
        /// Removes discount from this item
        /// </summary>
        public void RemoveDiscount()
        {
            DiscountAmount = null;
        }

        /// <summary>
        /// Sets tax amount for this item
        /// </summary>
        public void SetTaxAmount(Money taxAmount)
        {
            if (taxAmount == null)
                throw new ArgumentNullException(nameof(taxAmount));

            if (!taxAmount.Currency.Equals(UnitPrice.Currency))
                throw new ArgumentException("Tax currency must match item currency");

            TaxAmount = taxAmount;
        }

        /// <summary>
        /// Gets the final price after discount
        /// </summary>
        public Money GetFinalPrice()
        {
            var finalAmount = TotalPrice.Amount - (DiscountAmount?.Amount ?? 0);
            return new Money(Math.Max(0, finalAmount), TotalPrice.Currency);
        }

        /// <summary>
        /// Gets the total price including tax
        /// </summary>
        public Money GetTotalPriceWithTax()
        {
            var finalPrice = GetFinalPrice();
            var totalWithTax = finalPrice.Amount + (TaxAmount?.Amount ?? 0);
            return new Money(totalWithTax, finalPrice.Currency);
        }

        /// <summary>
        /// Sets variant information
        /// </summary>
        public void SetVariantInfo(string variantInfo)
        {
            VariantInfo = string.IsNullOrWhiteSpace(variantInfo) ? null : variantInfo.Trim();
        }

        /// <summary>
        /// Sets special instructions
        /// </summary>
        public void SetSpecialInstructions(string instructions)
        {
            SpecialInstructions = string.IsNullOrWhiteSpace(instructions) ? null : instructions.Trim();
        }

        /// <summary>
        /// Gets a display name for the item including variant info
        /// </summary>
        public string GetDisplayName()
        {
            if (string.IsNullOrEmpty(VariantInfo))
                return ProductName;

            return $"{ProductName} ({VariantInfo})";
        }

        /// <summary>
        /// Validates the order item
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(ProductName) &&
                   !string.IsNullOrWhiteSpace(ProductSku) &&
                   UnitPrice != null &&
                   UnitPrice.Amount > 0 &&
                   Quantity > 0 &&
                   TotalPrice != null &&
                   TotalPrice.Amount > 0;
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the savings amount if discount is applied
        /// </summary>
        public Money? GetSavingsAmount()
        {
            return DiscountAmount;
        }

        /// <summary>
        /// Gets the discount percentage if discount is applied
        /// </summary>
        public decimal? GetDiscountPercentage()
        {
            if (DiscountAmount == null || TotalPrice.Amount == 0)
                return null;

            return (DiscountAmount.Amount / TotalPrice.Amount) * 100;
        }

        #endregion
    }
}
