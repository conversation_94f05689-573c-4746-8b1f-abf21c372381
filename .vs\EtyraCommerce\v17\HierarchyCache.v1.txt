﻿++Solution 'EtyraCommerce' ‎ (6 of 6 projects)
i:{00000000-0000-0000-0000-000000000000}:EtyraCommerce.sln
++Modules
i:{00000000-0000-0000-0000-000000000000}:Modules
++Core
i:{00000000-0000-0000-0000-000000000000}:Core
++EtyraCommerce.Domain
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:EtyraCommerce.Domain
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>714
++Dependencies
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:>650
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>651
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>797
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>739
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>652
i:{************************************}:>653
++Entities
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\
++Base
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\base\
++AuditableBaseEntity.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\base\auditablebaseentity.cs
++BaseEntity.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\base\baseentity.cs
++IAuditableEntity.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\base\iauditableentity.cs
++ValueObjects
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\valueobjects\
++packages.config
++Analyzers
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:>656
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>715
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>834
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>740
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>654
i:{************************************}:>655
++Frameworks
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:>686
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>727
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>843
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>749
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>706
i:{************************************}:>705
++Currency.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\currency.cs
++Email.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\email.cs
++Money.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\money.cs
++PhoneNumber.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\phonenumber.cs
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:>690
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>728
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>844
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>750
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>710
i:{************************************}:>709
++EtyraCommerce.Application
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:EtyraCommerce.Application
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>848
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>856
++DTOs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\
++Common
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\
++CustomResponse
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\customresponse\
++CustomResponseDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\customresponse\customresponsedto.cs
++NoContentDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\customresponse\nocontentdto.cs
++Repositories
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\
++IReadRepository.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\ireadrepository.cs
++IRepository.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\irepository.cs
++IWriteRepository.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\iwriterepository.cs
++Services
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\
++LoggerService
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\loggerservice\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\loggerservice\
++ILoggerService.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\loggerservice\iloggerservice.cs
++IService.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\iservice.cs
++UnitOfWork
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\unitofwork\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\unitofwork\
++IUnitOfWork.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\unitofwork\iunitofwork.cs
++Packages
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>729
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>877
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>711
++Projects
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>713
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>847
i:{6d697786-b015-459c-9aec-39ee898c9b65}:>855
++ApiResponse.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\apiresponse.cs
++AuditableDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\auditabledto.cs
++BaseDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\basedto.cs
++PagedResult.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\pagedresult.cs
++SearchRequest.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\searchrequest.cs
++ServiceResult.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\serviceresult.cs
++ValidationResult.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\common\validationresult.cs
++Microsoft.EntityFrameworkCore.Analyzers
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.6\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.6\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.6\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{6d697786-b015-459c-9aec-39ee898c9b65}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.EntityFrameworkCore (9.0.6)
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>730
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>896
++Infrastructure
i:{00000000-0000-0000-0000-000000000000}:Infrastructure
++EtyraCommerce.Persistence
i:{ff6f80da-5b6f-40c2-8af3-ca2017956d89}:EtyraCommerce.Persistence
++AutoMapper (14.0.0)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>878
++Microsoft.AspNetCore.Http.Abstractions (2.3.0)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>1486
++Npgsql.EntityFrameworkCore.PostgreSQL (9.0.4)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>897
++Serilog (4.3.0)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>889
++EtyraCommerce.Infrastructure
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>849
i:{ff6f80da-5b6f-40c2-8af3-ca2017956d89}:EtyraCommerce.Infrastructure
++Contexts
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\contexts\
++EtyraCommerceDbContext.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\contexts\etyracommercedbcontext.cs
++ReadRepository.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\readrepository.cs
++WriteRepository.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\writerepository.cs
++LoggerService.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\loggerservice\loggerservice.cs
++Service.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\service.cs
++UnitOfWork.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\unitofwork\unitofwork.cs
++Presentation
i:{00000000-0000-0000-0000-000000000000}:Presentation
++EtyraCommerce.API
i:{f9c312da-ae97-4943-82b4-3d8f8f8c87a8}:EtyraCommerce.API
++Connected Services 
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>646
i:{************************************}:>647
++Properties
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\properties\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\properties\
++Controllers
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\controllers\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\controllers\
++appsettings.json
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\appsettings.json
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\appsettings.json
++EtyraCommerce.API.http
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\etyracommerce.api.http
++Program.cs
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\program.cs
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\program.cs
++WeatherForecast.cs
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\weatherforecast.cs
++No service dependencies discovered
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>649
i:{************************************}:>648
++launchSettings.json
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\properties\launchsettings.json
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\properties\launchsettings.json
++WeatherForecastController.cs
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\controllers\weatherforecastcontroller.cs
++appsettings.Development.json
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:f:\software development\etyracommerce\presentation\etyracommerce.api\appsettings.development.json
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\appsettings.development.json
++Microsoft.AspNetCore.Analyzers
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.Extensions.ObjectPool
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++System.Collections.Immutable
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++Microsoft.AspNetCore.App
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>708
i:{************************************}:>707
++Swashbuckle.AspNetCore (6.6.2)
i:{78728e7e-4c54-4b3d-9ffd-6a2bdfbfdb02}:>712
++EtyraCommerce.WEB
i:{f9c312da-ae97-4943-82b4-3d8f8f8c87a8}:EtyraCommerce.WEB
++wwwroot
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\
++Models
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\models\
++Views
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\
++css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\css\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\
++js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\js\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\
++lib
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\
++favicon.ico
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\favicon.ico
++HomeController.cs
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\controllers\homecontroller.cs
++ErrorViewModel.cs
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\models\errorviewmodel.cs
++Home
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\home\
++Shared
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\shared\
++_ViewImports.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\_viewimports.cshtml
++_ViewStart.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\_viewstart.cshtml
++site.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\css\site.css
++site.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\js\site.js
++bootstrap
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\
++jquery
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\
++jquery-validation
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\
++jquery-validation-unobtrusive
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation-unobtrusive\
++Index.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\home\index.cshtml
++Privacy.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\home\privacy.cshtml
++_Layout.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\shared\_layout.cshtml
++_ValidationScriptsPartial.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\shared\_validationscriptspartial.cshtml
++Error.cshtml
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\shared\error.cshtml
++dist
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\dist\
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\dist\
++LICENSE
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\license
++LICENSE.txt
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\license.txt
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation-unobtrusive\license.txt
++LICENSE.md
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\license.md
++jquery.validate.unobtrusive.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js
++_Layout.cshtml.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\views\shared\_layout.cshtml.css
++jquery.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\dist\jquery.js
++additional-methods.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\dist\additional-methods.js
++jquery.validate.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\dist\jquery.validate.js
++jquery.validate.unobtrusive.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js
++bootstrap.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.css
++bootstrap-grid.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css
++bootstrap-reboot.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css
++bootstrap-utilities.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css
++bootstrap.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.js
++jquery.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\dist\jquery.min.js
++additional-methods.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\dist\additional-methods.min.js
++jquery.validate.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js
++bootstrap.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map
++bootstrap.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css
++bootstrap.rtl.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css
++bootstrap-grid.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map
++bootstrap-grid.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css
++bootstrap-grid.rtl.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css
++bootstrap-reboot.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map
++bootstrap-reboot.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css
++bootstrap-reboot.rtl.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css
++bootstrap-utilities.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map
++bootstrap-utilities.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css
++bootstrap-utilities.rtl.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css
++bootstrap.bundle.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js
++bootstrap.esm.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js
++bootstrap.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map
++bootstrap.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js
++jquery.min.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\jquery\dist\jquery.min.map
++bootstrap.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map
++bootstrap.rtl.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map
++bootstrap.rtl.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css
++bootstrap-grid.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map
++bootstrap-grid.rtl.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map
++bootstrap-grid.rtl.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css
++bootstrap-reboot.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map
++bootstrap-reboot.rtl.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map
++bootstrap-reboot.rtl.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css
++bootstrap-utilities.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map
++bootstrap-utilities.rtl.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map
++bootstrap-utilities.rtl.min.css
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css
++bootstrap.bundle.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map
++bootstrap.bundle.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js
++bootstrap.esm.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map
++bootstrap.esm.min.js
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js
++bootstrap.min.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map
++bootstrap.rtl.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map
++bootstrap-grid.rtl.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map
++bootstrap-reboot.rtl.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map
++bootstrap-utilities.rtl.min.css.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map
++bootstrap.bundle.min.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map
++bootstrap.esm.min.js.map
i:{************************************}:f:\software development\etyracommerce\presentation\etyracommerce.web\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map
++Configurations
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\
++BaseEntityConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\baseentityconfiguration.cs
++AuditableBaseEntityConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\auditablebaseentityconfiguration.cs
++ValueObjectConversions.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\valueobjects\valueobjectconversions.cs
++Examples
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\examples\
++ExampleEntityConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\examples\exampleentityconfiguration.cs
++User.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\user\user.cs
++Autofac (8.3.0)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>1627
++UserConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\userconfiguration.cs
++ServiceModule.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\servicemodule.cs
++NewFolder
++Mapping
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\mapping\
++MapProfile.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\mapping\mapprofile.cs
++Microsoft.Extensions.Http (9.0.6)
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:>1637
++User
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\user\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\user\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\user\
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\user\
++UserDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\userdto.cs
++CreateUserDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\createuserdto.cs
++UpdateUserDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\updateuserdto.cs
++UserLoginDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\userlogindto.cs
++UserSearchDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\usersearchdto.cs
++ProductDimensions.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\valueobjects\productdimensions.cs
++Product
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\
++Product.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\product.cs
++ProductDescription.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productdescription.cs
++ProductCategory.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productcategory.cs
++ProductImage.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productimage.cs
++ProductDiscount.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productdiscount.cs
++WarehouseProduct.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\warehouseproduct.cs
++Category
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\category\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\category\
++Category.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\category\category.cs
++ProductVariant.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productvariant.cs
++ProductAttribute.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\product\productattribute.cs
++ProductConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\productconfiguration.cs
++ProductDescriptionConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\productdescriptionconfiguration.cs
++ProductCategoryConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\productcategoryconfiguration.cs
++CategoryConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\categoryconfiguration.cs
++ProductImageConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\productimageconfiguration.cs
++ProductDiscountConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\productdiscountconfiguration.cs
++WarehouseProductConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\warehouseproductconfiguration.cs
++ProductDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\productdto.cs
++CreateProductDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\createproductdto.cs
++UpdateProductDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\updateproductdto.cs
++ProductDescriptionDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\productdescriptiondto.cs
++ProductImageDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\productimagedto.cs
++ProductDiscountDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\productdiscountdto.cs
++ProductAttributeDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\product\productattributedto.cs
++CategoryDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\category\categorydto.cs
++CategoryDescription.cs
i:{557f91e7-9500-4b86-9749-e5e8c0c45b3d}:f:\software development\etyracommerce\core\etyracommerce.domain\entities\category\categorydescription.cs
++CategoryDescriptionConfiguration.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\configurations\categorydescriptionconfiguration.cs
++CategoryDescriptionDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\category\categorydescriptiondto.cs
++IUserService.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\iuserservice.cs
++UserService.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\user\userservice.cs
++Commands
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\commands\
++LoginUserCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\loginusercommand.cs
++RegisterUserCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\registerusercommand.cs
++ActivateUserCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\activateusercommand.cs
++DeactivateUserCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\deactivateusercommand.cs
++ChangePasswordCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\changepasswordcommand.cs
++ResetPasswordCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\resetpasswordcommand.cs
++GeneratePasswordResetTokenCommand.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\commands\generatepasswordresettokencommand.cs
++Queries
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\
++GetUserQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\getuserquery.cs
++GetUserByEmailQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\getuserbyemailquery.cs
++GetUserByUsernameQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\getuserbyusernamequery.cs
++SearchUsersQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\searchusersquery.cs
++GetUserStatisticsQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\getuserstatisticsquery.cs
++MediatR (12.5.0)
++ValidateUserExistsQuery.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\queries\validateuserexistsquery.cs
++Handlers
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\
++LoginUserCommandHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\commands\loginusercommandhandler.cs
++RegisterUserCommandHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\commands\registerusercommandhandler.cs
++ActivateUserCommandHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\commands\activateusercommandhandler.cs
++ChangePasswordCommandHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\commands\changepasswordcommandhandler.cs
++GetUserQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\getuserqueryhandler.cs
++GetUserByEmailQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\getuserbyemailqueryhandler.cs
++SearchUsersQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\searchusersqueryhandler.cs
++GetUserStatisticsQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\getuserstatisticsqueryhandler.cs
++UserStatisticsDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\userstatisticsdto.cs
++UserLoginHistoryDto.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\dtos\user\userloginhistorydto.cs
++IUserReadRepository.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\user\iuserreadrepository.cs
++IUserWriteRepository.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\repositories\user\iuserwriterepository.cs
++UserReadRepository.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\user\userreadrepository.cs
++UserWriteRepository.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\repositories\user\userwriterepository.cs
++DeactivateUserCommandHandler.cs
++GetUserByUsernameQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\getuserbyusernamequeryhandler.cs
++ValidateUserExistsQueryHandler.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\handlers\queries\validateuserexistsqueryhandler.cs
++MediatR (12.2.0)
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:>1765
++IUserProcessService.cs
i:{711ea98f-a1d9-4509-b261-1c3bd2dbdf5d}:f:\software development\etyracommerce\core\etyracommerce.application\services\user\iuserprocessservice.cs
++UserProcessService.cs
i:{50c1a7f2-e634-424b-b39d-39d4409f9172}:f:\software development\etyracommerce\infrastructure\etyracommerce.persistence\services\user\userprocessservice.cs
