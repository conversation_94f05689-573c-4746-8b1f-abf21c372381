using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// Product image DTO
    /// </summary>
    public class ProductImageDto : BaseDto
    {
        /// <summary>
        /// Image URL or path
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Thumbnail image URL or path
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// Alternative text for accessibility
        /// </summary>
        public string? AltText { get; set; }

        /// <summary>
        /// Image title
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Image description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Whether this is the main product image
        /// </summary>
        public bool IsMain { get; set; }

        /// <summary>
        /// Image type
        /// </summary>
        public ProductImageType Type { get; set; }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// Image width in pixels
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// Image height in pixels
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// MIME type (e.g., "image/jpeg", "image/png")
        /// </summary>
        public string? MimeType { get; set; }

        /// <summary>
        /// Original filename
        /// </summary>
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Gets the display URL (thumbnail if available, otherwise main image)
        /// </summary>
        public string DisplayUrl => !string.IsNullOrEmpty(ThumbnailUrl) ? ThumbnailUrl : ImageUrl;

        /// <summary>
        /// Gets formatted file size
        /// </summary>
        public string FormattedFileSize
        {
            get
            {
                if (!FileSize.HasValue) return "Unknown";

                var size = FileSize.Value;
                if (size < 1024) return $"{size} B";
                if (size < 1024 * 1024) return $"{size / 1024:F1} KB";
                if (size < 1024 * 1024 * 1024) return $"{size / (1024 * 1024):F1} MB";
                return $"{size / (1024 * 1024 * 1024):F1} GB";
            }
        }

        /// <summary>
        /// Gets image dimensions as string
        /// </summary>
        public string? Dimensions
        {
            get
            {
                if (!Width.HasValue || !Height.HasValue) return null;
                return $"{Width} × {Height}";
            }
        }
    }

    /// <summary>
    /// DTO for creating product image
    /// </summary>
    public class CreateProductImageDto
    {
        /// <summary>
        /// Image URL or path
        /// </summary>
        [Required(ErrorMessage = "Image URL is required")]
        [StringLength(500, ErrorMessage = "Image URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Image URL must be a valid URL")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Thumbnail image URL or path
        /// </summary>
        [StringLength(500, ErrorMessage = "Thumbnail URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Thumbnail URL must be a valid URL")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// Alternative text for accessibility
        /// </summary>
        [StringLength(200, ErrorMessage = "Alt text cannot exceed 200 characters")]
        public string? AltText { get; set; }

        /// <summary>
        /// Image title
        /// </summary>
        [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        public string? Title { get; set; }

        /// <summary>
        /// Image description
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether this is the main product image
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// Image type
        /// </summary>
        public ProductImageType Type { get; set; } = ProductImageType.Gallery;

        /// <summary>
        /// File size in bytes
        /// </summary>
        [Range(1, long.MaxValue, ErrorMessage = "File size must be positive")]
        public long? FileSize { get; set; }

        /// <summary>
        /// Image width in pixels
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Width must be positive")]
        public int? Width { get; set; }

        /// <summary>
        /// Image height in pixels
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Height must be positive")]
        public int? Height { get; set; }

        /// <summary>
        /// MIME type (e.g., "image/jpeg", "image/png")
        /// </summary>
        [StringLength(100, ErrorMessage = "MIME type cannot exceed 100 characters")]
        public string? MimeType { get; set; }

        /// <summary>
        /// Original filename
        /// </summary>
        [StringLength(255, ErrorMessage = "Original filename cannot exceed 255 characters")]
        public string? OriginalFileName { get; set; }
    }

    /// <summary>
    /// DTO for updating product image
    /// </summary>
    public class UpdateProductImageDto
    {
        /// <summary>
        /// Alternative text for accessibility
        /// </summary>
        [StringLength(200, ErrorMessage = "Alt text cannot exceed 200 characters")]
        public string? AltText { get; set; }

        /// <summary>
        /// Image title
        /// </summary>
        [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        public string? Title { get; set; }

        /// <summary>
        /// Image description
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Sort order must be non-negative")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether this is the main product image
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// Image type
        /// </summary>
        public ProductImageType Type { get; set; } = ProductImageType.Gallery;
    }

    /// <summary>
    /// Product image type enumeration
    /// </summary>
    public enum ProductImageType
    {
        Main = 0,
        Gallery = 1,
        Thumbnail = 2,
        Zoom = 3,
        Variant = 4
    }
}
