using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Queries
{
    /// <summary>
    /// Handler for GetCartQuery
    /// </summary>
    public class GetCartQueryHandler : IRequestHandler<GetCartQuery, CustomResponseDto<CartDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<GetCartQueryHandler> _logger;

        public GetCartQueryHandler(
            ICartProcessService cartProcessService,
            ILogger<GetCartQueryHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CartDto>> Handle(GetCartQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get cart query for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);

                // Validation
                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartDto>.BadRequest("Either Customer ID or Session ID is required");

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartDto>.BadRequest("Cannot specify both Customer ID and Session ID");

                // Delegate to process service
                var result = await _cartProcessService.GetCartAsync(
                    request.CustomerId,
                    request.SessionId,
                    request.IncludeInactive,
                    request.IncludeExpired);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Get cart query processed successfully for CustomerId: {CustomerId}, SessionId: {SessionId}, CartId: {CartId}",
                        request.CustomerId, request.SessionId, result.Data?.Id);
                }
                else
                {
                    _logger.LogInformation("Get cart query completed with no cart found for CustomerId: {CustomerId}, SessionId: {SessionId}",
                        request.CustomerId, request.SessionId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get cart query for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.CustomerId, request.SessionId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while retrieving cart");
            }
        }
    }
}
