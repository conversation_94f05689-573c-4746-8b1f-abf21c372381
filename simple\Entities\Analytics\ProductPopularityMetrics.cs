using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;

namespace EtyraApp.Domain.Entities.Analytics;

public class ProductPopularityMetrics : BaseEntity
{
    /// <summary>
    /// Ürün ID
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Hesaplama tarihi (ay/yıl bazında)
    /// </summary>
    public DateTime CalculationDate { get; set; }

    /// <summary>
    /// Bu ayki sipariş sayısı
    /// </summary>
    public int CurrentMonthOrderCount { get; set; }

    /// <summary>
    /// Bu ayki toplam sipariş adedi
    /// </summary>
    public int CurrentMonthQuantityCount { get; set; }

    /// <summary>
    /// Önceki ayki sipariş sayısı
    /// </summary>
    public int PreviousMonthOrderCount { get; set; }

    /// <summary>
    /// Önceki ayki toplam sipariş adedi
    /// </summary>
    public int PreviousMonthQuantityCount { get; set; }

    /// <summary>
    /// Sipariş sayısı bazlı korelasyon puanı
    /// </summary>
    public int OrderBasedCorrelationScore { get; set; }

    /// <summary>
    /// Adet bazlı korelasyon puanı
    /// </summary>
    public int QuantityBasedCorrelationScore { get; set; }

    /// <summary>
    /// Kar marjı yüzdesi
    /// </summary>
    public decimal ProfitMargin { get; set; }

    /// <summary>
    /// Karlılık sınıfı (A, B, C, D, E, X)
    /// </summary>
    public char ProfitabilityGrade { get; set; }

    /// <summary>
    /// Sipariş bazlı popülerlik puanı (örn: "19B")
    /// </summary>
    [MaxLength(10)]
    public string OrderBasedPopularityScore { get; set; } = string.Empty;

    /// <summary>
    /// Adet bazlı popülerlik puanı (örn: "67D")
    /// </summary>
    [MaxLength(10)]
    public string QuantityBasedPopularityScore { get; set; } = string.Empty;

    /// <summary>
    /// Bu dönemde ilk stok giriş tarihi
    /// </summary>
    public DateTime? FirstStockEntryDate { get; set; }

    /// <summary>
    /// Bu dönemde son stok giriş tarihi
    /// </summary>
    public DateTime? LastStockEntryDate { get; set; }

    /// <summary>
    /// Bu dönemde stokta olduğu toplam gün sayısı
    /// </summary>
    public int StockAvailableDaysInPeriod { get; set; }

    /// <summary>
    /// Bu dönemde hiç stokta oldu mu?
    /// </summary>
    public bool WasInStockDuringPeriod { get; set; }

    /// <summary>
    /// Son güncelleme tarihi
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    // Navigation Property
    public Product Product { get; set; }

    public int CurrentStock { get; set; }

    /// <summary>
    /// Minimum stok uyarı seviyesi
    /// </summary>
    public int MinStockAlert { get; set; }
}
