﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EtyraApp.Domain.Entities.Common;

public abstract class BaseEntity
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }


    [Column("CreatedDate", TypeName = "datetime")]
    [DataType(DataType.DateTime)]
    public DateTime CreatedDate { get; set; }

    [Column("ModifiedDate", TypeName = "datetime")]
    [DataType(DataType.DateTime)]
    public DateTime? ModifiedDate { get; set; }
}