/* _content/EtyraCommerce.WEB/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-qdkdj6bbzl] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-qdkdj6bbzl] {
  color: #0077cc;
}

.btn-primary[b-qdkdj6bbzl] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-qdkdj6bbzl], .nav-pills .show > .nav-link[b-qdkdj6bbzl] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-qdkdj6bbzl] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-qdkdj6bbzl] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-qdkdj6bbzl] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-qdkdj6bbzl] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-qdkdj6bbzl] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
