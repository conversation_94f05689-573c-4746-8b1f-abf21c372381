using EtyraCommerce.Domain.Entities.Product;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for updating an existing product
    /// </summary>
    public class UpdateProductDto
    {
        /// <summary>
        /// Product name/title
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product rating (1-5 stars)
        /// </summary>
        [Range(1, 5, ErrorMessage = "Stars must be between 1 and 5")]
        public int? Stars { get; set; }

        /// <summary>
        /// AI Platform ID for integration
        /// </summary>
        public int? AiPlatformId { get; set; }

        #region Product Codes & Identifiers

        /// <summary>
        /// European Article Number (EAN-13)
        /// </summary>
        [StringLength(13, ErrorMessage = "EAN cannot exceed 13 characters")]
        [RegularExpression(@"^\d{13}$", ErrorMessage = "EAN must be exactly 13 digits")]
        public string? EAN { get; set; }

        /// <summary>
        /// Manufacturer Part Number
        /// </summary>
        [StringLength(12, ErrorMessage = "MPN cannot exceed 12 characters")]
        public string? MPN { get; set; }

        /// <summary>
        /// Product barcode
        /// </summary>
        [StringLength(20, ErrorMessage = "Barcode cannot exceed 20 characters")]
        public string? Barcode { get; set; }

        /// <summary>
        /// Product brand
        /// </summary>
        [StringLength(20, ErrorMessage = "Brand cannot exceed 20 characters")]
        public string? Brand { get; set; }

        /// <summary>
        /// Universal Product Code
        /// </summary>
        [StringLength(12, ErrorMessage = "UPC cannot exceed 12 characters")]
        [RegularExpression(@"^\d{12}$", ErrorMessage = "UPC must be exactly 12 digits")]
        public string? UPC { get; set; }

        #endregion

        #region Pricing & Currency

        /// <summary>
        /// Base price amount
        /// </summary>
        [Required(ErrorMessage = "Base price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Base price must be greater than 0")]
        public decimal BasePrice { get; set; }

        /// <summary>
        /// Base price currency
        /// </summary>
        [Required(ErrorMessage = "Base price currency is required")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string BasePriceCurrency { get; set; } = "USD";

        /// <summary>
        /// Cost price amount (optional)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Cost must be non-negative")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// Cost price currency (optional)
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? CostCurrency { get; set; }

        /// <summary>
        /// Sale price amount (optional)
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Sale price must be greater than 0")]
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// Sale price currency (optional)
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? SalePriceCurrency { get; set; }

        /// <summary>
        /// Default currency for this product
        /// </summary>
        [Required(ErrorMessage = "Default currency is required")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string DefaultCurrency { get; set; } = "USD";

        #endregion

        #region Dimensions

        /// <summary>
        /// Product dimensions
        /// </summary>
        public UpdateProductDimensionsDto? Dimensions { get; set; }

        #endregion

        #region Images

        /// <summary>
        /// Main product image URL
        /// </summary>
        [StringLength(500, ErrorMessage = "Main image URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Main image must be a valid URL")]
        public string? MainImage { get; set; }

        /// <summary>
        /// Thumbnail image URL
        /// </summary>
        [StringLength(500, ErrorMessage = "Thumbnail image URL cannot exceed 500 characters")]
        [Url(ErrorMessage = "Thumbnail image must be a valid URL")]
        public string? ThumbnailImage { get; set; }

        #endregion

        #region Inventory & Status

        /// <summary>
        /// Product status
        /// </summary>
        public ProductStatus Status { get; set; } = ProductStatus.Draft;

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Type { get; set; } = ProductType.Simple;

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// Whether product is digital (no shipping required)
        /// </summary>
        public bool IsDigital { get; set; } = false;

        /// <summary>
        /// Whether product is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether product requires shipping
        /// </summary>
        public bool RequiresShipping { get; set; } = true;

        /// <summary>
        /// Whether product is taxable
        /// </summary>
        public bool IsTaxable { get; set; } = true;



        #endregion

        #region SEO & Marketing

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
        public string? Slug { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        [StringLength(120, ErrorMessage = "Meta title cannot exceed 120 characters")]
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        [StringLength(350, ErrorMessage = "Meta description cannot exceed 350 characters")]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags (comma separated)
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        #endregion

        #region Dates

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Product launch date
        /// </summary>
        public DateTime? LaunchDate { get; set; }

        /// <summary>
        /// Product available start date
        /// </summary>
        public DateTime? AvailableStartDate { get; set; }

        /// <summary>
        /// Product available end date
        /// </summary>
        public DateTime? AvailableEndDate { get; set; }

        /// <summary>
        /// Product discontinue date
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Category IDs to assign to this product
        /// </summary>
        public List<Guid> CategoryIds { get; set; } = new();

        /// <summary>
        /// Primary category ID
        /// </summary>
        public Guid? PrimaryCategoryId { get; set; }

        /// <summary>
        /// Product descriptions in different languages
        /// </summary>
        public List<UpdateProductDescriptionDto> Descriptions { get; set; } = new();

        /// <summary>
        /// Product images
        /// </summary>
        public List<UpdateProductImageDto> Images { get; set; } = new();

        /// <summary>
        /// Product attributes
        /// </summary>
        public List<UpdateProductAttributeDto> Attributes { get; set; } = new();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the display name
        /// </summary>
        public string DisplayName => Name;

        #endregion

        #region Validation

        /// <summary>
        /// Custom validation for sale dates
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (SaleStartDate.HasValue && SaleEndDate.HasValue && SaleStartDate > SaleEndDate)
            {
                yield return new ValidationResult(
                    "Sale start date cannot be after sale end date",
                    new[] { nameof(SaleStartDate), nameof(SaleEndDate) });
            }

            if (SalePrice.HasValue && SalePrice >= BasePrice)
            {
                yield return new ValidationResult(
                    "Sale price must be less than base price",
                    new[] { nameof(SalePrice) });
            }

            if (Cost.HasValue && Cost > BasePrice)
            {
                yield return new ValidationResult(
                    "Cost should not exceed base price",
                    new[] { nameof(Cost) });
            }

            if (PrimaryCategoryId.HasValue && !CategoryIds.Contains(PrimaryCategoryId.Value))
            {
                yield return new ValidationResult(
                    "Primary category must be included in category list",
                    new[] { nameof(PrimaryCategoryId) });
            }
        }

        #endregion
    }

    /// <summary>
    /// DTO for updating product dimensions
    /// </summary>
    public class UpdateProductDimensionsDto
    {
        [Range(0, double.MaxValue, ErrorMessage = "Length must be non-negative")]
        public decimal Length { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Width must be non-negative")]
        public decimal Width { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Height must be non-negative")]
        public decimal Height { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Weight must be non-negative")]
        public decimal Weight { get; set; }

        [StringLength(10, ErrorMessage = "Unit cannot exceed 10 characters")]
        public string Unit { get; set; } = "cm";

        [StringLength(10, ErrorMessage = "Weight unit cannot exceed 10 characters")]
        public string WeightUnit { get; set; } = "kg";
    }
}
