{"format": 1, "restore": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {}}, "projects": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj", "projectName": "EtyraCommerce.Application", "projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "projectName": "EtyraCommerce.Domain", "projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}