﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Global_Trade;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;

namespace EtyraApp.Domain.Entities.Catalog;

public class Category : BaseEntity
{
    public int? ParentCategoryId { get; set; }
    public int? AiPlatformId { get; set; }
    public ICollection<ProductCategory> ProductCategories { get; set; }
    public ICollection<CategoryDescription> CategoryDescriptions { get; set; }
    public ICollection<IntegrationStoreCategory>? IntegrationStoreCategories { get; set; }
    public ICollection<ImportProduct>? ImportProducts { get; set; }

    public string? Image { get; set; }
    public int? ProductsAdded { get; set; } = 0;
    public int? ShoppingCategoryCode { get; set; }
    public HsCode HsCode { get; set; }
    public int HsCodeId { get; set; }

}