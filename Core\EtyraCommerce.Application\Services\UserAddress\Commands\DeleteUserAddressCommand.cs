using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.Services.UserAddress.Commands
{
    /// <summary>
    /// Command for deleting a user address
    /// </summary>
    public class DeleteUserAddressCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// ID of the address to delete
        /// </summary>
        [Required(ErrorMessage = "Address ID is required")]
        public Guid AddressId { get; set; }

        /// <summary>
        /// ID of the user who owns this address (for security validation)
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Indicates whether to perform hard delete (true) or soft delete (false)
        /// Default is false (soft delete)
        /// </summary>
        public bool HardDelete { get; set; } = false;

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public DeleteUserAddressCommand() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public DeleteUserAddressCommand(Guid addressId, Guid userId, bool hardDelete = false)
        {
            AddressId = addressId;
            UserId = userId;
            HardDelete = hardDelete;
        }
    }

    /// <summary>
    /// Command for setting an address as default
    /// </summary>
    public class SetDefaultAddressCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// ID of the address to set as default
        /// </summary>
        [Required(ErrorMessage = "Address ID is required")]
        public Guid AddressId { get; set; }

        /// <summary>
        /// ID of the user who owns this address (for security validation)
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public SetDefaultAddressCommand() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public SetDefaultAddressCommand(Guid addressId, Guid userId)
        {
            AddressId = addressId;
            UserId = userId;
        }
    }

    /// <summary>
    /// Command for activating/deactivating an address
    /// </summary>
    public class ToggleAddressStatusCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// ID of the address to toggle
        /// </summary>
        [Required(ErrorMessage = "Address ID is required")]
        public Guid AddressId { get; set; }

        /// <summary>
        /// ID of the user who owns this address (for security validation)
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// New active status for the address
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public ToggleAddressStatusCommand() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public ToggleAddressStatusCommand(Guid addressId, Guid userId, bool isActive)
        {
            AddressId = addressId;
            UserId = userId;
            IsActive = isActive;
        }
    }
}
