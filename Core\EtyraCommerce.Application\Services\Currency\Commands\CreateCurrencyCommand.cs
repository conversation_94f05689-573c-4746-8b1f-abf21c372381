using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Commands;

/// <summary>
/// Command to create a new currency
/// </summary>
public class CreateCurrencyCommand : IRequest<CustomResponseDto<CurrencyDto>>
{
    /// <summary>
    /// Currency code (ISO 4217 format, e.g., USD, EUR, TRY)
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Currency name (e.g., US Dollar, Euro, Turkish Lira)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Currency symbol (e.g., $, €, ₺)
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Number of decimal places for this currency
    /// </summary>
    public int DecimalPlaces { get; set; } = 2;

    /// <summary>
    /// Whether this currency is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether this currency should be set as default
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Display order for sorting currencies
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Custom format pattern for displaying amounts in this currency
    /// </summary>
    public string? FormatPattern { get; set; }
}
