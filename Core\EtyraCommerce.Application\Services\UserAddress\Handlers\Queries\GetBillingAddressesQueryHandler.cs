using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Queries
{
    /// <summary>
    /// Handler for getting addresses suitable for billing
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class GetBillingAddressesQueryHandler : IRequestHandler<GetBillingAddressesQuery, CustomResponseDto<List<UserAddressDto>>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<GetBillingAddressesQueryHandler> _logger;

        public GetBillingAddressesQueryHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<GetBillingAddressesQueryHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> Handle(GetBillingAddressesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling GetBillingAddressesQuery for UserId: {UserId}, IncludeInactive: {IncludeInactive}",
                    request.UserId, request.IncludeInactive);

                // Validate request
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetBillingAddressesQuery received with empty UserId");
                    return CustomResponseDto<List<UserAddressDto>>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessGetBillingAddressesAsync(request.UserId, request.IncludeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("GetBillingAddressesQuery handled successfully for UserId: {UserId}, Count: {Count}",
                        request.UserId, result.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("GetBillingAddressesQuery failed for UserId: {UserId}, Error: {Error}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling GetBillingAddressesQuery for UserId: {UserId}", request.UserId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving billing addresses");
            }
        }
    }
}
