using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.Services.User.Commands;
using EtyraCommerce.Application.Services.User.Handlers.Commands;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class RegisterUserCommandHandlerTests
    {
        private readonly Mock<IUserProcessService> _mockUserProcessService;
        private readonly Mock<ILogger<RegisterUserCommandHandler>> _mockLogger;
        private readonly RegisterUserCommandHandler _handler;

        public RegisterUserCommandHandlerTests()
        {
            _mockUserProcessService = new Mock<IUserProcessService>();
            _mockLogger = new Mock<ILogger<RegisterUserCommandHandler>>();
            _handler = new RegisterUserCommandHandler(_mockUserProcessService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidRegistrationData_ReturnsSuccessWithUserDto()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                PhoneNumber = "+1234567890",
                AcceptTerms = true,
                Culture = "en-US",
                TimeZone = "UTC"
            };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = command.Email,
                Username = command.Username,
                FirstName = command.FirstName,
                LastName = command.LastName,
                IsActive = true,
                IsEmailConfirmed = false
            };

            var createUserDto = new CreateUserDto
            {
                FirstName = command.FirstName,
                LastName = command.LastName,
                Email = command.Email,
                PhoneNumber = command.PhoneNumber,
                Username = command.Username,
                Password = command.Password,
                ConfirmPassword = command.ConfirmPassword,
                Culture = command.Culture,
                TimeZone = command.TimeZone,
                AcceptTerms = command.AcceptTerms,
                SubscribeToNewsletter = command.SubscribeToNewsletter
            };

            var expectedResponse = CustomResponseDto<UserDto>.Created(expectedUserDto, "User registered successfully");

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(expectedUserDto.Id);
            result.Data.Email.Should().Be(expectedUserDto.Email);
            result.Data.Username.Should().Be(expectedUserDto.Username);
            result.Data.FirstName.Should().Be(expectedUserDto.FirstName);
            result.Data.LastName.Should().Be(expectedUserDto.LastName);

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.Is<CreateUserDto>(dto =>
                    dto.Email == command.Email &&
                    dto.Username == command.Username &&
                    dto.FirstName == command.FirstName &&
                    dto.LastName == command.LastName &&
                    dto.Password == command.Password &&
                    dto.ConfirmPassword == command.ConfirmPassword &&
                    dto.PhoneNumber == command.PhoneNumber &&
                    dto.AcceptTerms == command.AcceptTerms &&
                    dto.Culture == command.Culture &&
                    dto.TimeZone == command.TimeZone
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmailAlreadyExists_ReturnsFailureFromProcessService()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.BadRequest("Email already exists");

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email already exists");

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_UsernameAlreadyExists_ReturnsFailureFromProcessService()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "existinguser",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.BadRequest("Username already exists");

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Username already exists");

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during registration");

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()),
                Times.Once
            );
        }

        [Theory]
        [InlineData("John", "Doe", "<EMAIL>", "johndoe")]
        [InlineData("Jane", "Smith", "<EMAIL>", "janesmith")]
        [InlineData("Test", "User", "<EMAIL>", "testuser123")]
        public async Task Handle_VariousValidInputs_CallsProcessServiceWithCorrectParameters(
            string firstName, string lastName, string email, string username)
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = firstName,
                LastName = lastName,
                Email = email,
                Username = username,
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.Created(
                new UserDto { Id = Guid.NewGuid(), Email = email, Username = username },
                "User registered successfully"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.Is<CreateUserDto>(dto =>
                    dto.FirstName == firstName &&
                    dto.LastName == lastName &&
                    dto.Email == email &&
                    dto.Username == username
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_OptionalFieldsNotProvided_StillCallsProcessService()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true,
                // PhoneNumber, Culture, TimeZone not provided
            };

            var expectedResponse = CustomResponseDto<UserDto>.Created(
                new UserDto { Id = Guid.NewGuid(), Email = command.Email },
                "User registered successfully"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.Is<CreateUserDto>(dto =>
                    dto.Email == command.Email &&
                    dto.Username == command.Username &&
                    dto.PhoneNumber == command.PhoneNumber &&
                    dto.Culture == command.Culture &&
                    dto.TimeZone == command.TimeZone
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_SubscribeToNewsletterTrue_PassesCorrectValueToProcessService()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true,
                SubscribeToNewsletter = true
            };

            var expectedResponse = CustomResponseDto<UserDto>.Created(
                new UserDto { Id = Guid.NewGuid(), Email = command.Email },
                "User registered successfully"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessRegisterAsync(It.IsAny<CreateUserDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockUserProcessService.Verify(
                x => x.ProcessRegisterAsync(It.Is<CreateUserDto>(dto =>
                    dto.SubscribeToNewsletter == true
                )),
                Times.Once
            );
        }
    }
}
