using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Features.Shipping.Countries.Queries;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Shipping
{
    /// <summary>
    /// Country service implementation - delegates to CQRS handlers
    /// </summary>
    public class CountryService : ICountryService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CountryService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CountryService(
            IMediator mediator,
            ILogger<CountryService> logger)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates a new country
        /// </summary>
        public async Task<CreateCountryResponse> CreateCountryAsync(CreateCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("CountryService.CreateCountryAsync called for: {CountryName} ({CountryCode})", command.Name, command.Code);
                
                var result = await _mediator.Send(command, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CountryService.CreateCountryAsync for: {CountryName} ({CountryCode})", command.Name, command.Code);
                throw;
            }
        }

        /// <summary>
        /// Updates an existing country
        /// </summary>
        public async Task<UpdateCountryResponse> UpdateCountryAsync(UpdateCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("CountryService.UpdateCountryAsync called for CountryId: {CountryId}", command.Id);
                
                var result = await _mediator.Send(command, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CountryService.UpdateCountryAsync for CountryId: {CountryId}", command.Id);
                throw;
            }
        }

        /// <summary>
        /// Deletes a country (soft delete)
        /// </summary>
        public async Task<DeleteCountryResponse> DeleteCountryAsync(DeleteCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("CountryService.DeleteCountryAsync called for CountryId: {CountryId}", command.Id);
                
                var result = await _mediator.Send(command, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CountryService.DeleteCountryAsync for CountryId: {CountryId}", command.Id);
                throw;
            }
        }

        /// <summary>
        /// Gets a country by ID
        /// </summary>
        public async Task<GetCountryByIdResponse> GetCountryByIdAsync(GetCountryByIdQuery query, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("CountryService.GetCountryByIdAsync called for CountryId: {CountryId}", query.Id);
                
                var result = await _mediator.Send(query, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CountryService.GetCountryByIdAsync for CountryId: {CountryId}", query.Id);
                throw;
            }
        }

        /// <summary>
        /// Gets all countries with optional filtering
        /// </summary>
        public async Task<GetAllCountriesResponse> GetAllCountriesAsync(GetAllCountriesQuery query, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("CountryService.GetAllCountriesAsync called with filters - EU: {IsEuMember}, Shipping: {IsShippingEnabled}, Search: {SearchTerm}",
                    query.IsEuMember, query.IsShippingEnabled, query.SearchTerm);
                
                var result = await _mediator.Send(query, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CountryService.GetAllCountriesAsync");
                throw;
            }
        }
    }
}
