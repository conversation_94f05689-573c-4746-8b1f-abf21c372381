using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using EtyraCommerce.Application.Services.Currency.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers;

/// <summary>
/// Currency management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CurrencyController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CurrencyController> _logger;

    public CurrencyController(
        IMediator mediator,
        ILogger<CurrencyController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all currencies
    /// </summary>
    /// <param name="onlyActive">Filter only active currencies</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>List of currencies</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetAllCurrencies(
        [FromQuery] bool? onlyActive = null,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting all currencies with filters - OnlyActive: {OnlyActive}, Tracking: {Tracking}",
                onlyActive, tracking);

            var query = new GetAllCurrenciesQuery
            {
                OnlyActive = onlyActive,
                Tracking = tracking
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} currencies successfully", result.Data?.Count ?? 0);
                return Ok(result);
            }

            _logger.LogWarning("Failed to retrieve currencies: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all currencies");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get currency by ID
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>Currency details</returns>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCurrencyById(
        Guid id,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting currency by ID: {CurrencyId}", id);

            var query = new GetCurrencyByIdQuery(id, tracking);
            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved currency {CurrencyId} successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Currency {CurrencyId} not found: {Message}", id, result.Message);
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting currency {CurrencyId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get currency by code
    /// </summary>
    /// <param name="code">Currency code (e.g., USD, EUR, TRY)</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>Currency details</returns>
    [HttpGet("by-code/{code}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCurrencyByCode(
        string code,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting currency by code: {CurrencyCode}", code);

            var query = new GetCurrencyByCodeQuery(code, tracking);
            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved currency {CurrencyCode} successfully", code);
                return Ok(result);
            }

            _logger.LogWarning("Currency {CurrencyCode} not found: {Message}", code, result.Message);
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting currency {CurrencyCode}", code);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get default currency
    /// </summary>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>Default currency details</returns>
    [HttpGet("default")]
    [AllowAnonymous]
    public async Task<IActionResult> GetDefaultCurrency(
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting default currency");

            var query = new GetDefaultCurrencyQuery
            {
                Tracking = tracking
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved default currency successfully");
                return Ok(result);
            }

            _logger.LogWarning("Default currency not found: {Message}", result.Message);
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting default currency");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Create a new currency
    /// </summary>
    /// <param name="command">Create currency command</param>
    /// <returns>Created currency</returns>
    [HttpPost]
    public async Task<IActionResult> CreateCurrency([FromBody] CreateCurrencyCommand command)
    {
        try
        {
            _logger.LogInformation("Creating new currency: {Code}", command.Code);

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {Code} created successfully with ID: {CurrencyId}", 
                    command.Code, result.Data?.Id);
                return CreatedAtAction(nameof(GetCurrencyById), new { id = result.Data?.Id }, result);
            }

            _logger.LogWarning("Failed to create currency {Code}: {Message}", command.Code, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating currency {Code}", command.Code);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Update an existing currency
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="command">Update currency command</param>
    /// <returns>Updated currency</returns>
    [HttpPut("{id:guid}")]
    public async Task<IActionResult> UpdateCurrency(Guid id, [FromBody] UpdateCurrencyCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                _logger.LogWarning("Currency ID mismatch: URL ID {UrlId} vs Command ID {CommandId}", id, command.Id);
                return BadRequest("Currency ID mismatch");
            }

            _logger.LogInformation("Updating currency: {CurrencyId}", id);

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} updated successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update currency {CurrencyId}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating currency {CurrencyId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Delete a currency
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>No content if successful</returns>
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteCurrency(Guid id)
    {
        try
        {
            _logger.LogInformation("Deleting currency: {CurrencyId}", id);

            var command = new DeleteCurrencyCommand { Id = id };
            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} deleted successfully", id);
                return NoContent();
            }

            _logger.LogWarning("Failed to delete currency {CurrencyId}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting currency {CurrencyId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Set a currency as default
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>Updated currency</returns>
    [HttpPost("{id:guid}/set-default")]
    public async Task<IActionResult> SetDefaultCurrency(Guid id)
    {
        try
        {
            _logger.LogInformation("Setting currency {CurrencyId} as default", id);

            var command = new SetDefaultCurrencyCommand(id);
            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} set as default successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to set currency {CurrencyId} as default: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while setting currency {CurrencyId} as default", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
}
