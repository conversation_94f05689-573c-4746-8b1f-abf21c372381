using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Payment.Queries;

/// <summary>
/// Query to get all payment methods
/// </summary>
public class GetAllPaymentMethodsQuery : IRequest<CustomResponseDto<List<PaymentMethodDto>>>
{
    /// <summary>
    /// Whether to include only active payment methods
    /// </summary>
    public bool? OnlyActive { get; set; }

    /// <summary>
    /// Filter by payment method type
    /// </summary>
    public PaymentMethodType? Type { get; set; }

    /// <summary>
    /// Order amount to check availability and calculate fees
    /// </summary>
    public decimal? OrderAmount { get; set; }

    /// <summary>
    /// Order currency to check availability and calculate fees
    /// </summary>
    public string? OrderCurrency { get; set; }

    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="onlyActive">Whether to include only active payment methods</param>
    /// <param name="type">Filter by payment method type</param>
    /// <param name="orderAmount">Order amount to check availability</param>
    /// <param name="orderCurrency">Order currency to check availability</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetAllPaymentMethodsQuery(
        bool? onlyActive = null, 
        PaymentMethodType? type = null,
        decimal? orderAmount = null,
        string? orderCurrency = null,
        bool tracking = false)
    {
        OnlyActive = onlyActive;
        Type = type;
        OrderAmount = orderAmount;
        OrderCurrency = orderCurrency;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetAllPaymentMethodsQuery() { }
}

/// <summary>
/// Query to get active payment methods for checkout
/// </summary>
public class GetActivePaymentMethodsQuery : IRequest<CustomResponseDto<List<PaymentMethodDto>>>
{
    /// <summary>
    /// Order amount to check availability and calculate fees
    /// </summary>
    public decimal OrderAmount { get; set; }

    /// <summary>
    /// Order currency to check availability and calculate fees
    /// </summary>
    public string OrderCurrency { get; set; } = "TRY";

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="orderAmount">Order amount to check availability</param>
    /// <param name="orderCurrency">Order currency to check availability</param>
    public GetActivePaymentMethodsQuery(decimal orderAmount, string orderCurrency = "TRY")
    {
        OrderAmount = orderAmount;
        OrderCurrency = orderCurrency;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetActivePaymentMethodsQuery() { }
}
