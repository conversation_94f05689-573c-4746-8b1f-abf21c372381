using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class TransactionCategory : BaseEntity
{
    [MaxLength(100)]
    public string Name { get; set; }
    
    public TransactionType Type { get; set; } // <PERSON><PERSON>r veya Gider kategorisi
    
    public int? ParentCategoryId { get; set; }
    public TransactionCategory ParentCategory { get; set; }
    
    public ICollection<TransactionCategory> SubCategories { get; set; }
    
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    
    public ICollection<Transaction> Transactions { get; set; }
    public bool IsActive { get; set; }
    public TransactionType TransactionType { get; set; }


}