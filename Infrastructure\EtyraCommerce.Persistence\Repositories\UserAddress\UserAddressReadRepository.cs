using EtyraCommerce.Application.Repositories.UserAddress;
using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.UserAddress
{
    /// <summary>
    /// Read repository implementation for UserAddress entity
    /// </summary>
    public class UserAddressReadRepository : ReadRepository<Domain.Entities.User.UserAddress>, IUserAddressReadRepository
    {
        public UserAddressReadRepository(EtyraCommerceDbContext context) : base(context)
        {
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesAsync(Guid userId, bool includeInactive = false)
        {
            var query = Table.Where(a => a.UserId == userId && !a.IsDeleted);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByTypeAsync(Guid userId, AddressType addressType, bool includeInactive = false)
        {
            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a => a.Type == addressType || a.Type == AddressType.Both);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<Domain.Entities.User.UserAddress?> GetDefaultAddressAsync(Guid userId, AddressType? addressType = null)
        {
            var query = Table
                .Where(a => a.UserId == userId && a.IsDefault && a.IsActive && !a.IsDeleted);

            if (addressType.HasValue)
            {
                query = query.Where(a => a.Type == addressType.Value || a.Type == AddressType.Both);
            }

            return await query.FirstOrDefaultAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetBillingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a => a.Type == AddressType.Billing || a.Type == AddressType.Both);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetShippingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a => a.Type == AddressType.Shipping || a.Type == AddressType.Both);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<Domain.Entities.User.UserAddress?> GetUserAddressByIdAsync(Guid addressId, Guid userId)
        {
            return await Table
                .Where(a => a.Id == addressId && a.UserId == userId && !a.IsDeleted)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> UserHasAddressesAsync(Guid userId, bool includeInactive = false)
        {
            var query = Table.Where(a => a.UserId == userId && !a.IsDeleted);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query.AnyAsync();
        }

        public async Task<int> GetUserAddressCountAsync(Guid userId, bool includeInactive = false)
        {
            var query = Table.Where(a => a.UserId == userId && !a.IsDeleted);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query.CountAsync();
        }

        public async Task<bool> AddressBelongsToUserAsync(Guid addressId, Guid userId)
        {
            return await Table
                .Where(a => a.Id == addressId && a.UserId == userId && !a.IsDeleted)
                .AnyAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByIdsAsync(List<Guid> addressIds, Guid userId)
        {
            return await Table
                .Where(a => addressIds.Contains(a.Id) && a.UserId == userId && !a.IsDeleted)
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> SearchUserAddressesAsync(Guid userId, string searchText, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return await GetUserAddressesAsync(userId, includeInactive);

            var searchTerm = searchText.Trim().ToLower();

            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a =>
                    a.Address.Street.ToLower().Contains(searchTerm) ||
                    a.Address.City.ToLower().Contains(searchTerm) ||
                    a.Address.State.ToLower().Contains(searchTerm) ||
                    a.Address.Country.ToLower().Contains(searchTerm) ||
                    (a.Address.AddressLine2 != null && a.Address.AddressLine2.ToLower().Contains(searchTerm)) ||
                    (a.Label != null && a.Label.ToLower().Contains(searchTerm)) ||
                    a.FirstName.ToLower().Contains(searchTerm) ||
                    a.LastName.ToLower().Contains(searchTerm));

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByCountryAsync(Guid userId, string country, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(country))
                return new List<Domain.Entities.User.UserAddress>();

            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a => a.Address.Country.ToLower() == country.Trim().ToLower());

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByCityAsync(Guid userId, string city, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(city))
                return new List<Domain.Entities.User.UserAddress>();

            var query = Table
                .Where(a => a.UserId == userId && !a.IsDeleted)
                .Where(a => a.Address.City.ToLower() == city.Trim().ToLower());

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.IsDefault)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Domain.Entities.User.UserAddress>> GetRecentUserAddressesAsync(Guid userId, int count = 5, bool includeInactive = false)
        {
            var query = Table.Where(a => a.UserId == userId && !a.IsDeleted);

            if (!includeInactive)
                query = query.Where(a => a.IsActive);

            return await query
                .OrderByDescending(a => a.UpdatedAt ?? a.CreatedAt)
                .Take(count)
                .ToListAsync();
        }
    }
}
