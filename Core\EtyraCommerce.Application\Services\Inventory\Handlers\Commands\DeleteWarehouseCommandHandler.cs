using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for deleting warehouses
    /// </summary>
    public class DeleteWarehouseCommandHandler : IRequestHandler<DeleteWarehouseCommand, CustomResponseDto<bool>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<DeleteWarehouseCommandHandler> _logger;

        public DeleteWarehouseCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<DeleteWarehouseCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(DeleteWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling delete warehouse command for ID: {WarehouseId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("Warehouse ID is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessDeleteWarehouseAsync(request.Id);

                _logger.LogInformation("Delete warehouse command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling delete warehouse command for ID: {WarehouseId}", request.Id);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while deleting warehouse");
            }
        }
    }
}
