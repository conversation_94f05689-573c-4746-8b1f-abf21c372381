using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Product entity
    /// </summary>
    public class ProductConfiguration : AuditableBaseEntityConfiguration<Product>
    {
        public override void Configure(EntityTypeBuilder<Product> builder)
        {
            // Apply base configuration (BaseEntity + AuditableBaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("products", "etyra_core");

            #region Basic Properties

            // Model
            builder.Property(x => x.Model)
                .HasColumnName("model")
                .HasMaxLength(20)
                .IsRequired();

            // Name
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Stars
            builder.Property(x => x.Stars)
                .HasColumnName("stars")
                .IsRequired(false);

            // AI Platform ID
            builder.Property(x => x.AiPlatformId)
                .HasColumnName("ai_platform_id")
                .IsRequired(false);

            #endregion

            #region Product Codes & Identifiers

            // EAN
            builder.Property(x => x.EAN)
                .HasColumnName("ean")
                .HasMaxLength(13)
                .IsRequired(false);

            // MPN
            builder.Property(x => x.MPN)
                .HasColumnName("mpn")
                .HasMaxLength(12)
                .IsRequired(false);

            // Barcode
            builder.Property(x => x.Barcode)
                .HasColumnName("barcode")
                .HasMaxLength(20)
                .IsRequired(false);

            // Brand
            builder.Property(x => x.Brand)
                .HasColumnName("brand")
                .HasMaxLength(20)
                .IsRequired(false);

            // UPC
            builder.Property(x => x.UPC)
                .HasColumnName("upc")
                .HasMaxLength(12)
                .IsRequired(false);

            // SKU
            builder.Property(x => x.SKU)
                .HasColumnName("sku")
                .HasMaxLength(50)
                .IsRequired();

            #endregion

            #region Value Objects

            // ProductDimensions (JSON)
            ValueObjectConversions.ConfigureNullableProductDimensionsAsJson<Product>(
                builder.Property(x => x.Dimensions),
                "dimensions");

            // BasePrice (separate columns)
            builder.OwnsOne(x => x.BasePrice, price =>
            {
                price.Property(p => p.Amount)
                    .HasColumnName("base_price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                price.Property(p => p.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("base_price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Cost (separate columns, optional)
            builder.OwnsOne(x => x.Cost, cost =>
            {
                cost.Property(c => c.Amount)
                    .HasColumnName("cost_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                cost.Property(c => c.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("cost_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // SalePrice (separate columns, optional)
            builder.OwnsOne(x => x.SalePrice, salePrice =>
            {
                salePrice.Property(sp => sp.Amount)
                    .HasColumnName("sale_price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                salePrice.Property(sp => sp.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("sale_price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // DefaultCurrency
            ValueObjectConversions.ConfigureCurrency<Product>(
                builder.Property(x => x.DefaultCurrency),
                "default_currency");

            #endregion

            #region Images

            // Main Image
            builder.Property(x => x.MainImage)
                .HasColumnName("main_image")
                .HasMaxLength(500)
                .IsRequired(false);

            // Thumbnail Image
            builder.Property(x => x.ThumbnailImage)
                .HasColumnName("thumbnail_image")
                .HasMaxLength(500)
                .IsRequired(false);

            #endregion

            #region Status & Type

            // Status
            builder.Property(x => x.Status)
                .HasColumnName("status")
                .HasConversion<int>()
                .IsRequired();

            // Type
            builder.Property(x => x.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();

            // Boolean Properties
            builder.Property(x => x.IsFeatured)
                .HasColumnName("is_featured")
                .HasDefaultValue(false)
                .IsRequired();

            builder.Property(x => x.IsDigital)
                .HasColumnName("is_digital")
                .HasDefaultValue(false)
                .IsRequired();

            #endregion

            #region SEO & Marketing

            // Slug
            builder.Property(x => x.Slug)
                .HasColumnName("slug")
                .HasMaxLength(200)
                .IsRequired();

            // Meta Title
            builder.Property(x => x.MetaTitle)
                .HasColumnName("meta_title")
                .HasMaxLength(120)
                .IsRequired(false);

            // Meta Description
            builder.Property(x => x.MetaDescription)
                .HasColumnName("meta_description")
                .HasMaxLength(350)
                .IsRequired(false);

            // Meta Keywords
            builder.Property(x => x.MetaKeywords)
                .HasColumnName("meta_keywords")
                .HasMaxLength(200)
                .IsRequired(false);

            // Tags
            builder.Property(x => x.Tags)
                .HasColumnName("tags")
                .HasMaxLength(500)
                .IsRequired(false);

            #endregion

            #region Dates

            // Sale Start Date
            builder.Property(x => x.SaleStartDate)
                .HasColumnName("sale_start_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Sale End Date
            builder.Property(x => x.SaleEndDate)
                .HasColumnName("sale_end_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Launch Date
            builder.Property(x => x.LaunchDate)
                .HasColumnName("launch_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Discontinue Date
            builder.Property(x => x.DiscontinueDate)
                .HasColumnName("discontinue_date")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            #endregion

            #region Navigation Properties

            // Product Descriptions (One-to-Many)
            builder.HasMany(x => x.Descriptions)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product Categories (One-to-Many)
            builder.HasMany(x => x.ProductCategories)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product Images (One-to-Many)
            builder.HasMany(x => x.Images)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product Discounts (One-to-Many)
            builder.HasMany(x => x.Discounts)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Warehouse Products (One-to-Many)
            builder.HasMany(x => x.WarehouseProducts)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product Variants (One-to-Many)
            builder.HasMany(x => x.Variants)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product Attributes (One-to-Many)
            builder.HasMany(x => x.Attributes)
                .WithOne(x => x.Product)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Unique Indexes
            builder.HasIndex(x => x.SKU)
                .IsUnique()
                .HasDatabaseName("ix_products_sku_unique");

            builder.HasIndex(x => x.Slug)
                .IsUnique()
                .HasDatabaseName("ix_products_slug_unique");

            // Performance Indexes
            builder.HasIndex(x => x.Model)
                .HasDatabaseName("ix_products_model");

            builder.HasIndex(x => x.Name)
                .HasDatabaseName("ix_products_name");

            builder.HasIndex(x => x.Brand)
                .HasDatabaseName("ix_products_brand");

            builder.HasIndex(x => x.Status)
                .HasDatabaseName("ix_products_status");

            builder.HasIndex(x => x.Type)
                .HasDatabaseName("ix_products_type");

            builder.HasIndex(x => x.IsFeatured)
                .HasDatabaseName("ix_products_is_featured");

            // Product Code Indexes
            builder.HasIndex(x => x.EAN)
                .HasDatabaseName("ix_products_ean");

            builder.HasIndex(x => x.Barcode)
                .HasDatabaseName("ix_products_barcode");

            // Composite Indexes
            builder.HasIndex(x => new { x.Status, x.Type })
                .HasDatabaseName("ix_products_status_type");

            builder.HasIndex(x => new { x.Brand, x.Status })
                .HasDatabaseName("ix_products_brand_status");

            builder.HasIndex(x => new { x.IsFeatured, x.Status })
                .HasDatabaseName("ix_products_featured_status");

            // Sale Indexes
            builder.HasIndex(x => x.SaleStartDate)
                .HasDatabaseName("ix_products_sale_start_date");

            builder.HasIndex(x => x.SaleEndDate)
                .HasDatabaseName("ix_products_sale_end_date");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_Products_Model_NotEmpty",
                "LENGTH(TRIM(model)) > 0");

            builder.HasCheckConstraint("CK_Products_Name_NotEmpty",
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("CK_Products_SKU_NotEmpty",
                "LENGTH(TRIM(sku)) > 0");

            builder.HasCheckConstraint("CK_Products_Stars_Range",
                "stars IS NULL OR (stars >= 1 AND stars <= 5)");

            builder.HasCheckConstraint("CK_Products_Stock_NonNegative",
                "total_stock_quantity >= 0");

            builder.HasCheckConstraint("CK_Products_MinStock_NonNegative",
                "min_stock_alert >= 0");

            builder.HasCheckConstraint("CK_Products_SaleDates",
                "sale_start_date IS NULL OR sale_end_date IS NULL OR sale_start_date <= sale_end_date");

            #endregion
        }

        protected override string GetTableName() => "products";
    }
}
