using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Queries
{
    /// <summary>
    /// Handler for GetAllCategoriesQuery
    /// </summary>
    public class GetAllCategoriesQueryHandler : IRequestHandler<GetAllCategoriesQuery, CustomResponseDto<PagedResult<CategoryDto>>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<GetAllCategoriesQueryHandler> _logger;

        public GetAllCategoriesQueryHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<GetAllCategoriesQueryHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<CategoryDto>>> Handle(GetAllCategoriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get all categories query with search term: {SearchTerm}", request.SearchTerm ?? "None");

                // Validation
                if (request.PageNumber < 1)
                    return CustomResponseDto<PagedResult<CategoryDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<CategoryDto>>.BadRequest("Page size must be between 1 and 100");

                if (request.Level.HasValue && (request.Level < 0 || request.Level > 10))
                    return CustomResponseDto<PagedResult<CategoryDto>>.BadRequest("Level must be between 0 and 10");

                if (!string.IsNullOrEmpty(request.SearchTerm) && request.SearchTerm.Length > 100)
                    return CustomResponseDto<PagedResult<CategoryDto>>.BadRequest("Search term cannot exceed 100 characters");

                // Delegate to process service
                var result = await _categoryProcessService.ProcessGetAllCategoriesAsync(
                    request.SearchTerm,
                    request.IsActive,
                    request.ShowInMenu,
                    request.ParentCategoryId,
                    request.Level,
                    request.IncludeChildren,
                    request.IncludeDescriptions,
                    request.LanguageCode,
                    request.SortBy,
                    request.SortDirection,
                    request.PageNumber,
                    request.PageSize);

                _logger.LogInformation("Get all categories query processed successfully");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get all categories query");
                return CustomResponseDto<PagedResult<CategoryDto>>.InternalServerError("An error occurred while retrieving categories");
            }
        }
    }
}
