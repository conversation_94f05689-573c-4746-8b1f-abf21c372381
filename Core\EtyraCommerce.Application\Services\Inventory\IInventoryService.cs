using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;

namespace EtyraCommerce.Application.Services.Inventory
{
    /// <summary>
    /// Inventory service interface for MediatR command/query dispatch
    /// </summary>
    public interface IInventoryService
    {
        #region Inventory Management

        /// <summary>
        /// Creates a new inventory item
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> CreateInventoryAsync(CreateInventoryDto createDto);

        /// <summary>
        /// Updates an inventory item
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> UpdateInventoryAsync(UpdateInventoryDto updateDto);

        /// <summary>
        /// Gets inventory by ID
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> GetInventoryByIdAsync(Guid id);

        /// <summary>
        /// Gets inventory by product ID
        /// </summary>
        Task<CustomResponseDto<List<InventoryDto>>> GetInventoryByProductAsync(Guid productId, Guid? warehouseId = null, bool activeWarehousesOnly = true);

        /// <summary>
        /// Gets stock status for a product
        /// </summary>
        Task<CustomResponseDto<StockStatusDto>> GetStockStatusAsync(Guid productId, bool activeWarehousesOnly = true);

        /// <summary>
        /// Gets low stock items
        /// </summary>
        Task<CustomResponseDto<List<LowStockItemDto>>> GetLowStockItemsAsync(Guid? warehouseId = null, bool activeWarehousesOnly = true, int? maxItems = null);

        #endregion

        #region Stock Operations

        /// <summary>
        /// Reserves stock for an order
        /// </summary>
        Task<CustomResponseDto<bool>> ReserveStockAsync(StockReservationDto reservationDto);

        /// <summary>
        /// Allocates reserved stock for confirmed order
        /// </summary>
        Task<CustomResponseDto<bool>> AllocateStockAsync(StockAllocationDto allocationDto);

        /// <summary>
        /// Releases stock reservation
        /// </summary>
        Task<CustomResponseDto<bool>> ReleaseReservationAsync(string reference, string? reason = null, Guid? userId = null);

        /// <summary>
        /// Adjusts stock quantity (physical count)
        /// </summary>
        Task<CustomResponseDto<bool>> AdjustStockAsync(StockAdjustmentDto adjustmentDto);

        /// <summary>
        /// Transfers stock between warehouses
        /// </summary>
        Task<CustomResponseDto<bool>> TransferStockAsync(StockTransferDto transferDto);

        #endregion

        #region Warehouse Management

        /// <summary>
        /// Creates a new warehouse
        /// </summary>
        Task<CustomResponseDto<WarehouseDto>> CreateWarehouseAsync(CreateWarehouseDto createDto);

        /// <summary>
        /// Updates a warehouse
        /// </summary>
        Task<CustomResponseDto<WarehouseDto>> UpdateWarehouseAsync(UpdateWarehouseDto updateDto);

        /// <summary>
        /// Deletes a warehouse
        /// </summary>
        Task<CustomResponseDto<bool>> DeleteWarehouseAsync(Guid id);

        /// <summary>
        /// Gets all warehouses with filtering and pagination
        /// </summary>
        Task<CustomResponseDto<PagedResult<WarehouseDto>>> GetAllWarehousesAsync(WarehouseFilterDto filterDto);

        #endregion

        #region Transactions & Reporting

        /// <summary>
        /// Gets inventory transactions with filtering and pagination
        /// </summary>
        Task<CustomResponseDto<PagedResult<InventoryTransactionDto>>> GetInventoryTransactionsAsync(InventoryTransactionFilterDto filterDto);

        #endregion
    }
}
