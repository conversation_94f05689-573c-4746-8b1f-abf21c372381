using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Features.Shipping.Countries.Queries;

namespace EtyraCommerce.Application.Interfaces.Services.Shipping
{
    /// <summary>
    /// Interface for Country Process Service - Contains business logic methods
    /// Called by CQRS Handlers for actual business processing
    /// </summary>
    public interface ICountryProcessService
    {
        /// <summary>
        /// Processes country creation with business logic
        /// </summary>
        /// <param name="command">Create country command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Create country response</returns>
        Task<CreateCountryResponse> ProcessCreateCountryAsync(CreateCountryCommand command, CancellationToken cancellationToken = default);

        /// <summary>
        /// Processes country update with business logic
        /// </summary>
        /// <param name="command">Update country command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Update country response</returns>
        Task<UpdateCountryResponse> ProcessUpdateCountryAsync(UpdateCountryCommand command, CancellationToken cancellationToken = default);

        /// <summary>
        /// Processes country deletion with business logic
        /// </summary>
        /// <param name="command">Delete country command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Delete country response</returns>
        Task<DeleteCountryResponse> ProcessDeleteCountryAsync(DeleteCountryCommand command, CancellationToken cancellationToken = default);

        /// <summary>
        /// Processes get country by ID with business logic
        /// </summary>
        /// <param name="query">Get country by ID query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Get country by ID response</returns>
        Task<GetCountryByIdResponse> ProcessGetCountryByIdAsync(GetCountryByIdQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Processes get all countries with business logic
        /// </summary>
        /// <param name="query">Get all countries query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Get all countries response</returns>
        Task<GetAllCountriesResponse> ProcessGetAllCountriesAsync(GetAllCountriesQuery query, CancellationToken cancellationToken = default);
    }
}
