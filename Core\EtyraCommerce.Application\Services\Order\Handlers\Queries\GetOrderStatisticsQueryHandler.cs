using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Queries
{
    /// <summary>
    /// Handler for GetOrderStatisticsQuery
    /// </summary>
    public class GetOrderStatisticsQueryHandler : IRequestHandler<GetOrderStatisticsQuery, CustomResponseDto<OrderStatisticsDto>>
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<GetOrderStatisticsQueryHandler> _logger;

        public GetOrderStatisticsQueryHandler(
            IOrderService orderService,
            ILogger<GetOrderStatisticsQueryHandler> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<OrderStatisticsDto>> Handle(GetOrderStatisticsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get order statistics query");

                // Validation
                if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
                    return CustomResponseDto<OrderStatisticsDto>.BadRequest("Start date cannot be greater than end date");

                if (request.TopCustomersCount < 0 || request.TopCustomersCount > 100)
                    return CustomResponseDto<OrderStatisticsDto>.BadRequest("Top customers count must be between 0 and 100");

                if (request.RecentOrdersCount < 0 || request.RecentOrdersCount > 100)
                    return CustomResponseDto<OrderStatisticsDto>.BadRequest("Recent orders count must be between 0 and 100");

                // Delegate to service
                var result = await _orderService.GetOrderStatisticsAsync(
                    request.StartDate,
                    request.EndDate,
                    request.CustomerId,
                    request.Currency,
                    request.TopCustomersCount,
                    request.RecentOrdersCount);

                _logger.LogInformation("Get order statistics query processed successfully");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get order statistics query");
                return CustomResponseDto<OrderStatisticsDto>.InternalServerError("An error occurred while retrieving order statistics");
            }
        }
    }
}
