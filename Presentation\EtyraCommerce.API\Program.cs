using Autofac;
using Autofac.Extensions.DependencyInjection;
using EtyraCommerce.Application.Services.Authentication;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.Services.Category;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Product;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.Services.UserAddress;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Seeds;
using EtyraCommerce.Persistence.Services.Authentication;
using EtyraCommerce.Persistence.Services.Cart;
using EtyraCommerce.Persistence.Services.Category;
using EtyraCommerce.Persistence.Services.Inventory;
using EtyraCommerce.Persistence.Services.Order;
using EtyraCommerce.Persistence.Services.Product;
using EtyraCommerce.Persistence.Services.User;
using EtyraCommerce.Persistence.Services.UserAddress;
using EtyraCommerce.Persistence.UnitOfWork;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Configure Autofac
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());
builder.Host.ConfigureContainer<ContainerBuilder>(containerBuilder =>
{
    // Register services
    containerBuilder.RegisterType<UserService>().As<IUserService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<UserProcessService>().As<IUserProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<UserAddressService>().As<IUserAddressService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<UserAddressProcessService>().As<IUserAddressProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<CategoryService>().As<ICategoryService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<CategoryProcessService>().As<ICategoryProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<ProductService>().As<IProductService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<ProductProcessService>().As<IProductProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<OrderService>().As<IOrderService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<OrderProcessService>().As<IOrderProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<InventoryService>().As<IInventoryService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<InventoryProcessService>().As<IInventoryProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<CartService>().As<ICartService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<CartProcessService>().As<ICartProcessService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<JwtService>().As<IJwtService>().InstancePerLifetimeScope();
    containerBuilder.RegisterType<UnitOfWork>().As<IUnitOfWork>().InstancePerLifetimeScope();

    // Register specific repositories
    containerBuilder.RegisterType<EtyraCommerce.Persistence.Repositories.User.UserReadRepository>()
        .As<EtyraCommerce.Application.Repositories.User.IUserReadRepository>()
        .InstancePerLifetimeScope();

    containerBuilder.RegisterType<EtyraCommerce.Persistence.Repositories.User.UserWriteRepository>()
        .As<EtyraCommerce.Application.Repositories.User.IUserWriteRepository>()
        .InstancePerLifetimeScope();

    containerBuilder.RegisterType<EtyraCommerce.Persistence.Repositories.UserAddress.UserAddressReadRepository>()
        .As<EtyraCommerce.Application.Repositories.UserAddress.IUserAddressReadRepository>()
        .InstancePerLifetimeScope();

    containerBuilder.RegisterType<EtyraCommerce.Persistence.Repositories.UserAddress.UserAddressWriteRepository>()
        .As<EtyraCommerce.Application.Repositories.UserAddress.IUserAddressWriteRepository>()
        .InstancePerLifetimeScope();

    // Register generic repositories
    containerBuilder.RegisterGeneric(typeof(EtyraCommerce.Persistence.Repositories.ReadRepository<>))
        .As(typeof(EtyraCommerce.Application.Repositories.IReadRepository<>))
        .InstancePerLifetimeScope();

    containerBuilder.RegisterGeneric(typeof(EtyraCommerce.Persistence.Repositories.WriteRepository<>))
        .As(typeof(EtyraCommerce.Application.Repositories.IWriteRepository<>))
        .InstancePerLifetimeScope();
});

// Add Entity Framework
builder.Services.AddDbContext<EtyraCommerceDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(EtyraCommerce.Persistence.Mapping.MapProfile));

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader()
              .WithExposedHeaders("*");
    });

    options.AddPolicy("AllowAll", policy =>
    {
        policy.SetIsOriginAllowed(_ => true)
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// Add MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(EtyraCommerce.Application.Services.User.Commands.LoginUserCommand).Assembly));

// Add JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"];

if (string.IsNullOrEmpty(secretKey))
{
    throw new InvalidOperationException("JWT SecretKey is not configured in appsettings.json");
}

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddControllers();

// Configure Swagger/OpenAPI with JWT support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "EtyraCommerce API",
        Version = "v1",
        Description = "EtyraCommerce E-commerce Platform API"
    });

    // Add JWT Authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // Include XML comments
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Seed data
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<EtyraCommerceDbContext>();
    await PaymentMethodSeeder.SeedAsync(context);
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "EtyraCommerce API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

// Add CORS (must be before UseAuthentication and UseAuthorization)
app.UseCors("AllowAll");

app.UseHttpsRedirection();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Add health check endpoint
app.MapGet("/health", () => "EtyraCommerce API is running!");

app.Run();
