using EtyraCommerce.Domain.Entities.Product;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ProductDescription entity
    /// </summary>
    public class ProductDescriptionConfiguration : BaseEntityConfiguration<ProductDescription>
    {
        public override void Configure(EntityTypeBuilder<ProductDescription> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("product_descriptions", "etyra_core");

            #region Properties

            // Name
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasColumnType("text")
                .IsRequired();

            // Short Description
            builder.Property(x => x.ShortDescription)
                .HasColumnName("short_description")
                .HasMaxLength(500)
                .IsRequired(false);

            // Meta Title
            builder.Property(x => x.MetaTitle)
                .HasColumnName("meta_title")
                .HasMaxLength(120)
                .IsRequired(false);

            // Meta Description
            builder.Property(x => x.MetaDescription)
                .HasColumnName("meta_description")
                .HasMaxLength(350)
                .IsRequired(false);

            // Meta Keywords
            builder.Property(x => x.MetaKeywords)
                .HasColumnName("meta_keywords")
                .HasMaxLength(200)
                .IsRequired(false);

            // Tags
            builder.Property(x => x.Tags)
                .HasColumnName("tags")
                .HasMaxLength(500)
                .IsRequired(false);

            // Slug
            builder.Property(x => x.Slug)
                .HasColumnName("slug")
                .HasMaxLength(200)
                .IsRequired(false);

            // Language Code
            builder.Property(x => x.LanguageCode)
                .HasColumnName("language_code")
                .HasMaxLength(10)
                .HasDefaultValue("en-US")
                .IsRequired();

            // Store ID
            builder.Property(x => x.StoreId)
                .HasColumnName("store_id")
                .IsRequired(false);

            // Product ID
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            #endregion

            #region Navigation Properties

            // Product (Many-to-One)
            builder.HasOne(x => x.Product)
                .WithMany(x => x.Descriptions)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Composite Primary Key Alternative (Product + Language + Store)
            builder.HasIndex(x => new { x.ProductId, x.LanguageCode, x.StoreId })
                .IsUnique()
                .HasDatabaseName("ix_product_descriptions_product_language_store_unique");

            // Performance Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_product_descriptions_product_id");

            builder.HasIndex(x => x.LanguageCode)
                .HasDatabaseName("ix_product_descriptions_language_code");

            builder.HasIndex(x => x.StoreId)
                .HasDatabaseName("ix_product_descriptions_store_id");

            builder.HasIndex(x => x.Slug)
                .HasDatabaseName("ix_product_descriptions_slug");

            // Search Indexes
            builder.HasIndex(x => x.Name)
                .HasDatabaseName("ix_product_descriptions_name");

            // Composite Indexes
            builder.HasIndex(x => new { x.LanguageCode, x.StoreId })
                .HasDatabaseName("ix_product_descriptions_language_store");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_ProductDescriptions_Name_NotEmpty",
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("CK_ProductDescriptions_Description_NotEmpty",
                "LENGTH(TRIM(description)) > 0");

            builder.HasCheckConstraint("CK_ProductDescriptions_LanguageCode_NotEmpty",
                "LENGTH(TRIM(language_code)) > 0");

            #endregion
        }

        protected override string GetTableName() => "product_descriptions";
    }
}
