using MediatR;
using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Domain.Enums.Shipping;

namespace EtyraCommerce.Application.Features.Shipping.ShippingMethods.Queries
{
    /// <summary>
    /// Query to get shipping methods by zone with rates
    /// </summary>
    public class GetShippingMethodsByZoneQuery : IRequest<GetShippingMethodsByZoneResponse>
    {
        /// <summary>
        /// Shipping zone ID
        /// </summary>
        public Guid ShippingZoneId { get; set; }

        /// <summary>
        /// Filter by method type (optional)
        /// </summary>
        public ShippingMethodType? MethodType { get; set; }

        /// <summary>
        /// Filter by carrier name (optional)
        /// </summary>
        public string? CarrierName { get; set; }

        /// <summary>
        /// Include rates in response
        /// </summary>
        public bool IncludeRates { get; set; } = true;

        /// <summary>
        /// Package weight for rate calculation (optional)
        /// </summary>
        public decimal? PackageWeight { get; set; }

        /// <summary>
        /// Order amount for rate calculation (optional)
        /// </summary>
        public decimal? OrderAmount { get; set; }

        /// <summary>
        /// Currency code for rate calculation
        /// </summary>
        public string CurrencyCode { get; set; } = "RON";

        /// <summary>
        /// Constructor
        /// </summary>
        public GetShippingMethodsByZoneQuery(Guid shippingZoneId)
        {
            ShippingZoneId = shippingZoneId;
        }
    }

    /// <summary>
    /// Response for get shipping methods by zone query
    /// </summary>
    public class GetShippingMethodsByZoneResponse
    {
        /// <summary>
        /// Shipping zone information
        /// </summary>
        public ShippingZoneInfo Zone { get; set; } = null!;

        /// <summary>
        /// Available shipping methods
        /// </summary>
        public List<ShippingMethodWithRates> ShippingMethods { get; set; } = new();

        /// <summary>
        /// Total count of methods
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Count by method type
        /// </summary>
        public Dictionary<ShippingMethodType, int> CountByType { get; set; } = new();

        /// <summary>
        /// Count by carrier
        /// </summary>
        public Dictionary<string, int> CountByCarrier { get; set; } = new();
    }

    /// <summary>
    /// Shipping zone info DTO
    /// </summary>
    public class ShippingZoneInfo
    {
        /// <summary>
        /// Zone ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Zone name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Zone code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Zone type
        /// </summary>
        public ZoneType Type { get; set; }

        /// <summary>
        /// Default delivery time range
        /// </summary>
        public string DeliveryTimeRange { get; set; } = string.Empty;
    }

    /// <summary>
    /// Shipping method with rates DTO
    /// </summary>
    public class ShippingMethodWithRates
    {
        /// <summary>
        /// Method ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Method name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Method code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Carrier name
        /// </summary>
        public string CarrierName { get; set; } = string.Empty;

        /// <summary>
        /// Carrier code
        /// </summary>
        public string CarrierCode { get; set; } = string.Empty;

        /// <summary>
        /// Method type
        /// </summary>
        public ShippingMethodType Type { get; set; }

        /// <summary>
        /// Method type display name
        /// </summary>
        public string TypeDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Method description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Minimum delivery days
        /// </summary>
        public int MinDeliveryDays { get; set; }

        /// <summary>
        /// Maximum delivery days
        /// </summary>
        public int MaxDeliveryDays { get; set; }

        /// <summary>
        /// Maximum weight supported
        /// </summary>
        public decimal MaxWeight { get; set; }

        /// <summary>
        /// Has tracking support
        /// </summary>
        public bool HasTracking { get; set; }

        /// <summary>
        /// Has insurance support
        /// </summary>
        public bool HasInsurance { get; set; }

        /// <summary>
        /// Supports cash on delivery
        /// </summary>
        public bool SupportsCashOnDelivery { get; set; }

        /// <summary>
        /// Available rates for this method
        /// </summary>
        public List<ShippingRateInfo> Rates { get; set; } = new();

        /// <summary>
        /// Calculated shipping cost (if weight/amount provided)
        /// </summary>
        public ShippingCostInfo? CalculatedCost { get; set; }

        /// <summary>
        /// Display name with delivery time
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Delivery time text
        /// </summary>
        public string DeliveryTimeText { get; set; } = string.Empty;

        /// <summary>
        /// Features text (tracking, insurance, COD)
        /// </summary>
        public string FeaturesText { get; set; } = string.Empty;
    }

    /// <summary>
    /// Shipping rate info DTO
    /// </summary>
    public class ShippingRateInfo
    {
        /// <summary>
        /// Rate ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Calculation type
        /// </summary>
        public RateCalculationType CalculationType { get; set; }

        /// <summary>
        /// Calculation type display name
        /// </summary>
        public string CalculationTypeDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Weight range (for weight-based rates)
        /// </summary>
        public string? WeightRange { get; set; }

        /// <summary>
        /// Base cost
        /// </summary>
        public decimal BaseCostAmount { get; set; }

        /// <summary>
        /// Base cost currency
        /// </summary>
        public string BaseCostCurrency { get; set; } = string.Empty;

        /// <summary>
        /// Additional cost per kg (if applicable)
        /// </summary>
        public decimal? AdditionalCostPerKgAmount { get; set; }

        /// <summary>
        /// Free shipping threshold (if applicable)
        /// </summary>
        public decimal? FreeShippingThresholdAmount { get; set; }

        /// <summary>
        /// Effective date range
        /// </summary>
        public string? EffectiveDateRange { get; set; }
    }

    /// <summary>
    /// Calculated shipping cost info DTO
    /// </summary>
    public class ShippingCostInfo
    {
        /// <summary>
        /// Calculated cost amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Cost currency
        /// </summary>
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Is free shipping applied
        /// </summary>
        public bool IsFreeShipping { get; set; }

        /// <summary>
        /// Calculation details
        /// </summary>
        public string CalculationDetails { get; set; } = string.Empty;
    }
}
