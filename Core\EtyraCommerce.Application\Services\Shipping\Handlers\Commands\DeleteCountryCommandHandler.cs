using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Shipping.Handlers.Commands
{
    /// <summary>
    /// Handler for deleting a country (soft delete)
    /// Delegates business logic to CountryProcessService
    /// </summary>
    public class DeleteCountryCommandHandler : IRequestHandler<DeleteCountryCommand, DeleteCountryResponse>
    {
        private readonly ICountryProcessService _countryProcessService;
        private readonly ILogger<DeleteCountryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public DeleteCountryCommandHandler(
            ICountryProcessService countryProcessService,
            ILogger<DeleteCountryCommandHandler> logger)
        {
            _countryProcessService = countryProcessService ?? throw new ArgumentNullException(nameof(countryProcessService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the delete country command
        /// </summary>
        public async Task<DeleteCountryResponse> Handle(DeleteCountryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing delete country command for CountryId: {CountryId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                {
                    _logger.LogWarning("Delete country command failed: Country ID is required");
                    throw new ArgumentException("Country ID is required");
                }

                // Delegate to CountryProcessService for business logic
                var result = await _countryProcessService.ProcessDeleteCountryAsync(request, cancellationToken);

                if (result != null)
                {
                    _logger.LogInformation("Country deletion successful for CountryId: {CountryId}, Name: {CountryName}",
                        request.Id, result.Name);
                }
                else
                {
                    _logger.LogWarning("Country deletion failed for CountryId: {CountryId}", request.Id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing delete country command for CountryId: {CountryId}", request.Id);
                throw;
            }
        }
    }
}
