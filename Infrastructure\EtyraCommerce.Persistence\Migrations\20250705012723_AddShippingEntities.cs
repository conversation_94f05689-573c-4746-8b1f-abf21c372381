﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddShippingEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "etyra_shipping");

            migrationBuilder.CreateTable(
                name: "Countries",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    IsoCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    PhoneCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    IsShippingEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsEuMember = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ShippingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Countries", x => x.Id);
                    table.CheckConstraint("CK_Countries_Code_Length", "LENGTH(\"Code\") = 2");
                    table.CheckConstraint("CK_Countries_CurrencyCode_Length", "LENGTH(\"CurrencyCode\") = 3");
                    table.CheckConstraint("CK_Countries_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.CheckConstraint("CK_Countries_IsoCode_Length", "LENGTH(\"IsoCode\") = 3");
                },
                comment: "Countries available for shipping in the European market");

            migrationBuilder.CreateTable(
                name: "ShippingZones",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DefaultMinDeliveryDays = table.Column<int>(type: "integer", nullable: false),
                    DefaultMaxDeliveryDays = table.Column<int>(type: "integer", nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShippingZones", x => x.Id);
                    table.CheckConstraint("CK_ShippingZones_DeliveryDays", "\"DefaultMinDeliveryDays\" > 0 AND \"DefaultMaxDeliveryDays\" > 0 AND \"DefaultMinDeliveryDays\" <= \"DefaultMaxDeliveryDays\"");
                    table.CheckConstraint("CK_ShippingZones_DisplayOrder", "\"DisplayOrder\" >= 0");
                },
                comment: "Shipping zones for organizing countries and regions by delivery characteristics");

            migrationBuilder.CreateTable(
                name: "Regions",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsShippingEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShippingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Regions", x => x.Id);
                    table.CheckConstraint("CK_Regions_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.ForeignKey(
                        name: "FK_Regions_Countries_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Administrative regions within countries (states, provinces, counties, etc.)");

            migrationBuilder.CreateTable(
                name: "ShippingMethods",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CarrierName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CarrierCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ShippingZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    MinDeliveryDays = table.Column<int>(type: "integer", nullable: false),
                    MaxDeliveryDays = table.Column<int>(type: "integer", nullable: false),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxLength = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWidth = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxHeight = table.Column<decimal>(type: "numeric", nullable: true),
                    HasTracking = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    HasInsurance = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SupportsCashOnDelivery = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ApiEndpoint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ApiCredentials = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ApiConfiguration = table.Column<string>(type: "text", nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShippingMethods", x => x.Id);
                    table.CheckConstraint("CK_ShippingMethods_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.ForeignKey(
                        name: "FK_ShippingMethods_ShippingZones_ShippingZoneId",
                        column: x => x.ShippingZoneId,
                        principalSchema: "etyra_shipping",
                        principalTable: "ShippingZones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Shipping methods available for each shipping zone with carrier information");

            migrationBuilder.CreateTable(
                name: "ShippingZoneCountries",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ShippingZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    OverrideMinDeliveryDays = table.Column<int>(type: "integer", nullable: true),
                    OverrideMaxDeliveryDays = table.Column<int>(type: "integer", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShippingZoneCountries", x => x.Id);
                    table.CheckConstraint("CK_ShippingZoneCountries_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.ForeignKey(
                        name: "FK_ShippingZoneCountries_Countries_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ShippingZoneCountries_ShippingZones_ShippingZoneId",
                        column: x => x.ShippingZoneId,
                        principalSchema: "etyra_shipping",
                        principalTable: "ShippingZones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Mapping between shipping zones and countries with cost modifiers");

            migrationBuilder.CreateTable(
                name: "Cities",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsShippingEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsMajorCity = table.Column<bool>(type: "boolean", nullable: false),
                    Population = table.Column<int>(type: "integer", nullable: true),
                    Latitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    ShippingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RegionId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cities", x => x.Id);
                    table.CheckConstraint("CK_Cities_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.CheckConstraint("CK_Cities_Latitude", "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)");
                    table.CheckConstraint("CK_Cities_Longitude", "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)");
                    table.ForeignKey(
                        name: "FK_Cities_Regions_RegionId",
                        column: x => x.RegionId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Regions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Cities within regions for detailed shipping address management");

            migrationBuilder.CreateTable(
                name: "ShippingRates",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ShippingZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    ShippingMethodId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CalculationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    MinWeight = table.Column<decimal>(type: "numeric(10,3)", precision: 10, scale: 3, nullable: false),
                    MaxWeight = table.Column<decimal>(type: "numeric(10,3)", precision: 10, scale: 3, nullable: false),
                    BaseCostAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    BaseCostCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AdditionalCostPerKgAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    AdditionalCostPerKgCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    FreeShippingThresholdAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    FreeShippingThresholdCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    MinOrderAmountAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    MinOrderAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    MaxOrderAmountAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxOrderAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveUntil = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShippingRates", x => x.Id);
                    table.CheckConstraint("CK_ShippingRates_AdditionalCostPerKgAmount", "\"AdditionalCostPerKgAmount\" IS NULL OR \"AdditionalCostPerKgAmount\" >= 0");
                    table.CheckConstraint("CK_ShippingRates_BaseCostAmount", "\"BaseCostAmount\" >= 0");
                    table.CheckConstraint("CK_ShippingRates_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.CheckConstraint("CK_ShippingRates_EffectivePeriod", "\"EffectiveUntil\" IS NULL OR \"EffectiveFrom\" <= \"EffectiveUntil\"");
                    table.CheckConstraint("CK_ShippingRates_WeightRange", "\"MinWeight\" <= \"MaxWeight\"");
                    table.ForeignKey(
                        name: "FK_ShippingRates_ShippingMethods_ShippingMethodId",
                        column: x => x.ShippingMethodId,
                        principalSchema: "etyra_shipping",
                        principalTable: "ShippingMethods",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ShippingRates_ShippingZones_ShippingZoneId",
                        column: x => x.ShippingZoneId,
                        principalSchema: "etyra_shipping",
                        principalTable: "ShippingZones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Shipping rate configurations for different shipping methods and conditions");

            migrationBuilder.CreateTable(
                name: "Districts",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsShippingEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsCentralDistrict = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    ShippingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CityId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Districts", x => x.Id);
                    table.CheckConstraint("CK_Districts_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.CheckConstraint("CK_Districts_Latitude", "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)");
                    table.CheckConstraint("CK_Districts_Longitude", "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)");
                    table.ForeignKey(
                        name: "FK_Districts_Cities_CityId",
                        column: x => x.CityId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Districts within cities for precise shipping address management");

            migrationBuilder.CreateTable(
                name: "ShippingZoneLocations",
                schema: "etyra_shipping",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ShippingZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: true),
                    RegionId = table.Column<Guid>(type: "uuid", nullable: true),
                    CityId = table.Column<Guid>(type: "uuid", nullable: true),
                    DistrictId = table.Column<Guid>(type: "uuid", nullable: true),
                    LocationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    OverrideMinDeliveryDays = table.Column<int>(type: "integer", nullable: true),
                    OverrideMaxDeliveryDays = table.Column<int>(type: "integer", nullable: true),
                    CostModifierPercentage = table.Column<decimal>(type: "numeric", nullable: true),
                    CostModifierFixed = table.Column<decimal>(type: "numeric", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShippingZoneLocations", x => x.Id);
                    table.CheckConstraint("CK_ShippingZoneLocations_CostModifierFixed", "\"CostModifierFixed\" IS NULL OR (\"CostModifierFixed\" >= -1000000 AND \"CostModifierFixed\" <= 1000000)");
                    table.CheckConstraint("CK_ShippingZoneLocations_CostModifierPercentage", "\"CostModifierPercentage\" IS NULL OR (\"CostModifierPercentage\" >= -100 AND \"CostModifierPercentage\" <= 1000)");
                    table.CheckConstraint("CK_ShippingZoneLocations_DisplayOrder", "\"DisplayOrder\" >= 0");
                    table.ForeignKey(
                        name: "FK_ShippingZoneLocations_Cities_CityId",
                        column: x => x.CityId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Cities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ShippingZoneLocations_Countries_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Countries",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ShippingZoneLocations_Districts_DistrictId",
                        column: x => x.DistrictId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Districts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ShippingZoneLocations_Regions_RegionId",
                        column: x => x.RegionId,
                        principalSchema: "etyra_shipping",
                        principalTable: "Regions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ShippingZoneLocations_ShippingZones_ShippingZoneId",
                        column: x => x.ShippingZoneId,
                        principalSchema: "etyra_shipping",
                        principalTable: "ShippingZones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Mapping between shipping zones and specific locations (regions, cities, districts) with cost modifiers");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_DisplayOrder",
                schema: "etyra_shipping",
                table: "Cities",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_IsShippingEnabled",
                schema: "etyra_shipping",
                table: "Cities",
                column: "IsShippingEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_Name_Region_Unique",
                schema: "etyra_shipping",
                table: "Cities",
                columns: new[] { "Name", "RegionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Cities_PostalCode",
                schema: "etyra_shipping",
                table: "Cities",
                column: "PostalCode");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_RegionId",
                schema: "etyra_shipping",
                table: "Cities",
                column: "RegionId");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_Code_Unique",
                schema: "etyra_shipping",
                table: "Countries",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Countries_DisplayOrder",
                schema: "etyra_shipping",
                table: "Countries",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_IsEuMember",
                schema: "etyra_shipping",
                table: "Countries",
                column: "IsEuMember");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_IsoCode_Unique",
                schema: "etyra_shipping",
                table: "Countries",
                column: "IsoCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Countries_IsShippingEnabled",
                schema: "etyra_shipping",
                table: "Countries",
                column: "IsShippingEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_Name_Unique",
                schema: "etyra_shipping",
                table: "Countries",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Districts_CityId",
                schema: "etyra_shipping",
                table: "Districts",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_Districts_DisplayOrder",
                schema: "etyra_shipping",
                table: "Districts",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Districts_IsShippingEnabled",
                schema: "etyra_shipping",
                table: "Districts",
                column: "IsShippingEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_Districts_Name_City_Unique",
                schema: "etyra_shipping",
                table: "Districts",
                columns: new[] { "Name", "CityId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Districts_PostalCode",
                schema: "etyra_shipping",
                table: "Districts",
                column: "PostalCode");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Code",
                schema: "etyra_shipping",
                table: "Regions",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_CountryId",
                schema: "etyra_shipping",
                table: "Regions",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_DisplayOrder",
                schema: "etyra_shipping",
                table: "Regions",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_IsShippingEnabled",
                schema: "etyra_shipping",
                table: "Regions",
                column: "IsShippingEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Name_Country_Unique",
                schema: "etyra_shipping",
                table: "Regions",
                columns: new[] { "Name", "CountryId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Type",
                schema: "etyra_shipping",
                table: "Regions",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_CarrierName",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                column: "CarrierName");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_DisplayOrder",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_IsActive",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_Name_Zone_Unique",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                columns: new[] { "Name", "ShippingZoneId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_ShippingZoneId",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                column: "ShippingZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingMethods_Type",
                schema: "etyra_shipping",
                table: "ShippingMethods",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingRates_DisplayOrder",
                schema: "etyra_shipping",
                table: "ShippingRates",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingRates_EffectivePeriod",
                schema: "etyra_shipping",
                table: "ShippingRates",
                columns: new[] { "EffectiveFrom", "EffectiveUntil" });

            migrationBuilder.CreateIndex(
                name: "IX_ShippingRates_IsActive",
                schema: "etyra_shipping",
                table: "ShippingRates",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingRates_ShippingMethodId",
                schema: "etyra_shipping",
                table: "ShippingRates",
                column: "ShippingMethodId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingRates_ShippingZoneId",
                schema: "etyra_shipping",
                table: "ShippingRates",
                column: "ShippingZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneCountries_CountryId",
                schema: "etyra_shipping",
                table: "ShippingZoneCountries",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneCountries_IsActive",
                schema: "etyra_shipping",
                table: "ShippingZoneCountries",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneCountries_ShippingZoneId",
                schema: "etyra_shipping",
                table: "ShippingZoneCountries",
                column: "ShippingZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneCountries_Zone_Country_Unique",
                schema: "etyra_shipping",
                table: "ShippingZoneCountries",
                columns: new[] { "ShippingZoneId", "CountryId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_CityId",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_CountryId",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_DistrictId",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "DistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_IsActive",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_LocationType",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "LocationType");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_RegionId",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "RegionId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_ShippingZoneId",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                column: "ShippingZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZoneLocations_Zone_Location_Type_Unique",
                schema: "etyra_shipping",
                table: "ShippingZoneLocations",
                columns: new[] { "ShippingZoneId", "CountryId", "RegionId", "CityId", "DistrictId", "LocationType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZones_DisplayOrder",
                schema: "etyra_shipping",
                table: "ShippingZones",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZones_IsActive",
                schema: "etyra_shipping",
                table: "ShippingZones",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZones_Name_Unique",
                schema: "etyra_shipping",
                table: "ShippingZones",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ShippingZones_Type",
                schema: "etyra_shipping",
                table: "ShippingZones",
                column: "Type");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ShippingRates",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "ShippingZoneCountries",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "ShippingZoneLocations",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "ShippingMethods",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "Districts",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "ShippingZones",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "Cities",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "Regions",
                schema: "etyra_shipping");

            migrationBuilder.DropTable(
                name: "Countries",
                schema: "etyra_shipping");
        }
    }
}
