using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// DTO for user search parameters
    /// </summary>
    public class UserSearchDto
    {
        /// <summary>
        /// Search term (searches in FirstName, LastName, Email, Username)
        /// </summary>
        [StringLength(100, ErrorMessage = "Search term cannot exceed 100 characters")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by enabled status
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// Filter by email confirmation status
        /// </summary>
        public bool? IsEmailConfirmed { get; set; }

        /// <summary>
        /// Filter by phone confirmation status
        /// </summary>
        public bool? IsPhoneConfirmed { get; set; }

        /// <summary>
        /// Filter by locked status
        /// </summary>
        public bool? IsLocked { get; set; }

        /// <summary>
        /// Filter by culture
        /// </summary>
        [StringLength(10, ErrorMessage = "Culture cannot exceed 10 characters")]
        public string? Culture { get; set; }

        /// <summary>
        /// Filter by creation date from
        /// </summary>
        public DateTime? CreatedFrom { get; set; }

        /// <summary>
        /// Filter by creation date to
        /// </summary>
        public DateTime? CreatedTo { get; set; }

        /// <summary>
        /// Filter by last login date from
        /// </summary>
        public DateTime? LastLoginFrom { get; set; }

        /// <summary>
        /// Filter by last login date to
        /// </summary>
        public DateTime? LastLoginTo { get; set; }

        /// <summary>
        /// Sort field
        /// </summary>
        public UserSortField SortBy { get; set; } = UserSortField.CreatedAt;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// User sort fields
    /// </summary>
    public enum UserSortField
    {
        FirstName,
        LastName,
        Email,
        Username,
        CreatedAt,
        UpdatedAt,
        LastLoginAt,
        IsEnabled,
        IsEmailConfirmed
    }





    /// <summary>
    /// Culture statistic
    /// </summary>
    public class CultureStatistic
    {
        public string Culture { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Timezone statistic
    /// </summary>
    public class TimezoneStatistic
    {
        public string TimeZone { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }
}
