﻿namespace EtyraApp.Domain.Entities.Accounting;

public enum TransactionType
{
    Income = 1,
    Expense = 2,
    Transfer = 3
}

public enum TransactionStatus
{
    Pending = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum InvoiceType
{
    Sales = 1,
    Purchase = 2
}

public enum InvoiceStatus
{
    Draft = 1,
    Sent = 2,
    Paid = 3,
    PartiallyPaid = 4,
    Overdue = 5,
    Cancelled = 6
}

public enum PaymentMethod
{
    Cash = 1,
    BankTransfer = 2,
    CreditCard = 3,
    Check = 4,
    Other = 5
} 