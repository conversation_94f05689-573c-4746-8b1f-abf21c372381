namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Generic service result wrapper with data
    /// </summary>
    /// <typeparam name="T">Type of data in the result</typeparam>
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<string> ValidationErrors { get; set; } = new();
        public string? ErrorCode { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Creates a successful result with data
        /// </summary>
        public static ServiceResult<T> Success(T data)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data
            };
        }

        /// <summary>
        /// Creates a successful result with data and message
        /// </summary>
        public static ServiceResult<T> Success(T data, string message)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data,
                ErrorMessage = message
            };
        }

        /// <summary>
        /// Creates a failure result with error message
        /// </summary>
        public static ServiceResult<T> Failure(string errorMessage)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Creates a failure result with error message and code
        /// </summary>
        public static ServiceResult<T> Failure(string errorMessage, string errorCode)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }

        /// <summary>
        /// Creates a failure result with validation errors
        /// </summary>
        public static ServiceResult<T> ValidationFailure(List<string> validationErrors)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ValidationErrors = validationErrors,
                ErrorMessage = "Validation failed"
            };
        }

        /// <summary>
        /// Creates a failure result with validation errors dictionary
        /// </summary>
        public static ServiceResult<T> ValidationFailure(Dictionary<string, List<string>> validationErrors)
        {
            var errors = validationErrors.SelectMany(kvp =>
                kvp.Value.Select(error => $"{kvp.Key}: {error}")).ToList();

            return new ServiceResult<T>
            {
                IsSuccess = false,
                ValidationErrors = errors,
                ErrorMessage = "Validation failed"
            };
        }

        /// <summary>
        /// Creates a failure result from exception
        /// </summary>
        public static ServiceResult<T> FromException(Exception exception)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ErrorMessage = exception.Message,
                ErrorCode = exception.GetType().Name
            };
        }

        /// <summary>
        /// Checks if result has validation errors
        /// </summary>
        public bool HasValidationErrors => ValidationErrors.Any();

        /// <summary>
        /// Gets all error messages combined
        /// </summary>
        public string GetAllErrors()
        {
            var errors = new List<string>();

            if (!string.IsNullOrEmpty(ErrorMessage))
                errors.Add(ErrorMessage);

            errors.AddRange(ValidationErrors);

            return string.Join("; ", errors);
        }

        /// <summary>
        /// Implicit conversion to bool (for easy success checking)
        /// </summary>
        public static implicit operator bool(ServiceResult<T> result) => result.IsSuccess;
    }

    /// <summary>
    /// Service result without data
    /// </summary>
    public class ServiceResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<string> ValidationErrors { get; set; } = new();
        public string? ErrorCode { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Creates a successful result
        /// </summary>
        public static ServiceResult Success()
        {
            return new ServiceResult { IsSuccess = true };
        }

        /// <summary>
        /// Creates a successful result with message
        /// </summary>
        public static ServiceResult Success(string message)
        {
            return new ServiceResult
            {
                IsSuccess = true,
                ErrorMessage = message
            };
        }

        /// <summary>
        /// Creates a failure result with error message
        /// </summary>
        public static ServiceResult Failure(string errorMessage)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Creates a failure result with error message and code
        /// </summary>
        public static ServiceResult Failure(string errorMessage, string errorCode)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }

        /// <summary>
        /// Creates a failure result with validation errors
        /// </summary>
        public static ServiceResult ValidationFailure(List<string> validationErrors)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ValidationErrors = validationErrors,
                ErrorMessage = "Validation failed"
            };
        }

        /// <summary>
        /// Creates a failure result with validation errors dictionary
        /// </summary>
        public static ServiceResult ValidationFailure(Dictionary<string, List<string>> validationErrors)
        {
            var errors = validationErrors.SelectMany(kvp =>
                kvp.Value.Select(error => $"{kvp.Key}: {error}")).ToList();

            return new ServiceResult
            {
                IsSuccess = false,
                ValidationErrors = errors,
                ErrorMessage = "Validation failed"
            };
        }

        /// <summary>
        /// Creates a failure result from exception
        /// </summary>
        public static ServiceResult FromException(Exception exception)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = exception.Message,
                ErrorCode = exception.GetType().Name
            };
        }

        /// <summary>
        /// Checks if result has validation errors
        /// </summary>
        public bool HasValidationErrors => ValidationErrors.Any();

        /// <summary>
        /// Gets all error messages combined
        /// </summary>
        public string GetAllErrors()
        {
            var errors = new List<string>();

            if (!string.IsNullOrEmpty(ErrorMessage))
                errors.Add(ErrorMessage);

            errors.AddRange(ValidationErrors);

            return string.Join("; ", errors);
        }

        /// <summary>
        /// Implicit conversion to bool (for easy success checking)
        /// </summary>
        public static implicit operator bool(ServiceResult result) => result.IsSuccess;

        /// <summary>
        /// Converts to generic ServiceResult<T>
        /// </summary>
        public ServiceResult<T> ToGeneric<T>(T? data = default)
        {
            return new ServiceResult<T>
            {
                IsSuccess = IsSuccess,
                Data = data,
                ErrorMessage = ErrorMessage,
                ValidationErrors = ValidationErrors,
                ErrorCode = ErrorCode,
                Timestamp = Timestamp
            };
        }
    }
}
