﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.RelationsTable;

public class ProductPriceAndQuantityChange : BaseEntity
{
    public Product Product { get; set; }
    public int ProductId { get; set; }
    public int PriceType { get; set; } // 1 - sale Price  2 - Cost  
    public decimal Amount { get; set; }
    public int Quantity { get; set; }
    [MaxLength(4)]
    public string? Currency { get; set; }


}