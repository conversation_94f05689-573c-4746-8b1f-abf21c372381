using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for WarehouseProduct entity
    /// </summary>
    public class WarehouseProductConfiguration : BaseEntityConfiguration<WarehouseProduct>
    {
        public override void Configure(EntityTypeBuilder<WarehouseProduct> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("warehouse_products", "etyra_core");

            #region Properties

            // Stock Quantity
            builder.Property(x => x.StockQuantity)
                .HasColumnName("stock_quantity")
                .HasDefaultValue(0)
                .IsRequired();

            // Min Stock Alert
            builder.Property(x => x.MinStockAlert)
                .HasColumnName("min_stock_alert")
                .HasDefaultValue(0)
                .IsRequired();

            // Max Stock Capacity
            builder.Property(x => x.MaxStockCapacity)
                .HasColumnName("max_stock_capacity")
                .IsRequired(false);

            // Reserved Stock
            builder.Property(x => x.ReservedStock)
                .HasColumnName("reserved_stock")
                .HasDefaultValue(0)
                .IsRequired();

            // Status
            builder.Property(x => x.Status)
                .HasColumnName("status")
                .HasConversion<int>()
                .IsRequired();

            // Location Code
            builder.Property(x => x.LocationCode)
                .HasColumnName("location_code")
                .HasMaxLength(50)
                .IsRequired(false);

            // Supplier Reference
            builder.Property(x => x.SupplierReference)
                .HasColumnName("supplier_reference")
                .HasMaxLength(100)
                .IsRequired(false);

            // Lead Time Days
            builder.Property(x => x.LeadTimeDays)
                .HasColumnName("lead_time_days")
                .IsRequired(false);

            // Last Stock Update
            builder.Property(x => x.LastStockUpdate)
                .HasColumnName("last_stock_update")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Last Stock Count
            builder.Property(x => x.LastStockCount)
                .HasColumnName("last_stock_count")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Is Primary Warehouse
            builder.Property(x => x.IsPrimaryWarehouse)
                .HasColumnName("is_primary_warehouse")
                .HasDefaultValue(false)
                .IsRequired();

            // Manage Stock
            builder.Property(x => x.ManageStock)
                .HasColumnName("manage_stock")
                .HasDefaultValue(true)
                .IsRequired();

            // Allow Backorders
            builder.Property(x => x.AllowBackorders)
                .HasColumnName("allow_backorders")
                .HasDefaultValue(false)
                .IsRequired();

            // Product ID
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            // Warehouse ID
            builder.Property(x => x.WarehouseId)
                .HasColumnName("warehouse_id")
                .IsRequired();

            #endregion

            #region Value Objects

            // Price (Money)
            builder.OwnsOne(x => x.Price, price =>
            {
                price.Property(p => p.Amount)
                    .HasColumnName("price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                price.Property(p => p.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Cost (Money)
            builder.OwnsOne(x => x.Cost, cost =>
            {
                cost.Property(c => c.Amount)
                    .HasColumnName("cost_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                cost.Property(c => c.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("cost_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            #endregion

            #region Navigation Properties

            // Product (Many-to-One)
            builder.HasOne(x => x.Product)
                .WithMany(x => x.WarehouseProducts)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Note: Warehouse entity will be created later
            // builder.HasOne(x => x.Warehouse)
            //     .WithMany(x => x.WarehouseProducts)
            //     .HasForeignKey(x => x.WarehouseId)
            //     .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Composite Primary Key Alternative (Product + Warehouse)
            builder.HasIndex(x => new { x.ProductId, x.WarehouseId })
                .IsUnique()
                .HasDatabaseName("ix_warehouse_products_product_warehouse_unique");

            // Performance Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_warehouse_products_product_id");

            builder.HasIndex(x => x.WarehouseId)
                .HasDatabaseName("ix_warehouse_products_warehouse_id");

            builder.HasIndex(x => x.Status)
                .HasDatabaseName("ix_warehouse_products_status");

            builder.HasIndex(x => x.IsPrimaryWarehouse)
                .HasDatabaseName("ix_warehouse_products_is_primary");

            builder.HasIndex(x => x.StockQuantity)
                .HasDatabaseName("ix_warehouse_products_stock_quantity");

            builder.HasIndex(x => x.LocationCode)
                .HasDatabaseName("ix_warehouse_products_location_code");

            // Composite Indexes
            builder.HasIndex(x => new { x.ProductId, x.IsPrimaryWarehouse })
                .HasDatabaseName("ix_warehouse_products_product_primary");

            builder.HasIndex(x => new { x.WarehouseId, x.Status })
                .HasDatabaseName("ix_warehouse_products_warehouse_status");

            builder.HasIndex(x => new { x.ProductId, x.Status })
                .HasDatabaseName("ix_warehouse_products_product_status");

            builder.HasIndex(x => new { x.WarehouseId, x.StockQuantity })
                .HasDatabaseName("ix_warehouse_products_warehouse_stock");

            // Stock Management Indexes
            builder.HasIndex(x => new { x.StockQuantity, x.MinStockAlert })
                .HasDatabaseName("ix_warehouse_products_stock_alert");

            builder.HasIndex(x => new { x.ProductId, x.StockQuantity, x.ReservedStock })
                .HasDatabaseName("ix_warehouse_products_product_stock_reserved");

            // Unique constraint for primary warehouse per product
            builder.HasIndex(x => new { x.ProductId, x.IsPrimaryWarehouse })
                .HasFilter("is_primary_warehouse = true")
                .IsUnique()
                .HasDatabaseName("ix_warehouse_products_product_primary_unique");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_WarehouseProducts_StockQuantity_NonNegative",
                "stock_quantity >= 0");

            builder.HasCheckConstraint("CK_WarehouseProducts_MinStockAlert_NonNegative",
                "min_stock_alert >= 0");

            builder.HasCheckConstraint("CK_WarehouseProducts_MaxStockCapacity_Positive",
                "max_stock_capacity IS NULL OR max_stock_capacity > 0");

            builder.HasCheckConstraint("CK_WarehouseProducts_ReservedStock_NonNegative",
                "reserved_stock >= 0");

            builder.HasCheckConstraint("CK_WarehouseProducts_ReservedStock_Range",
                "reserved_stock <= stock_quantity");

            builder.HasCheckConstraint("CK_WarehouseProducts_LeadTimeDays_NonNegative",
                "lead_time_days IS NULL OR lead_time_days >= 0");

            builder.HasCheckConstraint("CK_WarehouseProducts_StockCapacity_Range",
                "max_stock_capacity IS NULL OR stock_quantity <= max_stock_capacity");

            #endregion
        }

        protected override string GetTableName() => "warehouse_products";
    }
}
