using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to update an inventory item
    /// </summary>
    public class UpdateInventoryCommand : IRequest<CustomResponseDto<InventoryDto>>
    {
        public Guid Id { get; set; }
        public int? AvailableQuantity { get; set; }
        public int? MinStockLevel { get; set; }
        public int? MaxStockLevel { get; set; }
        public int? ReorderPoint { get; set; }
        public int? ReorderQuantity { get; set; }
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public int? LeadTimeDays { get; set; }
        public string? Notes { get; set; }
    }
}
