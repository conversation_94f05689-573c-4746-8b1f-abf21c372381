using EtyraCommerce.Domain.Entities.Product;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for advanced product search
    /// </summary>
    public class ProductSearchDto
    {
        #region Search Parameters

        /// <summary>
        /// Search term for product name, model, SKU, description, or brand
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// Search in product names
        /// </summary>
        public bool SearchInName { get; set; } = true;

        /// <summary>
        /// Search in product descriptions
        /// </summary>
        public bool SearchInDescription { get; set; } = true;

        /// <summary>
        /// Search in product model/code
        /// </summary>
        public bool SearchInModel { get; set; } = true;

        /// <summary>
        /// Search in product SKU
        /// </summary>
        public bool SearchInSKU { get; set; } = true;

        /// <summary>
        /// Search in product brand
        /// </summary>
        public bool SearchInBrand { get; set; } = true;

        /// <summary>
        /// Search in product tags/keywords
        /// </summary>
        public bool SearchInTags { get; set; } = true;

        /// <summary>
        /// Search in product attributes
        /// </summary>
        public bool SearchInAttributes { get; set; } = false;

        /// <summary>
        /// Case sensitive search
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// Exact match search (no partial matching)
        /// </summary>
        public bool ExactMatch { get; set; } = false;

        #endregion

        #region Pagination

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size (number of items per page)
        /// </summary>
        public int PageSize { get; set; } = 20;

        #endregion

        #region Advanced Filtering

        /// <summary>
        /// Filter by category IDs (OR logic)
        /// </summary>
        public List<Guid> CategoryIds { get; set; } = new();

        /// <summary>
        /// Filter by brands (OR logic)
        /// </summary>
        public List<string> Brands { get; set; } = new();

        /// <summary>
        /// Filter by product status
        /// </summary>
        public ProductStatus? Status { get; set; }

        /// <summary>
        /// Filter by product type
        /// </summary>
        public ProductType? Type { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by featured status
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Filter by digital status
        /// </summary>
        public bool? IsDigital { get; set; }

        /// <summary>
        /// Filter by minimum price
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Filter by maximum price
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// Filter by minimum rating
        /// </summary>
        public int? MinRating { get; set; }

        /// <summary>
        /// Filter by maximum rating
        /// </summary>
        public int? MaxRating { get; set; }

        /// <summary>
        /// Filter by minimum stock quantity
        /// </summary>
        public int? MinStock { get; set; }

        /// <summary>
        /// Filter by maximum stock quantity
        /// </summary>
        public int? MaxStock { get; set; }

        /// <summary>
        /// Filter by availability (in stock)
        /// </summary>
        public bool? InStock { get; set; }

        /// <summary>
        /// Filter by sale status (on sale)
        /// </summary>
        public bool? OnSale { get; set; }

        /// <summary>
        /// Filter by creation date from
        /// </summary>
        public DateTime? CreatedFrom { get; set; }

        /// <summary>
        /// Filter by creation date to
        /// </summary>
        public DateTime? CreatedTo { get; set; }

        /// <summary>
        /// Filter by product attributes (key-value pairs)
        /// </summary>
        public Dictionary<string, string> AttributeFilters { get; set; } = new();

        /// <summary>
        /// Filter by tags
        /// </summary>
        public List<string> Tags { get; set; } = new();

        #endregion

        #region Sorting

        /// <summary>
        /// Sort field
        /// </summary>
        public string SortBy { get; set; } = "Relevance";

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        #endregion

        #region Include Options

        /// <summary>
        /// Whether to include product descriptions
        /// </summary>
        public bool IncludeDescriptions { get; set; } = false;

        /// <summary>
        /// Whether to include product images
        /// </summary>
        public bool IncludeImages { get; set; } = false;

        /// <summary>
        /// Whether to include product categories
        /// </summary>
        public bool IncludeCategories { get; set; } = false;

        /// <summary>
        /// Whether to include product discounts
        /// </summary>
        public bool IncludeDiscounts { get; set; } = false;

        /// <summary>
        /// Whether to include product attributes
        /// </summary>
        public bool IncludeAttributes { get; set; } = false;

        /// <summary>
        /// Whether to include product variants
        /// </summary>
        public bool IncludeVariants { get; set; } = false;

        #endregion

        #region Localization

        /// <summary>
        /// Language code for descriptions (e.g., "en-US", "tr-TR")
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Store ID for store-specific descriptions
        /// </summary>
        public Guid? StoreId { get; set; }

        #endregion
    }
}
