using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.Services.Currency.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for getting all currencies
/// </summary>
public class GetAllCurrenciesQueryHandler : IRequestHandler<GetAllCurrenciesQuery, CustomResponseDto<List<CurrencyDto>>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<GetAllCurrenciesQueryHandler> _logger;

    public GetAllCurrenciesQueryHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<GetAllCurrenciesQueryHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get all currencies query
    /// </summary>
    /// <param name="request">Get all currencies query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of currencies</returns>
    public async Task<CustomResponseDto<List<CurrencyDto>>> Handle(GetAllCurrenciesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetAllCurrenciesQuery with OnlyActive: {OnlyActive}", request.OnlyActive);

            CustomResponseDto<List<CurrencyDto>> result;

            if (request.OnlyActive == true)
            {
                result = await _currencyProcessService.GetActiveCurrenciesAsync(request.Tracking);
            }
            else
            {
                result = await _currencyProcessService.GetAllAsync(request.Tracking);
            }

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} currencies", result.Data?.Count ?? 0);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve currencies. Errors: {Errors}", 
                    string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetAllCurrenciesQuery");
            return CustomResponseDto<List<CurrencyDto>>.InternalServerError("An error occurred while retrieving currencies");
        }
    }
}

/// <summary>
/// Handler for getting active currencies
/// </summary>
public class GetActiveCurrenciesQueryHandler : IRequestHandler<GetActiveCurrenciesQuery, CustomResponseDto<List<CurrencySummaryDto>>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<GetActiveCurrenciesQueryHandler> _logger;

    public GetActiveCurrenciesQueryHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<GetActiveCurrenciesQueryHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get active currencies query
    /// </summary>
    /// <param name="request">Get active currencies query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of active currencies summary</returns>
    public async Task<CustomResponseDto<List<CurrencySummaryDto>>> Handle(GetActiveCurrenciesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetActiveCurrenciesQuery");

            var result = await _currencyProcessService.GetActiveCurrenciesSummaryAsync(request.Tracking);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} active currencies", result.Data?.Count ?? 0);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve active currencies. Errors: {Errors}", 
                    string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetActiveCurrenciesQuery");
            return CustomResponseDto<List<CurrencySummaryDto>>.InternalServerError("An error occurred while retrieving active currencies");
        }
    }
}

/// <summary>
/// Handler for getting currency by ID
/// </summary>
public class GetCurrencyByIdQueryHandler : IRequestHandler<GetCurrencyByIdQuery, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<GetCurrencyByIdQueryHandler> _logger;

    public GetCurrencyByIdQueryHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<GetCurrencyByIdQueryHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get currency by ID query
    /// </summary>
    /// <param name="request">Get currency by ID query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Currency details</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(GetCurrencyByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetCurrencyByIdQuery for currency ID: {CurrencyId}", request.Id);

            var result = await _currencyProcessService.GetByIdAsync(request.Id, request.Tracking);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved currency {CurrencyId} successfully", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve currency {CurrencyId}. Errors: {Errors}", 
                    request.Id, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetCurrencyByIdQuery for currency ID: {CurrencyId}", request.Id);
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while retrieving the currency");
        }
    }
}

/// <summary>
/// Handler for getting currency by code
/// </summary>
public class GetCurrencyByCodeQueryHandler : IRequestHandler<GetCurrencyByCodeQuery, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<GetCurrencyByCodeQueryHandler> _logger;

    public GetCurrencyByCodeQueryHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<GetCurrencyByCodeQueryHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get currency by code query
    /// </summary>
    /// <param name="request">Get currency by code query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Currency details</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(GetCurrencyByCodeQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetCurrencyByCodeQuery for currency code: {Code}", request.Code);

            var result = await _currencyProcessService.GetByCodeAsync(request.Code, request.Tracking);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved currency {Code} successfully", request.Code);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve currency {Code}. Errors: {Errors}", 
                    request.Code, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetCurrencyByCodeQuery for currency code: {Code}", request.Code);
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while retrieving the currency");
        }
    }
}

/// <summary>
/// Handler for getting default currency
/// </summary>
public class GetDefaultCurrencyQueryHandler : IRequestHandler<GetDefaultCurrencyQuery, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<GetDefaultCurrencyQueryHandler> _logger;

    public GetDefaultCurrencyQueryHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<GetDefaultCurrencyQueryHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get default currency query
    /// </summary>
    /// <param name="request">Get default currency query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Default currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(GetDefaultCurrencyQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetDefaultCurrencyQuery");

            var result = await _currencyProcessService.GetDefaultCurrencyAsync(request.Tracking);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved default currency successfully: {Code}", result.Data?.Code);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve default currency. Errors: {Errors}", 
                    string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetDefaultCurrencyQuery");
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while retrieving the default currency");
        }
    }
}
