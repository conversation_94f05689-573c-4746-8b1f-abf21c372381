using EtyraCommerce.Domain.Entities.Product;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for filtering products
    /// </summary>
    public class ProductFilterDto
    {
        #region Pagination

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size (number of items per page)
        /// </summary>
        public int PageSize { get; set; } = 20;

        #endregion

        #region Filtering

        /// <summary>
        /// Filter by product status
        /// </summary>
        public ProductStatus? Status { get; set; }

        /// <summary>
        /// Filter by product type
        /// </summary>
        public ProductType? Type { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by featured status
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Filter by digital status
        /// </summary>
        public bool? IsDigital { get; set; }

        /// <summary>
        /// Filter by brand
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Filter by category ID
        /// </summary>
        public Guid? CategoryId { get; set; }

        /// <summary>
        /// Filter by minimum price
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Filter by maximum price
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// Filter by minimum stock quantity
        /// </summary>
        public int? MinStock { get; set; }

        /// <summary>
        /// Filter by maximum stock quantity
        /// </summary>
        public int? MaxStock { get; set; }

        /// <summary>
        /// Filter by creation date from
        /// </summary>
        public DateTime? CreatedFrom { get; set; }

        /// <summary>
        /// Filter by creation date to
        /// </summary>
        public DateTime? CreatedTo { get; set; }

        /// <summary>
        /// Filter by update date from
        /// </summary>
        public DateTime? UpdatedFrom { get; set; }

        /// <summary>
        /// Filter by update date to
        /// </summary>
        public DateTime? UpdatedTo { get; set; }

        /// <summary>
        /// Search term for product name, model, SKU, or description
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by SKU
        /// </summary>
        public string? SKU { get; set; }

        /// <summary>
        /// Filter by model
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// Filter by EAN
        /// </summary>
        public string? EAN { get; set; }

        /// <summary>
        /// Filter by barcode
        /// </summary>
        public string? Barcode { get; set; }

        #endregion

        #region Sorting

        /// <summary>
        /// Sort field
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        #endregion

        #region Include Options

        /// <summary>
        /// Whether to include product descriptions
        /// </summary>
        public bool IncludeDescriptions { get; set; } = false;

        /// <summary>
        /// Whether to include product images
        /// </summary>
        public bool IncludeImages { get; set; } = false;

        /// <summary>
        /// Whether to include product categories
        /// </summary>
        public bool IncludeCategories { get; set; } = false;

        /// <summary>
        /// Whether to include product discounts
        /// </summary>
        public bool IncludeDiscounts { get; set; } = false;

        /// <summary>
        /// Whether to include product attributes
        /// </summary>
        public bool IncludeAttributes { get; set; } = false;

        /// <summary>
        /// Whether to include product variants
        /// </summary>
        public bool IncludeVariants { get; set; } = false;

        /// <summary>
        /// Whether to include warehouse products (inventory)
        /// </summary>
        public bool IncludeInventory { get; set; } = false;

        /// <summary>
        /// Whether to include deleted products
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        #endregion

        #region Localization

        /// <summary>
        /// Language code for descriptions (e.g., "en-US", "tr-TR")
        /// If null, returns all languages
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Store ID for store-specific descriptions
        /// If null, returns all stores
        /// </summary>
        public Guid? StoreId { get; set; }

        #endregion
    }
}
