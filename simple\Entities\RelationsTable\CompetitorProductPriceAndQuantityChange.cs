﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.RelationsTable;

public class CompetitorProductPriceAndQuantityChange : BaseEntity
{
    public int ProductCompetitorId { get; set; }
    public int PriceType { get; set; } // 1 - sale Price  2 - Cost  
    public decimal Amount { get; set; }
    public int Quantity { get; set; }

    [MaxLength(4)]
    public string? Currency { get; set; }

    public int CompetitorProductId { get; set; }

    public int CompetitorId { get; set; }

    //public CompetitorSalesStatistics CompetitorSalesStatistics { get; set;}
    //public int CompetitorSalesStatisticsId { get;}

}