using EtyraCommerce.Application.DTOs.Authentication;
using EtyraCommerce.Application.DTOs.CustomResponse;

namespace EtyraCommerce.Application.Services.Authentication
{
    /// <summary>
    /// JWT Service Interface for token management
    /// </summary>
    public interface IJwtService
    {
        /// <summary>
        /// Generates JWT access token and refresh token for a user
        /// </summary>
        /// <param name="user">User entity</param>
        /// <param name="ipAddress">Client IP address</param>
        /// <param name="userAgent">Client user agent</param>
        /// <returns>Token response with access and refresh tokens</returns>
        Task<CustomResponseDto<TokenResponseDto>> GenerateTokenAsync(Domain.Entities.User.User user, string? ipAddress = null, string? userAgent = null);

        /// <summary>
        /// Refreshes an access token using a valid refresh token
        /// </summary>
        /// <param name="refreshTokenDto">Refresh token request</param>
        /// <returns>New token response</returns>
        Task<CustomResponseDto<TokenResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);

        /// <summary>
        /// Revokes a refresh token
        /// </summary>
        /// <param name="revokeTokenDto">Revoke token request</param>
        /// <returns>Success or failure response</returns>
        Task<CustomResponseDto<NoContentDto>> RevokeTokenAsync(RevokeTokenDto revokeTokenDto);

        /// <summary>
        /// Validates a JWT access token
        /// </summary>
        /// <param name="token">JWT token to validate</param>
        /// <returns>Token validation result</returns>
        Task<TokenValidationDto> ValidateTokenAsync(string token);

        /// <summary>
        /// Extracts user ID from JWT token
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>User ID if valid, null otherwise</returns>
        Guid? GetUserIdFromToken(string token);

        /// <summary>
        /// Extracts username from JWT token
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Username if valid, null otherwise</returns>
        string? GetUsernameFromToken(string token);

        /// <summary>
        /// Checks if a JWT token is expired
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>True if expired, false otherwise</returns>
        bool IsTokenExpired(string token);

        /// <summary>
        /// Gets token expiration date
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Expiration date if valid, null otherwise</returns>
        DateTime? GetTokenExpiration(string token);

        /// <summary>
        /// Revokes all refresh tokens for a user (logout from all devices)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="reason">Reason for revocation</param>
        /// <returns>Success or failure response</returns>
        Task<CustomResponseDto<NoContentDto>> RevokeAllUserTokensAsync(Guid userId, string? reason = null);

        /// <summary>
        /// Generates a secure random refresh token
        /// </summary>
        /// <returns>Random refresh token string</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// Gets JWT configuration settings
        /// </summary>
        /// <returns>JWT settings information</returns>
        JwtSettingsDto GetJwtSettings();
    }

    /// <summary>
    /// JWT Settings DTO for configuration information
    /// </summary>
    public class JwtSettingsDto
    {
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int AccessTokenExpirationMinutes { get; set; }
        public int RefreshTokenExpirationDays { get; set; }
        public string Algorithm { get; set; } = string.Empty;
    }
}
