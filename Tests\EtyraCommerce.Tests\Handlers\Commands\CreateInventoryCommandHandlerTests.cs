using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using EtyraCommerce.Application.Services.Inventory.Handlers.Commands;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class CreateInventoryCommandHandlerTests
    {
        private readonly Mock<IInventoryProcessService> _mockInventoryProcessService;
        private readonly Mock<ILogger<CreateInventoryCommandHandler>> _mockLogger;
        private readonly CreateInventoryCommandHandler _handler;

        public CreateInventoryCommandHandlerTests()
        {
            _mockInventoryProcessService = new Mock<IInventoryProcessService>();
            _mockLogger = new Mock<ILogger<CreateInventoryCommandHandler>>();
            _handler = new CreateInventoryCommandHandler(_mockInventoryProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessWithInventoryDto()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100,
                ReorderPoint = 10,
                MaxStockLevel = 500,
                LocationCode = "A1-B2-C3",
                Notes = "Initial stock setup"
            };

            var expectedDto = new InventoryDto
            {
                Id = Guid.NewGuid(),
                ProductId = command.ProductId,
                WarehouseId = command.WarehouseId,
                TotalQuantity = command.AvailableQuantity,
                AvailableQuantity = command.AvailableQuantity,
                ReservedQuantity = 0,
                AllocatedQuantity = 0,
                ReorderPoint = command.ReorderPoint,
                MaxStockLevel = command.MaxStockLevel,
                LocationCode = command.LocationCode,
                Notes = command.Notes
            };

            var expectedResponse = CustomResponseDto<InventoryDto>.Success(expectedDto, "Inventory created successfully");

            _mockInventoryProcessService
                .Setup(x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.ProductId.Should().Be(command.ProductId);
            result.Data.WarehouseId.Should().Be(command.WarehouseId);
            result.Data.TotalQuantity.Should().Be(command.AvailableQuantity);
            result.Data.ReorderPoint.Should().Be(command.ReorderPoint);
            result.Data.MaxStockLevel.Should().Be(command.MaxStockLevel);
            result.Data.LocationCode.Should().Be(command.LocationCode);
            result.Data.Notes.Should().Be(command.Notes);

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.Is<CreateInventoryDto>(dto =>
                    dto.ProductId == command.ProductId &&
                    dto.WarehouseId == command.WarehouseId &&
                    dto.AvailableQuantity == command.AvailableQuantity &&
                    dto.ReorderPoint == command.ReorderPoint &&
                    dto.MaxStockLevel == command.MaxStockLevel &&
                    dto.LocationCode == command.LocationCode &&
                    dto.Notes == command.Notes
                )), Times.Once);
        }

        [Fact]
        public async Task Handle_InvalidProductId_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.Empty, // Invalid
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Product ID is required");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_InvalidWarehouseId_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.Empty, // Invalid
                AvailableQuantity = 100
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Warehouse ID is required");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_NegativeAvailableQuantity_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = -10 // Invalid
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Available quantity cannot be negative");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_NegativeReorderPoint_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100,
                ReorderPoint = -5 // Invalid
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Stock levels cannot be negative");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_MaxStockLevelLessThanReorderPoint_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100,
                ReorderPoint = 50,
                MaxStockLevel = 30 // Less than reorder point
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("An error occurred while creating inventory");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ProcessServiceReturnsError_ReturnsError()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100,
                ReorderPoint = 10,
                MaxStockLevel = 500
            };

            var errorResponse = CustomResponseDto<InventoryDto>.BadRequest("Product not found");

            _mockInventoryProcessService
                .Setup(x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()))
                .ReturnsAsync(errorResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Be("Product not found");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CreateInventoryCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                AvailableQuantity = 100
            };

            _mockInventoryProcessService
                .Setup(x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("An error occurred while creating inventory");

            _mockInventoryProcessService.Verify(
                x => x.ProcessCreateInventoryAsync(It.IsAny<CreateInventoryDto>()),
                Times.Once);
        }

        #endregion
    }
}
