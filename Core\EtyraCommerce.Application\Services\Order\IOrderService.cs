using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;

namespace EtyraCommerce.Application.Services.Order
{
    /// <summary>
    /// Order service interface for read operations
    /// </summary>
    public interface IOrderService
    {
        /// <summary>
        /// Gets order by ID
        /// </summary>
        Task<CustomResponseDto<OrderDto>> GetOrderByIdAsync(Guid orderId, bool includeItems = true, bool includeCustomer = false, string? languageCode = null);

        /// <summary>
        /// Searches orders with advanced filtering
        /// </summary>
        Task<CustomResponseDto<PagedResult<OrderDto>>> SearchOrdersAsync(OrderSearchDto searchDto);

        /// <summary>
        /// Gets order statistics
        /// </summary>
        Task<CustomResponseDto<OrderStatisticsDto>> GetOrderStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? customerId = null,
            string? currency = null,
            int topCustomersCount = 10,
            int recentOrdersCount = 5);

        /// <summary>
        /// Gets orders by customer ID
        /// </summary>
        Task<CustomResponseDto<PagedResult<OrderDto>>> GetOrdersByCustomerIdAsync(Guid customerId, int page = 1, int pageSize = 10);

        /// <summary>
        /// Gets order by order number
        /// </summary>
        Task<CustomResponseDto<OrderDto>> GetOrderByOrderNumberAsync(string orderNumber, bool includeItems = true);

        /// <summary>
        /// Checks if order exists
        /// </summary>
        Task<bool> OrderExistsAsync(Guid orderId);

        /// <summary>
        /// Checks if order belongs to customer
        /// </summary>
        Task<bool> OrderBelongsToCustomerAsync(Guid orderId, Guid customerId);

        /// <summary>
        /// Creates a new order
        /// </summary>
        Task<CustomResponseDto<OrderDto>> CreateOrderAsync(CreateOrderDto createOrderDto);

        /// <summary>
        /// Updates order status
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> UpdateOrderStatusAsync(Guid orderId, UpdateOrderStatusDto updateStatusDto);

        /// <summary>
        /// Updates payment status
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> UpdatePaymentStatusAsync(Guid orderId, UpdatePaymentStatusDto updatePaymentDto);

        /// <summary>
        /// Confirms an order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ConfirmOrderAsync(Guid orderId);

        /// <summary>
        /// Cancels an order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> CancelOrderAsync(Guid orderId, string? reason = null, Guid? cancelledBy = null);

        /// <summary>
        /// Ships an order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ShipOrderAsync(Guid orderId, string? trackingNumber = null, DateTime? expectedDeliveryDate = null);

        /// <summary>
        /// Delivers an order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> DeliverOrderAsync(Guid orderId, DateTime? deliveryDate = null);
    }
}
