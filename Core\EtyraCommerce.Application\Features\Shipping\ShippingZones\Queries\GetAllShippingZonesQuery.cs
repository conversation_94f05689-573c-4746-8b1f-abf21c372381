using MediatR;
using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Domain.Enums.Shipping;

namespace EtyraCommerce.Application.Features.Shipping.ShippingZones.Queries
{
    /// <summary>
    /// Query to get all shipping zones with optional filtering
    /// </summary>
    public class GetAllShippingZonesQuery : IRequest<GetAllShippingZonesResponse>
    {
        /// <summary>
        /// Filter by zone type (optional)
        /// </summary>
        public ZoneType? ZoneType { get; set; }

        /// <summary>
        /// Search term for zone name or code (optional)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Include country count in response
        /// </summary>
        public bool IncludeCountryCount { get; set; } = false;

        /// <summary>
        /// Include shipping methods count in response
        /// </summary>
        public bool IncludeMethodCount { get; set; } = false;

        /// <summary>
        /// Order by field (Name, Code, DisplayOrder, Type)
        /// </summary>
        public string OrderBy { get; set; } = "DisplayOrder";

        /// <summary>
        /// Order direction (asc, desc)
        /// </summary>
        public string OrderDirection { get; set; } = "asc";
    }

    /// <summary>
    /// Response for get all shipping zones query
    /// </summary>
    public class GetAllShippingZonesResponse
    {
        /// <summary>
        /// List of shipping zones
        /// </summary>
        public List<ShippingZoneListItem> ShippingZones { get; set; } = new();

        /// <summary>
        /// Total count of shipping zones
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Count by zone type
        /// </summary>
        public Dictionary<ZoneType, int> CountByType { get; set; } = new();
    }

    /// <summary>
    /// Shipping zone list item DTO
    /// </summary>
    public class ShippingZoneListItem
    {
        /// <summary>
        /// Zone ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Zone name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Zone code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Zone type
        /// </summary>
        public ZoneType Type { get; set; }

        /// <summary>
        /// Zone type display name
        /// </summary>
        public string TypeDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Zone description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Default minimum delivery days
        /// </summary>
        public int DefaultMinDeliveryDays { get; set; }

        /// <summary>
        /// Default maximum delivery days
        /// </summary>
        public int DefaultMaxDeliveryDays { get; set; }

        /// <summary>
        /// Display order
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Number of countries (if requested)
        /// </summary>
        public int? CountryCount { get; set; }

        /// <summary>
        /// Number of shipping methods (if requested)
        /// </summary>
        public int? MethodCount { get; set; }

        /// <summary>
        /// Display name with delivery time info
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Delivery time text
        /// </summary>
        public string DeliveryTimeText { get; set; } = string.Empty;
    }
}
