using EtyraCommerce.Application.DTOs.Authentication;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Authentication;
using EtyraCommerce.Application.UnitOfWork;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace EtyraCommerce.Persistence.Services.Authentication
{
    /// <summary>
    /// JWT Service implementation for token management
    /// </summary>
    public class JwtService : IJwtService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<JwtService> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly JwtSecurityTokenHandler _tokenHandler;

        // JWT Configuration Keys
        private const string JWT_SECTION = "JwtSettings";
        private const string SECRET_KEY = "JwtSettings:SecretKey";
        private const string ISSUER_KEY = "JwtSettings:Issuer";
        private const string AUDIENCE_KEY = "JwtSettings:Audience";
        private const string ACCESS_TOKEN_EXPIRATION_KEY = "JwtSettings:AccessTokenExpirationMinutes";
        private const string REFRESH_TOKEN_EXPIRATION_KEY = "JwtSettings:RefreshTokenExpirationDays";

        public JwtService(
            IConfiguration configuration,
            ILogger<JwtService> logger,
            IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _logger = logger;
            _unitOfWork = unitOfWork;
            _tokenHandler = new JwtSecurityTokenHandler();
        }

        public async Task<CustomResponseDto<TokenResponseDto>> GenerateTokenAsync(Domain.Entities.User.User user, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                _logger.LogInformation("Generating JWT token for user: {UserId}", user.Id);

                // Generate access token
                var accessToken = GenerateAccessToken(user);
                var refreshToken = GenerateRefreshToken();

                // Calculate expiration times
                var accessTokenExpiration = DateTime.UtcNow.AddMinutes(GetAccessTokenExpirationMinutes());
                var refreshTokenExpiration = DateTime.UtcNow.AddDays(GetRefreshTokenExpirationDays());

                // Update user with refresh token
                user.SetRefreshToken(refreshToken, refreshTokenExpiration, ipAddress);

                // Save changes
                await _unitOfWork.CommitAsync();

                // Create response
                var tokenResponse = new TokenResponseDto(accessToken, refreshToken, accessTokenExpiration, refreshTokenExpiration)
                {
                    User = new TokenUserDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email.Value,
                        FullName = user.FullName,
                        Roles = new List<string> { "User" }, // TODO: Implement role system
                        Permissions = new List<string>()
                    }
                };

                _logger.LogInformation("JWT token generated successfully for user: {UserId}", user.Id);
                return CustomResponseDto<TokenResponseDto>.Success(200, tokenResponse, "Token generated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating JWT token for user: {UserId}", user.Id);
                return CustomResponseDto<TokenResponseDto>.InternalServerError("Failed to generate token");
            }
        }

        public async Task<CustomResponseDto<TokenResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                _logger.LogInformation("Refreshing JWT token");

                // Find user by refresh token
                var userRepository = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await userRepository.GetFirstOrDefaultAsync(u => u.RefreshToken == refreshTokenDto.RefreshToken);

                if (user == null)
                {
                    _logger.LogWarning("Invalid refresh token provided");
                    return CustomResponseDto<TokenResponseDto>.BadRequest("Invalid refresh token");
                }

                // Check if refresh token is active
                if (!user.IsRefreshTokenActive)
                {
                    _logger.LogWarning("Refresh token is expired or revoked for user: {UserId}", user.Id);
                    return CustomResponseDto<TokenResponseDto>.BadRequest("Refresh token is expired or revoked");
                }

                // Generate new tokens
                return await GenerateTokenAsync(user, refreshTokenDto.IpAddress, refreshTokenDto.UserAgent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing JWT token");
                return CustomResponseDto<TokenResponseDto>.InternalServerError("Failed to refresh token");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> RevokeTokenAsync(RevokeTokenDto revokeTokenDto)
        {
            try
            {
                _logger.LogInformation("Revoking refresh token");

                // Find user by refresh token
                var userRepository = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await userRepository.GetFirstOrDefaultAsync(u => u.RefreshToken == revokeTokenDto.RefreshToken);

                if (user == null)
                {
                    _logger.LogWarning("Invalid refresh token provided for revocation");
                    return CustomResponseDto<NoContentDto>.BadRequest("Invalid refresh token");
                }

                // Revoke the token
                user.RevokeRefreshToken(revokeTokenDto.IpAddress, revokeTokenDto.Reason);

                // Save changes
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("Refresh token revoked successfully for user: {UserId}", user.Id);
                return CustomResponseDto<NoContentDto>.Success(200, "Token revoked successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking refresh token");
                return CustomResponseDto<NoContentDto>.InternalServerError("Failed to revoke token");
            }
        }

        public async Task<TokenValidationDto> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenValidationParameters = GetTokenValidationParameters();
                var principal = _tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);

                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var usernameClaim = principal.FindFirst(ClaimTypes.Name)?.Value;
                var emailClaim = principal.FindFirst(ClaimTypes.Email)?.Value;

                return new TokenValidationDto
                {
                    IsValid = true,
                    UserId = Guid.TryParse(userIdClaim, out var userId) ? userId : null,
                    Username = usernameClaim,
                    Email = emailClaim,
                    ExpiresAt = validatedToken.ValidTo,
                    Claims = principal.Claims.ToDictionary(c => c.Type, c => c.Value)
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Token validation failed");
                return new TokenValidationDto
                {
                    IsValid = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public Guid? GetUserIdFromToken(string token)
        {
            try
            {
                var jsonToken = _tokenHandler.ReadJwtToken(token);
                var userIdClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
            }
            catch
            {
                return null;
            }
        }

        public string? GetUsernameFromToken(string token)
        {
            try
            {
                var jsonToken = _tokenHandler.ReadJwtToken(token);
                return jsonToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;
            }
            catch
            {
                return null;
            }
        }

        public bool IsTokenExpired(string token)
        {
            try
            {
                var jsonToken = _tokenHandler.ReadJwtToken(token);
                return jsonToken.ValidTo < DateTime.UtcNow;
            }
            catch
            {
                return true;
            }
        }

        public DateTime? GetTokenExpiration(string token)
        {
            try
            {
                var jsonToken = _tokenHandler.ReadJwtToken(token);
                return jsonToken.ValidTo;
            }
            catch
            {
                return null;
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> RevokeAllUserTokensAsync(Guid userId, string? reason = null)
        {
            try
            {
                _logger.LogInformation("Revoking all tokens for user: {UserId}", userId);

                var userRepository = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await userRepository.GetByIdAsync(userId);

                if (user == null)
                {
                    return CustomResponseDto<NoContentDto>.NotFound("User not found");
                }

                user.RevokeRefreshToken(reason: reason ?? "All tokens revoked");
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("All tokens revoked successfully for user: {UserId}", userId);
                return CustomResponseDto<NoContentDto>.Success(200, "All tokens revoked successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking all tokens for user: {UserId}", userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("Failed to revoke tokens");
            }
        }

        public string GenerateRefreshToken()
        {
            var randomBytes = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            return Convert.ToBase64String(randomBytes);
        }

        public JwtSettingsDto GetJwtSettings()
        {
            return new JwtSettingsDto
            {
                Issuer = _configuration[ISSUER_KEY] ?? "EtyraCommerce",
                Audience = _configuration[AUDIENCE_KEY] ?? "EtyraCommerce",
                AccessTokenExpirationMinutes = GetAccessTokenExpirationMinutes(),
                RefreshTokenExpirationDays = GetRefreshTokenExpirationDays(),
                Algorithm = SecurityAlgorithms.HmacSha256
            };
        }

        #region Private Methods

        private string GenerateAccessToken(Domain.Entities.User.User user)
        {
            var key = Encoding.UTF8.GetBytes(GetSecretKey());
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Name, user.Username),
                    new Claim(ClaimTypes.Email, user.Email.Value),
                    new Claim("FullName", user.FullName),
                    new Claim("EmailConfirmed", user.IsEmailConfirmed.ToString()),
                    // TODO: Add role claims when role system is implemented
                }),
                Expires = DateTime.UtcNow.AddMinutes(GetAccessTokenExpirationMinutes()),
                Issuer = _configuration[ISSUER_KEY],
                Audience = _configuration[AUDIENCE_KEY],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = _tokenHandler.CreateToken(tokenDescriptor);
            return _tokenHandler.WriteToken(token);
        }

        private TokenValidationParameters GetTokenValidationParameters()
        {
            var key = Encoding.UTF8.GetBytes(GetSecretKey());
            return new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration[ISSUER_KEY],
                ValidateAudience = true,
                ValidAudience = _configuration[AUDIENCE_KEY],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        }

        private string GetSecretKey()
        {
            var secretKey = _configuration[SECRET_KEY];
            if (string.IsNullOrEmpty(secretKey))
            {
                throw new InvalidOperationException("JWT Secret Key is not configured");
            }
            return secretKey;
        }

        private int GetAccessTokenExpirationMinutes()
        {
            return _configuration.GetValue<int>(ACCESS_TOKEN_EXPIRATION_KEY, 15); // Default 15 minutes
        }

        private int GetRefreshTokenExpirationDays()
        {
            return _configuration.GetValue<int>(REFRESH_TOKEN_EXPIRATION_KEY, 30); // Default 30 days
        }

        #endregion
    }
}
