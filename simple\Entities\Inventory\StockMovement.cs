using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;

namespace EtyraApp.Domain.Entities.Inventory;

public class StockMovement : BaseEntity
{
    /// <summary>
    /// Ürün ID
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Depo ID
    /// </summary>
    public int? WarehouseId { get; set; }

    /// <summary>
    /// Hareket tipi
    /// </summary>
    public StockMovementType MovementType { get; set; }

    /// <summary>
    /// Miktar (+ giriş, - çıkış)
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Hareket öncesi stok seviyesi
    /// </summary>
    public int StockLevelBefore { get; set; }

    /// <summary>
    /// Hareket sonrası stok seviyesi
    /// </summary>
    public int StockLevelAfter { get; set; }

    /// <summary>
    /// Hareket tarihi
    /// </summary>
    public DateTime MovementDate { get; set; }

    /// <summary>
    /// Hareket nedeni
    /// </summary>
    public StockMovementReason Reason { get; set; }

    /// <summary>
    /// Referans ID (OrderId, PurchaseId vb.)
    /// </summary>
    public int? ReferenceId { get; set; }

    /// <summary>
    /// Referans tipi (Order, Purchase, Return vb.)
    /// </summary>
    [MaxLength(50)]
    public string? ReferenceType { get; set; }

    /// <summary>
    /// Notlar
    /// </summary>
    [MaxLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// İşlemi yapan kullanıcı ID
    /// </summary>
    public int? CreatedBy { get; set; }

    // Navigation Properties
    public Product Product { get; set; }
    public Warehouse? Warehouse { get; set; }
}

/// <summary>
/// Stok hareket tipleri
/// </summary>
public enum StockMovementType
{
    /// <summary>
    /// Stok girişi
    /// </summary>
    StockIn = 1,

    /// <summary>
    /// Stok çıkışı
    /// </summary>
    StockOut = 2,

    /// <summary>
    /// Stok düzeltmesi
    /// </summary>
    Adjustment = 3,

    /// <summary>
    /// Depo transferi
    /// </summary>
    Transfer = 4,

    /// <summary>
    /// İade
    /// </summary>
    Return = 5
}

/// <summary>
/// Stok hareket nedenleri
/// </summary>
public enum StockMovementReason
{
    /// <summary>
    /// Satış
    /// </summary>
    Sale = 1,

    /// <summary>
    /// Satın alma
    /// </summary>
    Purchase = 2,

    /// <summary>
    /// Müşteri iadesi
    /// </summary>
    CustomerReturn = 3,

    /// <summary>
    /// Tedarikçi iadesi
    /// </summary>
    SupplierReturn = 4,

    /// <summary>
    /// Sayım düzeltmesi
    /// </summary>
    InventoryAdjustment = 5,

    /// <summary>
    /// Hasarlı ürün
    /// </summary>
    Damaged = 6,

    /// <summary>
    /// Kayıp
    /// </summary>
    Lost = 7,

    /// <summary>
    /// Depo transferi
    /// </summary>
    WarehouseTransfer = 8,

    /// <summary>
    /// Manuel düzeltme
    /// </summary>
    ManualAdjustment = 9
}
