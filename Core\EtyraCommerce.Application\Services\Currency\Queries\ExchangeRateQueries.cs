using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.Entities.Currency;
using MediatR;
using static EtyraCommerce.Domain.Entities.Currency.ExchangeRate;

namespace EtyraCommerce.Application.Services.Currency.Queries;

/// <summary>
/// Query to get all exchange rates
/// </summary>
public class GetAllExchangeRatesQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = false;

    /// <summary>
    /// Whether to include only currently valid rates (active and not expired)
    /// </summary>
    public bool ValidOnly { get; set; } = false;
}

/// <summary>
/// Query to get exchange rate by ID
/// </summary>
public class GetExchangeRateByIdQuery : IRequest<CustomResponseDto<ExchangeRateDto>>
{
    /// <summary>
    /// Exchange rate ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;
}

/// <summary>
/// Query to get exchange rates by currency pair
/// </summary>
public class GetExchangeRatesByCurrencyPairQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Base currency ID (from currency)
    /// </summary>
    public Guid FromCurrencyId { get; set; }

    /// <summary>
    /// Target currency ID (to currency)
    /// </summary>
    public Guid ToCurrencyId { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;

    /// <summary>
    /// Whether to include only currently valid rates (active and not expired)
    /// </summary>
    public bool ValidOnly { get; set; } = true;
}

/// <summary>
/// Query to get exchange rates by currency (either as base or target currency)
/// </summary>
public class GetExchangeRatesByCurrencyQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Currency ID to filter by
    /// </summary>
    public Guid CurrencyId { get; set; }

    /// <summary>
    /// Whether to get rates where this currency is the base currency (true) or target currency (false)
    /// </summary>
    public bool AsBaseCurrency { get; set; } = true;

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;

    /// <summary>
    /// Whether to include only currently valid rates (active and not expired)
    /// </summary>
    public bool ValidOnly { get; set; } = true;
}

/// <summary>
/// Query to get current exchange rate for currency pair
/// </summary>
public class GetCurrentExchangeRateQuery : IRequest<CustomResponseDto<ExchangeRateDto>>
{
    /// <summary>
    /// Base currency ID (from currency)
    /// </summary>
    public Guid FromCurrencyId { get; set; }

    /// <summary>
    /// Target currency ID (to currency)
    /// </summary>
    public Guid ToCurrencyId { get; set; }

    /// <summary>
    /// Date for which to get the rate (optional, uses current date if not specified)
    /// </summary>
    public DateTime? AsOfDate { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;
}

/// <summary>
/// Query to get current exchange rate by currency codes
/// </summary>
public class GetCurrentExchangeRateByCurrencyCodesQuery : IRequest<CustomResponseDto<ExchangeRateDto>>
{
    /// <summary>
    /// Base currency code (from currency)
    /// </summary>
    public string FromCurrencyCode { get; set; } = null!;

    /// <summary>
    /// Target currency code (to currency)
    /// </summary>
    public string ToCurrencyCode { get; set; } = null!;

    /// <summary>
    /// Date for which to get the rate (optional, uses current date if not specified)
    /// </summary>
    public DateTime? AsOfDate { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;
}

/// <summary>
/// Query to get exchange rates by source
/// </summary>
public class GetExchangeRatesBySourceQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Source of the exchange rates
    /// </summary>
    public ExchangeRateSource Source { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;

    /// <summary>
    /// Whether to include only currently valid rates (active and not expired)
    /// </summary>
    public bool ValidOnly { get; set; } = true;
}

/// <summary>
/// Query to get exchange rates by date range
/// </summary>
public class GetExchangeRatesByDateRangeQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Start date (inclusive)
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date (inclusive)
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Base currency ID (optional, filters by from currency)
    /// </summary>
    public Guid? FromCurrencyId { get; set; }

    /// <summary>
    /// Target currency ID (optional, filters by to currency)
    /// </summary>
    public Guid? ToCurrencyId { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;
}

/// <summary>
/// Query to get expired exchange rates
/// </summary>
public class GetExpiredExchangeRatesQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Date to check expiry against (optional, uses current date if not specified)
    /// </summary>
    public DateTime? AsOfDate { get; set; }

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;
}

/// <summary>
/// Query to get exchange rate history for currency pair
/// </summary>
public class GetExchangeRateHistoryQuery : IRequest<CustomResponseDto<List<ExchangeRateDto>>>
{
    /// <summary>
    /// Base currency ID (from currency)
    /// </summary>
    public Guid FromCurrencyId { get; set; }

    /// <summary>
    /// Target currency ID (to currency)
    /// </summary>
    public Guid ToCurrencyId { get; set; }

    /// <summary>
    /// Number of days to look back (optional, defaults to 30 days)
    /// </summary>
    public int? DaysBack { get; set; } = 30;

    /// <summary>
    /// Maximum number of records to return (optional, defaults to 100)
    /// </summary>
    public int? MaxRecords { get; set; } = 100;

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;
}

/// <summary>
/// Query to get exchange rate summary (for dashboard/overview)
/// </summary>
public class GetExchangeRateSummaryQuery : IRequest<CustomResponseDto<List<ExchangeRateSummaryDto>>>
{
    /// <summary>
    /// Whether to include only active rates
    /// </summary>
    public bool ActiveOnly { get; set; } = true;

    /// <summary>
    /// Whether to include only currently valid rates (active and not expired)
    /// </summary>
    public bool ValidOnly { get; set; } = true;

    /// <summary>
    /// Whether to track changes for EF Core
    /// </summary>
    public bool Tracking { get; set; } = false;
}
