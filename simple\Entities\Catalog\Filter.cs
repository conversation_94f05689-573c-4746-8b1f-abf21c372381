﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.RelationsTable;

namespace EtyraApp.Domain.Entities.Catalog;

public class Filter : BaseEntity
{
    public int FilterGroupId { get; set; }
    public int? AiPlatformId { get; set; }

    public FilterGroup FilterGroup { get; set; }
    public ICollection<FilterDescription> FilterDescriptions { get; set; }
    public ICollection<ProductFilter>? ProductFilters { get; set; }

}