using EtyraCommerce.Domain.Entities.Product;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ProductImage entity
    /// </summary>
    public class ProductImageConfiguration : BaseEntityConfiguration<ProductImage>
    {
        public override void Configure(EntityTypeBuilder<ProductImage> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("product_images", "etyra_core");

            #region Properties

            // Image URL
            builder.Property(x => x.ImageUrl)
                .HasColumnName("image_url")
                .HasMaxLength(500)
                .IsRequired();

            // Thumbnail URL
            builder.Property(x => x.ThumbnailUrl)
                .HasColumnName("thumbnail_url")
                .HasMaxLength(500)
                .IsRequired(false);

            // Alt Text
            builder.Property(x => x.AltText)
                .HasColumnName("alt_text")
                .HasMaxLength(200)
                .IsRequired(false);

            // Title
            builder.Property(x => x.Title)
                .HasColumnName("title")
                .HasMaxLength(200)
                .IsRequired(false);

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasMaxLength(500)
                .IsRequired(false);

            // Sort Order
            builder.Property(x => x.SortOrder)
                .HasColumnName("sort_order")
                .HasDefaultValue(0)
                .IsRequired();

            // Is Main
            builder.Property(x => x.IsMain)
                .HasColumnName("is_main")
                .HasDefaultValue(false)
                .IsRequired();

            // Type
            builder.Property(x => x.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();

            // File Size
            builder.Property(x => x.FileSize)
                .HasColumnName("file_size")
                .IsRequired(false);

            // Width
            builder.Property(x => x.Width)
                .HasColumnName("width")
                .IsRequired(false);

            // Height
            builder.Property(x => x.Height)
                .HasColumnName("height")
                .IsRequired(false);

            // MIME Type
            builder.Property(x => x.MimeType)
                .HasColumnName("mime_type")
                .HasMaxLength(100)
                .IsRequired(false);

            // Original File Name
            builder.Property(x => x.OriginalFileName)
                .HasColumnName("original_file_name")
                .HasMaxLength(255)
                .IsRequired(false);

            // Product ID
            builder.Property(x => x.ProductId)
                .HasColumnName("product_id")
                .IsRequired();

            #endregion

            #region Navigation Properties

            // Product (Many-to-One)
            builder.HasOne(x => x.Product)
                .WithMany(x => x.Images)
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Performance Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_product_images_product_id");

            builder.HasIndex(x => x.Type)
                .HasDatabaseName("ix_product_images_type");

            builder.HasIndex(x => x.IsMain)
                .HasDatabaseName("ix_product_images_is_main");

            builder.HasIndex(x => x.SortOrder)
                .HasDatabaseName("ix_product_images_sort_order");

            // Composite Indexes
            builder.HasIndex(x => new { x.ProductId, x.SortOrder })
                .HasDatabaseName("ix_product_images_product_sort");

            builder.HasIndex(x => new { x.ProductId, x.IsMain })
                .HasDatabaseName("ix_product_images_product_main");

            builder.HasIndex(x => new { x.ProductId, x.Type })
                .HasDatabaseName("ix_product_images_product_type");

            builder.HasIndex(x => new { x.ProductId, x.Type, x.SortOrder })
                .HasDatabaseName("ix_product_images_product_type_sort");

            // Unique constraint for main image per product
            builder.HasIndex(x => new { x.ProductId, x.IsMain })
                .HasFilter("is_main = true")
                .IsUnique()
                .HasDatabaseName("ix_product_images_product_main_unique");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_ProductImages_ImageUrl_NotEmpty",
                "LENGTH(TRIM(image_url)) > 0");

            builder.HasCheckConstraint("CK_ProductImages_SortOrder_NonNegative",
                "sort_order >= 0");

            builder.HasCheckConstraint("CK_ProductImages_FileSize_Positive",
                "file_size IS NULL OR file_size > 0");

            builder.HasCheckConstraint("CK_ProductImages_Dimensions_Positive",
                "width IS NULL OR width > 0");

            builder.HasCheckConstraint("CK_ProductImages_Height_Positive",
                "height IS NULL OR height > 0");

            #endregion
        }

        protected override string GetTableName() => "product_images";
    }
}
