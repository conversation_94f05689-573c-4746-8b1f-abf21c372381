﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddShoppingCartTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "shopping_carts",
                schema: "etyra_core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true, comment: "Customer ID who owns the cart (null for guest carts)"),
                    SessionId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true, comment: "Session ID for guest carts"),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    currency_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    currency_symbol = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    currency_decimal_places = table.Column<int>(type: "integer", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, comment: "Cart expiration date"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true, comment: "Whether this cart is active"),
                    subtotal_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    subtotal_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", maxLength: 255, nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", maxLength: 255, nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_shopping_carts", x => x.Id);
                    table.CheckConstraint("ck_shopping_carts_customer_or_session", "(\"CustomerId\" IS NOT NULL AND \"SessionId\" IS NULL) OR (\"CustomerId\" IS NULL AND \"SessionId\" IS NOT NULL)");
                    table.CheckConstraint("ck_shopping_carts_expires_future", "\"ExpiresAt\" > \"CreatedAt\"");
                    table.CheckConstraint("ck_shopping_carts_subtotal_positive", "subtotal_amount >= 0");
                },
                comment: "Shopping cart entity for storing customer cart items");

            migrationBuilder.CreateTable(
                name: "cart_items",
                schema: "etyra_core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false, comment: "Product ID"),
                    ProductName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false, comment: "Product name at the time of adding to cart"),
                    ProductSku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "Product SKU at the time of adding to cart"),
                    unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Quantity = table.Column<int>(type: "integer", nullable: false, comment: "Quantity of this item in cart"),
                    total_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    total_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    VariantInfo = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true, comment: "Product variant information (e.g., Size: L, Color: Red)"),
                    ProductImageUrl = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "Product image URL for display"),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true, comment: "Special notes for this item"),
                    ShoppingCartId = table.Column<Guid>(type: "uuid", nullable: false, comment: "Shopping cart ID this item belongs to"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cart_items", x => x.Id);
                    table.CheckConstraint("ck_cart_items_currencies_match", "unit_price_currency = total_price_currency");
                    table.CheckConstraint("ck_cart_items_quantity_positive", "\"Quantity\" > 0");
                    table.CheckConstraint("ck_cart_items_total_price_positive", "total_price_amount > 0");
                    table.CheckConstraint("ck_cart_items_unit_price_positive", "unit_price_amount > 0");
                    table.ForeignKey(
                        name: "FK_cart_items_shopping_carts_ShoppingCartId",
                        column: x => x.ShoppingCartId,
                        principalSchema: "etyra_core",
                        principalTable: "shopping_carts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "Cart item entity representing an item in a shopping cart");

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_cart_product",
                schema: "etyra_core",
                table: "cart_items",
                columns: new[] { "ShoppingCartId", "ProductId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_product_id",
                schema: "etyra_core",
                table: "cart_items",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_product_sku",
                schema: "etyra_core",
                table: "cart_items",
                column: "ProductSku");

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_shopping_cart_id",
                schema: "etyra_core",
                table: "cart_items",
                column: "ShoppingCartId");

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_customer_active",
                schema: "etyra_core",
                table: "shopping_carts",
                columns: new[] { "CustomerId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_customer_id",
                schema: "etyra_core",
                table: "shopping_carts",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_expires_at",
                schema: "etyra_core",
                table: "shopping_carts",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_is_active",
                schema: "etyra_core",
                table: "shopping_carts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_session_active",
                schema: "etyra_core",
                table: "shopping_carts",
                columns: new[] { "SessionId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "ix_shopping_carts_session_id",
                schema: "etyra_core",
                table: "shopping_carts",
                column: "SessionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "cart_items",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "shopping_carts",
                schema: "etyra_core");
        }
    }
}
