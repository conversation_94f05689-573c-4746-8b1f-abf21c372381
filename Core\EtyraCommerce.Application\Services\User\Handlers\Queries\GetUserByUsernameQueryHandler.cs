using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserByUsernameQuery - Validates and delegates to UserProcessService
    /// </summary>
    public class GetUserByUsernameQueryHandler : IRequestHandler<GetUserByUsernameQuery, CustomResponseDto<UserDto?>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<GetUserByUsernameQueryHandler> _logger;

        public GetUserByUsernameQueryHandler(
            IUserProcessService userProcessService,
            ILogger<GetUserByUsernameQueryHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto?>> Handle(GetUserByUsernameQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get user by username query for Username: {Username}", request.Username);

                // Validate username format
                if (string.IsNullOrWhiteSpace(request.Username))
                {
                    _logger.LogWarning("Get user by username query failed: Username is required");
                    return CustomResponseDto<UserDto?>.BadRequest("Username is required");
                }

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessGetUserByUsernameAsync(request.Username);

                if (result.IsSuccess)
                {
                    if (result.Data != null)
                    {
                        _logger.LogDebug("User found for Username: {Username}", request.Username);
                    }
                    else
                    {
                        _logger.LogDebug("User not found for Username: {Username}", request.Username);
                    }
                }
                else
                {
                    _logger.LogWarning("Get user by username query failed for Username: {Username}. Reason: {Message}",
                        request.Username, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get user by username query for Username: {Username}", request.Username);
                return CustomResponseDto<UserDto?>.InternalServerError("An error occurred while retrieving user");
            }
        }
    }
}
