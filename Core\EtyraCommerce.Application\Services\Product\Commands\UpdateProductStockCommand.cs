using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Commands
{
    /// <summary>
    /// Command for updating product stock
    /// </summary>
    public class UpdateProductStockCommand : IRequest<CustomResponseDto<ProductDto>>
    {
        /// <summary>
        /// Product ID to update stock for
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// New total stock quantity
        /// </summary>
        public int TotalStockQuantity { get; set; }

        /// <summary>
        /// Minimum stock alert level
        /// </summary>
        public int? MinStockAlert { get; set; }

        /// <summary>
        /// Whether to manage stock for this product
        /// </summary>
        public bool? ManageStock { get; set; }

        /// <summary>
        /// Reason for stock change
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Stock adjustment type
        /// </summary>
        public StockAdjustmentType AdjustmentType { get; set; } = StockAdjustmentType.Set;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateProductStockCommand()
        {
        }

        /// <summary>
        /// Constructor with product ID and stock quantity
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="stockQuantity">Stock quantity</param>
        public UpdateProductStockCommand(Guid productId, int stockQuantity)
        {
            ProductId = productId;
            TotalStockQuantity = stockQuantity;
        }

        /// <summary>
        /// Constructor with full parameters
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="stockQuantity">Stock quantity</param>
        /// <param name="adjustmentType">Adjustment type</param>
        /// <param name="reason">Reason for change</param>
        public UpdateProductStockCommand(Guid productId, int stockQuantity, StockAdjustmentType adjustmentType, string? reason = null)
        {
            ProductId = productId;
            TotalStockQuantity = stockQuantity;
            AdjustmentType = adjustmentType;
            Reason = reason;
        }
    }
}
