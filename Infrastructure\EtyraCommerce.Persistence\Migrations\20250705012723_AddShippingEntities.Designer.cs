﻿// <auto-generated />
using System;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    [DbContext(typeof(EtyraCommerceDbContext))]
    [Migration("20250705012723_AddShippingEntities")]
    partial class AddShippingEntities
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("etyra_core")
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CategoryCategory", b =>
                {
                    b.Property<Guid>("AncestorsId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DescendantsId")
                        .HasColumnType("uuid");

                    b.HasKey("AncestorsId", "DescendantsId");

                    b.HasIndex("DescendantsId");

                    b.ToTable("CategoryCategory", "etyra_core");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Cart.CartItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasComment("Special notes for this item");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasComment("Product ID");

                    b.Property<string>("ProductImageUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("Product image URL for display");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Product name at the time of adding to cart");

                    b.Property<string>("ProductSku")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("Product SKU at the time of adding to cart");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasComment("Quantity of this item in cart");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("ShoppingCartId")
                        .HasColumnType("uuid")
                        .HasComment("Shopping cart ID this item belongs to");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("VariantInfo")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasComment("Product variant information (e.g., Size: L, Color: Red)");

                    b.HasKey("Id");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_cart_items_product_id");

                    b.HasIndex("ProductSku")
                        .HasDatabaseName("ix_cart_items_product_sku");

                    b.HasIndex("ShoppingCartId")
                        .HasDatabaseName("ix_cart_items_shopping_cart_id");

                    b.HasIndex("ShoppingCartId", "ProductId", "VariantInfo")
                        .IsUnique()
                        .HasDatabaseName("ix_cart_items_cart_product_variant");

                    b.ToTable("cart_items", "etyra_core", t =>
                        {
                            t.HasComment("Cart item entity representing an item in a shopping cart");

                            t.HasCheckConstraint("ck_cart_items_currencies_match", "unit_price_currency = total_price_currency");

                            t.HasCheckConstraint("ck_cart_items_quantity_positive", "\"Quantity\" > 0");

                            t.HasCheckConstraint("ck_cart_items_total_price_positive", "total_price_amount > 0");

                            t.HasCheckConstraint("ck_cart_items_unit_price_positive", "unit_price_amount > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Cart.ShoppingCart", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasComment("Customer ID who owns the cart (null for guest carts)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Cart expiration date");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasComment("Whether this cart is active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("SessionId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasComment("Session ID for guest carts");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_shopping_carts_customer_id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("ix_shopping_carts_expires_at");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_shopping_carts_is_active");

                    b.HasIndex("SessionId")
                        .HasDatabaseName("ix_shopping_carts_session_id");

                    b.HasIndex("CustomerId", "IsActive")
                        .HasDatabaseName("ix_shopping_carts_customer_active");

                    b.HasIndex("SessionId", "IsActive")
                        .HasDatabaseName("ix_shopping_carts_session_active");

                    b.ToTable("shopping_carts", "etyra_core", t =>
                        {
                            t.HasComment("Shopping cart entity for storing customer cart items");

                            t.HasCheckConstraint("ck_shopping_carts_customer_or_session", "(\"CustomerId\" IS NOT NULL AND \"SessionId\" IS NULL) OR (\"CustomerId\" IS NULL AND \"SessionId\" IS NOT NULL)");

                            t.HasCheckConstraint("ck_shopping_carts_expires_future", "\"ExpiresAt\" > \"CreatedAt\"");

                            t.HasCheckConstraint("ck_shopping_carts_subtotal_positive", "subtotal_amount >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Category.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Icon")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("icon");

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(350)
                        .HasColumnType("character varying(350)")
                        .HasColumnName("meta_description");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("meta_keywords");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(120)
                        .HasColumnType("character varying(120)")
                        .HasColumnName("meta_title");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentCategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_category_id");

                    b.Property<int>("ProductCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("product_count");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<bool>("ShowInMenu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("show_in_menu");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("slug");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_categories_created_at");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_categories_created_by");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("ix_categories_deleted_by");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_categories_is_active");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_categories_is_deleted");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_categories_name");

                    b.HasIndex("ParentCategoryId")
                        .HasDatabaseName("ix_categories_parent_category_id");

                    b.HasIndex("ShowInMenu")
                        .HasDatabaseName("ix_categories_show_in_menu");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("ix_categories_slug_unique");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("ix_categories_sort_order");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_categories_updated_by");

                    b.HasIndex("CreatedBy", "CreatedAt")
                        .HasDatabaseName("ix_categories_created_by_created_at");

                    b.HasIndex("IsActive", "ShowInMenu")
                        .HasDatabaseName("ix_categories_active_menu");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_categories_is_deleted_created_at");

                    b.HasIndex("ParentCategoryId", "IsActive")
                        .HasDatabaseName("ix_categories_parent_active");

                    b.HasIndex("ParentCategoryId", "SortOrder")
                        .HasDatabaseName("ix_categories_parent_sort");

                    b.HasIndex("UpdatedBy", "UpdatedAt")
                        .HasDatabaseName("ix_categories_updated_by_updated_at");

                    b.HasIndex("ParentCategoryId", "IsActive", "SortOrder")
                        .HasDatabaseName("ix_categories_hierarchy");

                    b.ToTable("categories", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_Categories_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");

                            t.HasCheckConstraint("CK_Categories_NoSelfReference", "parent_category_id != id");

                            t.HasCheckConstraint("CK_Categories_ProductCount_NonNegative", "product_count >= 0");

                            t.HasCheckConstraint("CK_Categories_Slug_NotEmpty", "LENGTH(TRIM(slug)) > 0");

                            t.HasCheckConstraint("CK_Categories_SortOrder_NonNegative", "sort_order >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Category.CategoryDescription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("en-US")
                        .HasColumnName("language_code");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(350)
                        .HasColumnType("character varying(350)")
                        .HasColumnName("meta_description");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("meta_keywords");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(120)
                        .HasColumnType("character varying(120)")
                        .HasColumnName("meta_title");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("Slug")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("slug");

                    b.Property<Guid?>("StoreId")
                        .HasColumnType("uuid")
                        .HasColumnName("store_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_category_descriptions_category_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_category_descriptions_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_category_descriptions_is_deleted");

                    b.HasIndex("LanguageCode")
                        .HasDatabaseName("ix_category_descriptions_language_code");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_category_descriptions_name");

                    b.HasIndex("Slug")
                        .HasDatabaseName("ix_category_descriptions_slug");

                    b.HasIndex("StoreId")
                        .HasDatabaseName("ix_category_descriptions_store_id");

                    b.HasIndex("CategoryId", "LanguageCode")
                        .HasDatabaseName("ix_category_descriptions_category_language");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_category_descriptions_is_deleted_created_at");

                    b.HasIndex("LanguageCode", "StoreId")
                        .HasDatabaseName("ix_category_descriptions_language_store");

                    b.HasIndex("CategoryId", "LanguageCode", "StoreId")
                        .IsUnique()
                        .HasDatabaseName("ix_category_descriptions_category_language_store_unique");

                    b.ToTable("category_descriptions", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_CategoryDescriptions_LanguageCode_NotEmpty", "LENGTH(TRIM(language_code)) > 0");

                            t.HasCheckConstraint("CK_CategoryDescriptions_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Inventory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AllocatedQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("AvailableQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastPhysicalCount")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastStockUpdate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LeadTimeDays")
                        .HasColumnType("integer");

                    b.Property<string>("LocationCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("MaxStockLevel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("MinStockLevel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<int>("ReorderPoint")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("ReorderQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("ReservedQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("SupplierReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("WarehouseId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AvailableQuantity")
                        .HasDatabaseName("IX_Inventories_AvailableQuantity");

                    b.HasIndex("LastStockUpdate")
                        .HasDatabaseName("IX_Inventories_LastStockUpdate");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Inventories_ProductId");

                    b.HasIndex("ReorderPoint")
                        .HasDatabaseName("IX_Inventories_ReorderPoint");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Inventories_Status");

                    b.HasIndex("WarehouseId")
                        .HasDatabaseName("IX_Inventories_WarehouseId");

                    b.HasIndex("ProductId", "WarehouseId")
                        .IsUnique()
                        .HasDatabaseName("IX_Inventories_ProductId_WarehouseId");

                    b.ToTable("Inventories", "etyra_inventory");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.InventoryTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExternalReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("InventoryId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<int>("QuantityAfter")
                        .HasColumnType("integer");

                    b.Property<int>("QuantityBefore")
                        .HasColumnType("integer");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Reference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ReferenceType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<decimal?>("TotalCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("TransactionDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<decimal?>("UnitCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InventoryId")
                        .HasDatabaseName("IX_InventoryTransactions_InventoryId");

                    b.HasIndex("Reference")
                        .HasDatabaseName("IX_InventoryTransactions_Reference");

                    b.HasIndex("ReferenceType")
                        .HasDatabaseName("IX_InventoryTransactions_ReferenceType");

                    b.HasIndex("TransactionDate")
                        .HasDatabaseName("IX_InventoryTransactions_TransactionDate");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_InventoryTransactions_Type");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_InventoryTransactions_UserId");

                    b.HasIndex("Reference", "Type")
                        .HasDatabaseName("IX_InventoryTransactions_Reference_Type");

                    b.ToTable("InventoryTransactions", "etyra_inventory");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Warehouse", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsMain")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("ManagerName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Warehouses_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Warehouses_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Warehouses_Name");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_Warehouses_Type");

                    b.ToTable("Warehouses", "etyra_inventory");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Order.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("actual_delivery_date");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("customer_email");

                    b.Property<string>("CustomerFirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("customer_first_name");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<string>("CustomerLastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("customer_last_name");

                    b.Property<string>("CustomerPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("customer_phone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<DateTime?>("ExpectedDeliveryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expected_delivery_date");

                    b.Property<string>("InternalNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("internal_notes");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("order_number");

                    b.Property<Guid?>("PaymentMethodId")
                        .HasColumnType("uuid")
                        .HasColumnName("payment_method_id");

                    b.Property<string>("PaymentMethodName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("payment_method_name");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer")
                        .HasColumnName("payment_status");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("ShippingMethod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("shipping_method");

                    b.Property<int>("ShippingStatus")
                        .HasColumnType("integer")
                        .HasColumnName("shipping_status");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("TrackingNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("tracking_number");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_orders_created_at");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_order_created_by");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_orders_customer_id");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("ix_order_deleted_by");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_order_is_deleted");

                    b.HasIndex("OrderNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_orders_order_number");

                    b.HasIndex("PaymentMethodId")
                        .HasDatabaseName("ix_orders_payment_method_id");

                    b.HasIndex("PaymentStatus")
                        .HasDatabaseName("ix_orders_payment_status");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_orders_status");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_order_updated_by");

                    b.HasIndex("CreatedBy", "CreatedAt")
                        .HasDatabaseName("ix_order_created_by_created_at");

                    b.HasIndex("CustomerId", "Status")
                        .HasDatabaseName("ix_orders_customer_status");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_order_is_deleted_created_at");

                    b.HasIndex("Status", "CreatedAt")
                        .HasDatabaseName("ix_orders_status_created");

                    b.HasIndex("UpdatedBy", "UpdatedAt")
                        .HasDatabaseName("ix_order_updated_by_updated_at");

                    b.ToTable("orders", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("ck_orders_discount_positive", "discount_amount >= 0");

                            t.HasCheckConstraint("ck_orders_payment_status_valid", "payment_status BETWEEN 0 AND 6");

                            t.HasCheckConstraint("ck_orders_shipping_positive", "shipping_cost_amount >= 0");

                            t.HasCheckConstraint("ck_orders_shipping_status_valid", "shipping_status BETWEEN 0 AND 6");

                            t.HasCheckConstraint("ck_orders_status_valid", "status BETWEEN 0 AND 9");

                            t.HasCheckConstraint("ck_orders_subtotal_positive", "subtotal_amount >= 0");

                            t.HasCheckConstraint("ck_orders_tax_positive", "tax_amount >= 0");

                            t.HasCheckConstraint("ck_orders_total_positive", "total_amount >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Order.OrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("product_name");

                    b.Property<string>("ProductSku")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("product_sku");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("special_instructions");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("VariantInfo")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("variant_info");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_orderitem_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_orderitem_is_deleted");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_order_items_order_id");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_order_items_product_id");

                    b.HasIndex("ProductSku")
                        .HasDatabaseName("ix_order_items_product_sku");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_orderitem_is_deleted_created_at");

                    b.HasIndex("OrderId", "ProductId")
                        .HasDatabaseName("ix_order_items_order_product");

                    b.ToTable("order_items", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("ck_order_items_discount_positive", "discount_amount IS NULL OR discount_amount >= 0");

                            t.HasCheckConstraint("ck_order_items_quantity_positive", "quantity > 0");

                            t.HasCheckConstraint("ck_order_items_tax_positive", "tax_amount IS NULL OR tax_amount >= 0");

                            t.HasCheckConstraint("ck_order_items_total_price_positive", "total_price_amount > 0");

                            t.HasCheckConstraint("ck_order_items_unit_price_positive", "unit_price_amount > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Payment.PaymentMethod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("display_order");

                    b.Property<int>("FeeCalculationType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("fee_calculation_type");

                    b.Property<decimal>("FeeValue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValue(0m)
                        .HasColumnName("fee_value");

                    b.Property<string>("Instructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("instructions");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_payment_methods_code");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("ix_payment_methods_display_order");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_payment_methods_is_active");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_payment_methods_type");

                    b.ToTable("payment_methods", "etyra_core", t =>
                        {
                            t.HasComment("Payment methods available for orders");

                            t.HasCheckConstraint("ck_payment_methods_code_not_empty", "LENGTH(TRIM(code)) > 0");

                            t.HasCheckConstraint("ck_payment_methods_display_order_valid", "display_order >= 0");

                            t.HasCheckConstraint("ck_payment_methods_fee_value_valid", "fee_value >= -999999.9999 AND fee_value <= 999999.9999");

                            t.HasCheckConstraint("ck_payment_methods_minimum_maximum_order", "minimum_order_amount IS NULL OR maximum_order_amount IS NULL OR minimum_order_amount <= maximum_order_amount");

                            t.HasCheckConstraint("ck_payment_methods_name_not_empty", "LENGTH(TRIM(name)) > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int?>("AiPlatformId")
                        .HasColumnType("integer")
                        .HasColumnName("ai_platform_id");

                    b.Property<string>("Barcode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("barcode");

                    b.Property<string>("Brand")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("brand");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("DefaultCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("default_currency");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Dimensions")
                        .HasColumnType("jsonb")
                        .HasColumnName("dimensions");

                    b.Property<DateTime?>("DiscontinueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("discontinue_date");

                    b.Property<string>("EAN")
                        .HasMaxLength(13)
                        .HasColumnType("character varying(13)")
                        .HasColumnName("ean");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsDigital")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_digital");

                    b.Property<bool>("IsFeatured")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_featured");

                    b.Property<DateTime?>("LaunchDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("launch_date");

                    b.Property<string>("MPN")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)")
                        .HasColumnName("mpn");

                    b.Property<string>("MainImage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("main_image");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(350)
                        .HasColumnType("character varying(350)")
                        .HasColumnName("meta_description");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("meta_keywords");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(120)
                        .HasColumnType("character varying(120)")
                        .HasColumnName("meta_title");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("model");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<DateTime?>("SaleEndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("sale_end_date");

                    b.Property<DateTime?>("SaleStartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("sale_start_date");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("slug");

                    b.Property<int?>("Stars")
                        .HasColumnType("integer")
                        .HasColumnName("stars");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("tags");

                    b.Property<string>("ThumbnailImage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("thumbnail_image");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<string>("UPC")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)")
                        .HasColumnName("upc");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("Barcode")
                        .HasDatabaseName("ix_products_barcode");

                    b.HasIndex("Brand")
                        .HasDatabaseName("ix_products_brand");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_products_created_at");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_products_created_by");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("ix_products_deleted_by");

                    b.HasIndex("EAN")
                        .HasDatabaseName("ix_products_ean");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_products_is_deleted");

                    b.HasIndex("IsFeatured")
                        .HasDatabaseName("ix_products_is_featured");

                    b.HasIndex("Model")
                        .HasDatabaseName("ix_products_model");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_products_name");

                    b.HasIndex("SKU")
                        .IsUnique()
                        .HasDatabaseName("ix_products_sku_unique");

                    b.HasIndex("SaleEndDate")
                        .HasDatabaseName("ix_products_sale_end_date");

                    b.HasIndex("SaleStartDate")
                        .HasDatabaseName("ix_products_sale_start_date");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("ix_products_slug_unique");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_products_status");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_products_type");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_products_updated_by");

                    b.HasIndex("Brand", "Status")
                        .HasDatabaseName("ix_products_brand_status");

                    b.HasIndex("CreatedBy", "CreatedAt")
                        .HasDatabaseName("ix_products_created_by_created_at");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_products_is_deleted_created_at");

                    b.HasIndex("IsFeatured", "Status")
                        .HasDatabaseName("ix_products_featured_status");

                    b.HasIndex("Status", "Type")
                        .HasDatabaseName("ix_products_status_type");

                    b.HasIndex("UpdatedBy", "UpdatedAt")
                        .HasDatabaseName("ix_products_updated_by_updated_at");

                    b.ToTable("products", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_Products_MinStock_NonNegative", "min_stock_alert >= 0");

                            t.HasCheckConstraint("CK_Products_Model_NotEmpty", "LENGTH(TRIM(model)) > 0");

                            t.HasCheckConstraint("CK_Products_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");

                            t.HasCheckConstraint("CK_Products_SKU_NotEmpty", "LENGTH(TRIM(sku)) > 0");

                            t.HasCheckConstraint("CK_Products_SaleDates", "sale_start_date IS NULL OR sale_end_date IS NULL OR sale_start_date <= sale_end_date");

                            t.HasCheckConstraint("CK_Products_Stars_Range", "stars IS NULL OR (stars >= 1 AND stars <= 5)");

                            t.HasCheckConstraint("CK_Products_Stock_NonNegative", "total_stock_quantity >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Group")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFilterable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSearchable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductAttribute", "etyra_core");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsPrimary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_primary");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_product_categories_category_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_product_categories_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_product_categories_is_deleted");

                    b.HasIndex("IsPrimary")
                        .HasDatabaseName("ix_product_categories_is_primary");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_categories_product_id");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("ix_product_categories_sort_order");

                    b.HasIndex("CategoryId", "IsPrimary")
                        .HasDatabaseName("ix_product_categories_category_primary");

                    b.HasIndex("CategoryId", "SortOrder")
                        .HasDatabaseName("ix_product_categories_category_sort");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_product_categories_is_deleted_created_at");

                    b.HasIndex("ProductId", "CategoryId")
                        .IsUnique()
                        .HasDatabaseName("ix_product_categories_product_category_unique");

                    b.HasIndex("ProductId", "IsPrimary")
                        .HasDatabaseName("ix_product_categories_product_primary");

                    b.ToTable("product_categories", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_ProductCategories_SortOrder_NonNegative", "sort_order >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductDescription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("en-US")
                        .HasColumnName("language_code");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(350)
                        .HasColumnType("character varying(350)")
                        .HasColumnName("meta_description");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("meta_keywords");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(120)
                        .HasColumnType("character varying(120)")
                        .HasColumnName("meta_title");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("ShortDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("short_description");

                    b.Property<string>("Slug")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("slug");

                    b.Property<Guid?>("StoreId")
                        .HasColumnType("uuid")
                        .HasColumnName("store_id");

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("tags");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_product_descriptions_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_product_descriptions_is_deleted");

                    b.HasIndex("LanguageCode")
                        .HasDatabaseName("ix_product_descriptions_language_code");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_product_descriptions_name");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_descriptions_product_id");

                    b.HasIndex("Slug")
                        .HasDatabaseName("ix_product_descriptions_slug");

                    b.HasIndex("StoreId")
                        .HasDatabaseName("ix_product_descriptions_store_id");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_product_descriptions_is_deleted_created_at");

                    b.HasIndex("LanguageCode", "StoreId")
                        .HasDatabaseName("ix_product_descriptions_language_store");

                    b.HasIndex("ProductId", "LanguageCode", "StoreId")
                        .IsUnique()
                        .HasDatabaseName("ix_product_descriptions_product_language_store_unique");

                    b.ToTable("product_descriptions", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_ProductDescriptions_Description_NotEmpty", "LENGTH(TRIM(description)) > 0");

                            t.HasCheckConstraint("CK_ProductDescriptions_LanguageCode_NotEmpty", "LENGTH(TRIM(language_code)) > 0");

                            t.HasCheckConstraint("CK_ProductDescriptions_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductDiscount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("CanCombine")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("can_combine");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("CurrentUses")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("current_uses");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int?>("MaxQuantity")
                        .HasColumnType("integer")
                        .HasColumnName("max_quantity");

                    b.Property<int?>("MaxUses")
                        .HasColumnType("integer")
                        .HasColumnName("max_uses");

                    b.Property<int?>("MinQuantity")
                        .HasColumnType("integer")
                        .HasColumnName("min_quantity");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("priority");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_product_discounts_created_at");

                    b.HasIndex("EndDate")
                        .HasDatabaseName("ix_product_discounts_end_date");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_product_discounts_is_active");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_product_discounts_is_deleted");

                    b.HasIndex("Priority")
                        .HasDatabaseName("ix_product_discounts_priority");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_discounts_product_id");

                    b.HasIndex("StartDate")
                        .HasDatabaseName("ix_product_discounts_start_date");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_product_discounts_type");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_product_discounts_is_deleted_created_at");

                    b.HasIndex("ProductId", "IsActive")
                        .HasDatabaseName("ix_product_discounts_product_active");

                    b.HasIndex("ProductId", "Priority")
                        .HasDatabaseName("ix_product_discounts_product_priority");

                    b.HasIndex("IsActive", "StartDate", "EndDate")
                        .HasDatabaseName("ix_product_discounts_active_dates");

                    b.HasIndex("ProductId", "IsActive", "Priority")
                        .HasDatabaseName("ix_product_discounts_product_active_priority");

                    b.HasIndex("StartDate", "EndDate", "IsActive")
                        .HasDatabaseName("ix_product_discounts_date_range_active");

                    b.ToTable("product_discounts", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_ProductDiscounts_CurrentUses_NonNegative", "current_uses >= 0");

                            t.HasCheckConstraint("CK_ProductDiscounts_Date_Range", "start_date IS NULL OR end_date IS NULL OR start_date <= end_date");

                            t.HasCheckConstraint("CK_ProductDiscounts_MaxQuantity_Positive", "max_quantity IS NULL OR max_quantity > 0");

                            t.HasCheckConstraint("CK_ProductDiscounts_MaxUses_Positive", "max_uses IS NULL OR max_uses > 0");

                            t.HasCheckConstraint("CK_ProductDiscounts_MinQuantity_Positive", "min_quantity IS NULL OR min_quantity > 0");

                            t.HasCheckConstraint("CK_ProductDiscounts_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");

                            t.HasCheckConstraint("CK_ProductDiscounts_Percentage_Range", "type != 0 OR value <= 100");

                            t.HasCheckConstraint("CK_ProductDiscounts_Quantity_Range", "min_quantity IS NULL OR max_quantity IS NULL OR min_quantity <= max_quantity");

                            t.HasCheckConstraint("CK_ProductDiscounts_Uses_Range", "max_uses IS NULL OR current_uses <= max_uses");

                            t.HasCheckConstraint("CK_ProductDiscounts_Value_Positive", "value > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductImage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AltText")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("alt_text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size");

                    b.Property<int?>("Height")
                        .HasColumnType("integer")
                        .HasColumnName("height");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsMain")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_main");

                    b.Property<string>("MimeType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("mime_type");

                    b.Property<string>("OriginalFileName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("original_file_name");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("ThumbnailUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("thumbnail_url");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("Width")
                        .HasColumnType("integer")
                        .HasColumnName("width");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_product_images_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_product_images_is_deleted");

                    b.HasIndex("IsMain")
                        .HasDatabaseName("ix_product_images_is_main");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_images_product_id");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("ix_product_images_sort_order");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_product_images_type");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_product_images_is_deleted_created_at");

                    b.HasIndex("ProductId", "IsMain")
                        .IsUnique()
                        .HasDatabaseName("ix_product_images_product_main_unique")
                        .HasFilter("is_main = true");

                    b.HasIndex("ProductId", "SortOrder")
                        .HasDatabaseName("ix_product_images_product_sort");

                    b.HasIndex("ProductId", "Type")
                        .HasDatabaseName("ix_product_images_product_type");

                    b.HasIndex("ProductId", "Type", "SortOrder")
                        .HasDatabaseName("ix_product_images_product_type_sort");

                    b.ToTable("product_images", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_ProductImages_Dimensions_Positive", "width IS NULL OR width > 0");

                            t.HasCheckConstraint("CK_ProductImages_FileSize_Positive", "file_size IS NULL OR file_size > 0");

                            t.HasCheckConstraint("CK_ProductImages_Height_Positive", "height IS NULL OR height > 0");

                            t.HasCheckConstraint("CK_ProductImages_ImageUrl_NotEmpty", "LENGTH(TRIM(image_url)) > 0");

                            t.HasCheckConstraint("CK_ProductImages_SortOrder_NonNegative", "sort_order >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductVariant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Barcode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("barcode");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_default");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("ManageStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("manage_stock");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sku");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("StockQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("stock_quantity");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(10,3)")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_product_variants_is_active");

                    b.HasIndex("IsDefault")
                        .HasDatabaseName("ix_product_variants_is_default");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_variants_product_id");

                    b.HasIndex("SKU")
                        .IsUnique()
                        .HasDatabaseName("ix_product_variants_sku_unique");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("ix_product_variants_sort_order");

                    b.HasIndex("ProductId", "IsActive")
                        .HasDatabaseName("ix_product_variants_product_active");

                    b.HasIndex("ProductId", "IsDefault")
                        .HasDatabaseName("ix_product_variants_product_default");

                    b.ToTable("product_variants", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("ck_product_variants_cost_amount", "cost_amount IS NULL OR cost_amount >= 0");

                            t.HasCheckConstraint("ck_product_variants_price_amount", "price_amount IS NULL OR price_amount >= 0");

                            t.HasCheckConstraint("ck_product_variants_sort_order", "sort_order >= 0");

                            t.HasCheckConstraint("ck_product_variants_stock_quantity", "stock_quantity >= 0");

                            t.HasCheckConstraint("ck_product_variants_weight", "weight IS NULL OR weight >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductVariantAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ProductVariantId")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProductVariantId");

                    b.ToTable("ProductVariantAttribute", "etyra_core");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.WarehouseProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("AllowBackorders")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("allow_backorders");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsPrimaryWarehouse")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_primary_warehouse");

                    b.Property<DateTime?>("LastStockCount")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_stock_count");

                    b.Property<DateTime?>("LastStockUpdate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_stock_update");

                    b.Property<int?>("LeadTimeDays")
                        .HasColumnType("integer")
                        .HasColumnName("lead_time_days");

                    b.Property<string>("LocationCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("location_code");

                    b.Property<bool>("ManageStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("manage_stock");

                    b.Property<int?>("MaxStockCapacity")
                        .HasColumnType("integer")
                        .HasColumnName("max_stock_capacity");

                    b.Property<int>("MinStockAlert")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("min_stock_alert");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<int>("ReservedStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("reserved_stock");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("StockQuantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("stock_quantity");

                    b.Property<string>("SupplierReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("supplier_reference");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("WarehouseId")
                        .HasColumnType("uuid")
                        .HasColumnName("warehouse_id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_warehouse_products_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_warehouse_products_is_deleted");

                    b.HasIndex("IsPrimaryWarehouse")
                        .HasDatabaseName("ix_warehouse_products_is_primary");

                    b.HasIndex("LocationCode")
                        .HasDatabaseName("ix_warehouse_products_location_code");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_warehouse_products_product_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_warehouse_products_status");

                    b.HasIndex("StockQuantity")
                        .HasDatabaseName("ix_warehouse_products_stock_quantity");

                    b.HasIndex("WarehouseId")
                        .HasDatabaseName("ix_warehouse_products_warehouse_id");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_warehouse_products_is_deleted_created_at");

                    b.HasIndex("ProductId", "IsPrimaryWarehouse")
                        .IsUnique()
                        .HasDatabaseName("ix_warehouse_products_product_primary_unique")
                        .HasFilter("is_primary_warehouse = true");

                    b.HasIndex("ProductId", "Status")
                        .HasDatabaseName("ix_warehouse_products_product_status");

                    b.HasIndex("ProductId", "WarehouseId")
                        .IsUnique()
                        .HasDatabaseName("ix_warehouse_products_product_warehouse_unique");

                    b.HasIndex("StockQuantity", "MinStockAlert")
                        .HasDatabaseName("ix_warehouse_products_stock_alert");

                    b.HasIndex("WarehouseId", "Status")
                        .HasDatabaseName("ix_warehouse_products_warehouse_status");

                    b.HasIndex("WarehouseId", "StockQuantity")
                        .HasDatabaseName("ix_warehouse_products_warehouse_stock");

                    b.HasIndex("ProductId", "StockQuantity", "ReservedStock")
                        .HasDatabaseName("ix_warehouse_products_product_stock_reserved");

                    b.ToTable("warehouse_products", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_WarehouseProducts_LeadTimeDays_NonNegative", "lead_time_days IS NULL OR lead_time_days >= 0");

                            t.HasCheckConstraint("CK_WarehouseProducts_MaxStockCapacity_Positive", "max_stock_capacity IS NULL OR max_stock_capacity > 0");

                            t.HasCheckConstraint("CK_WarehouseProducts_MinStockAlert_NonNegative", "min_stock_alert >= 0");

                            t.HasCheckConstraint("CK_WarehouseProducts_ReservedStock_NonNegative", "reserved_stock >= 0");

                            t.HasCheckConstraint("CK_WarehouseProducts_ReservedStock_Range", "reserved_stock <= stock_quantity");

                            t.HasCheckConstraint("CK_WarehouseProducts_StockCapacity_Range", "max_stock_capacity IS NULL OR stock_quantity <= max_stock_capacity");

                            t.HasCheckConstraint("CK_WarehouseProducts_StockQuantity_NonNegative", "stock_quantity >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.City", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMajorCity")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShippingEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 7)
                        .HasColumnType("numeric(10,7)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 7)
                        .HasColumnType("numeric(10,7)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("Population")
                        .HasColumnType("integer");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("ShippingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_Cities_DisplayOrder");

                    b.HasIndex("IsShippingEnabled")
                        .HasDatabaseName("IX_Cities_IsShippingEnabled");

                    b.HasIndex("PostalCode")
                        .HasDatabaseName("IX_Cities_PostalCode");

                    b.HasIndex("RegionId")
                        .HasDatabaseName("IX_Cities_RegionId");

                    b.HasIndex("Name", "RegionId")
                        .IsUnique()
                        .HasDatabaseName("IX_Cities_Name_Region_Unique");

                    b.ToTable("Cities", "etyra_shipping", t =>
                        {
                            t.HasComment("Cities within regions for detailed shipping address management");

                            t.HasCheckConstraint("CK_Cities_DisplayOrder", "\"DisplayOrder\" >= 0");

                            t.HasCheckConstraint("CK_Cities_Latitude", "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)");

                            t.HasCheckConstraint("CK_Cities_Longitude", "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEuMember")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsShippingEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("IsoCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PhoneCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("ShippingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Countries_Code_Unique");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_Countries_DisplayOrder");

                    b.HasIndex("IsEuMember")
                        .HasDatabaseName("IX_Countries_IsEuMember");

                    b.HasIndex("IsShippingEnabled")
                        .HasDatabaseName("IX_Countries_IsShippingEnabled");

                    b.HasIndex("IsoCode")
                        .IsUnique()
                        .HasDatabaseName("IX_Countries_IsoCode_Unique");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Countries_Name_Unique");

                    b.ToTable("Countries", "etyra_shipping", t =>
                        {
                            t.HasComment("Countries available for shipping in the European market");

                            t.HasCheckConstraint("CK_Countries_Code_Length", "LENGTH(\"Code\") = 2");

                            t.HasCheckConstraint("CK_Countries_CurrencyCode_Length", "LENGTH(\"CurrencyCode\") = 3");

                            t.HasCheckConstraint("CK_Countries_DisplayOrder", "\"DisplayOrder\" >= 0");

                            t.HasCheckConstraint("CK_Countries_IsoCode_Length", "LENGTH(\"IsoCode\") = 3");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.District", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsCentralDistrict")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShippingEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 7)
                        .HasColumnType("numeric(10,7)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 7)
                        .HasColumnType("numeric(10,7)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("ShippingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CityId")
                        .HasDatabaseName("IX_Districts_CityId");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_Districts_DisplayOrder");

                    b.HasIndex("IsShippingEnabled")
                        .HasDatabaseName("IX_Districts_IsShippingEnabled");

                    b.HasIndex("PostalCode")
                        .HasDatabaseName("IX_Districts_PostalCode");

                    b.HasIndex("Name", "CityId")
                        .IsUnique()
                        .HasDatabaseName("IX_Districts_Name_City_Unique");

                    b.ToTable("Districts", "etyra_shipping", t =>
                        {
                            t.HasComment("Districts within cities for precise shipping address management");

                            t.HasCheckConstraint("CK_Districts_DisplayOrder", "\"DisplayOrder\" >= 0");

                            t.HasCheckConstraint("CK_Districts_Latitude", "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)");

                            t.HasCheckConstraint("CK_Districts_Longitude", "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShippingEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("ShippingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_Regions_Code");

                    b.HasIndex("CountryId")
                        .HasDatabaseName("IX_Regions_CountryId");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_Regions_DisplayOrder");

                    b.HasIndex("IsShippingEnabled")
                        .HasDatabaseName("IX_Regions_IsShippingEnabled");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_Regions_Type");

                    b.HasIndex("Name", "CountryId")
                        .IsUnique()
                        .HasDatabaseName("IX_Regions_Name_Country_Unique");

                    b.ToTable("Regions", "etyra_shipping", t =>
                        {
                            t.HasComment("Administrative regions within countries (states, provinces, counties, etc.)");

                            t.HasCheckConstraint("CK_Regions_DisplayOrder", "\"DisplayOrder\" >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingMethod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiConfiguration")
                        .HasColumnType("text");

                    b.Property<string>("ApiCredentials")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("CarrierCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CarrierName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("HasInsurance")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("HasTracking")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxHeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxLength")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWidth")
                        .HasColumnType("numeric");

                    b.Property<int>("MinDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("ShippingZoneId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SupportsCashOnDelivery")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CarrierName")
                        .HasDatabaseName("IX_ShippingMethods_CarrierName");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_ShippingMethods_DisplayOrder");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ShippingMethods_IsActive");

                    b.HasIndex("ShippingZoneId")
                        .HasDatabaseName("IX_ShippingMethods_ShippingZoneId");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_ShippingMethods_Type");

                    b.HasIndex("Name", "ShippingZoneId")
                        .IsUnique()
                        .HasDatabaseName("IX_ShippingMethods_Name_Zone_Unique");

                    b.ToTable("ShippingMethods", "etyra_shipping", t =>
                        {
                            t.HasComment("Shipping methods available for each shipping zone with carrier information");

                            t.HasCheckConstraint("CK_ShippingMethods_DisplayOrder", "\"DisplayOrder\" >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingRate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CalculationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveUntil")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal>("MaxWeight")
                        .HasPrecision(10, 3)
                        .HasColumnType("numeric(10,3)");

                    b.Property<decimal>("MinWeight")
                        .HasPrecision(10, 3)
                        .HasColumnType("numeric(10,3)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("ShippingMethodId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ShippingZoneId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_ShippingRates_DisplayOrder");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ShippingRates_IsActive");

                    b.HasIndex("ShippingMethodId")
                        .HasDatabaseName("IX_ShippingRates_ShippingMethodId");

                    b.HasIndex("ShippingZoneId");

                    b.HasIndex("EffectiveFrom", "EffectiveUntil")
                        .HasDatabaseName("IX_ShippingRates_EffectivePeriod");

                    b.ToTable("ShippingRates", "etyra_shipping", t =>
                        {
                            t.HasComment("Shipping rate configurations for different shipping methods and conditions");

                            t.HasCheckConstraint("CK_ShippingRates_AdditionalCostPerKgAmount", "\"AdditionalCostPerKgAmount\" IS NULL OR \"AdditionalCostPerKgAmount\" >= 0");

                            t.HasCheckConstraint("CK_ShippingRates_BaseCostAmount", "\"BaseCostAmount\" >= 0");

                            t.HasCheckConstraint("CK_ShippingRates_DisplayOrder", "\"DisplayOrder\" >= 0");

                            t.HasCheckConstraint("CK_ShippingRates_EffectivePeriod", "\"EffectiveUntil\" IS NULL OR \"EffectiveFrom\" <= \"EffectiveUntil\"");

                            t.HasCheckConstraint("CK_ShippingRates_WeightRange", "\"MinWeight\" <= \"MaxWeight\"");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DefaultMaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<int>("DefaultMinDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_ShippingZones_DisplayOrder");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ShippingZones_IsActive");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_ShippingZones_Name_Unique");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_ShippingZones_Type");

                    b.ToTable("ShippingZones", "etyra_shipping", t =>
                        {
                            t.HasComment("Shipping zones for organizing countries and regions by delivery characteristics");

                            t.HasCheckConstraint("CK_ShippingZones_DeliveryDays", "\"DefaultMinDeliveryDays\" > 0 AND \"DefaultMaxDeliveryDays\" > 0 AND \"DefaultMinDeliveryDays\" <= \"DefaultMaxDeliveryDays\"");

                            t.HasCheckConstraint("CK_ShippingZones_DisplayOrder", "\"DisplayOrder\" >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZoneCountry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("OverrideMaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<int?>("OverrideMinDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("ShippingZoneId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CountryId")
                        .HasDatabaseName("IX_ShippingZoneCountries_CountryId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ShippingZoneCountries_IsActive");

                    b.HasIndex("ShippingZoneId")
                        .HasDatabaseName("IX_ShippingZoneCountries_ShippingZoneId");

                    b.HasIndex("ShippingZoneId", "CountryId")
                        .IsUnique()
                        .HasDatabaseName("IX_ShippingZoneCountries_Zone_Country_Unique");

                    b.ToTable("ShippingZoneCountries", "etyra_shipping", t =>
                        {
                            t.HasComment("Mapping between shipping zones and countries with cost modifiers");

                            t.HasCheckConstraint("CK_ShippingZoneCountries_DisplayOrder", "\"DisplayOrder\" >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZoneLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CityId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("CostModifierFixed")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CostModifierPercentage")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid?>("DistrictId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LocationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("OverrideMaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<int?>("OverrideMinDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<Guid?>("RegionId")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("ShippingZoneId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CityId");

                    b.HasIndex("CountryId")
                        .HasDatabaseName("IX_ShippingZoneLocations_CountryId");

                    b.HasIndex("DistrictId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ShippingZoneLocations_IsActive");

                    b.HasIndex("LocationType")
                        .HasDatabaseName("IX_ShippingZoneLocations_LocationType");

                    b.HasIndex("RegionId");

                    b.HasIndex("ShippingZoneId")
                        .HasDatabaseName("IX_ShippingZoneLocations_ShippingZoneId");

                    b.HasIndex("ShippingZoneId", "CountryId", "RegionId", "CityId", "DistrictId", "LocationType")
                        .IsUnique()
                        .HasDatabaseName("IX_ShippingZoneLocations_Zone_Location_Type_Unique");

                    b.ToTable("ShippingZoneLocations", "etyra_shipping", t =>
                        {
                            t.HasComment("Mapping between shipping zones and specific locations (regions, cities, districts) with cost modifiers");

                            t.HasCheckConstraint("CK_ShippingZoneLocations_CostModifierFixed", "\"CostModifierFixed\" IS NULL OR (\"CostModifierFixed\" >= -1000000 AND \"CostModifierFixed\" <= 1000000)");

                            t.HasCheckConstraint("CK_ShippingZoneLocations_CostModifierPercentage", "\"CostModifierPercentage\" IS NULL OR (\"CostModifierPercentage\" >= -100 AND \"CostModifierPercentage\" <= 1000)");

                            t.HasCheckConstraint("CK_ShippingZoneLocations_DisplayOrder", "\"DisplayOrder\" >= 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.User.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Culture")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("en-US")
                        .HasColumnName("culture");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("email");

                    b.Property<string>("EmailConfirmationToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("email_confirmation_token");

                    b.Property<DateTime?>("EmailConfirmationTokenExpiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FailedLoginAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("failed_login_attempts");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsEmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_email_confirmed");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPhoneConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_phone_confirmed");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_at");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name");

                    b.Property<DateTime?>("LockedUntil")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("locked_until");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<DateTime?>("PasswordChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("password_hash");

                    b.Property<string>("PasswordResetToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("password_reset_token");

                    b.Property<DateTime?>("PasswordResetTokenExpiry")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("password_reset_token_expiry");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("password_salt");

                    b.Property<string>("PhoneConfirmationToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PhoneConfirmationTokenExpiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("phone_number");

                    b.Property<string>("ProfilePictureUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("profile_picture_url");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("refresh_token");

                    b.Property<DateTime?>("RefreshTokenCreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("refresh_token_created_at");

                    b.Property<string>("RefreshTokenCreatedByIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("refresh_token_created_by_ip");

                    b.Property<DateTime?>("RefreshTokenExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("refresh_token_expires_at");

                    b.Property<DateTime?>("RefreshTokenRevokedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("refresh_token_revoked_at");

                    b.Property<string>("RefreshTokenRevokedByIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("refresh_token_revoked_by_ip");

                    b.Property<string>("RefreshTokenRevokedReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("refresh_token_revoked_reason");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("UTC")
                        .HasColumnName("time_zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_users_created_at");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_users_created_by");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("ix_users_deleted_by");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_users_email_unique");

                    b.HasIndex("EmailConfirmationToken")
                        .HasDatabaseName("ix_users_email_confirmation_token");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_users_is_deleted");

                    b.HasIndex("IsEmailConfirmed")
                        .HasDatabaseName("ix_users_is_email_confirmed");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("ix_users_is_enabled");

                    b.HasIndex("LastLoginAt")
                        .HasDatabaseName("ix_users_last_login_at");

                    b.HasIndex("PasswordResetToken")
                        .HasDatabaseName("ix_users_password_reset_token");

                    b.HasIndex("RefreshToken")
                        .HasDatabaseName("ix_users_refresh_token");

                    b.HasIndex("RefreshTokenExpiresAt")
                        .HasDatabaseName("ix_users_refresh_token_expires_at");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_users_updated_by");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("ix_users_username_unique");

                    b.HasIndex("CreatedBy", "CreatedAt")
                        .HasDatabaseName("ix_users_created_by_created_at");

                    b.HasIndex("FirstName", "LastName")
                        .HasDatabaseName("ix_users_full_name");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_users_is_deleted_created_at");

                    b.HasIndex("IsEnabled", "IsEmailConfirmed")
                        .HasDatabaseName("ix_users_enabled_email_confirmed");

                    b.HasIndex("UpdatedBy", "UpdatedAt")
                        .HasDatabaseName("ix_users_updated_by_updated_at");

                    b.ToTable("users", "etyra_core", t =>
                        {
                            t.HasCheckConstraint("CK_Users_FailedLoginAttempts_NonNegative", "failed_login_attempts >= 0");

                            t.HasCheckConstraint("CK_Users_FirstName_NotEmpty", "LENGTH(TRIM(first_name)) > 0");

                            t.HasCheckConstraint("CK_Users_LastName_NotEmpty", "LENGTH(TRIM(last_name)) > 0");

                            t.HasCheckConstraint("CK_Users_Username_NotEmpty", "LENGTH(TRIM(username)) > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.User.UserAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("CompanyTitle")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DeliveryInstructions")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("delivery_instructions")
                        .HasComment("Additional delivery instructions for this address");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name")
                        .HasComment("First name of the person at this address");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active")
                        .HasComment("Indicates if this address is active and can be used");

                    b.Property<bool>("IsCompanyAddress")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_default")
                        .HasComment("Indicates if this is the default address for the user");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Label")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("label")
                        .HasComment("User-friendly label for this address (e.g., Home, Office)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name")
                        .HasComment("Last name of the person at this address");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("phone_number");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("TaxNumber")
                        .HasColumnType("text");

                    b.Property<string>("TaxOffice")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("address_type")
                        .HasComment("Address type: 1=Billing, 2=Shipping, 3=Both");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id")
                        .HasComment("Reference to the user who owns this address");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_user_addresses_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_user_addresses_is_deleted");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_addresses_user_id");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_user_addresses_is_deleted_created_at");

                    b.HasIndex("UserId", "IsActive")
                        .HasDatabaseName("ix_user_addresses_user_id_is_active");

                    b.HasIndex("UserId", "IsDefault")
                        .HasDatabaseName("ix_user_addresses_user_id_is_default");

                    b.HasIndex("UserId", "Type")
                        .HasDatabaseName("ix_user_addresses_user_id_type");

                    b.HasIndex("UserId", "IsActive", "Type")
                        .HasDatabaseName("ix_user_addresses_user_id_active_type");

                    b.ToTable("user_addresses", "etyra_core", t =>
                        {
                            t.HasComment("User addresses for billing and shipping");

                            t.HasCheckConstraint("ck_user_addresses_address_type", "address_type IN (1, 2, 3)");

                            t.HasCheckConstraint("ck_user_addresses_first_name_not_empty", "LENGTH(TRIM(first_name)) > 0");

                            t.HasCheckConstraint("ck_user_addresses_label_not_empty", "label IS NULL OR LENGTH(TRIM(label)) > 0");

                            t.HasCheckConstraint("ck_user_addresses_last_name_not_empty", "LENGTH(TRIM(last_name)) > 0");
                        });
                });

            modelBuilder.Entity("EtyraCommerce.Persistence.Configurations.Examples.ExampleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("email");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<string>("OptionalPrice")
                        .HasColumnType("jsonb")
                        .HasColumnName("optional_price_json");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("phone_number");

                    b.Property<string>("PreferredCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("preferred_currency");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_example_entities_created_at");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_example_entities_created_by");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("ix_example_entities_deleted_by");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_example_entities_email");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("ix_example_entities_is_deleted");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_example_entities_name");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_example_entities_updated_by");

                    b.HasIndex("CreatedBy", "CreatedAt")
                        .HasDatabaseName("ix_example_entities_created_by_created_at");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("ix_example_entities_is_deleted_created_at");

                    b.HasIndex("Name", "PreferredCurrency")
                        .HasDatabaseName("ix_example_entities_name_currency");

                    b.HasIndex("UpdatedBy", "UpdatedAt")
                        .HasDatabaseName("ix_example_entities_updated_by_updated_at");

                    b.ToTable("example_entities", "etyra_core");
                });

            modelBuilder.Entity("CategoryCategory", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Category.Category", null)
                        .WithMany()
                        .HasForeignKey("AncestorsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Category.Category", null)
                        .WithMany()
                        .HasForeignKey("DescendantsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Cart.CartItem", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Cart.ShoppingCart", "ShoppingCart")
                        .WithMany("CartItems")
                        .HasForeignKey("ShoppingCartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "TotalPrice", b1 =>
                        {
                            b1.Property<Guid>("CartItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("total_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_price_currency");

                            b1.HasKey("CartItemId");

                            b1.ToTable("cart_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("CartItemId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("CartItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("unit_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("unit_price_currency");

                            b1.HasKey("CartItemId");

                            b1.ToTable("cart_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("CartItemId");
                        });

                    b.Navigation("Product");

                    b.Navigation("ShoppingCart");

                    b.Navigation("TotalPrice")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Cart.ShoppingCart", b =>
                {
                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Currency", "Currency", b1 =>
                        {
                            b1.Property<Guid>("ShoppingCartId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Code")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.Property<int>("DecimalPlaces")
                                .HasColumnType("integer")
                                .HasColumnName("currency_decimal_places");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("currency_name");

                            b1.Property<string>("Symbol")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("currency_symbol");

                            b1.HasKey("ShoppingCartId");

                            b1.ToTable("shopping_carts", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ShoppingCartId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Subtotal", b1 =>
                        {
                            b1.Property<Guid>("ShoppingCartId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("subtotal_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("subtotal_currency");

                            b1.HasKey("ShoppingCartId");

                            b1.ToTable("shopping_carts", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ShoppingCartId");
                        });

                    b.Navigation("Currency")
                        .IsRequired();

                    b.Navigation("Subtotal")
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Category.Category", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Category.Category", "ParentCategory")
                        .WithMany("ChildCategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Category.CategoryDescription", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Category.Category", "Category")
                        .WithMany("Descriptions")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Inventory", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Inventory.Warehouse", "Warehouse")
                        .WithMany("InventoryItems")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.InventoryTransaction", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Inventory.Inventory", "Inventory")
                        .WithMany("Transactions")
                        .HasForeignKey("InventoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.User.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Inventory");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Warehouse", b =>
                {
                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Address", "Address", b1 =>
                        {
                            b1.Property<Guid>("WarehouseId")
                                .HasColumnType("uuid");

                            b1.Property<string>("AddressLine2")
                                .HasColumnType("text");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("AddressCity");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("AddressCountry");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("AddressPostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("AddressState");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("AddressStreet");

                            b1.HasKey("WarehouseId");

                            b1.ToTable("Warehouses", "etyra_inventory");

                            b1.WithOwner()
                                .HasForeignKey("WarehouseId");
                        });

                    b.Navigation("Address");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Order.Order", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.User.User", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("EtyraCommerce.Domain.Entities.Payment.PaymentMethod", "PaymentMethod")
                        .WithMany()
                        .HasForeignKey("PaymentMethodId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("AddressLine2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_line2");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_street");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "DiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("discount_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("discount_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "PaymentFee", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("payment_fee_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("payment_fee_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("AddressLine2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_line2");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "ShippingCost", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("shipping_cost_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("shipping_cost_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Subtotal", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("subtotal_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("subtotal_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "TaxAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("tax_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("tax_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Total", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("DiscountAmount")
                        .IsRequired();

                    b.Navigation("PaymentFee");

                    b.Navigation("PaymentMethod");

                    b.Navigation("ShippingAddress")
                        .IsRequired();

                    b.Navigation("ShippingCost")
                        .IsRequired();

                    b.Navigation("Subtotal")
                        .IsRequired();

                    b.Navigation("TaxAmount")
                        .IsRequired();

                    b.Navigation("Total")
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Order.OrderItem", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Order.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "DiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("discount_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("discount_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "TaxAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("tax_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("tax_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "TotalPrice", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("total_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_price_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("unit_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("unit_price_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.Navigation("DiscountAmount");

                    b.Navigation("Order");

                    b.Navigation("Product");

                    b.Navigation("TaxAmount");

                    b.Navigation("TotalPrice")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Payment.PaymentMethod", b =>
                {
                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Currency", "FeeCurrency", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Code")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("fee_currency_code");

                            b1.Property<int>("DecimalPlaces")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(2)
                                .HasColumnName("fee_currency_decimal_places");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("fee_currency_name");

                            b1.Property<string>("Symbol")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("fee_currency_symbol");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MaximumOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("maximum_order_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("maximum_order_currency");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MinimumOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("minimum_order_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("minimum_order_currency");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.Navigation("FeeCurrency");

                    b.Navigation("MaximumOrderAmount");

                    b.Navigation("MinimumOrderAmount");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.Product", b =>
                {
                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "BasePrice", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("base_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("base_price_currency");

                            b1.HasKey("ProductId");

                            b1.ToTable("products", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Cost", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("cost_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("cost_currency");

                            b1.HasKey("ProductId");

                            b1.ToTable("products", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "SalePrice", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("sale_price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("sale_price_currency");

                            b1.HasKey("ProductId");

                            b1.ToTable("products", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.Navigation("BasePrice")
                        .IsRequired();

                    b.Navigation("Cost");

                    b.Navigation("SalePrice");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductAttribute", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("Attributes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductCategory", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Category.Category", "Category")
                        .WithMany("ProductCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("ProductCategories")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductDescription", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("Descriptions")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductDiscount", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("Discounts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MaxDiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("ProductDiscountId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("max_discount_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("max_discount_currency");

                            b1.HasKey("ProductDiscountId");

                            b1.ToTable("product_discounts", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductDiscountId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MinOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("ProductDiscountId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("min_order_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("min_order_currency");

                            b1.HasKey("ProductDiscountId");

                            b1.ToTable("product_discounts", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductDiscountId");
                        });

                    b.Navigation("MaxDiscountAmount");

                    b.Navigation("MinOrderAmount");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductImage", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("Images")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductVariant", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("Variants")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Cost", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("cost_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("cost_currency");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("product_variants", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("price_currency");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("product_variants", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.ProductDimensions", "Dimensions", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid");

                            b1.Property<decimal?>("Height")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("dimensions_height");

                            b1.Property<decimal?>("Length")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("dimensions_length");

                            b1.Property<string>("Unit")
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("dimensions_unit");

                            b1.Property<decimal?>("Weight")
                                .HasColumnType("decimal(10,3)")
                                .HasColumnName("dimensions_weight");

                            b1.Property<string>("WeightUnit")
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("dimensions_weight_unit");

                            b1.Property<decimal?>("Width")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("dimensions_width");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("product_variants", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId");
                        });

                    b.Navigation("Cost");

                    b.Navigation("Dimensions");

                    b.Navigation("Price");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductVariantAttribute", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.ProductVariant", "ProductVariant")
                        .WithMany("Attributes")
                        .HasForeignKey("ProductVariantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductVariant");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.WarehouseProduct", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Product.Product", "Product")
                        .WithMany("WarehouseProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Cost", b1 =>
                        {
                            b1.Property<Guid>("WarehouseProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("cost_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("cost_currency");

                            b1.HasKey("WarehouseProductId");

                            b1.ToTable("warehouse_products", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("WarehouseProductId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("WarehouseProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("price_currency");

                            b1.HasKey("WarehouseProductId");

                            b1.ToTable("warehouse_products", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("WarehouseProductId");
                        });

                    b.Navigation("Cost")
                        .IsRequired();

                    b.Navigation("Price")
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.City", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.Region", "Region")
                        .WithMany("Cities")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Region");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.District", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.City", "City")
                        .WithMany("Districts")
                        .HasForeignKey("CityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("City");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.Region", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.Country", "Country")
                        .WithMany("Regions")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingMethod", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", "ShippingZone")
                        .WithMany("ShippingMethods")
                        .HasForeignKey("ShippingZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ShippingZone");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingRate", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.ShippingMethod", "ShippingMethod")
                        .WithMany("ShippingRates")
                        .HasForeignKey("ShippingMethodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", "ShippingZone")
                        .WithMany("ShippingRates")
                        .HasForeignKey("ShippingZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "AdditionalCostPerKg", b1 =>
                        {
                            b1.Property<Guid>("ShippingRateId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("AdditionalCostPerKgAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("AdditionalCostPerKgCurrency");

                            b1.HasKey("ShippingRateId");

                            b1.ToTable("ShippingRates", "etyra_shipping");

                            b1.WithOwner()
                                .HasForeignKey("ShippingRateId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "BaseCost", b1 =>
                        {
                            b1.Property<Guid>("ShippingRateId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("BaseCostAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("BaseCostCurrency");

                            b1.HasKey("ShippingRateId");

                            b1.ToTable("ShippingRates", "etyra_shipping");

                            b1.WithOwner()
                                .HasForeignKey("ShippingRateId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "FreeShippingThreshold", b1 =>
                        {
                            b1.Property<Guid>("ShippingRateId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("FreeShippingThresholdAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("FreeShippingThresholdCurrency");

                            b1.HasKey("ShippingRateId");

                            b1.ToTable("ShippingRates", "etyra_shipping");

                            b1.WithOwner()
                                .HasForeignKey("ShippingRateId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MaxOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("ShippingRateId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("MaxOrderAmountAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("MaxOrderAmountCurrency");

                            b1.HasKey("ShippingRateId");

                            b1.ToTable("ShippingRates", "etyra_shipping");

                            b1.WithOwner()
                                .HasForeignKey("ShippingRateId");
                        });

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "MinOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("ShippingRateId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("MinOrderAmountAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("MinOrderAmountCurrency");

                            b1.HasKey("ShippingRateId");

                            b1.ToTable("ShippingRates", "etyra_shipping");

                            b1.WithOwner()
                                .HasForeignKey("ShippingRateId");
                        });

                    b.Navigation("AdditionalCostPerKg");

                    b.Navigation("BaseCost")
                        .IsRequired();

                    b.Navigation("FreeShippingThreshold");

                    b.Navigation("MaxOrderAmount");

                    b.Navigation("MinOrderAmount");

                    b.Navigation("ShippingMethod");

                    b.Navigation("ShippingZone");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZoneCountry", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.Country", "Country")
                        .WithMany("ShippingZones")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", "ShippingZone")
                        .WithMany("Countries")
                        .HasForeignKey("ShippingZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("ShippingZone");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZoneLocation", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.City", "City")
                        .WithMany("ShippingZoneLocations")
                        .HasForeignKey("CityId");

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId");

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.District", "District")
                        .WithMany("ShippingZoneLocations")
                        .HasForeignKey("DistrictId");

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.Region", "Region")
                        .WithMany("ShippingZoneLocations")
                        .HasForeignKey("RegionId");

                    b.HasOne("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", "ShippingZone")
                        .WithMany("Locations")
                        .HasForeignKey("ShippingZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("City");

                    b.Navigation("Country");

                    b.Navigation("District");

                    b.Navigation("Region");

                    b.Navigation("ShippingZone");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.User.UserAddress", b =>
                {
                    b.HasOne("EtyraCommerce.Domain.Entities.User.User", "User")
                        .WithMany("Addresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_addresses_user_id");

                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Address", "Address", b1 =>
                        {
                            b1.Property<Guid>("UserAddressId")
                                .HasColumnType("uuid");

                            b1.Property<string>("AddressLine2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("address_line2");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("address_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("address_street");

                            b1.HasKey("UserAddressId");

                            b1.ToTable("user_addresses", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("UserAddressId");
                        });

                    b.Navigation("Address")
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EtyraCommerce.Persistence.Configurations.Examples.ExampleEntity", b =>
                {
                    b.OwnsOne("EtyraCommerce.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("ExampleEntityId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("price_currency");

                            b1.HasKey("ExampleEntityId");

                            b1.ToTable("example_entities", "etyra_core");

                            b1.WithOwner()
                                .HasForeignKey("ExampleEntityId");
                        });

                    b.Navigation("Price")
                        .IsRequired();
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Cart.ShoppingCart", b =>
                {
                    b.Navigation("CartItems");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Category.Category", b =>
                {
                    b.Navigation("ChildCategories");

                    b.Navigation("Descriptions");

                    b.Navigation("ProductCategories");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Inventory", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Inventory.Warehouse", b =>
                {
                    b.Navigation("InventoryItems");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Order.Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.Product", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("Descriptions");

                    b.Navigation("Discounts");

                    b.Navigation("Images");

                    b.Navigation("ProductCategories");

                    b.Navigation("Variants");

                    b.Navigation("WarehouseProducts");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Product.ProductVariant", b =>
                {
                    b.Navigation("Attributes");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.City", b =>
                {
                    b.Navigation("Districts");

                    b.Navigation("ShippingZoneLocations");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.Country", b =>
                {
                    b.Navigation("Regions");

                    b.Navigation("ShippingZones");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.District", b =>
                {
                    b.Navigation("ShippingZoneLocations");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.Region", b =>
                {
                    b.Navigation("Cities");

                    b.Navigation("ShippingZoneLocations");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingMethod", b =>
                {
                    b.Navigation("ShippingRates");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.Shipping.ShippingZone", b =>
                {
                    b.Navigation("Countries");

                    b.Navigation("Locations");

                    b.Navigation("ShippingMethods");

                    b.Navigation("ShippingRates");
                });

            modelBuilder.Entity("EtyraCommerce.Domain.Entities.User.User", b =>
                {
                    b.Navigation("Addresses");
                });
#pragma warning restore 612, 618
        }
    }
}
