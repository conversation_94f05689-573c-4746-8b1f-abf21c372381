using AutoMapper;
using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProductEntity = EtyraCommerce.Domain.Entities.Product.Product;

namespace EtyraCommerce.Persistence.Services.Cart
{
    /// <summary>
    /// Cart process service implementation for business logic operations
    /// </summary>
    public class CartProcessService : ICartProcessService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<CartProcessService> _logger;
        private readonly EtyraCommerceDbContext _context;

        public CartProcessService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<CartProcessService> logger,
            EtyraCommerceDbContext context)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _context = context;
        }

        #region Cart Management

        public async Task<CustomResponseDto<CartDto>> ProcessAddToCartAsync(AddToCartDto addToCartDto, Guid? customerId = null, string currency = "USD")
        {
            try
            {
                _logger.LogInformation("Processing add to cart for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    addToCartDto.ProductId, customerId, addToCartDto.SessionId);

                // Önce cart var mı kontrol et (tracking olmadan)
                var existingCart = await GetCartEntityAsync(customerId, addToCartDto.SessionId, tracking: false);

                ShoppingCart cart;
                if (existingCart != null)
                {
                    // Mevcut cart'ı tracking ile al - CartItems ile birlikte
                    _logger.LogInformation($"Found existing cart: {existingCart.Id}. Getting tracked version with items.");
                    cart = await GetCartEntityAsync(customerId, addToCartDto.SessionId, tracking: true);
                    if (cart == null)
                        return CustomResponseDto<CartDto>.NotFound("Cart not found for tracking");

                    _logger.LogInformation($"Tracked cart loaded with {cart.CartItems.Count} items");
                }
                else
                {
                    // Yeni cart oluştur ve kaydet
                    var currencyObj = Currency.FromCode(currency);
                    var newCart = customerId.HasValue
                        ? new ShoppingCart(customerId.Value, currencyObj)
                        : new ShoppingCart(addToCartDto.SessionId!, currencyObj);

                    var cartWriteRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                    await cartWriteRepo.AddAsync(newCart);
                    await _unitOfWork.SaveChangesAsync();

                    // Fresh cart entity'sini al (RowVersion güncel olsun) - CartItems ile birlikte
                    _logger.LogInformation($"Fetching fresh cart with items - CustomerId: {customerId}, SessionId: {addToCartDto.SessionId}");
                    cart = await GetCartEntityAsync(
                        customerId: customerId,
                        sessionId: customerId.HasValue ? null : addToCartDto.SessionId,
                        tracking: true);

                    _logger.LogInformation($"Fresh cart result: {(cart != null ? $"Found cart {cart.Id}" : "Cart is null")}");
                    if (cart == null)
                        return CustomResponseDto<CartDto>.NotFound("Cart not found after creation");
                }

                // Validate product availability
                var productValidation = await ValidateProductAvailabilityAsync(addToCartDto.ProductId, addToCartDto.Quantity);
                if (!productValidation.IsSuccess)
                    return CustomResponseDto<CartDto>.BadRequest(productValidation.Message);

                // Get product details
                var product = await GetProductDetailsAsync(addToCartDto.ProductId);
                if (product == null)
                    return CustomResponseDto<CartDto>.NotFound("Product not found");

                // Add item to cart - EXISTING ITEM CHECK
                _logger.LogInformation($"Adding item to cart - ProductId: {product.Id}, Quantity: {addToCartDto.Quantity}");

                // Aynı ürün sepette var mı kontrol et
                _logger.LogInformation($"Checking for existing item. Cart has {cart.CartItems.Count} items");
                foreach (var item in cart.CartItems)
                {
                    _logger.LogInformation($"Existing item: ProductId={item.ProductId}, VariantInfo='{item.VariantInfo}'");
                }
                _logger.LogInformation($"Looking for: ProductId={product.Id}, VariantInfo='{addToCartDto.VariantInfo ?? string.Empty}'");

                var existingItem = cart.CartItems.FirstOrDefault(x =>
                    x.ProductId == product.Id &&
                    x.VariantInfo == (addToCartDto.VariantInfo ?? string.Empty));

                if (existingItem != null)
                {
                    // Existing item - quantity artır
                    _logger.LogInformation($"Product already exists in cart. Updating quantity from {existingItem.Quantity} to {existingItem.Quantity + addToCartDto.Quantity}");

                    existingItem.UpdateQuantity(existingItem.Quantity + addToCartDto.Quantity);

                    // Notes güncelle (varsa)
                    if (!string.IsNullOrEmpty(addToCartDto.Notes))
                        existingItem.SetNotes(addToCartDto.Notes);

                    var cartItemRepo = _unitOfWork.WriteRepository<CartItem>();
                    await cartItemRepo.UpdateAsync(existingItem);

                    _logger.LogInformation($"Item quantity updated. CartId: {cart.Id}, ItemId: {existingItem.Id}, NewQuantity: {existingItem.Quantity}");
                }
                else
                {
                    // New item - yeni CartItem oluştur
                    _logger.LogInformation($"Adding new item to cart");

                    var cartItemRepo = _unitOfWork.WriteRepository<CartItem>();
                    var newCartItem = new CartItem(
                        product.Id,
                        product.Name,
                        product.SKU,
                        product.BasePrice,
                        addToCartDto.Quantity);

                    // ShoppingCartId set et
                    newCartItem.ShoppingCartId = cart.Id;

                    // Variant info ve notes set et
                    if (!string.IsNullOrEmpty(addToCartDto.VariantInfo))
                        newCartItem.SetVariantInfo(addToCartDto.VariantInfo);

                    if (!string.IsNullOrEmpty(addToCartDto.Notes))
                        newCartItem.SetNotes(addToCartDto.Notes);
                    else
                        newCartItem.SetNotes("Gift wrap requested"); // Test için

                    // Set product image if available
                    var productImage = await GetProductImageAsync(product.Id);
                    if (!string.IsNullOrEmpty(productImage))
                        newCartItem.SetProductImageUrl(productImage);

                    // CartItem'ı INSERT et
                    await cartItemRepo.AddAsync(newCartItem);

                    _logger.LogInformation($"New item added. CartId: {cart.Id}, ItemId: {newCartItem.Id}");
                }

                // Cart'ı güncelle
                var cartRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                await cartRepo.UpdateAsync(cart);

                await _unitOfWork.SaveChangesAsync();

                // Map to DTO and return
                var cartDto = _mapper.Map<CartDto>(cart);

                _logger.LogInformation("Successfully added item to cart. ProductId: {ProductId}, CartId: {CartId}, TotalItems: {TotalItems}",
                    addToCartDto.ProductId, cart.Id, cart.TotalItems);

                return CustomResponseDto<CartDto>.Success(200, cartDto, "Item added to cart successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing add to cart for ProductId: {ProductId}, CustomerId: {CustomerId}",
                    addToCartDto.ProductId, customerId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while adding item to cart");
            }
        }

        public async Task<CustomResponseDto<CartDto>> ProcessUpdateCartItemAsync(UpdateCartItemDto updateCartItemDto, Guid? customerId = null, string? sessionId = null)
        {
            try
            {
                _logger.LogInformation("Processing update cart item for ProductId: {ProductId}, Quantity: {Quantity}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    updateCartItemDto.ProductId, updateCartItemDto.Quantity, customerId, sessionId);

                // Get cart
                var cartEntity = await GetCartEntityAsync(customerId, sessionId, tracking: false);
                if (cartEntity == null)
                    return CustomResponseDto<CartDto>.NotFound("Cart not found");

                // Get tracked version
                var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cart = await cartReadRepo.GetByIdAsync(cartEntity.Id, tracking: true);
                if (cart == null)
                    return CustomResponseDto<CartDto>.NotFound("Cart not found");

                // If quantity is 0, remove the item
                if (updateCartItemDto.Quantity == 0)
                {
                    cart.RemoveItem(updateCartItemDto.ProductId);
                }
                else
                {
                    // Validate product availability for new quantity
                    var productValidation = await ValidateProductAvailabilityAsync(updateCartItemDto.ProductId, updateCartItemDto.Quantity);
                    if (!productValidation.IsSuccess)
                        return CustomResponseDto<CartDto>.BadRequest(productValidation.Message);

                    // Update item quantity
                    cart.UpdateItemQuantity(updateCartItemDto.ProductId, updateCartItemDto.Quantity);

                    // Update additional properties if provided
                    var item = cart.CartItems.FirstOrDefault(x => x.ProductId == updateCartItemDto.ProductId);
                    if (item != null)
                    {
                        if (!string.IsNullOrEmpty(updateCartItemDto.VariantInfo))
                            item.SetVariantInfo(updateCartItemDto.VariantInfo);

                        if (!string.IsNullOrEmpty(updateCartItemDto.Notes))
                            item.SetNotes(updateCartItemDto.Notes);
                    }
                }

                // Save changes
                var cartRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                await cartRepo.UpdateAsync(cart);
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO and return
                var cartDto = _mapper.Map<CartDto>(cart);

                _logger.LogInformation("Successfully updated cart item. ProductId: {ProductId}, CartId: {CartId}, NewQuantity: {Quantity}",
                    updateCartItemDto.ProductId, cart.Id, updateCartItemDto.Quantity);

                return CustomResponseDto<CartDto>.Success(200, cartDto, "Cart item updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update cart item for ProductId: {ProductId}, CustomerId: {CustomerId}",
                    updateCartItemDto.ProductId, customerId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while updating cart item");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessRemoveFromCartAsync(RemoveFromCartDto removeFromCartDto, Guid? customerId = null, string? sessionId = null)
        {
            try
            {
                _logger.LogInformation("Processing remove from cart for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    removeFromCartDto.ProductId, customerId, sessionId);

                // Get cart
                var cartEntity = await GetCartEntityAsync(customerId, sessionId, tracking: false);
                if (cartEntity == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Cart not found");

                // Get tracked version
                var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cart = await cartReadRepo.GetByIdAsync(cartEntity.Id, tracking: true);
                if (cart == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Cart not found");

                // Remove item
                cart.RemoveItem(removeFromCartDto.ProductId);

                // Save changes
                var cartRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                await cartRepo.UpdateAsync(cart);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Successfully removed item from cart. ProductId: {ProductId}, CartId: {CartId}",
                    removeFromCartDto.ProductId, cart.Id);

                return CustomResponseDto<NoContentDto>.Success(200, "Item removed from cart successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing remove from cart for ProductId: {ProductId}, CustomerId: {CustomerId}",
                    removeFromCartDto.ProductId, customerId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while removing item from cart");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessClearCartAsync(Guid? customerId = null, string? sessionId = null)
        {
            try
            {
                _logger.LogInformation("Processing clear cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);

                // Get cart
                var cartEntity = await GetCartEntityAsync(customerId, sessionId, tracking: false);
                if (cartEntity == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Cart not found");

                // Get tracked version
                var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cart = await cartReadRepo.GetByIdAsync(cartEntity.Id, tracking: true);
                if (cart == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Cart not found");

                // Delete the entire cart (cascade delete will handle cart items)
                _logger.LogInformation("Deleting cart with cascade. Current item count: {ItemCount}", cart.CartItems.Count);

                // Hard delete using ExecuteDeleteAsync to bypass soft delete
                await _context.ShoppingCarts
                    .Where(c => c.Id == cart.Id)
                    .ExecuteDeleteAsync();
                _logger.LogInformation("Cart and items deleted successfully via cascade delete");

                _logger.LogInformation("Successfully cleared cart. CartId: {CartId}", cart.Id);

                return CustomResponseDto<NoContentDto>.Success(200, "Cart cleared successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing clear cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while clearing cart");
            }
        }

        public async Task<CustomResponseDto<CartDto>> ProcessMergeCartAsync(MergeCartDto mergeCartDto)
        {
            try
            {
                _logger.LogInformation("Processing merge cart for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    mergeCartDto.SessionId, mergeCartDto.CustomerId);

                // Get guest cart
                var guestCart = await GetCartEntityAsync(null, mergeCartDto.SessionId);
                if (guestCart == null)
                    return CustomResponseDto<CartDto>.NotFound("Guest cart not found");

                // Get or create user cart
                var userCartResult = await GetOrCreateCartAsync(mergeCartDto.CustomerId, null, guestCart.Currency.Code);
                if (!userCartResult.IsSuccess)
                    return CustomResponseDto<CartDto>.BadRequest(userCartResult.Message);

                var userCart = await GetCartEntityAsync(mergeCartDto.CustomerId, null);
                if (userCart == null)
                    return CustomResponseDto<CartDto>.InternalServerError("Failed to create user cart");

                // Merge items from guest cart to user cart
                foreach (var guestItem in guestCart.CartItems)
                {
                    userCart.AddItem(
                        guestItem.ProductId,
                        guestItem.ProductName,
                        guestItem.ProductSku,
                        guestItem.UnitPrice,
                        guestItem.Quantity);

                    // Copy additional properties
                    var mergedItem = userCart.CartItems.FirstOrDefault(x => x.ProductId == guestItem.ProductId);
                    if (mergedItem != null)
                    {
                        if (!string.IsNullOrEmpty(guestItem.VariantInfo))
                            mergedItem.SetVariantInfo(guestItem.VariantInfo);
                        if (!string.IsNullOrEmpty(guestItem.Notes))
                            mergedItem.SetNotes(guestItem.Notes);
                        if (!string.IsNullOrEmpty(guestItem.ProductImageUrl))
                            mergedItem.SetProductImageUrl(guestItem.ProductImageUrl);
                    }
                }

                // Deactivate guest cart
                guestCart.Deactivate();

                // Save changes
                var cartRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                await cartRepo.UpdateAsync(userCart);
                await cartRepo.UpdateAsync(guestCart);
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO and return
                var cartDto = _mapper.Map<CartDto>(userCart);

                _logger.LogInformation("Successfully merged cart. GuestCartId: {GuestCartId}, UserCartId: {UserCartId}, TotalItems: {TotalItems}",
                    guestCart.Id, userCart.Id, userCart.TotalItems);

                return CustomResponseDto<CartDto>.Success(200, cartDto, "Cart merged successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing merge cart for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    mergeCartDto.SessionId, mergeCartDto.CustomerId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while merging cart");
            }
        }

        #endregion

        #region Cart Retrieval

        public async Task<CustomResponseDto<CartDto>> GetCartAsync(Guid? customerId = null, string? sessionId = null, bool includeInactive = false, bool includeExpired = false)
        {
            try
            {
                _logger.LogInformation("Getting cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);

                var cart = await GetCartEntityAsync(customerId, sessionId, includeInactive, includeExpired);
                if (cart == null)
                    return CustomResponseDto<CartDto>.NotFound("Cart not found");

                var cartDto = _mapper.Map<CartDto>(cart);
                return CustomResponseDto<CartDto>.Success(200, cartDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while retrieving cart");
            }
        }

        public async Task<CustomResponseDto<CartSummaryDto>> GetCartSummaryAsync(Guid? customerId = null, string? sessionId = null)
        {
            try
            {
                _logger.LogInformation("Getting cart summary for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);

                var cart = await GetCartEntityAsync(customerId, sessionId);
                if (cart == null)
                    return CustomResponseDto<CartSummaryDto>.NotFound("Cart not found");

                var cartSummaryDto = _mapper.Map<CartSummaryDto>(cart);
                return CustomResponseDto<CartSummaryDto>.Success(200, cartSummaryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cart summary for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);
                return CustomResponseDto<CartSummaryDto>.InternalServerError("An error occurred while retrieving cart summary");
            }
        }

        public async Task<CustomResponseDto<CartDto>> GetCartByIdAsync(Guid cartId)
        {
            try
            {
                _logger.LogInformation("Getting cart by ID: {CartId}", cartId);

                var cartRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cartsQuery = await cartRepo.GetAllAsync(tracking: false);
                var carts = await cartsQuery.ToListAsync();
                var cart = carts.FirstOrDefault(c => c.Id == cartId);

                if (cart == null)
                    return CustomResponseDto<CartDto>.NotFound("Cart not found");

                var cartDto = _mapper.Map<CartDto>(cart);
                return CustomResponseDto<CartDto>.Success(200, cartDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cart by ID: {CartId}", cartId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while retrieving cart");
            }
        }

        #endregion

        #region Cart Utilities

        public async Task<bool> ValidateCartOwnershipAsync(Guid cartId, Guid? customerId = null, string? sessionId = null)
        {
            try
            {
                var cartRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cartsQuery = await cartRepo.GetAllAsync(tracking: false);
                var carts = await cartsQuery.ToListAsync();
                var cart = carts.FirstOrDefault(c => c.Id == cartId);

                if (cart == null) return false;

                if (customerId.HasValue)
                    return cart.CustomerId == customerId;
                else if (!string.IsNullOrEmpty(sessionId))
                    return cart.SessionId == sessionId;

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating cart ownership for CartId: {CartId}", cartId);
                return false;
            }
        }

        public async Task<CustomResponseDto<int>> CleanupExpiredCartsAsync()
        {
            try
            {
                _logger.LogInformation("Starting cleanup of expired carts");

                var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cartsQuery = await cartReadRepo.GetAllAsync(tracking: false);
                var carts = await cartsQuery.ToListAsync();
                var expiredCarts = carts.Where(c => c.IsExpired()).ToList();

                var cartWriteRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                foreach (var cart in expiredCarts)
                {
                    cart.Deactivate();
                    await cartWriteRepo.UpdateAsync(cart);
                }

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Cleaned up {Count} expired carts", expiredCarts.Count);
                return CustomResponseDto<int>.Success(200, expiredCarts.Count, $"Cleaned up {expiredCarts.Count} expired carts");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired carts");
                return CustomResponseDto<int>.InternalServerError("An error occurred while cleaning up expired carts");
            }
        }

        public async Task<CustomResponseDto<CartDto>> GetOrCreateCartAsync(Guid? customerId = null, string? sessionId = null, string currency = "USD")
        {
            try
            {
                // Try to get existing cart
                var existingCart = await GetCartEntityAsync(customerId, sessionId);
                if (existingCart != null)
                {
                    var existingCartDto = _mapper.Map<CartDto>(existingCart);
                    return CustomResponseDto<CartDto>.Success(200, existingCartDto);
                }

                // Create new cart
                var currencyObj = Currency.FromCode(currency);
                var newCart = customerId.HasValue
                    ? new ShoppingCart(customerId.Value, currencyObj)
                    : new ShoppingCart(sessionId ?? Guid.NewGuid().ToString(), currencyObj);

                var cartRepo = _unitOfWork.WriteRepository<ShoppingCart>();
                await cartRepo.AddAsync(newCart);
                await _unitOfWork.SaveChangesAsync();

                var newCartDto = _mapper.Map<CartDto>(newCart);
                return CustomResponseDto<CartDto>.Success(201, newCartDto, "Cart created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    customerId, sessionId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while getting or creating cart");
            }
        }

        public async Task<CustomResponseDto<bool>> ValidateProductAvailabilityAsync(Guid productId, int quantity)
        {
            try
            {
                // Get product
                var product = await GetProductDetailsAsync(productId);
                if (product == null)
                    return CustomResponseDto<bool>.NotFound("Product not found");

                // Basic validation - product should be published
                if (product.Status != EtyraCommerce.Domain.Entities.Product.ProductStatus.Published)
                    return CustomResponseDto<bool>.BadRequest("Product is not available");

                // TODO: Add inventory validation when inventory system is integrated
                // For now, assume product is available
                return CustomResponseDto<bool>.Success(200, true, "Product is available");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating product availability for ProductId: {ProductId}", productId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating product availability");
            }
        }

        #endregion

        #region Helper Methods



        private async Task<ShoppingCart?> GetCartEntityAsync(Guid? customerId, string? sessionId, bool includeInactive = false, bool includeExpired = false, bool tracking = false)
        {
            var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
            var cartsQuery = await cartReadRepo.GetAllAsync(tracking: tracking);

            // CartItems ve Product bilgilerini include et - HER ZAMAN
            var carts = await cartsQuery
                .Include(c => c.CartItems.Where(ci => !ci.IsDeleted))
                .ThenInclude(ci => ci.Product)
                .ToListAsync();

            if (customerId.HasValue)
            {
                return carts.FirstOrDefault(c => c.CustomerId == customerId &&
                    (includeInactive || c.IsActive) &&
                    (includeExpired || !c.IsExpired()));
            }
            else if (!string.IsNullOrEmpty(sessionId))
            {
                return carts.FirstOrDefault(c => c.SessionId == sessionId &&
                    (includeInactive || c.IsActive) &&
                    (includeExpired || !c.IsExpired()));
            }

            return null;
        }

        private async Task<ProductEntity?> GetProductDetailsAsync(Guid productId)
        {
            var productRepo = _unitOfWork.ReadRepository<ProductEntity>();
            var productsQuery = await productRepo.GetAllAsync(tracking: false);
            var products = await productsQuery.ToListAsync();
            return products.FirstOrDefault(p => p.Id == productId);
        }

        private async Task<string?> GetProductImageAsync(Guid productId)
        {
            // TODO: Implement product image retrieval
            // This would typically get the primary image URL from ProductImage entity
            return null;
        }

        private async Task<CustomResponseDto<CartDto>> AddItemToExistingCartAsync(Guid cartId, AddToCartDto addToCartDto, string currency)
        {
            try
            {
                // Fresh cart'ı al
                var cartReadRepo = _unitOfWork.ReadRepository<ShoppingCart>();
                var cart = await cartReadRepo.GetByIdAsync(cartId, tracking: true);
                if (cart == null)
                    return CustomResponseDto<CartDto>.NotFound("Cart not found");

                // Product'ı al
                var productReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Product.Product>();
                var products = await productReadRepo.GetAllAsync(tracking: false);
                var product = await products.FirstOrDefaultAsync(p => p.Id == addToCartDto.ProductId);
                if (product == null)
                    return CustomResponseDto<CartDto>.NotFound("Product not found");

                // Mevcut item var mı kontrol et
                var existingItem = cart.CartItems.FirstOrDefault(ci => ci.ProductId == addToCartDto.ProductId);
                if (existingItem != null)
                {
                    // Mevcut item'ı güncelle
                    existingItem.UpdateQuantity(addToCartDto.Quantity);
                    if (!string.IsNullOrEmpty(addToCartDto.Notes))
                        existingItem.SetNotes(addToCartDto.Notes);
                    if (!string.IsNullOrEmpty(addToCartDto.VariantInfo))
                        existingItem.SetVariantInfo(addToCartDto.VariantInfo);
                }
                else
                {
                    // Yeni item ekle - ShoppingCart.AddItem metodunu kullan
                    cart.AddItem(
                        addToCartDto.ProductId,
                        product.Name,
                        product.Model ?? product.Name, // SKU yerine Model kullan
                        product.EffectivePrice,
                        addToCartDto.Quantity
                    );

                    // Eklenen item'ı bul ve ek bilgileri set et
                    var addedItem = cart.CartItems.FirstOrDefault(ci => ci.ProductId == addToCartDto.ProductId);
                    if (addedItem != null)
                    {
                        if (!string.IsNullOrEmpty(addToCartDto.VariantInfo))
                            addedItem.SetVariantInfo(addToCartDto.VariantInfo);
                        if (!string.IsNullOrEmpty(addToCartDto.Notes))
                            addedItem.SetNotes(addToCartDto.Notes);
                        if (!string.IsNullOrEmpty(product.MainImage))
                            addedItem.SetProductImageUrl(product.MainImage);
                    }
                }

                // Değişiklikleri kaydet
                await _unitOfWork.SaveChangesAsync();

                // DTO'ya çevir ve döndür
                var cartDto = _mapper.Map<CartDto>(cart);
                return CustomResponseDto<CartDto>.Success(cartDto, "Item added to cart successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to existing cart {CartId}, ProductId: {ProductId}", cartId, addToCartDto.ProductId);
                return CustomResponseDto<CartDto>.Failure(500, "An error occurred while adding item to cart");
            }
        }

        #endregion
    }
}
