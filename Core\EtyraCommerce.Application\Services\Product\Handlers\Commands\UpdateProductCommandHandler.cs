using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Commands
{
    /// <summary>
    /// Handler for updating an existing product
    /// </summary>
    public class UpdateProductCommandHandler : IRequestHandler<UpdateProductCommand, CustomResponseDto<ProductDto>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<UpdateProductCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public UpdateProductCommandHandler(
            IProductProcessService productProcessService,
            ILogger<UpdateProductCommandHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the update product command
        /// </summary>
        /// <param name="request">Update product command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Product DTO response</returns>
        public async Task<CustomResponseDto<ProductDto>> Handle(UpdateProductCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing update product command for ProductId: {ProductId}, Name: {Name}",
                    request.ProductId, request.Name);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<ProductDto>.BadRequest("Product ID is required");

                if (string.IsNullOrWhiteSpace(request.Name))
                    return CustomResponseDto<ProductDto>.BadRequest("Product name is required");

                if (request.BasePrice <= 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Base price must be greater than 0");

                if (string.IsNullOrWhiteSpace(request.BasePriceCurrency))
                    return CustomResponseDto<ProductDto>.BadRequest("Base price currency is required");

                if (string.IsNullOrWhiteSpace(request.DefaultCurrency))
                    return CustomResponseDto<ProductDto>.BadRequest("Default currency is required");

                // Validate sale price if provided
                if (request.SalePrice.HasValue && request.SalePrice >= request.BasePrice)
                    return CustomResponseDto<ProductDto>.BadRequest("Sale price must be less than base price");

                // Validate cost if provided
                if (request.Cost.HasValue && request.Cost > request.BasePrice)
                    return CustomResponseDto<ProductDto>.BadRequest("Cost should not exceed base price");

                // Validate sale dates if provided
                if (request.SaleStartDate.HasValue && request.SaleEndDate.HasValue &&
                    request.SaleStartDate > request.SaleEndDate)
                    return CustomResponseDto<ProductDto>.BadRequest("Sale start date cannot be after sale end date");

                // Validate availability dates if provided
                if (request.AvailableStartDate.HasValue && request.AvailableEndDate.HasValue &&
                    request.AvailableStartDate > request.AvailableEndDate)
                    return CustomResponseDto<ProductDto>.BadRequest("Available start date cannot be after available end date");

                // Validate stock quantities
                if (request.TotalStockQuantity < 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Stock quantity cannot be negative");

                if (request.MinStockAlert < 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Min stock alert cannot be negative");

                // Validate EAN format if provided
                if (!string.IsNullOrWhiteSpace(request.EAN) && request.EAN.Length != 13)
                    return CustomResponseDto<ProductDto>.BadRequest("EAN must be exactly 13 digits");

                // Validate rating if provided
                if (request.Stars.HasValue && (request.Stars < 1 || request.Stars > 5))
                    return CustomResponseDto<ProductDto>.BadRequest("Stars must be between 1 and 5");

                // Create DTO for business logic
                var updateDto = new UpdateProductDto
                {
                    Name = request.Name,
                    Stars = request.Stars,
                    AiPlatformId = request.AiPlatformId,
                    EAN = request.EAN,
                    MPN = request.MPN,
                    Barcode = request.Barcode,
                    Brand = request.Brand,
                    UPC = request.UPC,
                    BasePrice = request.BasePrice,
                    BasePriceCurrency = request.BasePriceCurrency,
                    Cost = request.Cost,
                    CostCurrency = request.CostCurrency,
                    SalePrice = request.SalePrice,
                    SalePriceCurrency = request.SalePriceCurrency,
                    DefaultCurrency = request.DefaultCurrency,
                    Dimensions = request.Dimensions,
                    MainImage = request.MainImage,
                    ThumbnailImage = request.ThumbnailImage,
                    Status = request.Status,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    IsDigital = request.IsDigital,
                    RequiresShipping = request.RequiresShipping,
                    IsTaxable = request.IsTaxable,
                    Slug = request.Slug,
                    MetaTitle = request.MetaTitle,
                    MetaDescription = request.MetaDescription,
                    MetaKeywords = request.MetaKeywords,
                    AvailableStartDate = request.AvailableStartDate,
                    AvailableEndDate = request.AvailableEndDate,
                    SaleStartDate = request.SaleStartDate,
                    SaleEndDate = request.SaleEndDate,
                    DiscontinueDate = request.DiscontinueDate,
                    PrimaryCategoryId = request.PrimaryCategoryId,
                    CategoryIds = request.CategoryIds,
                    Descriptions = request.Descriptions,
                    Images = request.Images,
                    Attributes = request.Attributes
                };

                // Delegate to business logic service
                var result = await _productProcessService.ProcessUpdateProductAsync(request.ProductId, updateDto);

                _logger.LogInformation("Update product command processed successfully for ProductId: {ProductId}",
                    request.ProductId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update product command for ProductId: {ProductId}",
                    request.ProductId);
                return CustomResponseDto<ProductDto>.InternalServerError("An error occurred while updating the product");
            }
        }
    }
}
