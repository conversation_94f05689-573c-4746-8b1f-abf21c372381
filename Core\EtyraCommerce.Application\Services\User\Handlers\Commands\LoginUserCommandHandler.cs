using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Commands
{
    /// <summary>
    /// Handler for LoginUserCommand - Validates and delegates to UserProcessService
    /// </summary>
    public class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, CustomResponseDto<UserDto>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<LoginUserCommandHandler> _logger;

        public LoginUserCommandHandler(
            IUserProcessService userProcessService,
            ILogger<LoginUserCommandHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto>> Handle(LoginUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing login request for user: {EmailOrUsername}", request.EmailOrUsername);

                // Validation
                if (string.IsNullOrWhiteSpace(request.EmailOrUsername))
                {
                    _logger.LogWarning("Login attempt with empty email/username");
                    return CustomResponseDto<UserDto>.BadRequest("Email or username is required");
                }

                if (string.IsNullOrWhiteSpace(request.Password))
                {
                    _logger.LogWarning("Login attempt with empty password for user: {EmailOrUsername}", request.EmailOrUsername);
                    return CustomResponseDto<UserDto>.BadRequest("Password is required");
                }

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessLoginAsync(request.EmailOrUsername, request.Password);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Login successful for user: {EmailOrUsername}", request.EmailOrUsername);

                    // TODO: Log login attempt with IP and User Agent
                    // TODO: Generate JWT token
                    // TODO: Update last login timestamp
                }
                else
                {
                    _logger.LogWarning("Login failed for user: {EmailOrUsername}. Reason: {Message}",
                        request.EmailOrUsername, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing login command for user: {EmailOrUsername}", request.EmailOrUsername);
                return CustomResponseDto<UserDto>.InternalServerError("An error occurred during login");
            }
        }
    }
}
