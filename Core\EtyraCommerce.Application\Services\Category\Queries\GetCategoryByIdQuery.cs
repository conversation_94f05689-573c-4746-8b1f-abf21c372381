using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Queries
{
    /// <summary>
    /// Query for getting a category by ID
    /// </summary>
    public class GetCategoryByIdQuery : IRequest<CustomResponseDto<CategoryDto>>
    {
        /// <summary>
        /// Category ID to retrieve
        /// </summary>
        public Guid CategoryId { get; set; }

        /// <summary>
        /// Whether to include child categories
        /// </summary>
        public bool IncludeChildren { get; set; } = false;

        /// <summary>
        /// Whether to include parent category
        /// </summary>
        public bool IncludeParent { get; set; } = false;

        /// <summary>
        /// Whether to include descriptions in all languages
        /// </summary>
        public bool IncludeDescriptions { get; set; } = true;

        /// <summary>
        /// Specific language code to filter descriptions
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Creates query from category ID
        /// </summary>
        public static GetCategoryByIdQuery Create(Guid categoryId, bool includeChildren = false, bool includeParent = false, bool includeDescriptions = true, string? languageCode = null)
        {
            return new GetCategoryByIdQuery
            {
                CategoryId = categoryId,
                IncludeChildren = includeChildren,
                IncludeParent = includeParent,
                IncludeDescriptions = includeDescriptions,
                LanguageCode = languageCode
            };
        }
    }
}
