using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Queries
{
    /// <summary>
    /// Handler for advanced product search
    /// </summary>
    public class SearchProductsQueryHandler : IRequestHandler<SearchProductsQuery, CustomResponseDto<PagedResult<ProductDto>>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<SearchProductsQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public SearchProductsQueryHandler(
            IProductProcessService productProcessService,
            ILogger<SearchProductsQueryHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the search products query
        /// </summary>
        /// <param name="request">Search products query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paged product DTO response</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> Handle(SearchProductsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing search products query - SearchTerm: {SearchTerm}, Page: {Page}, PageSize: {PageSize}",
                    request.SearchTerm, request.Page, request.PageSize);

                // Validation
                if (string.IsNullOrWhiteSpace(request.SearchTerm))
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Search term is required");

                if (request.Page < 1)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page size must be between 1 and 100");

                // Validate price range
                if (request.MinPrice.HasValue && request.MaxPrice.HasValue && request.MinPrice > request.MaxPrice)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum price cannot be greater than maximum price");

                // Validate rating range
                if (request.MinRating.HasValue && request.MaxRating.HasValue && request.MinRating > request.MaxRating)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum rating cannot be greater than maximum rating");

                // Validate stock range
                if (request.MinStock.HasValue && request.MaxStock.HasValue && request.MinStock > request.MaxStock)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum stock cannot be greater than maximum stock");

                // Validate date range
                if (request.CreatedFrom.HasValue && request.CreatedTo.HasValue && request.CreatedFrom > request.CreatedTo)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Created from date cannot be after created to date");

                // Validate sort direction
                if (!string.IsNullOrEmpty(request.SortDirection) &&
                    !request.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
                    !request.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Sort direction must be 'asc' or 'desc'");

                // Create search DTO for business logic
                var searchDto = new ProductSearchDto
                {
                    SearchTerm = request.SearchTerm,
                    SearchInName = request.SearchInName,
                    SearchInDescription = request.SearchInDescription,
                    SearchInModel = request.SearchInModel,
                    SearchInSKU = request.SearchInSKU,
                    SearchInBrand = request.SearchInBrand,
                    SearchInTags = request.SearchInTags,
                    SearchInAttributes = request.SearchInAttributes,
                    CaseSensitive = request.CaseSensitive,
                    ExactMatch = request.ExactMatch,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    CategoryIds = request.CategoryIds,
                    Brands = request.Brands,
                    Status = request.Status,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    IsDigital = request.IsDigital,
                    MinPrice = request.MinPrice,
                    MaxPrice = request.MaxPrice,
                    MinRating = request.MinRating,
                    MaxRating = request.MaxRating,
                    MinStock = request.MinStock,
                    MaxStock = request.MaxStock,
                    InStock = request.InStock,
                    OnSale = request.OnSale,
                    CreatedFrom = request.CreatedFrom,
                    CreatedTo = request.CreatedTo,
                    AttributeFilters = request.AttributeFilters,
                    Tags = request.Tags,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    IncludeDescriptions = request.IncludeDescriptions,
                    IncludeImages = request.IncludeImages,
                    IncludeCategories = request.IncludeCategories,
                    IncludeDiscounts = request.IncludeDiscounts,
                    IncludeAttributes = request.IncludeAttributes,
                    IncludeVariants = request.IncludeVariants,
                    LanguageCode = request.LanguageCode,
                    StoreId = request.StoreId
                };

                // Delegate to business logic service
                var result = await _productProcessService.ProcessSearchProductsAsync(searchDto);

                _logger.LogInformation("Search products query processed successfully - SearchTerm: {SearchTerm}, Total: {Total}, Page: {Page}",
                    request.SearchTerm, result.Data?.TotalCount, request.Page);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing search products query - SearchTerm: {SearchTerm}, Page: {Page}",
                    request.SearchTerm, request.Page);
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("An error occurred while searching products");
            }
        }
    }
}
