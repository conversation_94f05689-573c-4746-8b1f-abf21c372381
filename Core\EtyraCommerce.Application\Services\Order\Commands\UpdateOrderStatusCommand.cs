using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Commands
{
    /// <summary>
    /// Command to update order status
    /// </summary>
    public class UpdateOrderStatusCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Order ID
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// New order status
        /// </summary>
        public OrderStatus Status { get; set; }

        /// <summary>
        /// Reason for status change (optional)
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Tracking number (for shipped status)
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// Expected delivery date (for shipped status)
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// Actual delivery date (for delivered status)
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }
    }
}
