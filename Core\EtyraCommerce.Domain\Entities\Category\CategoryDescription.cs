namespace EtyraCommerce.Domain.Entities.Category
{
    /// <summary>
    /// Category description in different languages and stores
    /// </summary>
    public class CategoryDescription : BaseEntity
    {
        /// <summary>
        /// Category name in specific language
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description in specific language
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// SEO-friendly URL slug
        /// </summary>
        public string? Slug { get; set; }

        /// <summary>
        /// Language code (e.g., "en-US", "tr-TR")
        /// </summary>
        public string LanguageCode { get; set; } = "en-US";

        /// <summary>
        /// Store ID (for multi-store support)
        /// </summary>
        public Guid? StoreId { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Related category
        /// </summary>
        public Category Category { get; set; } = null!;

        /// <summary>
        /// Category ID
        /// </summary>
        public Guid CategoryId { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public CategoryDescription()
        {
        }

        /// <summary>
        /// Creates a new category description
        /// </summary>
        public CategoryDescription(Guid categoryId, string name, string? description,
            string? metaTitle, string? metaDescription, string? metaKeywords,
            string? slug, string languageCode, Guid? storeId = null)
        {
            CategoryId = categoryId;
            Name = name;
            Description = description;
            MetaTitle = metaTitle;
            MetaDescription = metaDescription;
            MetaKeywords = metaKeywords;
            Slug = slug;
            LanguageCode = languageCode;
            StoreId = storeId;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Generates SEO-friendly slug from name
        /// </summary>
        public void GenerateSlug()
        {
            if (string.IsNullOrWhiteSpace(Name)) return;

            Slug = Name.ToLowerInvariant()
                      .Replace(" ", "-")
                      .Replace("_", "-")
                      .Trim('-');

            MarkAsUpdated();
        }

        /// <summary>
        /// Updates SEO meta information
        /// </summary>
        public void UpdateSeoMeta(string? metaTitle = null, string? metaDescription = null, string? metaKeywords = null)
        {
            if (metaTitle != null) MetaTitle = metaTitle;
            if (metaDescription != null) MetaDescription = metaDescription;
            if (metaKeywords != null) MetaKeywords = metaKeywords;

            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"CategoryDescription [CategoryId: {CategoryId}, Language: {LanguageCode}, Name: {Name}]";
        }
    }
}
