using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;

namespace EtyraCommerce.Application.Services.User
{
    /// <summary>
    /// Interface for User Process Service - Contains business logic methods
    /// Called by CQRS Handlers for actual business processing
    /// Inherits generic service capabilities for CRUD operations
    /// </summary>
    public interface IUserProcessService : IService<Domain.Entities.User.User, UserDto>
    {
        #region Authentication & Authorization

        /// <summary>
        /// Processes user login with business logic
        /// </summary>
        /// <param name="emailOrUsername">Email or username</param>
        /// <param name="password">Password</param>
        /// <returns>User DTO if successful</returns>
        Task<CustomResponseDto<UserDto>> ProcessLoginAsync(string emailOrUsername, string password);

        /// <summary>
        /// Processes user login and returns User entity for JWT token generation
        /// </summary>
        /// <param name="emailOrUsername">Email or username</param>
        /// <param name="password">Password</param>
        /// <returns>User entity if successful</returns>
        Task<CustomResponseDto<Domain.Entities.User.User>> ProcessLoginForTokenAsync(string emailOrUsername, string password);

        /// <summary>
        /// Processes user registration with business logic
        /// </summary>
        /// <param name="registerDto">Registration data</param>
        /// <returns>Created user DTO</returns>
        Task<CustomResponseDto<UserDto>> ProcessRegisterAsync(CreateUserDto registerDto);

        /// <summary>
        /// Processes password change with business logic
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessChangePasswordAsync(Guid userId, string currentPassword, string newPassword);

        /// <summary>
        /// Processes password reset with business logic
        /// </summary>
        /// <param name="email">Email address</param>
        /// <param name="token">Reset token</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessResetPasswordAsync(string email, string token, string newPassword);

        /// <summary>
        /// Generates password reset token
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessGeneratePasswordResetTokenAsync(string email);

        #endregion

        #region Account Management

        /// <summary>
        /// Processes user activation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessActivateUserAsync(Guid userId);

        /// <summary>
        /// Processes user deactivation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessDeactivateUserAsync(Guid userId);

        /// <summary>
        /// Processes email confirmation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="token">Confirmation token</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessConfirmEmailAsync(Guid userId, string token);

        /// <summary>
        /// Processes phone confirmation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="token">Confirmation token</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessConfirmPhoneAsync(Guid userId, string token);

        /// <summary>
        /// Processes user locking
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="lockUntil">Lock until date</param>
        /// <param name="reason">Lock reason</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessLockUserAsync(Guid userId, DateTime lockUntil, string reason);

        /// <summary>
        /// Processes user unlocking
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessUnlockUserAsync(Guid userId);

        #endregion

        #region User Queries

        /// <summary>
        /// Gets user by email with business logic
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>User DTO if found</returns>
        Task<CustomResponseDto<UserDto?>> ProcessGetUserByEmailAsync(string email);

        /// <summary>
        /// Gets user by username with business logic
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User DTO if found</returns>
        Task<CustomResponseDto<UserDto?>> ProcessGetUserByUsernameAsync(string username);

        /// <summary>
        /// Validates if email exists
        /// </summary>
        /// <param name="email">Email address</param>
        /// <param name="excludeUserId">User ID to exclude from check</param>
        /// <returns>True if exists</returns>
        Task<CustomResponseDto<bool>> ProcessEmailExistsAsync(string email, Guid? excludeUserId = null);

        /// <summary>
        /// Validates if username exists
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="excludeUserId">User ID to exclude from check</param>
        /// <returns>True if exists</returns>
        Task<CustomResponseDto<bool>> ProcessUsernameExistsAsync(string username, Guid? excludeUserId = null);

        /// <summary>
        /// Searches users with business logic
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>Paged user results</returns>
        Task<CustomResponseDto<PagedResult<UserDto>>> ProcessSearchUsersAsync(UserSearchDto searchDto);

        #endregion

        #region Statistics & Analytics

        /// <summary>
        /// Gets user statistics with business logic
        /// </summary>
        /// <returns>User statistics</returns>
        Task<CustomResponseDto<UserStatisticsDto>> ProcessGetUserStatisticsAsync();

        /// <summary>
        /// Gets user login history with business logic
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paged login history</returns>
        Task<CustomResponseDto<PagedResult<UserLoginHistoryDto>>> ProcessGetUserLoginHistoryAsync(Guid userId, int pageNumber = 1, int pageSize = 20);

        #endregion
    }
}
