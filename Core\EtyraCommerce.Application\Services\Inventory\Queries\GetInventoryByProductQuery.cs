using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Queries
{
    /// <summary>
    /// Query to get inventory by product ID
    /// </summary>
    public class GetInventoryByProductQuery : IRequest<CustomResponseDto<List<InventoryDto>>>
    {
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public bool ActiveWarehousesOnly { get; set; } = true;
    }
}
