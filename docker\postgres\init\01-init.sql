-- EtyraCommerce Database Initialization Script

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create schemas for future multi-tenant support
CREATE SCHEMA IF NOT EXISTS etyra_core;
CREATE SCHEMA IF NOT EXISTS etyra_modules;

-- Set default search path
ALTER DATABASE etyracommerce SET search_path TO etyra_core, etyra_modules, public;

-- Create basic audit columns function
CREATE OR REPLACE FUNCTION etyra_core.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create basic audit table template
CREATE TABLE IF NOT EXISTS etyra_core.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON etyra_core.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON etyra_core.audit_log(created_at);

-- Grant permissions
GRANT USAGE ON SCHEMA etyra_core TO etyra;
GRANT USAGE ON SCHEMA etyra_modules TO etyra;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA etyra_core TO etyra;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA etyra_modules TO etyra;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA etyra_core TO etyra;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA etyra_modules TO etyra;
