namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Address value object representing a physical address
    /// </summary>
    public class Address : IEquatable<Address>
    {
        public string Street { get; private set; }
        public string City { get; private set; }
        public string State { get; private set; }
        public string PostalCode { get; private set; }
        public string Country { get; private set; }
        public string? AddressLine2 { get; private set; }

        // Parameterless constructor for EF Core
        private Address()
        {
            Street = string.Empty;
            City = string.Empty;
            State = string.Empty;
            PostalCode = string.Empty;
            Country = string.Empty;
        }

        public Address(string street, string city, string state, string postalCode, string country, string? addressLine2 = null)
        {
            if (string.IsNullOrWhiteSpace(street))
                throw new ArgumentException("Street cannot be empty", nameof(street));
            if (string.IsNullOrWhiteSpace(city))
                throw new ArgumentException("City cannot be empty", nameof(city));
            if (string.IsNullOrWhiteSpace(state))
                throw new ArgumentException("State cannot be empty", nameof(state));
            if (string.IsNullOrWhiteSpace(postalCode))
                throw new ArgumentException("Postal code cannot be empty", nameof(postalCode));
            if (string.IsNullOrWhiteSpace(country))
                throw new ArgumentException("Country cannot be empty", nameof(country));

            Street = street.Trim();
            City = city.Trim();
            State = state.Trim();
            PostalCode = postalCode.Trim();
            Country = country.Trim();
            AddressLine2 = string.IsNullOrWhiteSpace(addressLine2) ? null : addressLine2.Trim();
        }

        /// <summary>
        /// Creates an address from components
        /// </summary>
        public static Address Create(string street, string city, string state, string postalCode, string country, string? addressLine2 = null)
        {
            return new Address(street, city, state, postalCode, country, addressLine2);
        }

        /// <summary>
        /// Gets the full address as a formatted string
        /// </summary>
        public string GetFullAddress()
        {
            var parts = new List<string> { Street };

            if (!string.IsNullOrEmpty(AddressLine2))
                parts.Add(AddressLine2);

            parts.Add($"{City}, {State} {PostalCode}");
            parts.Add(Country);

            return string.Join(Environment.NewLine, parts);
        }

        /// <summary>
        /// Gets the address as a single line
        /// </summary>
        public string GetSingleLineAddress()
        {
            var parts = new List<string> { Street };

            if (!string.IsNullOrEmpty(AddressLine2))
                parts.Add(AddressLine2);

            parts.Add($"{City}, {State} {PostalCode}, {Country}");

            return string.Join(", ", parts);
        }

        /// <summary>
        /// Checks if this is a valid shipping address
        /// </summary>
        public bool IsValidForShipping()
        {
            return !string.IsNullOrWhiteSpace(Street) &&
                   !string.IsNullOrWhiteSpace(City) &&
                   !string.IsNullOrWhiteSpace(State) &&
                   !string.IsNullOrWhiteSpace(PostalCode) &&
                   !string.IsNullOrWhiteSpace(Country);
        }

        #region Equality

        public bool Equals(Address? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;

            return Street.Equals(other.Street, StringComparison.OrdinalIgnoreCase) &&
                   City.Equals(other.City, StringComparison.OrdinalIgnoreCase) &&
                   State.Equals(other.State, StringComparison.OrdinalIgnoreCase) &&
                   PostalCode.Equals(other.PostalCode, StringComparison.OrdinalIgnoreCase) &&
                   Country.Equals(other.Country, StringComparison.OrdinalIgnoreCase) &&
                   (AddressLine2?.Equals(other.AddressLine2, StringComparison.OrdinalIgnoreCase) ?? other.AddressLine2 == null);
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as Address);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(
                Street.ToLowerInvariant(),
                City.ToLowerInvariant(),
                State.ToLowerInvariant(),
                PostalCode.ToLowerInvariant(),
                Country.ToLowerInvariant(),
                AddressLine2?.ToLowerInvariant());
        }

        public static bool operator ==(Address? left, Address? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(Address? left, Address? right)
        {
            return !Equals(left, right);
        }

        #endregion

        public override string ToString()
        {
            return GetSingleLineAddress();
        }
    }
}
