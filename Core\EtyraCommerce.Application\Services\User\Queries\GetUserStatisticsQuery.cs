using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for getting user statistics for admin dashboard
    /// </summary>
    public class GetUserStatisticsQuery : IRequest<CustomResponseDto<UserStatisticsDto>>
    {
        /// <summary>
        /// Date range start for statistics calculation
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Date range end for statistics calculation
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Whether to include deleted users in statistics
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Whether to include detailed breakdown by time periods
        /// </summary>
        public bool IncludeDetailedBreakdown { get; set; } = true;

        public GetUserStatisticsQuery() { }

        public GetUserStatisticsQuery(DateTime? fromDate = null, DateTime? toDate = null, bool includeDeleted = false)
        {
            FromDate = fromDate;
            ToDate = toDate;
            IncludeDeleted = includeDeleted;
        }
    }
}
