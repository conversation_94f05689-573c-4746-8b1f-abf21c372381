using EtyraCommerce.Application.DTOs.Authentication;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.Authentication;
using EtyraCommerce.Application.Services.User;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// User management API controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    [EnableCors("AllowAll")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IUserProcessService _userProcessService;
        private readonly IJwtService _jwtService;
        private readonly ILogger<UserController> _logger;

        public UserController(
            IUserService userService,
            IUserProcessService userProcessService,
            IJwtService jwtService,
            ILogger<UserController> logger)
        {
            _userService = userService;
            _userProcessService = userProcessService;
            _jwtService = jwtService;
            _logger = logger;
        }

        /// <summary>
        /// User login with email/username and password
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>JWT token response</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 500)]
        public async Task<IActionResult> Login([FromBody] UserLoginDto loginDto)
        {
            try
            {
                _logger.LogInformation("Login attempt for user: {EmailOrUsername}", loginDto.EmailOrUsername);

                // Get user entity for JWT token generation
                var userResult = await _userProcessService.ProcessLoginForTokenAsync(loginDto.EmailOrUsername, loginDto.Password);
                if (!userResult.IsSuccess)
                {
                    return StatusCode(userResult.StatusCode, CustomResponseDto<TokenResponseDto>.Failure(
                        userResult.StatusCode, userResult.Message, userResult.Message));
                }

                var user = userResult.Data;
                if (user == null)
                {
                    return BadRequest(CustomResponseDto<TokenResponseDto>.BadRequest("Invalid login response"));
                }

                // Generate JWT token
                var tokenResult = await _jwtService.GenerateTokenAsync(user, GetClientIpAddress(), Request.Headers["User-Agent"].ToString());

                return StatusCode(tokenResult.StatusCode, tokenResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {EmailOrUsername}", loginDto.EmailOrUsername);
                return StatusCode(500, CustomResponseDto<TokenResponseDto>.InternalServerError("An error occurred during login"));
            }
        }

        /// <summary>
        /// Register a new user account
        /// </summary>
        /// <param name="registerDto">Registration data</param>
        /// <returns>User registration result</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 201)]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 500)]
        public async Task<IActionResult> Register([FromBody] CreateUserDto registerDto)
        {
            try
            {
                _logger.LogInformation("Registration attempt for email: {Email}", registerDto.Email);

                var result = await _userService.RegisterAsync(registerDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", registerDto.Email);
                return StatusCode(500, CustomResponseDto<UserDto>.InternalServerError("An error occurred during registration"));
            }
        }

        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="changePasswordDto">Password change data</param>
        /// <returns>Password change result</returns>
        [HttpPost("change-password")]
        [Authorize]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 401)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 500)]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(CustomResponseDto<NoContentDto>.Unauthorized("User not authenticated"));
                }

                _logger.LogInformation("Password change attempt for user: {UserId}", userId);

                var result = await _userService.ChangePasswordAsync(userId.Value, changePasswordDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password change");
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred during password change"));
            }
        }

        /// <summary>
        /// Refresh JWT access token using refresh token
        /// </summary>
        /// <param name="refreshTokenDto">Refresh token data</param>
        /// <returns>New JWT token response</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<TokenResponseDto>), 500)]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto)
        {
            try
            {
                _logger.LogInformation("Token refresh attempt");

                // Set IP address and user agent
                refreshTokenDto.IpAddress = GetClientIpAddress();
                refreshTokenDto.UserAgent = Request.Headers["User-Agent"].ToString();

                var result = await _jwtService.RefreshTokenAsync(refreshTokenDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, CustomResponseDto<TokenResponseDto>.InternalServerError("An error occurred during token refresh"));
            }
        }

        /// <summary>
        /// Revoke refresh token (logout)
        /// </summary>
        /// <param name="revokeTokenDto">Revoke token data</param>
        /// <returns>Token revocation result</returns>
        [HttpPost("revoke-token")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 500)]
        public async Task<IActionResult> RevokeToken([FromBody] RevokeTokenDto revokeTokenDto)
        {
            try
            {
                _logger.LogInformation("Token revocation attempt");

                // Set IP address and user agent
                revokeTokenDto.IpAddress = GetClientIpAddress();
                revokeTokenDto.UserAgent = Request.Headers["User-Agent"].ToString();

                var result = await _jwtService.RevokeTokenAsync(revokeTokenDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token revocation");
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred during token revocation"));
            }
        }

        /// <summary>
        /// Logout from all devices (revoke all refresh tokens)
        /// </summary>
        /// <returns>Logout result</returns>
        [HttpPost("logout-all")]
        [Authorize]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 401)]
        [ProducesResponseType(typeof(CustomResponseDto<NoContentDto>), 500)]
        public async Task<IActionResult> LogoutAll()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(CustomResponseDto<NoContentDto>.Unauthorized("User not authenticated"));
                }

                _logger.LogInformation("Logout all devices for user: {UserId}", userId);

                var result = await _jwtService.RevokeAllUserTokensAsync(userId.Value, "Logout from all devices");
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout all");
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred during logout"));
            }
        }

        /// <summary>
        /// Get current user profile
        /// </summary>
        /// <returns>Current user data</returns>
        [HttpGet("profile")]
        [Authorize]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 401)]
        [ProducesResponseType(typeof(CustomResponseDto<UserDto>), 500)]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(CustomResponseDto<UserDto>.Unauthorized("User not authenticated"));
                }

                _logger.LogInformation("Profile request for user: {UserId}", userId);

                var result = await _userService.GetByIdAsync(userId.Value);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile");
                return StatusCode(500, CustomResponseDto<UserDto>.InternalServerError("An error occurred while getting profile"));
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Gets the current user ID from JWT token
        /// </summary>
        /// <returns>User ID if authenticated, null otherwise</returns>
        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        /// <summary>
        /// Gets the client IP address
        /// </summary>
        /// <returns>Client IP address</returns>
        private string? GetClientIpAddress()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString();
        }



        #endregion
    }
}
