using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Commands;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Product management API controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [EnableCors("AllowAll")]
    public class ProductController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ProductController> _logger;

        public ProductController(IMediator mediator, ILogger<ProductController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all products with pagination and filtering
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchTerm">Search term</param>
        /// <param name="categoryId">Category ID</param>
        /// <param name="isActive">Active status filter</param>
        /// <param name="isFeatured">Featured status filter</param>
        /// <returns>Paginated list of products</returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<CustomResponseDto<PagedResult<ProductDto>>>> GetProducts(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? searchTerm = null,
            [FromQuery] Guid? categoryId = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool? isFeatured = null)
        {
            try
            {
                var query = new GetAllProductsQuery
                {
                    Page = page,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    CategoryId = categoryId,
                    IsActive = isActive,
                    IsFeatured = isFeatured
                };
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting products");
                return StatusCode(500, CustomResponseDto<PagedResult<ProductDto>>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Product details</returns>
        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> GetProduct(Guid id)
        {
            try
            {
                var query = new GetProductByIdQuery { ProductId = id };
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return NotFound(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting product with ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Search products
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Search results</returns>
        [HttpPost("search")]
        [AllowAnonymous]
        public async Task<ActionResult<CustomResponseDto<PagedResult<ProductDto>>>> SearchProducts(
            [FromBody] string searchTerm,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = new SearchProductsQuery
                {
                    SearchTerm = searchTerm,
                    Page = page,
                    PageSize = pageSize
                };
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while searching products");
                return StatusCode(500, CustomResponseDto<PagedResult<ProductDto>>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Get products by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="includeChildCategories">Include child categories</param>
        /// <returns>Products in category</returns>
        [HttpGet("category/{categoryId}")]
        [AllowAnonymous]
        public async Task<ActionResult<CustomResponseDto<PagedResult<ProductDto>>>> GetProductsByCategory(
            Guid categoryId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] bool includeChildCategories = false)
        {
            try
            {
                var query = new GetProductsByCategoryQuery
                {
                    CategoryId = categoryId,
                    Page = page,
                    PageSize = pageSize,
                    IncludeChildCategories = includeChildCategories
                };
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting products by category: {CategoryId}", categoryId);
                return StatusCode(500, CustomResponseDto<PagedResult<ProductDto>>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Create new product
        /// </summary>
        /// <param name="command">Product creation command</param>
        /// <returns>Created product</returns>
        [HttpPost]
        [AllowAnonymous] // TODO: Remove this for production
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> CreateProduct([FromBody] CreateProductCommand command)
        {
            try
            {
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return CreatedAtAction(nameof(GetProduct), new { id = result.Data.Id }, result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating product");
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Update existing product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="command">Product update command</param>
        /// <returns>Updated product</returns>
        [HttpPut("{id}")]
        [AllowAnonymous] // TODO: Remove this for production
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> UpdateProduct(Guid id, [FromBody] UpdateProductCommand command)
        {
            try
            {
                command.ProductId = id;
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating product with ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Delete product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<CustomResponseDto<NoContentDto>>> DeleteProduct(Guid id)
        {
            try
            {
                var command = new DeleteProductCommand { ProductId = id };
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting product with ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<NoContentDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Update product stock
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="command">Stock update command</param>
        /// <returns>Update result</returns>
        [HttpPatch("{id}/stock")]
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> UpdateProductStock(Guid id, [FromBody] UpdateProductStockCommand command)
        {
            try
            {
                command.ProductId = id;
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating product stock for ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Update product price
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="command">Price update command</param>
        /// <returns>Update result</returns>
        [HttpPatch("{id}/price")]
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> UpdateProductPrice(Guid id, [FromBody] UpdateProductPriceCommand command)
        {
            try
            {
                command.ProductId = id;
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating product price for ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }

        /// <summary>
        /// Update product status
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="command">Status update command</param>
        /// <returns>Update result</returns>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult<CustomResponseDto<ProductDto>>> UpdateProductStatus(Guid id, [FromBody] UpdateProductStatusCommand command)
        {
            try
            {
                command.ProductId = id;
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return Ok(result);
                }

                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating product status for ID: {ProductId}", id);
                return StatusCode(500, CustomResponseDto<ProductDto>.Failure(500, "Internal server error occurred"));
            }
        }
    }
}
