using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Commands
{
    /// <summary>
    /// Handler for creating a new product
    /// </summary>
    public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, CustomResponseDto<ProductDto>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<CreateProductCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public CreateProductCommandHandler(
            IProductProcessService productProcessService,
            ILogger<CreateProductCommandHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the create product command
        /// </summary>
        /// <param name="request">Create product command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Product DTO response</returns>
        public async Task<CustomResponseDto<ProductDto>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing create product command for Model: {Model}, Name: {Name}",
                    request.Model, request.Name);

                // Validation
                if (string.IsNullOrWhiteSpace(request.Model))
                    return CustomResponseDto<ProductDto>.BadRequest("Product model is required");

                if (string.IsNullOrWhiteSpace(request.Name))
                    return CustomResponseDto<ProductDto>.BadRequest("Product name is required");

                if (string.IsNullOrWhiteSpace(request.SKU))
                    return CustomResponseDto<ProductDto>.BadRequest("Product SKU is required");

                if (request.BasePrice <= 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Base price must be greater than 0");

                if (string.IsNullOrWhiteSpace(request.BasePriceCurrency))
                    return CustomResponseDto<ProductDto>.BadRequest("Base price currency is required");

                if (string.IsNullOrWhiteSpace(request.DefaultCurrency))
                    return CustomResponseDto<ProductDto>.BadRequest("Default currency is required");

                // Validate sale price if provided
                if (request.SalePrice.HasValue && request.SalePrice >= request.BasePrice)
                    return CustomResponseDto<ProductDto>.BadRequest("Sale price must be less than base price");

                // Validate cost if provided
                if (request.Cost.HasValue && request.Cost > request.BasePrice)
                    return CustomResponseDto<ProductDto>.BadRequest("Cost should not exceed base price");

                // Validate sale dates if provided
                if (request.SaleStartDate.HasValue && request.SaleEndDate.HasValue &&
                    request.SaleStartDate > request.SaleEndDate)
                    return CustomResponseDto<ProductDto>.BadRequest("Sale start date cannot be after sale end date");

                // Validate availability dates if provided
                if (request.AvailableStartDate.HasValue && request.AvailableEndDate.HasValue &&
                    request.AvailableStartDate > request.AvailableEndDate)
                    return CustomResponseDto<ProductDto>.BadRequest("Available start date cannot be after available end date");

                // Validate stock quantities
                if (request.TotalStockQuantity < 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Stock quantity cannot be negative");

                if (request.MinStockAlert < 0)
                    return CustomResponseDto<ProductDto>.BadRequest("Min stock alert cannot be negative");

                // Validate EAN format if provided
                if (!string.IsNullOrWhiteSpace(request.EAN) && request.EAN.Length != 13)
                    return CustomResponseDto<ProductDto>.BadRequest("EAN must be exactly 13 digits");

                // Validate rating if provided
                if (request.Stars.HasValue && (request.Stars < 1 || request.Stars > 5))
                    return CustomResponseDto<ProductDto>.BadRequest("Stars must be between 1 and 5");

                // Create DTO for business logic
                var createDto = new CreateProductDto
                {
                    Model = request.Model,
                    Name = request.Name,
                    Stars = request.Stars,
                    AiPlatformId = request.AiPlatformId,
                    EAN = request.EAN,
                    MPN = request.MPN,
                    Barcode = request.Barcode,
                    Brand = request.Brand,
                    UPC = request.UPC,
                    SKU = request.SKU,
                    BasePrice = request.BasePrice,
                    BasePriceCurrency = request.BasePriceCurrency,
                    Cost = request.Cost,
                    CostCurrency = request.CostCurrency,
                    SalePrice = request.SalePrice,
                    SalePriceCurrency = request.SalePriceCurrency,
                    DefaultCurrency = request.DefaultCurrency,
                    Dimensions = request.Dimensions,
                    MainImage = request.MainImage,
                    ThumbnailImage = request.ThumbnailImage,
                    Status = request.Status,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    IsDigital = request.IsDigital,
                    RequiresShipping = request.RequiresShipping,
                    IsTaxable = request.IsTaxable,
                    Slug = request.Slug,
                    MetaTitle = request.MetaTitle,
                    MetaDescription = request.MetaDescription,
                    MetaKeywords = request.MetaKeywords,
                    AvailableStartDate = request.AvailableStartDate,
                    AvailableEndDate = request.AvailableEndDate,
                    SaleStartDate = request.SaleStartDate,
                    SaleEndDate = request.SaleEndDate,
                    DiscontinueDate = request.DiscontinueDate,
                    PrimaryCategoryId = request.PrimaryCategoryId,
                    CategoryIds = request.CategoryIds,
                    Descriptions = request.Descriptions,
                    Images = request.Images,
                    Attributes = request.Attributes
                };

                // Delegate to business logic service
                var result = await _productProcessService.ProcessCreateProductAsync(createDto);

                _logger.LogInformation("Create product command processed successfully for Model: {Model}, ProductId: {ProductId}",
                    request.Model, result.Data?.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing create product command for Model: {Model}, Name: {Name}",
                    request.Model, request.Name);
                return CustomResponseDto<ProductDto>.InternalServerError("An error occurred while creating the product");
            }
        }
    }
}
