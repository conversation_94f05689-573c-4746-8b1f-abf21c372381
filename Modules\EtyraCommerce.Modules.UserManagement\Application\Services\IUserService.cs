using EtyraCommerce.Modules.UserManagement.Application.DTOs;

namespace EtyraCommerce.Modules.UserManagement.Application.Services
{
    /// <summary>
    /// User Service Interface
    /// Defines all user-related business operations
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Get paginated list of users
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paginated user list</returns>
        Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details or null if not found</returns>
        Task<UserDto?> GetUserByIdAsync(Guid id);

        /// <summary>
        /// Get user by email address
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>User details or null if not found</returns>
        Task<UserDto?> GetUserByEmailAsync(string email);

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="createUserDto">User creation data</param>
        /// <returns>Service result with created user data</returns>
        Task<ServiceResult<UserDto>> CreateUserAsync(CreateUserDto createUserDto);

        /// <summary>
        /// Update an existing user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="updateUserDto">User update data</param>
        /// <returns>Service result with updated user data</returns>
        Task<ServiceResult<UserDto>> UpdateUserAsync(Guid id, UpdateUserDto updateUserDto);

        /// <summary>
        /// Delete a user (soft delete)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Service result indicating success or failure</returns>
        Task<ServiceResult> DeleteUserAsync(Guid id);

        /// <summary>
        /// Search users by name or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated search results</returns>
        Task<PagedResult<UserDto>> SearchUsersAsync(string searchTerm, int pageNumber, int pageSize);

        /// <summary>
        /// Activate a user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> ActivateUserAsync(Guid id);

        /// <summary>
        /// Deactivate a user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> DeactivateUserAsync(Guid id);

        /// <summary>
        /// Confirm user email address
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="confirmationToken">Email confirmation token</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> ConfirmEmailAsync(Guid id, string confirmationToken);

        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> ChangePasswordAsync(Guid id, string currentPassword, string newPassword);

        /// <summary>
        /// Reset user password
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> ResetPasswordAsync(string email);

        /// <summary>
        /// Assign roles to user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="roles">List of role names</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> AssignRolesAsync(Guid id, List<string> roles);

        /// <summary>
        /// Remove roles from user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="roles">List of role names to remove</param>
        /// <returns>Service result</returns>
        Task<ServiceResult> RemoveRolesAsync(Guid id, List<string> roles);

        /// <summary>
        /// Get user statistics
        /// </summary>
        /// <returns>User statistics</returns>
        Task<UserStatisticsDto> GetUserStatisticsAsync();
    }

    /// <summary>
    /// User statistics DTO
    /// </summary>
    public class UserStatisticsDto
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int UnconfirmedEmails { get; set; }
        public int NewUsersThisMonth { get; set; }
        public int NewUsersToday { get; set; }
    }
}
