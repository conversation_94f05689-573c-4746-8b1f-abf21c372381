﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Shipping;

public class GoogleShoppingCountry : BaseEntity
{
    [MaxLength(50)]
    public string? CountryName { get; set; }
    [MaxLength(5)]
    public string? CountryCode { get; set; }
    [MaxLength(4)]
    public string? CountryCurrency { get; set; }
    public int MinDeliveryDay { get; set; } = 0;
    public int MaxDeliveryDay { get; set; }
    public int ShippingCostType { get; set; } // = > 1: Flat 2: Weight
    public string? ShippingCost { get; set; } // Dize olarak  kayıt edip  parse edilecek
    public decimal FreeShippingCost { get; set; }
    public decimal StoreCurrencyValue { get; set; }

    public Store store { get; set; }
    public int StoreId { get; set; }

}
