using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Queries
{
    /// <summary>
    /// Query to search orders with advanced filtering
    /// </summary>
    public class SearchOrdersQuery : IRequest<CustomResponseDto<PagedResult<OrderDto>>>
    {
        /// <summary>
        /// Search term (order number, customer name, email)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Customer ID filter
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Order status filter
        /// </summary>
        public OrderStatus? Status { get; set; }

        /// <summary>
        /// Payment status filter
        /// </summary>
        public PaymentStatus? PaymentStatus { get; set; }

        /// <summary>
        /// Shipping status filter
        /// </summary>
        public ShippingStatus? ShippingStatus { get; set; }

        /// <summary>
        /// Minimum order total
        /// </summary>
        public decimal? MinTotal { get; set; }

        /// <summary>
        /// Maximum order total
        /// </summary>
        public decimal? MaxTotal { get; set; }

        /// <summary>
        /// Currency filter
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Start date for order creation filter
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for order creation filter
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Payment method filter
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Shipping method filter
        /// </summary>
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Include order items in results
        /// </summary>
        public bool IncludeItems { get; set; } = false;

        /// <summary>
        /// Include customer information in results
        /// </summary>
        public bool IncludeCustomer { get; set; } = false;

        /// <summary>
        /// Sort field
        /// </summary>
        public OrderSortField SortBy { get; set; } = OrderSortField.CreatedAt;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;
    }
}
