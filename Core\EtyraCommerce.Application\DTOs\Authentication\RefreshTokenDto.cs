using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Authentication
{
    /// <summary>
    /// DTO for refresh token operations
    /// </summary>
    public class RefreshTokenDto
    {
        /// <summary>
        /// Refresh token value
        /// </summary>
        [Required(ErrorMessage = "Refresh token is required")]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// IP address of the client making the request
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the client
        /// </summary>
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// DTO for revoking refresh tokens
    /// </summary>
    public class RevokeTokenDto
    {
        /// <summary>
        /// Refresh token to revoke
        /// </summary>
        [Required(ErrorMessage = "Refresh token is required")]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// Reason for revoking the token
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// IP address of the client making the request
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the client
        /// </summary>
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// DTO for token validation response
    /// </summary>
    public class TokenValidationDto
    {
        /// <summary>
        /// Indicates if the token is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// User ID associated with the token
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Username associated with the token
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Email associated with the token
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Token expiration date
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Roles associated with the token
        /// </summary>
        public List<string>? Roles { get; set; }

        /// <summary>
        /// Claims associated with the token
        /// </summary>
        public Dictionary<string, string>? Claims { get; set; }

        /// <summary>
        /// Error message if token is invalid
        /// </summary>
        public string? ErrorMessage { get; set; }

        public TokenValidationDto()
        {
            Roles = new List<string>();
            Claims = new Dictionary<string, string>();
        }
    }
}
