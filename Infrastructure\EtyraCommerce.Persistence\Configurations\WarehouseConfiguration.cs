using EtyraCommerce.Domain.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Warehouse entity
    /// </summary>
    public class WarehouseConfiguration : IEntityTypeConfiguration<Warehouse>
    {
        public void Configure(EntityTypeBuilder<Warehouse> builder)
        {
            // Table configuration
            builder.ToTable("Warehouses", "etyra_inventory");

            // Primary key
            builder.HasKey(w => w.Id);

            // Properties
            builder.Property(w => w.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(w => w.Code)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(w => w.Description)
                .HasMaxLength(500);

            builder.Property(w => w.Phone)
                .HasMaxLength(20);

            builder.Property(w => w.Email)
                .HasMaxLength(100);

            builder.Property(w => w.ManagerName)
                .HasMaxLength(100);

            builder.Property(w => w.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(w => w.IsMain)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(w => w.Type)
                .IsRequired()
                .HasConversion<int>()
                .HasDefaultValue(WarehouseType.Physical);

            builder.Property(w => w.SortOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Address value object configuration
            builder.OwnsOne(w => w.Address, address =>
            {
                address.Property(a => a.Street)
                    .HasColumnName("AddressStreet")
                    .HasMaxLength(200);

                address.Property(a => a.City)
                    .HasColumnName("AddressCity")
                    .HasMaxLength(100);

                address.Property(a => a.State)
                    .HasColumnName("AddressState")
                    .HasMaxLength(100);

                address.Property(a => a.PostalCode)
                    .HasColumnName("AddressPostalCode")
                    .HasMaxLength(20);

                address.Property(a => a.Country)
                    .HasColumnName("AddressCountry")
                    .HasMaxLength(100);
            });

            // Indexes
            builder.HasIndex(w => w.Code)
                .IsUnique()
                .HasDatabaseName("IX_Warehouses_Code");

            builder.HasIndex(w => w.Name)
                .HasDatabaseName("IX_Warehouses_Name");

            builder.HasIndex(w => w.IsActive)
                .HasDatabaseName("IX_Warehouses_IsActive");

            builder.HasIndex(w => w.Type)
                .HasDatabaseName("IX_Warehouses_Type");

            // Relationships
            builder.HasMany(w => w.InventoryItems)
                .WithOne(i => i.Warehouse)
                .HasForeignKey(i => i.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // Audit fields (inherited from AuditableBaseEntity)
            builder.Property(w => w.CreatedAt)
                .IsRequired();

            builder.Property(w => w.UpdatedAt);

            builder.Property(w => w.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(w => w.DeletedAt);

            builder.Property(w => w.RowVersion)
                .IsRowVersion();

            // Global query filter for soft delete
            builder.HasQueryFilter(w => !w.IsDeleted);
        }
    }
}
