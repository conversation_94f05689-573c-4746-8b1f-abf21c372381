using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Inventory transaction controller for audit and reporting
    /// </summary>
    [ApiController]
    [Route("api/inventory-transactions")]
    [Authorize]
    public class InventoryTransactionController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<InventoryTransactionController> _logger;

        public InventoryTransactionController(IInventoryService inventoryService, ILogger<InventoryTransactionController> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
        }

        /// <summary>
        /// Get inventory transactions with pagination and filtering
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(CustomResponseDto<PagedResult<InventoryTransactionDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<PagedResult<InventoryTransactionDto>>>> GetInventoryTransactions(
            [FromQuery] Guid? inventoryId = null,
            [FromQuery] Guid? productId = null,
            [FromQuery] Guid? warehouseId = null,
            [FromQuery] InventoryTransactionType? type = null,
            [FromQuery] string? reference = null,
            [FromQuery] string? referenceType = null,
            [FromQuery] Guid? userId = null,
            [FromQuery] DateTime? transactionDateFrom = null,
            [FromQuery] DateTime? transactionDateTo = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "TransactionDate",
            [FromQuery] string sortDirection = "DESC")
        {
            try
            {
                _logger.LogInformation("Getting inventory transactions with pagination");

                var filterDto = new InventoryTransactionFilterDto
                {
                    InventoryId = inventoryId,
                    ProductId = productId,
                    WarehouseId = warehouseId,
                    Type = type,
                    Reference = reference,
                    ReferenceType = referenceType,
                    UserId = userId,
                    TransactionDateFrom = transactionDateFrom,
                    TransactionDateTo = transactionDateTo,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _inventoryService.GetInventoryTransactionsAsync(filterDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions");
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory transactions"));
            }
        }

        /// <summary>
        /// Get inventory transactions for a specific product
        /// </summary>
        [HttpGet("product/{productId:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<PagedResult<InventoryTransactionDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<PagedResult<InventoryTransactionDto>>>> GetTransactionsByProduct(
            Guid productId,
            [FromQuery] Guid? warehouseId = null,
            [FromQuery] InventoryTransactionType? type = null,
            [FromQuery] DateTime? transactionDateFrom = null,
            [FromQuery] DateTime? transactionDateTo = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "TransactionDate",
            [FromQuery] string sortDirection = "DESC")
        {
            try
            {
                _logger.LogInformation("Getting inventory transactions for product: {ProductId}", productId);

                var filterDto = new InventoryTransactionFilterDto
                {
                    ProductId = productId,
                    WarehouseId = warehouseId,
                    Type = type,
                    TransactionDateFrom = transactionDateFrom,
                    TransactionDateTo = transactionDateTo,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _inventoryService.GetInventoryTransactionsAsync(filterDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions for product: {ProductId}", productId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory transactions"));
            }
        }

        /// <summary>
        /// Get inventory transactions for a specific warehouse
        /// </summary>
        [HttpGet("warehouse/{warehouseId:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<PagedResult<InventoryTransactionDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<PagedResult<InventoryTransactionDto>>>> GetTransactionsByWarehouse(
            Guid warehouseId,
            [FromQuery] Guid? productId = null,
            [FromQuery] InventoryTransactionType? type = null,
            [FromQuery] DateTime? transactionDateFrom = null,
            [FromQuery] DateTime? transactionDateTo = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "TransactionDate",
            [FromQuery] string sortDirection = "DESC")
        {
            try
            {
                _logger.LogInformation("Getting inventory transactions for warehouse: {WarehouseId}", warehouseId);

                var filterDto = new InventoryTransactionFilterDto
                {
                    WarehouseId = warehouseId,
                    ProductId = productId,
                    Type = type,
                    TransactionDateFrom = transactionDateFrom,
                    TransactionDateTo = transactionDateTo,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _inventoryService.GetInventoryTransactionsAsync(filterDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions for warehouse: {WarehouseId}", warehouseId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory transactions"));
            }
        }

        /// <summary>
        /// Get inventory transactions by reference (e.g., order ID)
        /// </summary>
        [HttpGet("reference/{reference}")]
        [ProducesResponseType(typeof(CustomResponseDto<PagedResult<InventoryTransactionDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<PagedResult<InventoryTransactionDto>>>> GetTransactionsByReference(
            string reference,
            [FromQuery] string? referenceType = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "TransactionDate",
            [FromQuery] string sortDirection = "DESC")
        {
            try
            {
                _logger.LogInformation("Getting inventory transactions for reference: {Reference}", reference);

                var filterDto = new InventoryTransactionFilterDto
                {
                    Reference = reference,
                    ReferenceType = referenceType,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _inventoryService.GetInventoryTransactionsAsync(filterDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions for reference: {Reference}", reference);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory transactions"));
            }
        }
    }
}
