﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Analysis;

public class Statistics : BaseEntity
{
    [MaxLength(50)]
    public string? TransactionBaseName { get; set; }
    [MaxLength(50)]
    public string? TransactionName { get; set; }
    public int? Quantity { get; set; } = 0;
    public int? StoreId { get; set; } = 0;
    public int BazaarId { get; set; } = 0;

    public decimal? TotalAmount { get; set; }
    public decimal? TotalCost { get; set; }
    public int? RelatedQuantity { get; set; }
    public int? RelatedStructure { get; set; }
    public bool Status { get; set; } = true;
}