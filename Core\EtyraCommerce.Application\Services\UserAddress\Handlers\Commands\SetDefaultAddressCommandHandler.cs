using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Commands
{
    /// <summary>
    /// Handler for setting an address as default
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class SetDefaultAddressCommandHandler : IRequestHandler<SetDefaultAddressCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<SetDefaultAddressCommandHandler> _logger;

        public SetDefaultAddressCommandHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<SetDefaultAddressCommandHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(SetDefaultAddressCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling SetDefaultAddressCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);

                // Validate request
                if (request.AddressId == Guid.Empty)
                {
                    _logger.LogWarning("SetDefaultAddressCommand received with empty AddressId for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<NoContentDto>.BadRequest("Address ID is required");
                }

                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("SetDefaultAddressCommand received with empty UserId for AddressId: {AddressId}", request.AddressId);
                    return CustomResponseDto<NoContentDto>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessSetDefaultAddressAsync(request.AddressId, request.UserId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("SetDefaultAddressCommand handled successfully for AddressId: {AddressId}, UserId: {UserId}",
                        request.AddressId, request.UserId);
                }
                else
                {
                    _logger.LogWarning("SetDefaultAddressCommand failed for AddressId: {AddressId}, UserId: {UserId}, Error: {Error}",
                        request.AddressId, request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling SetDefaultAddressCommand for AddressId: {AddressId}, UserId: {UserId}",
                    request.AddressId, request.UserId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while setting the default address");
            }
        }
    }
}
