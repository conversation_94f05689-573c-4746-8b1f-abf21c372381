using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to adjust stock quantity (physical count)
    /// </summary>
    public class AdjustStockCommand : IRequest<CustomResponseDto<bool>>
    {
        public Guid InventoryId { get; set; }
        public int NewQuantity { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public Guid? UserId { get; set; }
    }
}
