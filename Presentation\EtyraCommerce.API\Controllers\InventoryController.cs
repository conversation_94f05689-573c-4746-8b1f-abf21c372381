using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Inventory management controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class InventoryController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<InventoryController> _logger;

        public InventoryController(IInventoryService inventoryService, ILogger<InventoryController> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
        }

        #region Inventory Management

        /// <summary>
        /// Get inventory by ID
        /// </summary>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<InventoryDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 404)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<InventoryDto>>> GetInventoryById(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting inventory by ID: {InventoryId}", id);

                var result = await _inventoryService.GetInventoryByIdAsync(id);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        404 => NotFound(result),
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by ID: {InventoryId}", id);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory"));
            }
        }

        /// <summary>
        /// Get inventory by product ID
        /// </summary>
        [HttpGet("product/{productId:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<List<InventoryDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<List<InventoryDto>>>> GetInventoryByProduct(
            Guid productId,
            [FromQuery] Guid? warehouseId = null,
            [FromQuery] bool activeWarehousesOnly = true)
        {
            try
            {
                _logger.LogInformation("Getting inventory by product ID: {ProductId}", productId);

                var result = await _inventoryService.GetInventoryByProductAsync(productId, warehouseId, activeWarehousesOnly);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by product ID: {ProductId}", productId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving inventory"));
            }
        }

        /// <summary>
        /// Get stock status for a product
        /// </summary>
        [HttpGet("stock-status/{productId:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<StockStatusDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<StockStatusDto>>> GetStockStatus(
            Guid productId,
            [FromQuery] bool activeWarehousesOnly = true)
        {
            try
            {
                _logger.LogInformation("Getting stock status for product ID: {ProductId}", productId);

                var result = await _inventoryService.GetStockStatusAsync(productId, activeWarehousesOnly);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock status for product ID: {ProductId}", productId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving stock status"));
            }
        }

        /// <summary>
        /// Get low stock items
        /// </summary>
        [HttpGet("low-stock")]
        [ProducesResponseType(typeof(CustomResponseDto<List<LowStockItemDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<List<LowStockItemDto>>>> GetLowStockItems(
            [FromQuery] Guid? warehouseId = null,
            [FromQuery] bool activeWarehousesOnly = true,
            [FromQuery] int? maxItems = null)
        {
            try
            {
                _logger.LogInformation("Getting low stock items");

                var result = await _inventoryService.GetLowStockItemsAsync(warehouseId, activeWarehousesOnly, maxItems);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving low stock items"));
            }
        }

        /// <summary>
        /// Create new inventory
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(CustomResponseDto<InventoryDto>), 201)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<InventoryDto>>> CreateInventory([FromBody] CreateInventoryDto createDto)
        {
            try
            {
                _logger.LogInformation("Creating inventory for product {ProductId} in warehouse {WarehouseId}",
                    createDto.ProductId, createDto.WarehouseId);

                var result = await _inventoryService.CreateInventoryAsync(createDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return CreatedAtAction(nameof(GetInventoryById), new { id = result.Data!.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inventory for product {ProductId}", createDto.ProductId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while creating inventory"));
            }
        }

        /// <summary>
        /// Update inventory
        /// </summary>
        [HttpPut]
        [ProducesResponseType(typeof(CustomResponseDto<InventoryDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 404)]
        public async Task<ActionResult<CustomResponseDto<InventoryDto>>> UpdateInventory([FromBody] UpdateInventoryDto updateDto)
        {
            try
            {
                _logger.LogInformation("Updating inventory {InventoryId}", updateDto.Id);

                var result = await _inventoryService.UpdateInventoryAsync(updateDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        404 => NotFound(result),
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inventory {InventoryId}", updateDto.Id);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while updating inventory"));
            }
        }

        #endregion

        #region Stock Operations

        /// <summary>
        /// Reserve stock
        /// </summary>
        [HttpPost("reserve")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<bool>>> ReserveStock([FromBody] StockReservationDto reservationDto)
        {
            try
            {
                _logger.LogInformation("Reserving {Quantity} units of product {ProductId} for reference {Reference}",
                    reservationDto.Quantity, reservationDto.ProductId, reservationDto.Reference);

                var result = await _inventoryService.ReserveStockAsync(reservationDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reserving stock for product {ProductId}", reservationDto.ProductId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while reserving stock"));
            }
        }

        /// <summary>
        /// Allocate stock
        /// </summary>
        [HttpPost("allocate")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<bool>>> AllocateStock([FromBody] StockAllocationDto allocationDto)
        {
            try
            {
                _logger.LogInformation("Allocating {Quantity} units of product {ProductId} for reference {Reference}",
                    allocationDto.Quantity, allocationDto.ProductId, allocationDto.Reference);

                var result = await _inventoryService.AllocateStockAsync(allocationDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error allocating stock for product {ProductId}", allocationDto.ProductId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while allocating stock"));
            }
        }

        /// <summary>
        /// Release reservation
        /// </summary>
        [HttpPost("release")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<bool>>> ReleaseReservation([FromBody] ReleaseReservationRequest request)
        {
            try
            {
                _logger.LogInformation("Releasing reservation for reference {Reference}", request.Reference);

                var result = await _inventoryService.ReleaseReservationAsync(request.Reference, request.Reason, request.UserId);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error releasing reservation for reference {Reference}", request.Reference);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while releasing reservation"));
            }
        }

        /// <summary>
        /// Adjust stock
        /// </summary>
        [HttpPost("adjust")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<bool>>> AdjustStock([FromBody] StockAdjustmentDto adjustmentDto)
        {
            try
            {
                _logger.LogInformation("Adjusting stock for inventory {InventoryId} to {NewQuantity}",
                    adjustmentDto.InventoryId, adjustmentDto.NewQuantity);

                var result = await _inventoryService.AdjustStockAsync(adjustmentDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adjusting stock for inventory {InventoryId}", adjustmentDto.InventoryId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while adjusting stock"));
            }
        }

        /// <summary>
        /// Transfer stock between warehouses
        /// </summary>
        [HttpPost("transfer")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<bool>>> TransferStock([FromBody] StockTransferDto transferDto)
        {
            try
            {
                _logger.LogInformation("Transferring {Quantity} units of product {ProductId} from warehouse {FromWarehouseId} to {ToWarehouseId}",
                    transferDto.Quantity, transferDto.ProductId, transferDto.FromWarehouseId, transferDto.ToWarehouseId);

                var result = await _inventoryService.TransferStockAsync(transferDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring stock for product {ProductId}", transferDto.ProductId);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while transferring stock"));
            }
        }

        #endregion
    }
}
