﻿using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.Entities.Category;
using EtyraCommerce.Domain.Entities.Inventory;
using EtyraCommerce.Domain.Entities.Order;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Domain.Entities.User;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Linq.Expressions;

namespace EtyraCommerce.Persistence.Contexts
{
    /// <summary>
    /// EtyraCommerce main database context
    /// </summary>
    public class EtyraCommerceDbContext : DbContext
    {
        public EtyraCommerceDbContext(DbContextOptions<EtyraCommerceDbContext> options) : base(options)
        {
        }

        #region DbSets - Core Entities

        /// <summary>
        /// Users DbSet
        /// </summary>
        public DbSet<User> Users { get; set; } = null!;

        /// <summary>
        /// User Addresses DbSet
        /// </summary>
        public DbSet<UserAddress> UserAddresses { get; set; } = null!;

        /// <summary>
        /// Products DbSet
        /// </summary>
        public DbSet<Product> Products { get; set; } = null!;

        /// <summary>
        /// Product Descriptions DbSet
        /// </summary>
        public DbSet<ProductDescription> ProductDescriptions { get; set; } = null!;

        /// <summary>
        /// Product Categories DbSet (Many-to-Many relation)
        /// </summary>
        public DbSet<ProductCategory> ProductCategories { get; set; } = null!;

        /// <summary>
        /// Product Images DbSet
        /// </summary>
        public DbSet<ProductImage> ProductImages { get; set; } = null!;

        /// <summary>
        /// Product Discounts DbSet
        /// </summary>
        public DbSet<ProductDiscount> ProductDiscounts { get; set; } = null!;

        /// <summary>
        /// Warehouse Products DbSet
        /// </summary>
        public DbSet<WarehouseProduct> WarehouseProducts { get; set; } = null!;

        /// <summary>
        /// Product Variants DbSet
        /// </summary>
        public DbSet<ProductVariant> ProductVariants { get; set; } = null!;

        /// <summary>
        /// Categories DbSet
        /// </summary>
        public DbSet<Category> Categories { get; set; } = null!;

        /// <summary>
        /// Category Descriptions DbSet
        /// </summary>
        public DbSet<CategoryDescription> CategoryDescriptions { get; set; } = null!;

        /// <summary>
        /// Orders DbSet
        /// </summary>
        public DbSet<Order> Orders { get; set; } = null!;

        /// <summary>
        /// Order Items DbSet
        /// </summary>
        public DbSet<OrderItem> OrderItems { get; set; } = null!;

        #endregion

        #region DbSets - Inventory Entities

        /// <summary>
        /// Warehouses DbSet
        /// </summary>
        public DbSet<Warehouse> Warehouses { get; set; } = null!;

        /// <summary>
        /// Inventories DbSet
        /// </summary>
        public DbSet<Inventory> Inventories { get; set; } = null!;

        /// <summary>
        /// Inventory Transactions DbSet
        /// </summary>
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; } = null!;

        #endregion

        #region DbSets - Cart Entities

        /// <summary>
        /// Shopping Carts DbSet
        /// </summary>
        public DbSet<ShoppingCart> ShoppingCarts { get; set; } = null!;

        /// <summary>
        /// Cart Items DbSet
        /// </summary>
        public DbSet<CartItem> CartItems { get; set; } = null!;

        #endregion

        #region DbSets - Payment Entities

        /// <summary>
        /// Payment Methods DbSet
        /// </summary>
        public DbSet<PaymentMethod> PaymentMethods { get; set; } = null!;

        #endregion

        #region DbSets - Module Entities

        // Module entities will be added here
        // Example: public DbSet<ModuleEntity> ModuleEntities { get; set; }

        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply configurations from assemblies
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(EtyraCommerceDbContext).Assembly);

            // Global query filters for soft delete
            ApplyGlobalQueryFilters(modelBuilder);

            // Configure value conversions
            ConfigureValueConversions(modelBuilder);

            // Set default schema
            modelBuilder.HasDefaultSchema("etyra_core");
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

            // Enable sensitive data logging in development
#if DEBUG
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
#endif
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Handle audit fields before saving
            HandleAuditFields();

            return await base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            // Handle audit fields before saving
            HandleAuditFields();

            return base.SaveChanges();
        }

        #region Private Methods

        /// <summary>
        /// Applies global query filters for soft delete
        /// </summary>
        private void ApplyGlobalQueryFilters(ModelBuilder modelBuilder)
        {
            // Apply soft delete filter to all entities that inherit from BaseEntity
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var property = Expression.Property(parameter, nameof(BaseEntity.IsDeleted));
                    var filter = Expression.Lambda(Expression.Equal(property, Expression.Constant(false)), parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(filter);
                }
            }
        }

        /// <summary>
        /// Configures value conversions for custom value objects
        /// </summary>
        private void ConfigureValueConversions(ModelBuilder modelBuilder)
        {
            // Global value conversions are now handled in ValueObjectConversions class
            // and applied in specific entity configurations.
            // This method is kept for any global conversions that might be needed later.

            // Example of global conversion (if needed):
            // modelBuilder.Entity<BaseEntity>()
            //     .Property<Email>("Email")
            //     .HasConversion(ValueObjectConversions.EmailConverter);
        }

        /// <summary>
        /// Handles audit fields (CreatedAt, UpdatedAt, etc.) before saving
        /// </summary>
        private void HandleAuditFields()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        entry.Entity.UpdatedAt = null;
                        break;

                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        // Prevent CreatedAt from being modified
                        entry.Property(e => e.CreatedAt).IsModified = false;
                        break;

                    case EntityState.Deleted:
                        // Convert hard delete to soft delete
                        entry.State = EntityState.Modified;
                        entry.Entity.IsDeleted = true;
                        entry.Entity.DeletedAt = DateTime.UtcNow;
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                }
            }

            // Handle auditable entities
            var auditableEntries = ChangeTracker.Entries<AuditableBaseEntity>();

            foreach (var entry in auditableEntries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        // CreatedBy will be set by the service layer
                        break;

                    case EntityState.Modified:
                        // UpdatedBy will be set by the service layer
                        // Prevent CreatedBy from being modified
                        entry.Property(e => e.CreatedBy).IsModified = false;
                        break;

                    case EntityState.Deleted:
                        // DeletedBy will be set by the service layer
                        break;
                }
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets entities including soft deleted ones
        /// </summary>
        public IQueryable<T> GetWithDeleted<T>() where T : BaseEntity
        {
            return Set<T>().IgnoreQueryFilters();
        }

        /// <summary>
        /// Gets only soft deleted entities
        /// </summary>
        public IQueryable<T> GetOnlyDeleted<T>() where T : BaseEntity
        {
            return Set<T>().IgnoreQueryFilters().Where(e => e.IsDeleted);
        }

        /// <summary>
        /// Permanently deletes an entity (bypasses soft delete)
        /// </summary>
        public void HardDelete<T>(T entity) where T : BaseEntity
        {
            Set<T>().Remove(entity);
        }

        /// <summary>
        /// Restores a soft deleted entity
        /// </summary>
        public void Restore<T>(T entity) where T : BaseEntity
        {
            entity.Restore();
            Entry(entity).State = EntityState.Modified;
        }

        #endregion
    }
}