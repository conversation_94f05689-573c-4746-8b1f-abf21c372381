namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// DTO for user statistics (admin dashboard)
    /// </summary>
    public class UserStatisticsDto
    {
        /// <summary>
        /// Total number of users
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// Number of active users
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// Number of inactive users
        /// </summary>
        public int InactiveUsers { get; set; }

        /// <summary>
        /// Number of users with confirmed email
        /// </summary>
        public int EmailConfirmedUsers { get; set; }

        /// <summary>
        /// Number of users with unconfirmed email
        /// </summary>
        public int EmailUnconfirmedUsers { get; set; }

        /// <summary>
        /// Number of locked users
        /// </summary>
        public int LockedUsers { get; set; }

        /// <summary>
        /// Users registered today
        /// </summary>
        public int UsersRegisteredToday { get; set; }

        /// <summary>
        /// Users registered this week
        /// </summary>
        public int UsersRegisteredThisWeek { get; set; }

        /// <summary>
        /// Users registered this month
        /// </summary>
        public int UsersRegisteredThisMonth { get; set; }

        /// <summary>
        /// Users who logged in today
        /// </summary>
        public int UsersLoggedInToday { get; set; }

        /// <summary>
        /// Users who logged in this week
        /// </summary>
        public int UsersLoggedInThisWeek { get; set; }

        /// <summary>
        /// Users who logged in this month
        /// </summary>
        public int UsersLoggedInThisMonth { get; set; }

        /// <summary>
        /// Average users per day this month
        /// </summary>
        public double AverageUsersPerDay { get; set; }

        /// <summary>
        /// User growth percentage compared to last month
        /// </summary>
        public double GrowthPercentage { get; set; }

        /// <summary>
        /// Most popular registration day of week
        /// </summary>
        public string MostPopularRegistrationDay { get; set; } = string.Empty;

        /// <summary>
        /// Most popular login hour
        /// </summary>
        public int MostPopularLoginHour { get; set; }
    }
}
