using AutoMapper;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Order
{
    /// <summary>
    /// Order process service implementation for business logic operations
    /// </summary>
    public class OrderProcessService : IOrderProcessService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<OrderProcessService> _logger;
        private readonly ICartProcessService _cartProcessService;

        public OrderProcessService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<OrderProcessService> logger,
            ICartProcessService cartProcessService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _cartProcessService = cartProcessService;
        }

        #region Order Creation

        public async Task<CustomResponseDto<OrderDto>> ProcessCreateOrderAsync(CreateOrderDto createOrderDto)
        {
            try
            {
                _logger.LogInformation("Processing order creation for customer: {CustomerId}", createOrderDto.CustomerId);

                // Validate customer exists
                var customerExists = await ValidateCustomerAsync(createOrderDto.CustomerId);
                if (!customerExists)
                    return CustomResponseDto<OrderDto>.BadRequest("Customer not found");

                // Validate and get product information
                var productValidation = await ValidateOrderItemsAsync(createOrderDto.OrderItems);
                if (!productValidation.IsValid)
                    return CustomResponseDto<OrderDto>.BadRequest(productValidation.ErrorMessage);

                // Create order entity
                var currency = Currency.FromCode(createOrderDto.Currency);
                var billingAddress = _mapper.Map<Address>(createOrderDto.BillingAddress);
                var shippingAddress = _mapper.Map<Address>(createOrderDto.ShippingAddress);
                var customerEmail = new Email(createOrderDto.CustomerEmail);
                var customerPhone = !string.IsNullOrEmpty(createOrderDto.CustomerPhone)
                    ? new PhoneNumber(createOrderDto.CustomerPhone)
                    : null;

                var order = new Domain.Entities.Order.Order(
                    createOrderDto.CustomerId,
                    customerEmail,
                    createOrderDto.CustomerFirstName,
                    createOrderDto.CustomerLastName,
                    billingAddress,
                    shippingAddress,
                    currency);

                // Set optional properties
                if (!string.IsNullOrEmpty(createOrderDto.Notes))
                    order.Notes = createOrderDto.Notes;

                if (!string.IsNullOrEmpty(createOrderDto.ShippingMethod))
                    order.ShippingMethod = createOrderDto.ShippingMethod;

                // Payment method will be set separately via SetPaymentMethod if needed

                if (customerPhone != null)
                    order.CustomerPhone = customerPhone;

                // Add order items
                foreach (var itemDto in createOrderDto.OrderItems)
                {
                    var productInfo = productValidation.ProductInfos.First(p => p.ProductId == itemDto.ProductId);
                    var unitPrice = new Money(productInfo.Price, currency);

                    order.AddItem(
                        itemDto.ProductId,
                        productInfo.Name,
                        productInfo.Sku,
                        unitPrice,
                        itemDto.Quantity);

                    // Set variant info and special instructions if provided
                    if (!string.IsNullOrEmpty(itemDto.VariantInfo) || !string.IsNullOrEmpty(itemDto.SpecialInstructions))
                    {
                        var orderItem = order.OrderItems.Last();
                        if (!string.IsNullOrEmpty(itemDto.VariantInfo))
                            orderItem.SetVariantInfo(itemDto.VariantInfo);
                        if (!string.IsNullOrEmpty(itemDto.SpecialInstructions))
                            orderItem.SetSpecialInstructions(itemDto.SpecialInstructions);
                    }
                }

                // Add order to repository
                var orderRepo = _unitOfWork.WriteRepository<Domain.Entities.Order.Order>();
                await orderRepo.AddAsync(order);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                // Map to DTO and return
                var orderDto = _mapper.Map<OrderDto>(order);

                _logger.LogInformation("Order created successfully with ID: {OrderId}, Order Number: {OrderNumber}",
                    order.Id, order.OrderNumber);

                return CustomResponseDto<OrderDto>.Success(201, orderDto, "Order created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing order creation for customer: {CustomerId}", createOrderDto.CustomerId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating the order");
            }
        }

        #endregion

        #region Order Queries

        /// <summary>
        /// Processes get order by ID with business logic
        /// </summary>
        public async Task<CustomResponseDto<OrderDto>> GetOrderByIdAsync(Guid orderId, bool includeItems = true, bool includeCustomer = false, string? languageCode = null)
        {
            try
            {
                _logger.LogInformation("Processing get order by ID: {OrderId}", orderId);

                var orderRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var orderQuery = await orderRepo.GetAllAsync(tracking: false);

                var order = await orderQuery
                    .Where(o => o.Id == orderId && !o.IsDeleted)
                    .FirstOrDefaultAsync();

                if (order == null)
                    return CustomResponseDto<OrderDto>.NotFound("Order not found");

                var orderDto = _mapper.Map<OrderDto>(order);

                _logger.LogInformation("Order retrieved successfully: {OrderId}", orderId);
                return CustomResponseDto<OrderDto>.Success(200, orderDto, "Order retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order by ID: {OrderId}", orderId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order");
            }
        }

        /// <summary>
        /// Processes search orders with business logic
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<OrderDto>>> SearchOrdersAsync(OrderSearchDto searchDto)
        {
            try
            {
                _logger.LogInformation("Processing search orders with term: {SearchTerm}", searchDto.SearchTerm ?? "None");

                var orderRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var orderQuery = await orderRepo.GetAllAsync(tracking: false);

                var orders = await orderQuery
                    .Where(o => !o.IsDeleted)
                    .ToListAsync();

                var orderDtos = _mapper.Map<List<OrderDto>>(orders);
                var pagedResult = PagedResult<OrderDto>.Create(orderDtos, orderDtos.Count, searchDto.Page, searchDto.PageSize);

                _logger.LogInformation("Orders search completed. Found: {Count}", orderDtos.Count);
                return CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching orders");
                return CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while searching orders");
            }
        }

        /// <summary>
        /// Processes get orders by customer ID with business logic
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<OrderDto>>> GetOrdersByCustomerIdAsync(Guid customerId, int page = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Processing get orders for customer: {CustomerId}", customerId);

                var orderRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var orderQuery = await orderRepo.GetAllAsync(tracking: false);

                var orders = await orderQuery
                    .Where(o => o.CustomerId == customerId && !o.IsDeleted)
                    .ToListAsync();

                var orderDtos = _mapper.Map<List<OrderDto>>(orders);
                var pagedResult = PagedResult<OrderDto>.Create(orderDtos, orderDtos.Count, page, pageSize);

                _logger.LogInformation("Customer orders retrieved successfully: {CustomerId}, Count: {Count}", customerId, orderDtos.Count);
                return CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Customer orders retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders for customer: {CustomerId}", customerId);
                return CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while retrieving customer orders");
            }
        }

        /// <summary>
        /// Processes get order statistics with business logic
        /// </summary>
        public async Task<CustomResponseDto<OrderStatisticsDto>> GetOrderStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? customerId = null,
            string? currency = null,
            int topCustomersCount = 10,
            int recentOrdersCount = 5)
        {
            try
            {
                _logger.LogInformation("Processing get order statistics");

                var orderRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var orderQuery = await orderRepo.GetAllAsync(tracking: false);

                var orders = await orderQuery
                    .Where(o => !o.IsDeleted)
                    .ToListAsync();

                var statistics = new OrderStatisticsDto
                {
                    TotalOrders = orders.Count,
                    TotalRevenue = orders.Sum(o => o.Total.Amount),
                    AverageOrderValue = orders.Any() ? orders.Average(o => o.Total.Amount) : 0,
                    Currency = currency ?? "USD",
                    PendingOrders = orders.Count(o => o.Status == OrderStatus.Draft),
                    ConfirmedOrders = orders.Count(o => o.Status == OrderStatus.Confirmed),
                    ShippedOrders = orders.Count(o => o.Status == OrderStatus.Shipped),
                    DeliveredOrders = orders.Count(o => o.Status == OrderStatus.Delivered),
                    CancelledOrders = orders.Count(o => o.Status == OrderStatus.Cancelled)
                };

                _logger.LogInformation("Order statistics retrieved successfully");
                return CustomResponseDto<OrderStatisticsDto>.Success(200, statistics, "Order statistics retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order statistics");
                return CustomResponseDto<OrderStatisticsDto>.InternalServerError("An error occurred while retrieving order statistics");
            }
        }

        /// <summary>
        /// Processes get order by order number with business logic
        /// </summary>
        public async Task<CustomResponseDto<OrderDto>> GetOrderByOrderNumberAsync(string orderNumber, bool includeItems = true, bool includeCustomer = false, string? languageCode = null)
        {
            try
            {
                _logger.LogInformation("Processing get order by order number: {OrderNumber}", orderNumber);

                var orderRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var orderQuery = await orderRepo.GetAllAsync(tracking: false);

                var order = await orderQuery
                    .Where(o => o.OrderNumber == orderNumber && !o.IsDeleted)
                    .FirstOrDefaultAsync();

                if (order == null)
                {
                    _logger.LogWarning("Order not found with order number: {OrderNumber}", orderNumber);
                    return CustomResponseDto<OrderDto>.NotFound("Order not found");
                }

                var orderDto = _mapper.Map<OrderDto>(order);

                _logger.LogInformation("Order retrieved successfully by order number: {OrderNumber}", orderNumber);
                return CustomResponseDto<OrderDto>.Success(200, orderDto, "Order retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order by order number: {OrderNumber}", orderNumber);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order");
            }
        }

        #endregion

        #region Order Status Management

        public async Task<CustomResponseDto<NoContentDto>> ProcessUpdateOrderStatusAsync(
            Guid orderId,
            OrderStatus status,
            string? reason = null,
            string? trackingNumber = null,
            DateTime? expectedDeliveryDate = null,
            DateTime? actualDeliveryDate = null)
        {
            try
            {
                _logger.LogInformation("Processing order status update for order: {OrderId} to status: {Status}", orderId, status);

                // Get order
                var orderReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var order = await orderReadRepo.GetByIdAsync(orderId);



                // Entity will be tracked and updated automatically

                if (order == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Order not found");

                // Update status based on the new status
                switch (status)
                {
                    case OrderStatus.Confirmed:
                        order.Confirm();
                        break;

                    case OrderStatus.Cancelled:
                        order.Cancel(reason);
                        break;

                    case OrderStatus.Shipped:
                        order.MarkAsShipped(trackingNumber, expectedDeliveryDate);
                        break;

                    case OrderStatus.Delivered:
                        order.MarkAsDelivered(actualDeliveryDate);
                        break;

                    default:
                        // For other statuses, update directly
                        order.Status = status;
                        if (!string.IsNullOrEmpty(reason))
                            order.InternalNotes = $"Status changed to {status}: {reason}";
                        order.MarkAsUpdated();
                        break;
                }

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Order status updated successfully for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.Success(200, "Order status updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing order status update for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the order status");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessUpdatePaymentStatusAsync(
            Guid orderId,
            PaymentStatus paymentStatus,
            string? paymentMethod = null,
            string? paymentReference = null,
            string? notes = null)
        {
            try
            {
                _logger.LogInformation("Processing payment status update for order: {OrderId} to status: {PaymentStatus}", orderId, paymentStatus);

                // Get order
                var orderReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var order = await orderReadRepo.GetByIdAsync(orderId);

                if (order == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Order not found");

                // Entity will be tracked and updated automatically

                // Update payment status
                order.UpdatePaymentStatus(paymentStatus);

                // Payment method will be updated separately via SetPaymentMethod if needed

                // Add notes if provided
                if (!string.IsNullOrEmpty(notes) || !string.IsNullOrEmpty(paymentReference))
                {
                    var noteText = $"Payment status updated to {paymentStatus}";
                    if (!string.IsNullOrEmpty(paymentReference))
                        noteText += $" (Ref: {paymentReference})";
                    if (!string.IsNullOrEmpty(notes))
                        noteText += $" - {notes}";

                    order.InternalNotes = string.IsNullOrEmpty(order.InternalNotes)
                        ? noteText
                        : $"{order.InternalNotes}\n{noteText}";
                }

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Payment status updated successfully for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.Success(200, "Payment status updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment status update for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the payment status");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessCancelOrderAsync(
            Guid orderId,
            string? reason = null,
            Guid? cancelledBy = null)
        {
            try
            {
                _logger.LogInformation("Processing order cancellation for order: {OrderId}", orderId);

                // Get order
                var orderReadRepo = _unitOfWork.ReadRepository<Domain.Entities.Order.Order>();
                var order = await orderReadRepo.GetByIdAsync(orderId);

                if (order == null)
                    return CustomResponseDto<NoContentDto>.NotFound("Order not found");

                // Entity will be tracked and updated automatically

                // Check if order can be cancelled
                if (!order.CanBeCancelled())
                    return CustomResponseDto<NoContentDto>.BadRequest("Order cannot be cancelled in its current state");

                // Cancel order
                order.Cancel(reason);

                // Save changes
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Order cancelled successfully for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.Success(200, "Order cancelled successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing order cancellation for order: {OrderId}", orderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while cancelling the order");
            }
        }

        #endregion

        #region Helper Methods

        private async Task<bool> ValidateCustomerAsync(Guid customerId)
        {
            try
            {
                var userRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await userRepo.GetByIdAsync(customerId);
                return user != null && user.IsActive;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating customer: {CustomerId}", customerId);
                return false;
            }
        }

        private async Task<ProductValidationResult> ValidateOrderItemsAsync(List<CreateOrderItemDto> orderItems)
        {
            try
            {
                var productIds = orderItems.Select(x => x.ProductId).Distinct().ToList();
                var productRepo = _unitOfWork.ReadRepository<Domain.Entities.Product.Product>();
                var productsQuery = await productRepo.GetAllAsync(tracking: false);
                var products = await productsQuery
                    .Where(p => productIds.Contains(p.Id) && !p.IsDeleted)
                    .ToListAsync();

                // Filter active products in memory (IsActive is computed property)
                products = products.Where(p => p.IsActive).ToList();

                var productInfos = new List<ProductInfo>();
                var missingProducts = new List<Guid>();

                foreach (var productId in productIds)
                {
                    var product = products.FirstOrDefault(p => p.Id == productId);
                    if (product == null)
                    {
                        missingProducts.Add(productId);
                        continue;
                    }

                    // Check stock availability
                    var requestedQuantity = orderItems.Where(x => x.ProductId == productId).Sum(x => x.Quantity);

                    _logger.LogInformation("Stock check for product {ProductId}: Name={Name}, Requested={Requested}",
                        product.Id, product.Name, requestedQuantity);

                    // TODO: Implement stock check using new Inventory Management system
                    // Stock validation will be handled by InventoryProcessService.CheckStockAvailabilityAsync()
                    // For now, we'll skip stock validation until Inventory system is fully integrated

                    productInfos.Add(new ProductInfo
                    {
                        ProductId = product.Id,
                        Name = product.Name,
                        Sku = product.SKU,
                        Price = product.BasePrice.Amount,
                        StockQuantity = 0 // TODO: Get from Inventory Management system
                    });
                }

                if (missingProducts.Any())
                {
                    return new ProductValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"Products not found: {string.Join(", ", missingProducts)}"
                    };
                }

                return new ProductValidationResult
                {
                    IsValid = true,
                    ProductInfos = productInfos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating order items");
                return new ProductValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Error validating products"
                };
            }
        }

        #endregion

        #region Additional Process Methods (Placeholder for future implementation)

        public async Task<CustomResponseDto<NoContentDto>> ProcessConfirmOrderAsync(Guid orderId)
        {
            return await ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Confirmed);
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessShipOrderAsync(Guid orderId, string? trackingNumber = null, DateTime? expectedDeliveryDate = null)
        {
            return await ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Shipped, null, trackingNumber, expectedDeliveryDate);
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessDeliverOrderAsync(Guid orderId, DateTime? deliveryDate = null)
        {
            return await ProcessUpdateOrderStatusAsync(orderId, OrderStatus.Delivered, null, null, null, deliveryDate);
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessAddOrderItemAsync(Guid orderId, CreateOrderItemDto orderItemDto)
        {
            // TODO: Implement add item to existing order
            return CustomResponseDto<NoContentDto>.BadRequest("Adding items to existing orders is not yet implemented");
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessRemoveOrderItemAsync(Guid orderId, Guid productId)
        {
            // TODO: Implement remove item from existing order
            return CustomResponseDto<NoContentDto>.BadRequest("Removing items from existing orders is not yet implemented");
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessUpdateOrderItemQuantityAsync(Guid orderId, Guid productId, int newQuantity)
        {
            // TODO: Implement update item quantity in existing order
            return CustomResponseDto<NoContentDto>.BadRequest("Updating item quantities in existing orders is not yet implemented");
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessApplyOrderDiscountAsync(Guid orderId, decimal discountAmount)
        {
            // TODO: Implement apply discount to order
            return CustomResponseDto<NoContentDto>.BadRequest("Applying discounts to orders is not yet implemented");
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessSetShippingCostAsync(Guid orderId, decimal shippingCost, string? shippingMethod = null)
        {
            // TODO: Implement set shipping cost
            return CustomResponseDto<NoContentDto>.BadRequest("Setting shipping costs is not yet implemented");
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessSetTaxAmountAsync(Guid orderId, decimal taxAmount)
        {
            // TODO: Implement set tax amount
            return CustomResponseDto<NoContentDto>.BadRequest("Setting tax amounts is not yet implemented");
        }

        #endregion

        #region Cart to Order Conversion

        public async Task<CustomResponseDto<OrderDto>> CreateOrderFromCartAsync(CreateOrderFromCartCommand command)
        {
            try
            {
                _logger.LogInformation("Creating order from cart for CustomerId: {CustomerId}, SessionId: {SessionId}, PaymentMethod: {PaymentMethod}",
                    command.CustomerId, command.SessionId, command.PaymentMethod);

                // 1. Get cart
                var cartResult = await _cartProcessService.GetCartAsync(command.CustomerId, command.SessionId);
                if (!cartResult.IsSuccess || cartResult.Data == null)
                {
                    _logger.LogWarning("Cart not found for CustomerId: {CustomerId}, SessionId: {SessionId}",
                        command.CustomerId, command.SessionId);
                    return CustomResponseDto<OrderDto>.NotFound("Cart not found or is empty");
                }

                var cart = cartResult.Data;
                if (cart.CartItems == null || !cart.CartItems.Any())
                {
                    _logger.LogWarning("Cart is empty for CustomerId: {CustomerId}, SessionId: {SessionId}",
                        command.CustomerId, command.SessionId);
                    return CustomResponseDto<OrderDto>.BadRequest("Cart is empty");
                }

                // 2. Validate customer if authenticated
                if (command.CustomerId.HasValue)
                {
                    var customerExists = await ValidateCustomerAsync(command.CustomerId.Value);
                    if (!customerExists)
                    {
                        _logger.LogWarning("Customer not found: {CustomerId}", command.CustomerId);
                        return CustomResponseDto<OrderDto>.BadRequest("Customer not found");
                    }
                }

                // 3. Create order entity
                var currency = !string.IsNullOrEmpty(command.Currency)
                    ? Currency.FromCode(command.Currency)
                    : Currency.FromCode(cart.Currency);

                var billingAddress = _mapper.Map<Address>(command.BillingAddress);
                var shippingAddress = _mapper.Map<Address>(command.ShippingAddress);
                var customerEmail = new Email(command.CustomerEmail);
                var customerPhone = !string.IsNullOrEmpty(command.CustomerPhone)
                    ? new PhoneNumber(command.CustomerPhone)
                    : null;

                var order = new Domain.Entities.Order.Order(
                    command.CustomerId, // null for guest orders
                    customerEmail,
                    command.CustomerFirstName,
                    command.CustomerLastName,
                    billingAddress,
                    shippingAddress,
                    currency);

                // 4. Set optional properties
                if (!string.IsNullOrEmpty(command.Notes))
                    order.Notes = command.Notes;

                if (!string.IsNullOrEmpty(command.ShippingMethod))
                    order.ShippingMethod = command.ShippingMethod;

                // Payment method will be set separately via SetPaymentMethod if needed

                if (customerPhone != null)
                    order.CustomerPhone = customerPhone;

                // Set payment status based on payment method
                order.PaymentStatus = GetInitialPaymentStatus(command.PaymentMethod);

                // 5. Add order items from cart
                foreach (var cartItem in cart.CartItems)
                {
                    var unitPrice = new Money(cartItem.UnitPrice, currency);

                    order.AddItem(
                        cartItem.ProductId,
                        cartItem.ProductName,
                        cartItem.ProductSku,
                        unitPrice,
                        cartItem.Quantity);

                    // Set variant info if available
                    if (!string.IsNullOrEmpty(cartItem.VariantInfo))
                    {
                        var orderItem = order.OrderItems.Last();
                        orderItem.VariantInfo = cartItem.VariantInfo;
                    }
                }

                // 6. Save order
                var orderWriteRepo = _unitOfWork.WriteRepository<Domain.Entities.Order.Order>();
                await orderWriteRepo.AddAsync(order);
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("Order created successfully from cart. OrderId: {OrderId}, OrderNumber: {OrderNumber}",
                    order.Id, order.OrderNumber);

                // 7. Clear cart if requested
                if (command.ClearCartAfterOrder)
                {
                    try
                    {
                        await _cartProcessService.ProcessClearCartAsync(command.CustomerId, command.SessionId);
                        _logger.LogInformation("Cart cleared after order creation for CustomerId: {CustomerId}, SessionId: {SessionId}",
                            command.CustomerId, command.SessionId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to clear cart after order creation, but order was created successfully. OrderId: {OrderId}",
                            order.Id);
                        // Don't fail the order creation if cart clearing fails
                    }
                }

                // 8. Return order DTO
                var orderDto = _mapper.Map<OrderDto>(order);
                return CustomResponseDto<OrderDto>.Success(201, orderDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order from cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    command.CustomerId, command.SessionId);
                return CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating order from cart");
            }
        }

        private PaymentStatus GetInitialPaymentStatus(string paymentMethod)
        {
            return paymentMethod.ToLower() switch
            {
                "cashondelivery" or "cash_on_delivery" or "kapıda_ödeme" => PaymentStatus.Pending,
                "banktransfer" or "bank_transfer" or "banka_havalesi" => PaymentStatus.Pending,
                "free" or "ücretsiz" => PaymentStatus.Completed,
                _ => PaymentStatus.Pending
            };
        }

        #endregion
    }

    #region Helper Classes

    internal class ProductValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<ProductInfo> ProductInfos { get; set; } = new();
    }

    internal class ProductInfo
    {
        public Guid ProductId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Sku { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public int StockQuantity { get; set; }
    }

    #endregion
}
