﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Competitor;

public class Competitor : BaseEntity
{
    [MaxLength(70)]
    public string? CompetitorName { get; set; }

    [MaxLength(70)]
    public string? WebSite { get; set; }

    [MaxLength(500)]
    public string? Comment { get; set; }

    [MaxLength(250)]
    public string? SiteMap { get; set; }
    public bool Status { get; set; }
    public int FrequencyOfControl { get; set; } // 1 Hour = 24 , 6 Hour = 6 ,  1 Day = 1 blabla

    public int MatchingStatus { get; set; }  // price, none etc.

    public string? Image { get; set; }

    //public Country Country { get; set; }
    public int CountryId { get; set; }
    public ICollection<CompetitorProduct> CompetitorProducts { get; set; }

}