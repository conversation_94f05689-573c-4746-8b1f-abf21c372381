<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>EtyraCommerce.Application</RootNamespace>
    <AssemblyName>EtyraCommerce.Application</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\EtyraCommerce.Domain\EtyraCommerce.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="AutoMapper" Version="14.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTOs\User\" />
  </ItemGroup>

</Project>