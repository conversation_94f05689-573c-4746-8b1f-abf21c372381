using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting inventory transactions
    /// </summary>
    public class GetInventoryTransactionsQueryHandler : IRequestHandler<GetInventoryTransactionsQuery, CustomResponseDto<PagedResult<InventoryTransactionDto>>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetInventoryTransactionsQueryHandler> _logger;

        public GetInventoryTransactionsQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetInventoryTransactionsQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<InventoryTransactionDto>>> Handle(GetInventoryTransactionsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get inventory transactions query");

                // Validation
                if (request.PageNumber < 1)
                    return CustomResponseDto<PagedResult<InventoryTransactionDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<InventoryTransactionDto>>.BadRequest("Page size must be between 1 and 100");

                if (request.TransactionDateFrom.HasValue && request.TransactionDateTo.HasValue &&
                    request.TransactionDateFrom.Value > request.TransactionDateTo.Value)
                    return CustomResponseDto<PagedResult<InventoryTransactionDto>>.BadRequest("From date cannot be greater than to date");

                // Create filter DTO
                var filterDto = new InventoryTransactionFilterDto
                {
                    InventoryId = request.InventoryId,
                    ProductId = request.ProductId,
                    WarehouseId = request.WarehouseId,
                    Type = request.Type,
                    Reference = request.Reference,
                    ReferenceType = request.ReferenceType,
                    UserId = request.UserId,
                    TransactionDateFrom = request.TransactionDateFrom,
                    TransactionDateTo = request.TransactionDateTo,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetInventoryTransactionsAsync(filterDto);

                _logger.LogInformation("Get inventory transactions query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get inventory transactions query");
                return CustomResponseDto<PagedResult<InventoryTransactionDto>>.InternalServerError("An error occurred while retrieving inventory transactions");
            }
        }
    }
}
