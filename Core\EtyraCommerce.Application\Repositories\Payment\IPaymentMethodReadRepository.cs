using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.Repositories.Payment;

/// <summary>
/// Read repository interface for PaymentMethod entity
/// </summary>
public interface IPaymentMethodReadRepository : IReadRepository<PaymentMethod>
{
    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    Task<PaymentMethod?> GetByCodeAsync(string code, bool tracking = false);

    /// <summary>
    /// Get active payment methods ordered by display order
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active payment methods</returns>
    Task<List<PaymentMethod>> GetActivePaymentMethodsAsync(bool tracking = false);

    /// <summary>
    /// Get payment methods by type
    /// </summary>
    /// <param name="type">Payment method type</param>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    Task<List<PaymentMethod>> GetByTypeAsync(PaymentMethodType type, bool onlyActive = true, bool tracking = false);

    /// <summary>
    /// Check if payment method code exists
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    Task<bool> CodeExistsAsync(string code, Guid? excludeId = null);

    /// <summary>
    /// Get payment methods available for order amount
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of available payment methods</returns>
    Task<List<PaymentMethod>> GetAvailableForAmountAsync(decimal orderAmount, string orderCurrency, bool tracking = false);
}
