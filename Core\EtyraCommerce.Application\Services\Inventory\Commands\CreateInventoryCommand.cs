using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to create a new inventory item
    /// </summary>
    public class CreateInventoryCommand : IRequest<CustomResponseDto<InventoryDto>>
    {
        public Guid ProductId { get; set; }
        public Guid WarehouseId { get; set; }
        public int AvailableQuantity { get; set; } = 0;
        public int MinStockLevel { get; set; } = 0;
        public int MaxStockLevel { get; set; } = 0;
        public int ReorderPoint { get; set; } = 0;
        public int ReorderQuantity { get; set; } = 0;
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public int? LeadTimeDays { get; set; }
        public string? Notes { get; set; }
    }
}
