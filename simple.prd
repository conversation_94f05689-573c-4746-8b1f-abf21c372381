# EtyraCommerce - Value Objects Kullanım Örnekleri

## 💰 MONEY VALUE OBJECT

### Temel <PERSON>llan<PERSON>
```csharp
// Para oluşturma
var productPrice = new Money(99.99m, Currency.USD);
var shippingCost = new Money(15.50m, Currency.USD);
var turkishPrice = new Money(2500.00m, Currency.TRY);

// Sıfır para
var zero = Money.Zero(Currency.EUR);

// String'den oluşturma
var price = Money.Create(149.99m, "USD");
```

### Matematiksel İşlemler
```csharp
var basePrice = new Money(100.00m, Currency.USD);
var shipping = new Money(15.00m, Currency.USD);

// Toplama
var totalPrice = basePrice + shipping; // $115.00

// Çıkarma
var discount = new Money(10.00m, Currency.USD);
var finalPrice = totalPrice - discount; // $105.00

// Çarpma
var doublePrice = basePrice * 2; // $200.00
var halfPrice = basePrice / 2;   // $50.00

// Faktör ile çarpma
var bulkPrice = basePrice * 0.85m; // %15 indirim
```

### İş Kuralları
```csharp
var originalPrice = new Money(200.00m, Currency.USD);

// %20 indirim uygula
var discountedPrice = originalPrice.ApplyDiscount(0.20m); // $160.00

// %8 vergi ekle
var finalPrice = discountedPrice.ApplyTax(0.08m); // $172.80

// Kontroller
bool isZero = finalPrice.IsZero;        // false
bool isPositive = finalPrice.IsPositive; // true
```

### Formatlanmış Görüntüleme
```csharp
var usdPrice = new Money(1234.56m, Currency.USD);
var eurPrice = new Money(999.99m, Currency.EUR);
var tryPrice = new Money(15750.00m, Currency.TRY);

Console.WriteLine(usdPrice.Format()); // "$1,234.56"
Console.WriteLine(eurPrice.Format()); // "€999.99"
Console.WriteLine(tryPrice.Format()); // "15,750.00 ₺"
```

### E-ticaret Senaryoları
```csharp
// Sepet hesaplama
var items = new List<Money>
{
    new Money(29.99m, Currency.USD),
    new Money(45.50m, Currency.USD),
    new Money(12.75m, Currency.USD)
};

var subtotal = items.Aggregate((a, b) => a + b); // $88.24
var shipping = new Money(9.99m, Currency.USD);
var tax = subtotal.ApplyTax(0.0875m); // %8.75 vergi
var total = subtotal + shipping + (tax - subtotal); // Toplam

// Toplu indirim
var bulkDiscount = total.ApplyDiscount(0.10m); // %10 toplu indirim
```

## 📧 EMAIL VALUE OBJECT

### Temel Kullanım
```csharp
// Email oluşturma
var customerEmail = new Email("<EMAIL>");
var adminEmail = new Email("<EMAIL>");

// Güvenli oluşturma
var email = Email.TryCreate("invalid-email"); // null döner
if (email != null)
{
    Console.WriteLine($"Valid email: {email}");
}

// Validation
bool isValid = Email.IsValidEmail("<EMAIL>"); // true
```

### Email Analizi
```csharp
var email = new Email("<EMAIL>");

// Parçalar
string domain = email.Domain;     // "gmail.com"
string local = email.LocalPart;  // "john.doe"
string full = email.Value;       // "<EMAIL>"

// Domain kontrolleri
bool isGmail = email.IsFromDomain("gmail.com");           // true
bool isCommon = email.IsFromCommonProvider();             // true
bool isWork = email.IsFromDomains("company.com", "corp.com"); // false
```

### Privacy ve Güvenlik
```csharp
var sensitiveEmail = new Email("<EMAIL>");

// Maskelenmiş görüntüleme
string masked = sensitiveEmail.GetMasked(); // "jo****@company.com"

// Log'larda güvenli kullanım
Console.WriteLine($"User registered: {sensitiveEmail.GetMasked()}");
```

### E-ticaret Senaryoları
```csharp
// Müşteri kaydı
var newCustomer = new Email("<EMAIL>");

// Email doğrulama gerekli mi?
if (newCustomer.IsFromCommonProvider())
{
    // Hızlı doğrulama
    SendQuickVerification(newCustomer);
}
else
{
    // Detaylı doğrulama
    SendDetailedVerification(newCustomer);
}

// Newsletter aboneliği
var subscribers = new List<Email>
{
    new Email("<EMAIL>"),
    new Email("<EMAIL>"),
    new Email("<EMAIL>")
};

// Toplu email gönderimi
foreach (var subscriber in subscribers)
{
    SendNewsletter(subscriber);
    LogActivity($"Newsletter sent to {subscriber.GetMasked()}");
}
```

## 📱 PHONENUMBER VALUE OBJECT

### Temel Kullanım
```csharp
// Telefon numarası oluşturma
var turkishPhone = new PhoneNumber("+90 ************");
var usPhone = new PhoneNumber("+****************");
var ukPhone = new PhoneNumber("+44 20 7946 0958");

// Farklı formatlardan
var phone1 = new PhoneNumber("05551234567");     // Türkiye
var phone2 = new PhoneNumber("00905551234567");  // Uluslararası
var phone3 = new PhoneNumber("+905551234567");   // Standart

// Güvenli oluşturma
var phone = PhoneNumber.TryCreate("invalid-phone"); // null döner
```

### Telefon Analizi
```csharp
var phone = new PhoneNumber("+90 ************");

// Parçalar
string countryCode = phone.CountryCode; // "+90"
string number = phone.Number;          // "5551234567"
string full = phone.Value;             // "+905551234567"
```

### Formatlanmış Görüntüleme
```csharp
var turkishPhone = new PhoneNumber("+90 ************");
var usPhone = new PhoneNumber("+1 ************");
var ukPhone = new PhoneNumber("+44 20 7946 0958");

// Ülkeye özel formatlar
Console.WriteLine(turkishPhone.Format()); // "+90 (555) 123 45 67"
Console.WriteLine(usPhone.Format());      // "+****************"
Console.WriteLine(ukPhone.Format());      // "+44 2079 460 958"
```

### Privacy ve Güvenlik
```csharp
var customerPhone = new PhoneNumber("+90 ************");

// Maskelenmiş görüntüleme
string masked = customerPhone.GetMasked(); // "+90 5****67"

// Log'larda güvenli kullanım
Console.WriteLine($"SMS sent to {customerPhone.GetMasked()}");
```

### E-ticaret Senaryoları
```csharp
// Müşteri kaydı
var customer = new
{
    Email = new Email("<EMAIL>"),
    Phone = new PhoneNumber("+90 ************")
};

// SMS doğrulama
SendSMSVerification(customer.Phone);
LogActivity($"SMS sent to {customer.Phone.GetMasked()}");

// Sipariş bildirimi
var orderPhone = new PhoneNumber("****** 987 6543");
var message = $"Your order #12345 has been shipped!";
SendSMS(orderPhone, message);

// Toplu SMS kampanyası
var phoneNumbers = new List<PhoneNumber>
{
    new PhoneNumber("+90 ************"),
    new PhoneNumber("+90 ************"),
    new PhoneNumber("+90 ************")
};

foreach (var phone in phoneNumbers)
{
    SendPromotionalSMS(phone, "50% discount on all items!");
    LogActivity($"Promo SMS sent to {phone.GetMasked()}");
}
```

## 🏪 E-TİCARET KULLANIM ÖRNEKLERİ

### Ürün Modeli
```csharp
public class Product : BaseEntity
{
    public string Name { get; set; }
    public Money Price { get; set; }
    public Money? DiscountedPrice { get; set; }
    public string SKU { get; set; }
    
    // İndirimli fiyat hesaplama
    public Money GetFinalPrice()
    {
        return DiscountedPrice ?? Price;
    }
    
    // İndirim yüzdesi
    public decimal? GetDiscountPercentage()
    {
        if (DiscountedPrice == null) return null;
        
        var discount = Price - DiscountedPrice;
        return discount.Amount / Price.Amount;
    }
}

// Kullanım
var product = new Product
{
    Name = "Laptop",
    Price = new Money(1299.99m, Currency.USD),
    DiscountedPrice = new Money(999.99m, Currency.USD)
};

var savings = product.Price - product.GetFinalPrice(); // $300.00
var discountPercent = product.GetDiscountPercentage(); // 0.23 (%23)
```

### Müşteri Modeli
```csharp
public class Customer : AuditableBaseEntity
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public Email Email { get; set; }
    public PhoneNumber? Phone { get; set; }
    
    public string FullName => $"{FirstName} {LastName}";
    
    // İletişim bilgileri maskelenmiş
    public string GetMaskedContact()
    {
        var email = Email.GetMasked();
        var phone = Phone?.GetMasked() ?? "No phone";
        return $"{email} | {phone}";
    }
}

// Kullanım
var customer = new Customer
{
    FirstName = "John",
    LastName = "Doe",
    Email = new Email("<EMAIL>"),
    Phone = new PhoneNumber("+1 ************")
};

Console.WriteLine(customer.GetMaskedContact()); 
// "jo****@gmail.com | *********67"
```

### Sipariş Modeli
```csharp
public class Order : AuditableBaseEntity
{
    public string OrderNumber { get; set; }
    public Email CustomerEmail { get; set; }
    public PhoneNumber? CustomerPhone { get; set; }
    public Money Subtotal { get; set; }
    public Money ShippingCost { get; set; }
    public Money TaxAmount { get; set; }
    public Money Total { get; set; }
    
    // Toplam hesaplama
    public void CalculateTotal()
    {
        Total = Subtotal + ShippingCost + TaxAmount;
    }
    
    // İndirim uygulama
    public void ApplyDiscount(decimal percentage)
    {
        var discount = Subtotal.ApplyDiscount(percentage);
        var savedAmount = Subtotal - discount;
        Subtotal = discount;
        CalculateTotal();
    }
}

// Kullanım
var order = new Order
{
    OrderNumber = "ORD-2024-001",
    CustomerEmail = new Email("<EMAIL>"),
    CustomerPhone = new PhoneNumber("+90 ************"),
    Subtotal = new Money(150.00m, Currency.USD),
    ShippingCost = new Money(15.00m, Currency.USD),
    TaxAmount = new Money(13.13m, Currency.USD)
};

order.CalculateTotal(); // $178.13
order.ApplyDiscount(0.10m); // %10 indirim
// Yeni total: $160.13
```

### Sepet İşlemleri
```csharp
public class ShoppingCart
{
    private readonly List<CartItem> _items = new();
    private readonly Currency _currency;
    
    public ShoppingCart(Currency currency)
    {
        _currency = currency;
    }
    
    public void AddItem(Product product, int quantity)
    {
        var item = new CartItem
        {
            Product = product,
            Quantity = quantity,
            UnitPrice = product.GetFinalPrice(),
            Total = product.GetFinalPrice() * quantity
        };
        _items.Add(item);
    }
    
    public Money GetSubtotal()
    {
        return _items
            .Select(i => i.Total)
            .Aggregate(Money.Zero(_currency), (a, b) => a + b);
    }
    
    public Money CalculateTotal(decimal taxRate, Money shippingCost)
    {
        var subtotal = GetSubtotal();
        var tax = subtotal.ApplyTax(taxRate) - subtotal;
        return subtotal + tax + shippingCost;
    }
}

// Kullanım
var cart = new ShoppingCart(Currency.USD);

var laptop = new Product 
{ 
    Price = new Money(999.99m, Currency.USD) 
};
var mouse = new Product 
{ 
    Price = new Money(29.99m, Currency.USD) 
};

cart.AddItem(laptop, 1);
cart.AddItem(mouse, 2);

var subtotal = cart.GetSubtotal(); // $1,059.97
var shipping = new Money(15.00m, Currency.USD);
var total = cart.CalculateTotal(0.08m, shipping); // Vergi + kargo
```

## 🔧 VALIDATION ÖRNEKLERİ

### API Controller'da Validation
```csharp
[ApiController]
[Route("api/[controller]")]
public class CustomerController : ControllerBase
{
    [HttpPost]
    public IActionResult CreateCustomer([FromBody] CreateCustomerRequest request)
    {
        try
        {
            // Value Objects otomatik validation yapar
            var email = new Email(request.Email);
            var phone = PhoneNumber.TryCreate(request.Phone);
            
            if (phone == null && !string.IsNullOrEmpty(request.Phone))
            {
                return BadRequest("Invalid phone number format");
            }
            
            var customer = new Customer
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = email,
                Phone = phone
            };
            
            // Save customer...
            return Ok(customer);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
```

### Service Layer'da Business Logic
```csharp
public class OrderService
{
    public async Task<Order> CreateOrderAsync(CreateOrderRequest request)
    {
        // Value Objects ile güvenli veri işleme
        var customerEmail = new Email(request.CustomerEmail);
        var customerPhone = PhoneNumber.TryCreate(request.CustomerPhone);

        // Para hesaplamaları
        var subtotal = Money.Zero(Currency.USD);
        foreach (var item in request.Items)
        {
            var itemPrice = new Money(item.Price, Currency.USD);
            var itemTotal = itemPrice * item.Quantity;
            subtotal = subtotal + itemTotal;
        }

        // Vergi ve kargo
        var taxAmount = subtotal.ApplyTax(0.08m) - subtotal;
        var shippingCost = new Money(15.00m, Currency.USD);
        var total = subtotal + taxAmount + shippingCost;

        var order = new Order
        {
            CustomerEmail = customerEmail,
            CustomerPhone = customerPhone,
            Subtotal = subtotal,
            TaxAmount = taxAmount,
            ShippingCost = shippingCost,
            Total = total
        };

        return order;
    }
}
```

## 🏗️ REPOSITORY PATTERN KULLANIM ÖRNEKLERİ

### Repository Interface Kullanımı
```csharp
// Read Repository kullanımı
public class UserService
{
    private readonly IReadRepository<User> _readRepo;
    private readonly IWriteRepository<User> _writeRepo;
    private readonly IUnitOfWork _unitOfWork;

    public UserService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
        _readRepo = unitOfWork.ReadRepository<User>();
        _writeRepo = unitOfWork.WriteRepository<User>();
    }

    // Kullanıcı getirme
    public async Task<User?> GetByIdAsync(Guid id)
    {
        return await _readRepo.GetByIdAsync(id, tracking: false);
    }

    // Email ile kullanıcı arama
    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _readRepo.GetFirstOrDefaultAsync(u => u.Email == email, tracking: false);
    }

    // Aktif kullanıcıları getirme
    public async Task<List<User>> GetActiveUsersAsync()
    {
        var query = await _readRepo.GetWhereAsync(u => u.IsActive, tracking: false);
        return query.ToList();
    }

    // Sayfalama ile kullanıcı listesi
    public async Task<PagedResult<User>> GetUsersPagedAsync(int pageNumber, int pageSize)
    {
        return await _readRepo.GetPagedAsync(pageNumber, pageSize, tracking: false);
    }
}
```

### Write Repository Kullanımı
```csharp
public class UserService
{
    // Kullanıcı oluşturma
    public async Task<User> CreateUserAsync(CreateUserDto dto)
    {
        var user = new User
        {
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = new Email(dto.Email),
            Phone = PhoneNumber.TryCreate(dto.Phone)
        };

        var createdUser = await _writeRepo.AddAsync(user);
        await _unitOfWork.CommitAsync(); // Service layer'da SaveChanges

        return createdUser;
    }

    // Kullanıcı güncelleme
    public async Task<User> UpdateUserAsync(Guid id, UpdateUserDto dto)
    {
        var user = await _readRepo.GetByIdAsync(id);
        if (user == null)
            throw new NotFoundException("User not found");

        user.FirstName = dto.FirstName;
        user.LastName = dto.LastName;
        user.Phone = PhoneNumber.TryCreate(dto.Phone);

        var updatedUser = await _writeRepo.UpdateAsync(user);
        await _unitOfWork.CommitAsync();

        return updatedUser;
    }

    // Soft delete
    public async Task SoftDeleteUserAsync(Guid id)
    {
        await _writeRepo.SoftDeleteByIdAsync(id);
        await _unitOfWork.CommitAsync();
    }

    // Toplu işlemler
    public async Task CreateUsersAsync(List<CreateUserDto> dtoList)
    {
        var users = dtoList.Select(dto => new User
        {
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = new Email(dto.Email)
        });

        await _writeRepo.AddRangeAsync(users);
        await _unitOfWork.CommitAsync();
    }
}
```

### Transaction Yönetimi
```csharp
public class OrderService
{
    public async Task<Order> ProcessOrderAsync(CreateOrderDto dto)
    {
        // Transaction başlat
        await _unitOfWork.BeginTransactionAsync();

        try
        {
            // 1. Stok kontrolü ve güncelleme
            foreach (var item in dto.Items)
            {
                var product = await _productReadRepo.GetByIdAsync(item.ProductId);
                if (product.Stock < item.Quantity)
                    throw new InsufficientStockException();

                product.Stock -= item.Quantity;
                await _productWriteRepo.UpdateAsync(product);
            }

            // 2. Sipariş oluşturma
            var order = new Order
            {
                CustomerEmail = new Email(dto.CustomerEmail),
                Items = dto.Items,
                Total = CalculateTotal(dto.Items)
            };

            await _orderWriteRepo.AddAsync(order);

            // 3. Ödeme işlemi
            var payment = await ProcessPaymentAsync(order.Total);
            order.PaymentId = payment.Id;

            // Tüm değişiklikleri kaydet
            await _unitOfWork.CommitTransactionAsync();

            return order;
        }
        catch
        {
            // Hata durumunda rollback
            await _unitOfWork.RollbackTransactionAsync();
            throw;
        }
    }
}
```

## 🎯 SERVICE LAYER KULLANIM ÖRNEKLERİ

### Generic Service Implementation
```csharp
public class UserService : IService<User, UserDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public UserService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    // ID ile kullanıcı getirme
    public async Task<CustomResponseDto<UserDto?>> GetByIdAsync(Guid id, bool tracking = true)
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<User>();
            var user = await readRepo.GetByIdAsync(id, tracking);

            if (user == null)
                return CustomResponseDto<UserDto?>.NotFound("User not found");

            var dto = _mapper.Map<UserDto>(user);
            return CustomResponseDto<UserDto?>.Success(dto, "User retrieved successfully");
        }
        catch (Exception ex)
        {
            return CustomResponseDto<UserDto?>.InternalServerError(ex.Message);
        }
    }

    // Sayfalama ile kullanıcı listesi
    public async Task<CustomResponseDto<PagedResult<UserDto>>> GetPagedAsync(int pageNumber, int pageSize, bool tracking = true)
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<User>();
            var pagedResult = await readRepo.GetPagedAsync(pageNumber, pageSize, tracking);

            var dtoResult = pagedResult.Map(user => _mapper.Map<UserDto>(user));
            return CustomResponseDto<PagedResult<UserDto>>.Success(dtoResult);
        }
        catch (Exception ex)
        {
            return CustomResponseDto<PagedResult<UserDto>>.InternalServerError(ex.Message);
        }
    }

    // Kullanıcı oluşturma
    public async Task<CustomResponseDto<UserDto>> AddAsync(UserDto dto)
    {
        try
        {
            // Validation
            var validationResult = await ValidateAsync(dto);
            if (!validationResult.Data.IsValid)
                return CustomResponseDto<UserDto>.ValidationFailure(validationResult.Data.Errors);

            var writeRepo = _unitOfWork.WriteRepository<User>();
            var user = _mapper.Map<User>(dto);

            var createdUser = await writeRepo.AddAsync(user);
            await _unitOfWork.CommitAsync();

            var resultDto = _mapper.Map<UserDto>(createdUser);
            return CustomResponseDto<UserDto>.Created(resultDto, "User created successfully");
        }
        catch (Exception ex)
        {
            return CustomResponseDto<UserDto>.InternalServerError(ex.Message);
        }
    }

    // Arama işlemi
    public async Task<CustomResponseDto<PagedResult<UserDto>>> SearchAsync(SearchRequest searchRequest)
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<User>();

            // Arama kriterlerini oluştur
            Expression<Func<User, bool>> predicate = u => true;

            if (searchRequest.HasSearchTerm)
            {
                var searchTerm = searchRequest.GetNormalizedSearchTerm();
                predicate = u => u.FirstName.ToLower().Contains(searchTerm) ||
                               u.LastName.ToLower().Contains(searchTerm) ||
                               u.Email.ToLower().Contains(searchTerm);
            }

            // Tarih filtresi
            if (searchRequest.HasDateRange)
            {
                if (searchRequest.DateFrom.HasValue)
                    predicate = predicate.And(u => u.CreatedAt >= searchRequest.DateFrom.Value);

                if (searchRequest.DateTo.HasValue)
                    predicate = predicate.And(u => u.CreatedAt <= searchRequest.DateTo.Value);
            }

            var pagedResult = await readRepo.GetPagedWhereAsync(
                predicate,
                searchRequest.PageNumber,
                searchRequest.PageSize);

            var dtoResult = pagedResult.Map(user => _mapper.Map<UserDto>(user));
            return CustomResponseDto<PagedResult<UserDto>>.Success(dtoResult);
        }
        catch (Exception ex)
        {
            return CustomResponseDto<PagedResult<UserDto>>.InternalServerError(ex.Message);
        }
    }
}
```

### Custom Service Methods
```csharp
public class ProductService : IService<Product, ProductDto>
{
    // Kategoriye göre ürünler
    public async Task<CustomResponseDto<IEnumerable<ProductDto>>> GetByCategoryAsync(Guid categoryId)
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<Product>();
            var query = await readRepo.GetWhereAsync(p => p.CategoryId == categoryId, tracking: false);
            var products = query.ToList();

            var dtos = _mapper.Map<List<ProductDto>>(products);
            return CustomResponseDto<IEnumerable<ProductDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            return CustomResponseDto<IEnumerable<ProductDto>>.InternalServerError(ex.Message);
        }
    }

    // Öne çıkan ürünler
    public async Task<CustomResponseDto<IEnumerable<ProductDto>>> GetFeaturedAsync()
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<Product>();
            var query = await readRepo.GetWhereAsync(p => p.IsFeatured && p.IsActive, tracking: false);
            var products = query.Take(10).ToList();

            var dtos = _mapper.Map<List<ProductDto>>(products);
            return CustomResponseDto<IEnumerable<ProductDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            return CustomResponseDto<IEnumerable<ProductDto>>.InternalServerError(ex.Message);
        }
    }

    // Stok güncelleme
    public async Task<CustomResponseDto<NoContentDto>> UpdateStockAsync(Guid productId, int newStock)
    {
        try
        {
            var readRepo = _unitOfWork.ReadRepository<Product>();
            var writeRepo = _unitOfWork.WriteRepository<Product>();

            var product = await readRepo.GetByIdAsync(productId);
            if (product == null)
                return CustomResponseDto<NoContentDto>.NotFound("Product not found");

            product.Stock = newStock;
            product.UpdatedAt = DateTime.UtcNow;

            await writeRepo.UpdateAsync(product);
            await _unitOfWork.CommitAsync();

            return CustomResponseDto<NoContentDto>.Success(200, "Stock updated successfully");
        }
        catch (Exception ex)
        {
            return CustomResponseDto<NoContentDto>.InternalServerError(ex.Message);
        }
    }
}
```

## 🔧 VALIDATION KULLANIM ÖRNEKLERİ

### Service Layer Validation
```csharp
public class UserService
{
    public async Task<CustomResponseDto<ValidationResult>> ValidateAsync(UserDto dto)
    {
        var result = new ValidationResult();

        try
        {
            // Email validation
            if (string.IsNullOrEmpty(dto.Email))
                result.AddError("Email", "Email is required");
            else if (!Email.IsValidEmail(dto.Email))
                result.AddError("Email", "Invalid email format");
            else
            {
                // Email uniqueness check
                var readRepo = _unitOfWork.ReadRepository<User>();
                var emailExists = await readRepo.AnyAsync(u => u.Email == dto.Email);
                if (emailExists)
                    result.AddError("Email", "Email already exists");
            }

            // Phone validation
            if (!string.IsNullOrEmpty(dto.Phone) && !PhoneNumber.IsValidPhoneNumber(dto.Phone))
                result.AddError("Phone", "Invalid phone number format");

            // Name validation
            if (string.IsNullOrEmpty(dto.FirstName))
                result.AddError("FirstName", "First name is required");

            if (string.IsNullOrEmpty(dto.LastName))
                result.AddError("LastName", "Last name is required");

            return CustomResponseDto<ValidationResult>.Success(result);
        }
        catch (Exception ex)
        {
            result.AddGeneralError($"Validation error: {ex.Message}");
            return CustomResponseDto<ValidationResult>.InternalServerError(ex.Message);
        }
    }
}
```

### API Controller'da Service Kullanımı
```csharp
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IService<User, UserDto> _userService;

    public UsersController(IService<User, UserDto> userService)
    {
        _userService = userService;
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await _userService.GetByIdAsync(id);

        if (!result.IsSuccess)
            return StatusCode(result.StatusCode, result);

        return Ok(result);
    }

    [HttpGet]
    public async Task<IActionResult> GetPaged([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
    {
        var result = await _userService.GetPagedAsync(pageNumber, pageSize);
        return StatusCode(result.StatusCode, result);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] UserDto dto)
    {
        var result = await _userService.AddAsync(dto);
        return StatusCode(result.StatusCode, result);
    }

    [HttpPost("search")]
    public async Task<IActionResult> Search([FromBody] SearchRequest searchRequest)
    {
        var result = await _userService.SearchAsync(searchRequest);
        return StatusCode(result.StatusCode, result);
    }
}
```

## 💳 PAYMENT METHODS KULLANIM ÖRNEKLERİ

### Ön Tanımlı Payment Methods
```csharp
// Sistem başlangıcında otomatik eklenen payment methods
var defaultMethods = new List<PaymentMethod>
{
    // Kapıda Ödeme - 5 TL sabit ücret
    new PaymentMethod("Cash on Delivery", "COD", PaymentMethodType.CashOnDelivery,
        "Pay in cash when your order is delivered.")
    {
        DisplayOrder = 1,
        Instructions = "The courier will collect the payment amount in cash when delivering your order. Please have cash ready.",
        FeeCalculationType = FeeCalculationType.Fixed,
        FeeValue = 5.00m,
        FeeCurrency = new Currency("TRY", "Turkish Lira", "₺")
    },

    // Banka Havalesi - %3 indirim
    new PaymentMethod("Bank Transfer", "BANK_TRANSFER", PaymentMethodType.BankTransfer,
        "Pay by bank transfer and get 3% discount.")
    {
        DisplayOrder = 2,
        Instructions = "Make a transfer to the bank details that will be sent to you after order confirmation. Send the transfer receipt photo via WhatsApp.",
        FeeCalculationType = FeeCalculationType.Percentage,
        FeeValue = -3.00m, // Negatif = indirim
        FeeCurrency = new Currency("TRY", "Turkish Lira", "₺")
    },

    // Kredi Kartı - ücretsiz
    new PaymentMethod("Credit Card", "CREDIT_CARD", PaymentMethodType.CreditCard,
        "Secure payment with credit card.")
    {
        DisplayOrder = 3,
        Instructions = "You can make secure payments with your Visa, MasterCard and American Express cards.",
        FeeCalculationType = FeeCalculationType.None
    },

    // Ücretsiz - promosyon siparişleri için (başlangıçta pasif)
    new PaymentMethod("Free", "FREE", PaymentMethodType.Free,
        "Free payment for promotional orders.")
    {
        DisplayOrder = 4,
        IsActive = false,
        Instructions = "This order is free under the promotion.",
        FeeCalculationType = FeeCalculationType.None
    }
};
```

### Yeni Payment Method Ekleme
```csharp
// API ile yeni payment method ekleme
[HttpPost]
public async Task<IActionResult> CreatePaymentMethod([FromBody] CreatePaymentMethodDto dto)
{
    // PayPal ekleme örneği
    var paypalDto = new CreatePaymentMethodDto
    {
        Name = "PayPal",
        Code = "PAYPAL",
        Type = PaymentMethodType.CreditCard,
        Description = "Pay securely with PayPal",
        Instructions = "You will be redirected to PayPal to complete your payment securely.",
        FeeCalculationType = FeeCalculationType.Percentage,
        FeeValue = 2.9m, // %2.9 PayPal ücreti
        FeeCurrencyCode = "USD",
        FeeCurrencyName = "US Dollar",
        FeeCurrencySymbol = "$",
        DisplayOrder = 5,
        IsActive = true
    };

    var result = await _paymentMethodService.CreateAsync(paypalDto);
    return StatusCode(result.StatusCode, result);
}

// Stripe ekleme örneği
var stripeDto = new CreatePaymentMethodDto
{
    Name = "Stripe",
    Code = "STRIPE",
    Type = PaymentMethodType.CreditCard,
    Description = "Pay with credit card via Stripe",
    Instructions = "Enter your credit card details securely.",
    FeeCalculationType = FeeCalculationType.Fixed,
    FeeValue = 0.30m, // $0.30 + %2.9
    FeeCurrencyCode = "USD",
    MinimumOrderAmount = 1.00m, // Minimum $1
    MinimumOrderCurrency = "USD",
    DisplayOrder = 6
};

// Iyzico ekleme örneği (Türkiye)
var iyzicoDto = new CreatePaymentMethodDto
{
    Name = "Iyzico",
    Code = "IYZICO",
    Type = PaymentMethodType.CreditCard,
    Description = "Türkiye'nin güvenli ödeme sistemi",
    Instructions = "Kredi kartınızla güvenli ödeme yapın.",
    FeeCalculationType = FeeCalculationType.Percentage,
    FeeValue = 2.65m, // %2.65 Iyzico ücreti
    FeeCurrencyCode = "TRY",
    MinimumOrderAmount = 10.00m, // Minimum 10 TL
    MinimumOrderCurrency = "TRY",
    DisplayOrder = 7
};
```

### Payment Method Güncelleme
```csharp
// Mevcut payment method'u güncelleme
[HttpPut("{id}")]
public async Task<IActionResult> UpdatePaymentMethod(Guid id, [FromBody] UpdatePaymentMethodDto dto)
{
    // Kapıda ödeme ücretini güncelleme
    var updateCodDto = new UpdatePaymentMethodDto
    {
        Name = "Cash on Delivery",
        Description = "Pay in cash when your order is delivered. Updated fee!",
        FeeCalculationType = FeeCalculationType.Fixed,
        FeeValue = 7.50m, // 5 TL'den 7.50 TL'ye çıkar
        FeeCurrencyCode = "TRY",
        IsActive = true
    };

    var result = await _paymentMethodService.UpdateAsync(id, updateCodDto);
    return StatusCode(result.StatusCode, result);
}

// Payment method'u pasif yapma
var deactivateDto = new UpdatePaymentMethodDto
{
    IsActive = false // Pasif yap
};

// Ücret kaldırma
var removeFeeDto = new UpdatePaymentMethodDto
{
    FeeCalculationType = FeeCalculationType.None,
    FeeValue = 0
};
```

### Payment Method Silme
```csharp
// Payment method silme
[HttpDelete("{id}")]
public async Task<IActionResult> DeletePaymentMethod(Guid id)
{
    var result = await _paymentMethodService.DeleteAsync(id);
    return StatusCode(result.StatusCode, result);
}

// Toplu silme
[HttpDelete("bulk")]
public async Task<IActionResult> DeleteMultiple([FromBody] List<Guid> ids)
{
    foreach (var id in ids)
    {
        await _paymentMethodService.DeleteAsync(id);
    }
    return Ok("Payment methods deleted successfully");
}
```

### Aktif Payment Methods Listeleme
```csharp
// Müşteriye gösterilecek aktif payment methods
[HttpGet("active")]
public async Task<IActionResult> GetActivePaymentMethods()
{
    var result = await _paymentMethodService.GetActiveAsync();

    // Response örneği:
    // [
    //   {
    //     "id": "guid",
    //     "name": "Cash on Delivery",
    //     "code": "COD",
    //     "description": "Pay in cash when your order is delivered.",
    //     "instructions": "The courier will collect...",
    //     "feeCalculationType": "Fixed",
    //     "feeValue": 5.00,
    //     "feeCurrency": "TRY",
    //     "displayOrder": 1,
    //     "isActive": true
    //   }
    // ]

    return StatusCode(result.StatusCode, result);
}
```

### Sipariş Tutarına Göre Uygun Payment Methods
```csharp
// Belirli sipariş tutarı için uygun payment methods
[HttpGet("available")]
public async Task<IActionResult> GetAvailableForAmount([FromQuery] decimal orderAmount, [FromQuery] string currency)
{
    var result = await _paymentMethodService.GetAvailableForAmountAsync(orderAmount, currency);

    // 50 TL sipariş için örnek response:
    // [
    //   {
    //     "id": "guid",
    //     "name": "Cash on Delivery",
    //     "calculatedFee": 5.00, // Hesaplanmış ücret
    //     "isAvailableForAmount": true,
    //     "finalAmount": 55.00 // Sipariş + ücret
    //   },
    //   {
    //     "id": "guid",
    //     "name": "Bank Transfer",
    //     "calculatedFee": -1.50, // %3 indirim
    //     "isAvailableForAmount": true,
    //     "finalAmount": 48.50 // Sipariş - indirim
    //   }
    // ]

    return StatusCode(result.StatusCode, result);
}
```

### Payment Method Fee Hesaplama
```csharp
// Belirli payment method için ücret hesaplama
[HttpGet("{id}/calculate-fee")]
public async Task<IActionResult> CalculateFee(Guid id, [FromQuery] decimal orderAmount, [FromQuery] string currency)
{
    var result = await _paymentMethodService.CalculateFeeAsync(id, orderAmount, currency);

    // Kapıda ödeme için 100 TL sipariş:
    // {
    //   "data": 5.00, // 5 TL sabit ücret
    //   "isSuccess": true,
    //   "message": "Fee calculated successfully"
    // }

    // Banka havalesi için 100 TL sipariş:
    // {
    //   "data": -3.00, // %3 indirim = -3 TL
    //   "isSuccess": true,
    //   "message": "Fee calculated successfully"
    // }

    return StatusCode(result.StatusCode, result);
}
```

### E-ticaret Entegrasyonu
```csharp
// Sipariş oluştururken payment method seçimi
public class CreateOrderDto
{
    public Guid PaymentMethodId { get; set; }
    public List<OrderItemDto> Items { get; set; }
    public string CustomerEmail { get; set; }
    // ... diğer alanlar
}

[HttpPost("orders")]
public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto dto)
{
    // 1. Payment method kontrolü
    var paymentMethodResult = await _paymentMethodService.GetByIdAsync(dto.PaymentMethodId);
    if (!paymentMethodResult.IsSuccess)
        return BadRequest("Invalid payment method");

    var paymentMethod = paymentMethodResult.Data;

    // 2. Sipariş tutarını hesapla
    var subtotal = dto.Items.Sum(i => i.Price * i.Quantity);

    // 3. Payment method ücretini hesapla
    var feeResult = await _paymentMethodService.CalculateFeeAsync(
        dto.PaymentMethodId, subtotal, "TRY");

    var paymentFee = feeResult.Data;
    var totalAmount = subtotal + paymentFee;

    // 4. Siparişi oluştur
    var order = new Order
    {
        PaymentMethodId = dto.PaymentMethodId,
        PaymentMethodName = paymentMethod.Name,
        Subtotal = new Money(subtotal, Currency.TRY),
        PaymentFee = new Money(paymentFee, Currency.TRY),
        Total = new Money(totalAmount, Currency.TRY)
    };

    // 5. Kaydet
    var orderResult = await _orderService.CreateAsync(order);
    return StatusCode(orderResult.StatusCode, orderResult);
}
```

### Admin Panel Yönetimi
```csharp
// Admin panel için payment methods yönetimi
[HttpGet("admin/payment-methods")]
public async Task<IActionResult> GetAllForAdmin()
{
    var result = await _paymentMethodService.GetAllAsync();

    // Tüm payment methods (aktif + pasif)
    return StatusCode(result.StatusCode, result);
}

// Sıralama güncelleme
[HttpPut("admin/reorder")]
public async Task<IActionResult> ReorderPaymentMethods([FromBody] List<PaymentMethodOrderDto> orders)
{
    foreach (var order in orders)
    {
        var updateDto = new UpdatePaymentMethodDto
        {
            DisplayOrder = order.DisplayOrder
        };
        await _paymentMethodService.UpdateAsync(order.Id, updateDto);
    }

    return Ok("Payment methods reordered successfully");
}

public class PaymentMethodOrderDto
{
    public Guid Id { get; set; }
    public int DisplayOrder { get; set; }
}
```

### Raporlama ve İstatistikler
```csharp
// Payment method kullanım istatistikleri
[HttpGet("admin/statistics")]
public async Task<IActionResult> GetPaymentMethodStatistics()
{
    var orders = await _orderService.GetAllAsync();

    var statistics = orders.Data
        .GroupBy(o => o.PaymentMethodName)
        .Select(g => new PaymentMethodStatDto
        {
            PaymentMethodName = g.Key,
            OrderCount = g.Count(),
            TotalAmount = g.Sum(o => o.Total.Amount),
            AverageOrderAmount = g.Average(o => o.Total.Amount),
            TotalFees = g.Sum(o => o.PaymentFee.Amount)
        })
        .OrderByDescending(s => s.OrderCount)
        .ToList();

    return Ok(statistics);
}

public class PaymentMethodStatDto
{
    public string PaymentMethodName { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal AverageOrderAmount { get; set; }
    public decimal TotalFees { get; set; }
}
```
