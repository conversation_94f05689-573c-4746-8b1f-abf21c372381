using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to release stock reservation
    /// </summary>
    public class ReleaseReservationCommand : IRequest<CustomResponseDto<bool>>
    {
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
        public Guid? UserId { get; set; }
    }
}
