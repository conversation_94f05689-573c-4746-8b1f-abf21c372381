using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Account;
using EtyraApp.Domain.Entities.Common;

namespace EtyraApp.Domain.Entities;

public class TodoNotes : BaseEntity
{
    public Todo Todos { get; set; }
    public int TodoId { get; set; }
    [MaxLength(500)] public string Content { get; set; }
    public User User { get; set; }
    public int UserId { get; set; }
    
    // Alıntılama için yeni alanlar
    public TodoNotes? QuotedComment { get; set; }
    public int? QuotedCommentId { get; set; }
}