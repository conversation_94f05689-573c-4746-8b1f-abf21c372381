using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;

namespace EtyraCommerce.Application.Services.Product
{
    /// <summary>
    /// Product process service interface for business logic operations
    /// </summary>
    public interface IProductProcessService
    {
        #region Command Processing

        /// <summary>
        /// Processes product creation with business logic
        /// </summary>
        /// <param name="createDto">Product creation data</param>
        /// <returns>Created product DTO</returns>
        Task<CustomResponseDto<ProductDto>> ProcessCreateProductAsync(CreateProductDto createDto);

        /// <summary>
        /// Processes product update with business logic
        /// </summary>
        /// <param name="productId">Product ID to update</param>
        /// <param name="updateDto">Product update data</param>
        /// <returns>Updated product DTO</returns>
        Task<CustomResponseDto<ProductDto>> ProcessUpdateProductAsync(Guid productId, UpdateProductDto updateDto);

        /// <summary>
        /// Processes product deletion with business logic
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <param name="deletionReason">Reason for deletion</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessDeleteProductAsync(Guid productId, bool hardDelete = false, bool forceDelete = false, string? deletionReason = null);

        #endregion

        #region Query Processing

        /// <summary>
        /// Processes get product by ID with business logic
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="includeDescriptions">Include descriptions</param>
        /// <param name="includeImages">Include images</param>
        /// <param name="includeCategories">Include categories</param>
        /// <param name="includeDiscounts">Include discounts</param>
        /// <param name="includeAttributes">Include attributes</param>
        /// <param name="includeVariants">Include variants</param>
        /// <param name="includeInventory">Include inventory</param>
        /// <param name="includeDeleted">Include deleted products</param>
        /// <param name="languageCode">Language code for descriptions</param>
        /// <param name="storeId">Store ID for store-specific data</param>
        /// <returns>Product DTO</returns>
        Task<CustomResponseDto<ProductDto>> ProcessGetProductByIdAsync(
            Guid productId,
            bool includeDescriptions = false,
            bool includeImages = false,
            bool includeCategories = false,
            bool includeDiscounts = false,
            bool includeAttributes = false,
            bool includeVariants = false,
            bool includeInventory = false,
            bool includeDeleted = false,
            string? languageCode = null,
            Guid? storeId = null);

        /// <summary>
        /// Processes get all products with business logic
        /// </summary>
        /// <param name="filterDto">Filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessGetAllProductsAsync(ProductFilterDto filterDto);

        /// <summary>
        /// Processes product search with business logic
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessSearchProductsAsync(ProductSearchDto searchDto);

        /// <summary>
        /// Processes get products by category with business logic
        /// </summary>
        /// <param name="categoryFilterDto">Category filter criteria</param>
        /// <returns>Paged product DTOs</returns>
        Task<CustomResponseDto<PagedResult<ProductDto>>> ProcessGetProductsByCategoryAsync(ProductCategoryFilterDto categoryFilterDto);

        #endregion

        #region Business Logic Helpers

        /// <summary>
        /// Validates product business rules
        /// </summary>
        /// <param name="product">Product entity</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateProductBusinessRulesAsync(Domain.Entities.Product.Product product);

        /// <summary>
        /// Checks if SKU is unique
        /// </summary>
        /// <param name="sku">SKU to check</param>
        /// <param name="excludeProductId">Product ID to exclude from check</param>
        /// <returns>True if unique</returns>
        Task<bool> IsSkuUniqueAsync(string sku, Guid? excludeProductId = null);

        /// <summary>
        /// Checks if EAN is unique
        /// </summary>
        /// <param name="ean">EAN to check</param>
        /// <param name="excludeProductId">Product ID to exclude from check</param>
        /// <returns>True if unique</returns>
        Task<bool> IsEanUniqueAsync(string ean, Guid? excludeProductId = null);

        /// <summary>
        /// Generates SEO-friendly slug for product
        /// </summary>
        /// <param name="name">Product name</param>
        /// <param name="model">Product model</param>
        /// <param name="excludeProductId">Product ID to exclude from uniqueness check</param>
        /// <returns>Unique slug</returns>
        Task<string> GenerateProductSlugAsync(string name, string model, Guid? excludeProductId = null);

        /// <summary>
        /// Checks if product can be deleted
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <returns>True if can be deleted</returns>
        Task<bool> CanDeleteProductAsync(Guid productId, bool forceDelete = false);

        /// <summary>
        /// Updates product stock quantity
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="quantity">New quantity</param>
        /// <param name="reason">Reason for stock change</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> UpdateProductStockAsync(Guid productId, int quantity, string? reason = null);

        /// <summary>
        /// Calculates effective price for product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="customerId">Customer ID for personalized pricing</param>
        /// <returns>Effective price</returns>
        Task<decimal> CalculateEffectivePriceAsync(Guid productId, Guid? customerId = null);

        #endregion
    }
}
