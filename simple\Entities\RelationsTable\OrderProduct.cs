﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.Orders;

namespace EtyraApp.Domain.Entities.RelationsTable;

public class OrderProduct : BaseEntity
{
    public Product Product { get; set; }
    public int ProductId { get; set; }

    public Order Order { get; set; }
    public int OrderId { get; set; }

    public Store Store { get; set; }
    public int StoreId { get; set; }
    public Bazaar Bazaar { get; set; }
    public int BazaarId { get; set; }

    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public decimal? PercentDiscountValue { get; set; } = 0;

    public int WarehouseId { get; set; }
    public int Status { get; set; }
    public int RemainingQuantity { get; set; }

}