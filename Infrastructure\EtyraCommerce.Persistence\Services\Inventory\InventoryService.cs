using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Inventory
{
    /// <summary>
    /// Inventory service implementation for MediatR command/query dispatch
    /// </summary>
    public class InventoryService : IInventoryService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<InventoryService> _logger;

        public InventoryService(IMediator mediator, ILogger<InventoryService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        #region Inventory Management

        public async Task<CustomResponseDto<InventoryDto>> CreateInventoryAsync(CreateInventoryDto createDto)
        {
            _logger.LogInformation("Creating inventory for ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                createDto.ProductId, createDto.WarehouseId);

            var command = new CreateInventoryCommand
            {
                ProductId = createDto.ProductId,
                WarehouseId = createDto.WarehouseId,
                AvailableQuantity = createDto.AvailableQuantity,
                MinStockLevel = createDto.MinStockLevel,
                MaxStockLevel = createDto.MaxStockLevel,
                ReorderPoint = createDto.ReorderPoint,
                ReorderQuantity = createDto.ReorderQuantity,
                LocationCode = createDto.LocationCode,
                SupplierReference = createDto.SupplierReference,
                LeadTimeDays = createDto.LeadTimeDays,
                Notes = createDto.Notes
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<InventoryDto>> UpdateInventoryAsync(UpdateInventoryDto updateDto)
        {
            _logger.LogInformation("Updating inventory with ID: {InventoryId}", updateDto.Id);

            var command = new UpdateInventoryCommand
            {
                Id = updateDto.Id,
                AvailableQuantity = updateDto.AvailableQuantity,
                MinStockLevel = updateDto.MinStockLevel,
                MaxStockLevel = updateDto.MaxStockLevel,
                ReorderPoint = updateDto.ReorderPoint,
                ReorderQuantity = updateDto.ReorderQuantity,
                LocationCode = updateDto.LocationCode,
                SupplierReference = updateDto.SupplierReference,
                LeadTimeDays = updateDto.LeadTimeDays,
                Notes = updateDto.Notes
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<InventoryDto>> GetInventoryByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting inventory by ID: {InventoryId}", id);

            var query = new GetInventoryByIdQuery { Id = id };
            return await _mediator.Send(query);
        }

        public async Task<CustomResponseDto<List<InventoryDto>>> GetInventoryByProductAsync(Guid productId, Guid? warehouseId = null, bool activeWarehousesOnly = true)
        {
            _logger.LogInformation("Getting inventory by ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                productId, warehouseId?.ToString() ?? "All");

            var query = new GetInventoryByProductQuery
            {
                ProductId = productId,
                WarehouseId = warehouseId,
                ActiveWarehousesOnly = activeWarehousesOnly
            };

            return await _mediator.Send(query);
        }

        public async Task<CustomResponseDto<StockStatusDto>> GetStockStatusAsync(Guid productId, bool activeWarehousesOnly = true)
        {
            _logger.LogInformation("Getting stock status for ProductId: {ProductId}", productId);

            var query = new GetStockStatusQuery
            {
                ProductId = productId,
                ActiveWarehousesOnly = activeWarehousesOnly
            };

            return await _mediator.Send(query);
        }

        public async Task<CustomResponseDto<List<LowStockItemDto>>> GetLowStockItemsAsync(Guid? warehouseId = null, bool activeWarehousesOnly = true, int? maxItems = null)
        {
            _logger.LogInformation("Getting low stock items for WarehouseId: {WarehouseId}",
                warehouseId?.ToString() ?? "All");

            var query = new GetLowStockItemsQuery
            {
                WarehouseId = warehouseId,
                ActiveWarehousesOnly = activeWarehousesOnly,
                MaxItems = maxItems
            };

            return await _mediator.Send(query);
        }

        #endregion

        #region Stock Operations

        public async Task<CustomResponseDto<bool>> ReserveStockAsync(StockReservationDto reservationDto)
        {
            _logger.LogInformation("Reserving stock for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                reservationDto.ProductId, reservationDto.Quantity, reservationDto.Reference);

            var command = new ReserveStockCommand
            {
                ProductId = reservationDto.ProductId,
                WarehouseId = reservationDto.WarehouseId,
                Quantity = reservationDto.Quantity,
                Reference = reservationDto.Reference,
                Reason = reservationDto.Reason
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<bool>> AllocateStockAsync(StockAllocationDto allocationDto)
        {
            _logger.LogInformation("Allocating stock for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                allocationDto.ProductId, allocationDto.Quantity, allocationDto.Reference);

            var command = new AllocateStockCommand
            {
                ProductId = allocationDto.ProductId,
                WarehouseId = allocationDto.WarehouseId,
                Quantity = allocationDto.Quantity,
                Reference = allocationDto.Reference,
                Reason = allocationDto.Reason
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<bool>> ReleaseReservationAsync(string reference, string? reason = null, Guid? userId = null)
        {
            _logger.LogInformation("Releasing reservation for Reference: {Reference}", reference);

            var command = new ReleaseReservationCommand
            {
                Reference = reference,
                Reason = reason,
                UserId = userId
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<bool>> AdjustStockAsync(StockAdjustmentDto adjustmentDto)
        {
            _logger.LogInformation("Adjusting stock for InventoryId: {InventoryId}, NewQuantity: {NewQuantity}",
                adjustmentDto.InventoryId, adjustmentDto.NewQuantity);

            var command = new AdjustStockCommand
            {
                InventoryId = adjustmentDto.InventoryId,
                NewQuantity = adjustmentDto.NewQuantity,
                Reason = adjustmentDto.Reason,
                Notes = adjustmentDto.Notes
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<bool>> TransferStockAsync(StockTransferDto transferDto)
        {
            _logger.LogInformation("Transferring stock for ProductId: {ProductId}, From: {FromWarehouse}, To: {ToWarehouse}, Quantity: {Quantity}",
                transferDto.ProductId, transferDto.FromWarehouseId, transferDto.ToWarehouseId, transferDto.Quantity);

            var command = new TransferStockCommand
            {
                ProductId = transferDto.ProductId,
                FromWarehouseId = transferDto.FromWarehouseId,
                ToWarehouseId = transferDto.ToWarehouseId,
                Quantity = transferDto.Quantity,
                Reference = transferDto.Reference,
                Reason = transferDto.Reason
            };

            return await _mediator.Send(command);
        }

        #endregion

        #region Warehouse Management

        public async Task<CustomResponseDto<WarehouseDto>> CreateWarehouseAsync(CreateWarehouseDto createDto)
        {
            _logger.LogInformation("Creating warehouse: {Name} ({Code})", createDto.Name, createDto.Code);

            var command = new CreateWarehouseCommand
            {
                Name = createDto.Name,
                Code = createDto.Code,
                Description = createDto.Description,
                Address = createDto.Address,
                Phone = createDto.Phone,
                Email = createDto.Email,
                ManagerName = createDto.ManagerName,
                IsActive = createDto.IsActive,
                IsMain = createDto.IsMain,
                Type = createDto.Type,
                SortOrder = createDto.SortOrder
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<WarehouseDto>> UpdateWarehouseAsync(UpdateWarehouseDto updateDto)
        {
            _logger.LogInformation("Updating warehouse with ID: {WarehouseId}", updateDto.Id);

            var command = new UpdateWarehouseCommand
            {
                Id = updateDto.Id,
                Name = updateDto.Name,
                Description = updateDto.Description,
                Address = updateDto.Address,
                Phone = updateDto.Phone,
                Email = updateDto.Email,
                ManagerName = updateDto.ManagerName,
                IsActive = updateDto.IsActive,
                IsMain = updateDto.IsMain,
                Type = updateDto.Type,
                SortOrder = updateDto.SortOrder
            };

            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<bool>> DeleteWarehouseAsync(Guid id)
        {
            _logger.LogInformation("Deleting warehouse with ID: {WarehouseId}", id);

            var command = new DeleteWarehouseCommand { Id = id };
            return await _mediator.Send(command);
        }

        public async Task<CustomResponseDto<PagedResult<WarehouseDto>>> GetAllWarehousesAsync(WarehouseFilterDto filterDto)
        {
            _logger.LogInformation("Getting all warehouses with filter");

            var query = new GetAllWarehousesQuery
            {
                SearchTerm = filterDto.SearchTerm,
                IsActive = filterDto.IsActive,
                IsMain = filterDto.IsMain,
                Type = filterDto.Type,
                PageNumber = filterDto.PageNumber,
                PageSize = filterDto.PageSize,
                SortBy = filterDto.SortBy,
                SortDirection = filterDto.SortDirection
            };

            return await _mediator.Send(query);
        }

        #endregion

        #region Transactions & Reporting

        public async Task<CustomResponseDto<PagedResult<InventoryTransactionDto>>> GetInventoryTransactionsAsync(InventoryTransactionFilterDto filterDto)
        {
            _logger.LogInformation("Getting inventory transactions with filter");

            var query = new GetInventoryTransactionsQuery
            {
                InventoryId = filterDto.InventoryId,
                ProductId = filterDto.ProductId,
                WarehouseId = filterDto.WarehouseId,
                Type = filterDto.Type,
                Reference = filterDto.Reference,
                ReferenceType = filterDto.ReferenceType,
                UserId = filterDto.UserId,
                TransactionDateFrom = filterDto.TransactionDateFrom,
                TransactionDateTo = filterDto.TransactionDateTo,
                PageNumber = filterDto.PageNumber,
                PageSize = filterDto.PageSize,
                SortBy = filterDto.SortBy,
                SortDirection = filterDto.SortDirection
            };

            return await _mediator.Send(query);
        }

        #endregion
    }
}
