using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Handlers.Queries;
using EtyraCommerce.Application.Services.Order.Queries;
using EtyraCommerce.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Queries
{
    public class SearchOrdersQueryHandlerTests
    {
        private readonly Mock<IOrderService> _mockOrderService;
        private readonly Mock<ILogger<SearchOrdersQueryHandler>> _mockLogger;
        private readonly SearchOrdersQueryHandler _handler;

        public SearchOrdersQueryHandlerTests()
        {
            _mockOrderService = new Mock<IOrderService>();
            _mockLogger = new Mock<ILogger<SearchOrdersQueryHandler>>();
            //  _handler = new SearchOrdersQueryHandler(_mockOrderService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidQuery_ReturnsPagedOrderResults()
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                SearchTerm = "ORD-123",
                Page = 1,
                PageSize = 10,
                Status = OrderStatus.Confirmed,
                PaymentStatus = PaymentStatus.Completed,
                IncludeItems = true,
                IncludeCustomer = false
            };

            var orderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                OrderNumber = "ORD-20241201-123456",
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                Status = OrderStatus.Confirmed,
                PaymentStatus = PaymentStatus.Completed,
                Total = 100.00m,
                Currency = "USD"
            };

            var pagedResult = PagedResult<OrderDto>.Create(
                new List<OrderDto> { orderDto },
                1, // totalCount
                1, // pageNumber
                10 // pageSize
            );

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            var searchDto = new OrderSearchDto
            {
                SearchTerm = query.SearchTerm,
                CustomerId = query.CustomerId,
                Status = query.Status,
                PaymentStatus = query.PaymentStatus,
                ShippingStatus = query.ShippingStatus,
                MinTotal = query.MinTotal,
                MaxTotal = query.MaxTotal,
                Currency = query.Currency,
                StartDate = query.StartDate,
                EndDate = query.EndDate,
                PaymentMethod = query.PaymentMethod,
                ShippingMethod = query.ShippingMethod,
                PageNumber = query.Page,
                PageSize = query.PageSize,
                IncludeItems = query.IncludeItems,
                IncludeCustomer = query.IncludeCustomer,
                SortBy = query.SortBy,
                SortDirection = query.SortDirection
            };

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().HaveCount(1);
            result.Data.TotalCount.Should().Be(1);
            result.Data.PageNumber.Should().Be(1);

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto =>
                    dto.SearchTerm == query.SearchTerm &&
                    dto.Status == query.Status &&
                    dto.PaymentStatus == query.PaymentStatus &&
                    dto.PageNumber == query.Page &&
                    dto.PageSize == query.PageSize
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_InvalidPageNumber_ReturnsBadRequest()
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 0, // Invalid page number
                PageSize = 10
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Page number must be greater than 0");

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_InvalidPageSize_ReturnsBadRequest()
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 0 // Invalid page size
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Be("Page size must be between 1 and 100");

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()),
                Times.Never
            );
        }

        [Theory]
        [InlineData(1, 10)]
        [InlineData(2, 20)]
        [InlineData(5, 50)]
        [InlineData(10, 100)]
        public async Task Handle_VariousPageSizes_CallsServiceWithCorrectPagination(int page, int pageSize)
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = page,
                PageSize = pageSize
            };

            var pagedResult = PagedResult<OrderDto>.Empty(page, pageSize);

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto =>
                    dto.PageNumber == page && dto.PageSize == pageSize
                )),
                Times.Once
            );
        }

        [Theory]
        [InlineData(OrderStatus.Draft)]
        [InlineData(OrderStatus.Confirmed)]
        [InlineData(OrderStatus.Processing)]
        [InlineData(OrderStatus.Shipped)]
        [InlineData(OrderStatus.Delivered)]
        [InlineData(OrderStatus.Cancelled)]
        public async Task Handle_VariousOrderStatuses_CallsServiceWithCorrectStatus(OrderStatus status)
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10,
                Status = status
            };

            var pagedResult = new PagedResult<OrderDto> { Items = new List<OrderDto>() };
            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto => dto.Status == status)),
                Times.Once
            );
        }

        [Theory]
        [InlineData(PaymentStatus.Pending)]
        [InlineData(PaymentStatus.Completed)]
        [InlineData(PaymentStatus.Failed)]
        [InlineData(PaymentStatus.Refunded)]
        [InlineData(PaymentStatus.Partial)]
        public async Task Handle_VariousPaymentStatuses_CallsServiceWithCorrectPaymentStatus(PaymentStatus paymentStatus)
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10,
                PaymentStatus = paymentStatus
            };

            var pagedResult = new PagedResult<OrderDto> { Items = new List<OrderDto>() };
            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto => dto.PaymentStatus == paymentStatus)),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_WithDateRange_CallsServiceWithCorrectDateRange()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10,
                StartDate = startDate,
                EndDate = endDate
            };

            var pagedResult = new PagedResult<OrderDto> { Items = new List<OrderDto>() };
            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto =>
                    dto.StartDate == startDate && dto.EndDate == endDate
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_WithPriceRange_CallsServiceWithCorrectPriceRange()
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10,
                MinTotal = 50.00m,
                MaxTotal = 500.00m,
                Currency = "USD"
            };

            var pagedResult = new PagedResult<OrderDto> { Items = new List<OrderDto>() };
            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto =>
                    dto.MinTotal == 50.00m &&
                    dto.MaxTotal == 500.00m &&
                    dto.Currency == "USD"
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10
            };

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Be("An error occurred while searching orders");

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()),
                Times.Once
            );
        }

        [Theory]
        [InlineData("ORD-123")]
        [InlineData("<EMAIL>")]
        [InlineData("John Doe")]
        [InlineData("")]
        [InlineData(null)]
        public async Task Handle_VariousSearchTerms_CallsServiceWithCorrectSearchTerm(string? searchTerm)
        {
            // Arrange
            var query = new SearchOrdersQuery
            {
                Page = 1,
                PageSize = 10,
                SearchTerm = searchTerm
            };

            var pagedResult = new PagedResult<OrderDto> { Items = new List<OrderDto>() };
            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockOrderService
                .Setup(x => x.SearchOrdersAsync(It.IsAny<OrderSearchDto>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockOrderService.Verify(
                x => x.SearchOrdersAsync(It.Is<OrderSearchDto>(dto => dto.SearchTerm == searchTerm)),
                Times.Once
            );
        }

        #endregion
    }
}
