using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// DTO for shipping an order
    /// </summary>
    public class ShipOrderDto
    {
        /// <summary>
        /// Tracking number for the shipment
        /// </summary>
        [MaxLength(100)]
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// Expected delivery date
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// Shipping method used
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Shipping carrier
        /// </summary>
        [MaxLength(100)]
        public string? Carrier { get; set; }

        /// <summary>
        /// Additional shipping notes
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }
    }
}
