using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Generic search request with pagination and sorting
    /// </summary>
    public class SearchRequest
    {
        /// <summary>
        /// Search term to filter results
        /// </summary>
        [StringLength(500, ErrorMessage = "Search term cannot exceed 500 characters")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Field to sort by
        /// </summary>
        [StringLength(100, ErrorMessage = "Sort field cannot exceed 100 characters")]
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort direction (true for descending, false for ascending)
        /// </summary>
        public bool SortDescending { get; set; } = false;

        /// <summary>
        /// Additional filters as key-value pairs
        /// </summary>
        public Dictionary<string, string> Filters { get; set; } = new();

        /// <summary>
        /// Date range filter - start date
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Date range filter - end date
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Include inactive/deleted items
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Gets the skip count for pagination
        /// </summary>
        public int Skip => (PageNumber - 1) * PageSize;

        /// <summary>
        /// Gets the take count for pagination
        /// </summary>
        public int Take => PageSize;

        /// <summary>
        /// Checks if search term is provided
        /// </summary>
        public bool HasSearchTerm => !string.IsNullOrWhiteSpace(SearchTerm);

        /// <summary>
        /// Checks if sorting is specified
        /// </summary>
        public bool HasSorting => !string.IsNullOrWhiteSpace(SortBy);

        /// <summary>
        /// Checks if date range filter is applied
        /// </summary>
        public bool HasDateRange => DateFrom.HasValue || DateTo.HasValue;

        /// <summary>
        /// Checks if any filters are applied
        /// </summary>
        public bool HasFilters => Filters.Any();

        /// <summary>
        /// Gets normalized search term (trimmed and lowercase)
        /// </summary>
        public string? GetNormalizedSearchTerm()
        {
            return string.IsNullOrWhiteSpace(SearchTerm) ? null : SearchTerm.Trim().ToLowerInvariant();
        }

        /// <summary>
        /// Gets search terms split by spaces
        /// </summary>
        public List<string> GetSearchTerms()
        {
            var normalized = GetNormalizedSearchTerm();
            if (string.IsNullOrEmpty(normalized))
                return new List<string>();

            return normalized.Split(' ', StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        /// <summary>
        /// Adds a filter
        /// </summary>
        public SearchRequest AddFilter(string key, string value)
        {
            Filters[key] = value;
            return this;
        }

        /// <summary>
        /// Removes a filter
        /// </summary>
        public SearchRequest RemoveFilter(string key)
        {
            Filters.Remove(key);
            return this;
        }

        /// <summary>
        /// Gets filter value by key
        /// </summary>
        public string? GetFilter(string key)
        {
            return Filters.TryGetValue(key, out var value) ? value : null;
        }

        /// <summary>
        /// Checks if a specific filter exists
        /// </summary>
        public bool HasFilter(string key)
        {
            return Filters.ContainsKey(key) && !string.IsNullOrWhiteSpace(Filters[key]);
        }

        /// <summary>
        /// Sets date range filter
        /// </summary>
        public SearchRequest SetDateRange(DateTime? from, DateTime? to)
        {
            DateFrom = from;
            DateTo = to;
            return this;
        }

        /// <summary>
        /// Sets sorting
        /// </summary>
        public SearchRequest SetSorting(string sortBy, bool descending = false)
        {
            SortBy = sortBy;
            SortDescending = descending;
            return this;
        }

        /// <summary>
        /// Sets pagination
        /// </summary>
        public SearchRequest SetPagination(int pageNumber, int pageSize)
        {
            PageNumber = Math.Max(1, pageNumber);
            PageSize = Math.Max(1, Math.Min(pageSize, 100));
            return this;
        }

        /// <summary>
        /// Validates the search request
        /// </summary>
        public ValidationResult Validate()
        {
            var errors = new Dictionary<string, List<string>>();

            if (PageNumber < 1)
                errors.Add(nameof(PageNumber), new List<string> { "Page number must be greater than 0" });

            if (PageSize < 1 || PageSize > 100)
                errors.Add(nameof(PageSize), new List<string> { "Page size must be between 1 and 100" });

            if (!string.IsNullOrEmpty(SearchTerm) && SearchTerm.Length > 500)
                errors.Add(nameof(SearchTerm), new List<string> { "Search term cannot exceed 500 characters" });

            if (!string.IsNullOrEmpty(SortBy) && SortBy.Length > 100)
                errors.Add(nameof(SortBy), new List<string> { "Sort field cannot exceed 100 characters" });

            if (DateFrom.HasValue && DateTo.HasValue && DateFrom > DateTo)
                errors.Add(nameof(DateFrom), new List<string> { "Date from cannot be greater than date to" });

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }

        /// <summary>
        /// Creates a copy of the search request
        /// </summary>
        public SearchRequest Clone()
        {
            return new SearchRequest
            {
                SearchTerm = SearchTerm,
                PageNumber = PageNumber,
                PageSize = PageSize,
                SortBy = SortBy,
                SortDescending = SortDescending,
                Filters = new Dictionary<string, string>(Filters),
                DateFrom = DateFrom,
                DateTo = DateTo,
                IncludeInactive = IncludeInactive
            };
        }

        /// <summary>
        /// Gets query string representation for URLs
        /// </summary>
        public string ToQueryString()
        {
            var parameters = new List<string>();

            if (HasSearchTerm)
                parameters.Add($"searchTerm={Uri.EscapeDataString(SearchTerm!)}");

            parameters.Add($"pageNumber={PageNumber}");
            parameters.Add($"pageSize={PageSize}");

            if (HasSorting)
            {
                parameters.Add($"sortBy={Uri.EscapeDataString(SortBy!)}");
                parameters.Add($"sortDescending={SortDescending}");
            }

            if (DateFrom.HasValue)
                parameters.Add($"dateFrom={DateFrom.Value:yyyy-MM-dd}");

            if (DateTo.HasValue)
                parameters.Add($"dateTo={DateTo.Value:yyyy-MM-dd}");

            if (IncludeInactive)
                parameters.Add("includeInactive=true");

            foreach (var filter in Filters)
            {
                parameters.Add($"filters[{Uri.EscapeDataString(filter.Key)}]={Uri.EscapeDataString(filter.Value)}");
            }

            return string.Join("&", parameters);
        }

        /// <summary>
        /// Gets summary of search criteria
        /// </summary>
        public string GetSummary()
        {
            var parts = new List<string>();

            if (HasSearchTerm)
                parts.Add($"Search: '{SearchTerm}'");

            if (HasDateRange)
            {
                if (DateFrom.HasValue && DateTo.HasValue)
                    parts.Add($"Date: {DateFrom.Value:yyyy-MM-dd} to {DateTo.Value:yyyy-MM-dd}");
                else if (DateFrom.HasValue)
                    parts.Add($"Date from: {DateFrom.Value:yyyy-MM-dd}");
                else if (DateTo.HasValue)
                    parts.Add($"Date to: {DateTo.Value:yyyy-MM-dd}");
            }

            if (HasSorting)
                parts.Add($"Sort: {SortBy} {(SortDescending ? "desc" : "asc")}");

            if (HasFilters)
                parts.Add($"Filters: {Filters.Count}");

            parts.Add($"Page: {PageNumber}, Size: {PageSize}");

            return string.Join(" | ", parts);
        }

        public override string ToString() => GetSummary();
    }
}
