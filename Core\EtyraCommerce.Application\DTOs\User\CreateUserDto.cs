using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// DTO for creating a new user
    /// </summary>
    public class CreateUserDto
    {
        /// <summary>
        /// User's first name
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's email address
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(254, ErrorMessage = "Email cannot exceed 254 characters")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (optional)
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Unique username for login
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9_.-]+$", ErrorMessage = "Username can only contain letters, numbers, dots, hyphens, and underscores")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// User's password
        /// </summary>
        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be between 8 and 100 characters")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Password confirmation
        /// </summary>
        [Required(ErrorMessage = "Password confirmation is required")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        [DataType(DataType.Password)]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// User's preferred language/culture
        /// </summary>
        [StringLength(10, ErrorMessage = "Culture cannot exceed 10 characters")]
        public string Culture { get; set; } = "en-US";

        /// <summary>
        /// User's timezone
        /// </summary>
        [StringLength(50, ErrorMessage = "TimeZone cannot exceed 50 characters")]
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// Indicates if the user account should be enabled immediately
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Indicates if email confirmation should be skipped (admin use)
        /// </summary>
        public bool SkipEmailConfirmation { get; set; } = false;

        /// <summary>
        /// Additional notes about the user (admin use)
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        /// <summary>
        /// Terms and conditions acceptance
        /// </summary>
        public bool AcceptTerms { get; set; } = false;

        /// <summary>
        /// Newsletter subscription opt-in
        /// </summary>
        public bool SubscribeToNewsletter { get; set; } = false;

        /// <summary>
        /// Gets the user's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
}
