using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Commands
{
    /// <summary>
    /// Command to merge guest cart to user cart
    /// </summary>
    public class MergeCartCommand : IRequest<CustomResponseDto<CartDto>>
    {
        /// <summary>
        /// Guest session ID to merge from
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// Target customer ID to merge to
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Creates command from DTO
        /// </summary>
        public static MergeCartCommand FromDto(MergeCartDto dto)
        {
            return new MergeCartCommand
            {
                SessionId = dto.SessionId,
                CustomerId = dto.CustomerId
            };
        }
    }
}
