using EtyraCommerce.Application.Repositories;

namespace EtyraCommerce.Application.Repositories.Currency;

/// <summary>
/// Read repository interface for Currency entity
/// </summary>
public interface ICurrencyReadRepository : IReadRepository<Domain.Entities.Currency.Currency>
{
    /// <summary>
    /// Get currency by code
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency or null</returns>
    Task<Domain.Entities.Currency.Currency?> GetByCodeAsync(string code, bool tracking = false);

    /// <summary>
    /// Get active currencies ordered by display order
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies</returns>
    Task<List<Domain.Entities.Currency.Currency>> GetActiveCurrenciesAsync(bool tracking = false);

    /// <summary>
    /// Get default currency
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Default currency or null</returns>
    Task<Domain.Entities.Currency.Currency?> GetDefaultCurrencyAsync(bool tracking = false);

    /// <summary>
    /// Check if currency code exists
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    Task<bool> CodeExistsAsync(string code, Guid? excludeId = null);

    /// <summary>
    /// Check if currency is used in orders
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    Task<bool> IsUsedInOrdersAsync(Guid currencyId);

    /// <summary>
    /// Check if currency is used in products
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    Task<bool> IsUsedInProductsAsync(Guid currencyId);

    /// <summary>
    /// Check if currency is used in payment methods
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if currency is used</returns>
    Task<bool> IsUsedInPaymentMethodsAsync(Guid currencyId);

    /// <summary>
    /// Check if currency can be deleted (not used anywhere)
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <returns>True if can be deleted</returns>
    Task<bool> CanDeleteAsync(Guid currencyId);
}
