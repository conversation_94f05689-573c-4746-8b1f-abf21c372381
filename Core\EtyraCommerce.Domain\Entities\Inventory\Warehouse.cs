using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Inventory
{
    /// <summary>
    /// Warehouse entity for inventory management
    /// </summary>
    public class Warehouse : AuditableBaseEntity
    {
        #region Properties

        /// <summary>
        /// Warehouse name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Warehouse code (unique identifier)
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Warehouse description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Warehouse address
        /// </summary>
        public Address? Address { get; set; }

        /// <summary>
        /// Contact phone number
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Contact email
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Warehouse manager name
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Whether warehouse is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether this is the main warehouse
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// Warehouse type
        /// </summary>
        public WarehouseType Type { get; set; } = WarehouseType.Physical;

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Inventory items in this warehouse
        /// </summary>
        public virtual ICollection<Inventory> InventoryItems { get; set; } = new List<Inventory>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public Warehouse() { }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public Warehouse(string name, string code, string? description = null, Address? address = null,
            bool isActive = true, bool isMain = false, WarehouseType type = WarehouseType.Physical)
        {
            Name = name;
            Code = code;
            Description = description;
            Address = address;
            IsActive = isActive;
            IsMain = isMain;
            Type = type;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates warehouse information
        /// </summary>
        public void UpdateInfo(string name, string? description = null, Address? address = null,
            string? phone = null, string? email = null, string? managerName = null)
        {
            Name = name;
            Description = description;
            Address = address;
            Phone = phone;
            Email = email;
            ManagerName = managerName;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates warehouse status
        /// </summary>
        public void UpdateStatus(bool isActive, bool isMain = false)
        {
            IsActive = isActive;
            IsMain = isMain;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int sortOrder)
        {
            SortOrder = sortOrder;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"Warehouse [Id: {Id}, Code: {Code}, Name: {Name}, Type: {Type}, Active: {IsActive}]";
        }
    }

    /// <summary>
    /// Warehouse type enumeration
    /// </summary>
    public enum WarehouseType
    {
        Physical = 0,
        Virtual = 1,
        Dropship = 2,
        ThirdParty = 3
    }
}
