using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Queries
{
    /// <summary>
    /// Handler for GetCategoryTreeQuery
    /// </summary>
    public class GetCategoryTreeQueryHandler : IRequestHandler<GetCategoryTreeQuery, CustomResponseDto<List<CategoryDto>>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<GetCategoryTreeQueryHandler> _logger;

        public GetCategoryTreeQueryHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<GetCategoryTreeQueryHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> Handle(GetCategoryTreeQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get category tree query for root ID: {RootCategoryId}",
                    request.RootCategoryId?.ToString() ?? "Full Tree");

                // Validation
                if (request.MaxDepth.HasValue && (request.MaxDepth < 1 || request.MaxDepth > 10))
                    return CustomResponseDto<List<CategoryDto>>.BadRequest("Max depth must be between 1 and 10");

                // Delegate to process service
                var result = await _categoryProcessService.ProcessGetCategoryTreeAsync(
                    request.RootCategoryId,
                    request.MaxDepth,
                    request.ActiveOnly,
                    request.MenuOnly,
                    request.IncludeDescriptions,
                    request.LanguageCode,
                    request.SortBy,
                    request.SortDirection);

                _logger.LogInformation("Get category tree query processed successfully for root ID: {RootCategoryId}",
                    request.RootCategoryId?.ToString() ?? "Full Tree");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get category tree query for root ID: {RootCategoryId}",
                    request.RootCategoryId?.ToString() ?? "Full Tree");
                return CustomResponseDto<List<CategoryDto>>.InternalServerError("An error occurred while retrieving category tree");
            }
        }
    }
}
