﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Company;

public class Store : BaseEntity
{
    [MaxLength(50)]
    public string Name { get; set; }

    [MaxLength(50)]
    public string? BaseName { get; set; }

    [MaxLength(10)]
    public string? ButtonName { get; set; }

    [MaxLength(200)]
    public string? Address { get; set; }

    [MaxLength(50)]
    public string? WebSite { get; set; }

    [MaxLength(100)]
    public string? Email { get; set; }

    [MaxLength(100)]
    public string? BaseUrl { get; set; }

    [MaxLength(20)]
    public string? UserName { get; set; }

    [MaxLength(200)]
    public string? Password { get; set; }

    [MaxLength(200)]
    public string? ApiSecret { get; set; }

    [MaxLength(200)]
    public string? ApiKey { get; set; }

    public decimal PriceMultiplier { get; set; } = 1;

    //FTP 
    [MaxLength(200)]
    public string? FtpServer { get; set; }

    [MaxLength(200)]
    public string? FtpUserName { get; set; }

    [MaxLength(200)]
    public string? FtpPassword { get; set; }

    [MaxLength(200)]
    public string? RemoteDirectory { get; set; } = "image/catalog/product";

    [MaxLength(200)]
    public string? Image { get; set; }

    [MaxLength(200)]
    public string? InvoiceLogoImage { get; set; }

    //MYSQL
    [MaxLength(200)]
    public string? SqlServer { get; set; }

    [MaxLength(200)]
    public string? DbName { get; set; }

    [MaxLength(200)]
    public string? DbPassword { get; set; }

    [MaxLength(200)]
    public string? DbUserName { get; set; }


    [MaxLength(3)]
    public string? Currency { get; set; }

    public decimal ExchangeRate { get; set; }

    public bool IntegrationStatus { get; set; } = false;
    public Warehouse? Warehouse { get; set; }
    public int WarehouseId { get; set; }

    public ICollection<StoreLanguage> Languages { get; set; }
    public ICollection<ProductDescription> ProductDescriptions { get; set; }
    public ICollection<CategoryDescription> CategoryDescriptions { get; set; }


    public ICollection<OrderProduct>? OrderProducts { get; set; }
    public ICollection<ProductStore> ProductStores { get; set; }

    public ICollection<IntegrationStoreCategory>? IntegrationStoreCategories { get; set; }
    public ICollection<IntegrationStoreProduct>? IntegrationStoreProducts { get; set; }

    public ICollection<IntegrationStoreFilter>? IntegrationStoreFilters { get; set; }

}