using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Commands
{
    /// <summary>
    /// Command for deleting a category (soft delete)
    /// </summary>
    public class DeleteCategoryCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Category ID to delete
        /// </summary>
        public Guid CategoryId { get; set; }

        /// <summary>
        /// Whether to force delete (hard delete)
        /// </summary>
        public bool ForceDelete { get; set; } = false;

        /// <summary>
        /// Whether to delete child categories as well
        /// </summary>
        public bool DeleteChildren { get; set; } = false;

        /// <summary>
        /// Creates command from category ID
        /// </summary>
        public static DeleteCategoryCommand Create(Guid categoryId, bool forceDelete = false, bool deleteChildren = false)
        {
            return new DeleteCategoryCommand
            {
                CategoryId = categoryId,
                ForceDelete = forceDelete,
                DeleteChildren = deleteChildren
            };
        }
    }
}
