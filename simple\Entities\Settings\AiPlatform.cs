﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Settings;

public class AiPlatform : BaseEntity
{

    [MaxLength(25)]
    public string Name { get; set; }

    [MaxLength(25)]
    public string BaseName { get; set; }

    [MaxLength(50)]
    public string? WebSite { get; set; }

    [MaxLength(200)]
    public string? ApiKey { get; set; }


    [MaxLength(200)]
    public string? ApiSecret { get; set; }

    [MaxLength(200)]
    public string? ApiUrl { get; set; }

    [MaxLength(20)]
    public string? ApiVersion { get; set; }

    [MaxLength(20)]
    public string? ApiType { get; set; }

    [MaxLength(200)]
    public string? Model { get; set; }

    public bool Status { get; set; } = false;

    [MaxLength(200)]
    public string? Comment { get; set; }
}