using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Queries
{
    /// <summary>
    /// Query to get low stock items
    /// </summary>
    public class GetLowStockItemsQuery : IRequest<CustomResponseDto<List<LowStockItemDto>>>
    {
        public Guid? WarehouseId { get; set; }
        public bool ActiveWarehousesOnly { get; set; } = true;
        public int? MaxItems { get; set; }
    }
}
