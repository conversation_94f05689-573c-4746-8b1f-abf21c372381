using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingZone entity
    /// </summary>
    public class ShippingZoneConfiguration : IEntityTypeConfiguration<ShippingZone>
    {
        public void Configure(EntityTypeBuilder<ShippingZone> builder)
        {
            // Table configuration
            builder.ToTable("ShippingZones", "etyra_shipping");

            // Primary key
            builder.HasKey(sz => sz.Id);

            // Properties
            builder.Property(sz => sz.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sz => sz.Description)
                .HasMaxLength(500);

            builder.Property(sz => sz.MinDeliveryDays)
                .IsRequired();

            builder.Property(sz => sz.MaxDeliveryDays)
                .IsRequired();

            builder.Property(sz => sz.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(sz => sz.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Enum conversions
            builder.Property(sz => sz.ZoneType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Unique constraints
            builder.HasIndex(sz => sz.Name)
                .IsUnique()
                .HasDatabaseName("IX_ShippingZones_Name_Unique");

            // Other indexes
            builder.HasIndex(sz => sz.ZoneType)
                .HasDatabaseName("IX_ShippingZones_ZoneType");

            builder.HasIndex(sz => sz.IsActive)
                .HasDatabaseName("IX_ShippingZones_IsActive");

            builder.HasIndex(sz => sz.DisplayOrder)
                .HasDatabaseName("IX_ShippingZones_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZones_DeliveryDays",
                "\"MinDeliveryDays\" > 0 AND \"MaxDeliveryDays\" > 0 AND \"MinDeliveryDays\" <= \"MaxDeliveryDays\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZones_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Shipping zones for organizing countries and regions by delivery characteristics"));
        }
    }
}
