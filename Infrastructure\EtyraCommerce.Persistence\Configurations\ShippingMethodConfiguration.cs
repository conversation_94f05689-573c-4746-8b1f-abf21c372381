using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingMethod entity
    /// </summary>
    public class ShippingMethodConfiguration : IEntityTypeConfiguration<ShippingMethod>
    {
        public void Configure(EntityTypeBuilder<ShippingMethod> builder)
        {
            // Table configuration
            builder.ToTable("ShippingMethods", "etyra_shipping");

            // Primary key
            builder.HasKey(sm => sm.Id);

            // Properties
            builder.Property(sm => sm.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sm => sm.Description)
                .HasMaxLength(500);

            builder.Property(sm => sm.CarrierName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sm => sm.CarrierCode)
                .HasMaxLength(50);

            builder.Property(sm => sm.ApiEndpoint)
                .HasMaxLength(500);

            builder.Property(sm => sm.ApiCredentials)
                .HasMaxLength(1000);

            builder.Property(sm => sm.HasTracking)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(sm => sm.HasInsurance)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(sm => sm.SupportsCashOnDelivery)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(sm => sm.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(sm => sm.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Enum conversions
            builder.Property(sm => sm.MethodType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Foreign key relationships
            builder.HasOne(sm => sm.ShippingZone)
                .WithMany(sz => sz.ShippingMethods)
                .HasForeignKey(sm => sm.ShippingZoneId)
                .OnDelete(DeleteBehavior.Cascade);

            // Navigation properties
            builder.HasMany(sm => sm.ShippingRates)
                .WithOne(sr => sr.ShippingMethod)
                .HasForeignKey(sr => sr.ShippingMethodId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(sm => sm.ShippingZoneId)
                .HasDatabaseName("IX_ShippingMethods_ShippingZoneId");

            builder.HasIndex(sm => sm.CarrierName)
                .HasDatabaseName("IX_ShippingMethods_CarrierName");

            builder.HasIndex(sm => sm.MethodType)
                .HasDatabaseName("IX_ShippingMethods_MethodType");

            builder.HasIndex(sm => sm.IsActive)
                .HasDatabaseName("IX_ShippingMethods_IsActive");

            builder.HasIndex(sm => sm.DisplayOrder)
                .HasDatabaseName("IX_ShippingMethods_DisplayOrder");

            builder.HasIndex(sm => new { sm.Name, sm.ShippingZoneId })
                .IsUnique()
                .HasDatabaseName("IX_ShippingMethods_Name_Zone_Unique");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingMethods_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Shipping methods available for each shipping zone with carrier information"));
        }
    }
}
