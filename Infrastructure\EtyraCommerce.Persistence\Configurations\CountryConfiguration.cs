using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Country entity
    /// </summary>
    public class CountryConfiguration : IEntityTypeConfiguration<Country>
    {
        public void Configure(EntityTypeBuilder<Country> builder)
        {
            // Table configuration
            builder.ToTable("Countries", "etyra_shipping");

            // Primary key
            builder.HasKey(c => c.Id);

            // Properties
            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.Code)
                .IsRequired()
                .HasMaxLength(2);

            builder.Property(c => c.IsoCode)
                .IsRequired()
                .HasMaxLength(3);

            builder.Property(c => c.CurrencyCode)
                .IsRequired()
                .HasMaxLength(3);

            builder.Property(c => c.PhoneCode)
                .HasMaxLength(10);

            builder.Property(c => c.IsEu<PERSON>ember)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(c => c.IsShippingEnabled)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(c => c.ShippingNotes)
                .HasMaxLength(500);

            builder.Property(c => c.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Unique constraints
            builder.HasIndex(c => c.Code)
                .IsUnique()
                .HasDatabaseName("IX_Countries_Code_Unique");

            builder.HasIndex(c => c.IsoCode)
                .IsUnique()
                .HasDatabaseName("IX_Countries_IsoCode_Unique");

            builder.HasIndex(c => c.Name)
                .IsUnique()
                .HasDatabaseName("IX_Countries_Name_Unique");

            // Other indexes
            builder.HasIndex(c => c.IsEuMember)
                .HasDatabaseName("IX_Countries_IsEuMember");

            builder.HasIndex(c => c.IsShippingEnabled)
                .HasDatabaseName("IX_Countries_IsShippingEnabled");

            builder.HasIndex(c => c.DisplayOrder)
                .HasDatabaseName("IX_Countries_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Countries_Code_Length",
                "LENGTH(\"Code\") = 2"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Countries_IsoCode_Length",
                "LENGTH(\"IsoCode\") = 3"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Countries_CurrencyCode_Length",
                "LENGTH(\"CurrencyCode\") = 3"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Countries_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Countries available for shipping in the European market"));
        }
    }
}
