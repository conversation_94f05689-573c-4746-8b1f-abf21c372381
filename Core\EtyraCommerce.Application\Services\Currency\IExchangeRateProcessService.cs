using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services;
using EtyraCommerce.Domain.Entities.Currency;
using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.Services.Currency;

/// <summary>
/// Exchange rate process service interface for business logic operations
/// </summary>
public interface IExchangeRateProcessService : IService<ExchangeRate, ExchangeRateDto>
{
    #region Command Operations

    /// <summary>
    /// Process create exchange rate request
    /// </summary>
    /// <param name="createDto">Create exchange rate DTO</param>
    /// <returns>Created exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> ProcessCreateAsync(CreateExchangeRateDto createDto);

    /// <summary>
    /// Process update exchange rate request
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="updateDto">Update exchange rate DTO</param>
    /// <returns>Updated exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> ProcessUpdateAsync(Guid id, UpdateExchangeRateDto updateDto);

    /// <summary>
    /// Process delete exchange rate request
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <returns>Success result</returns>
    Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id);

    /// <summary>
    /// Process create or update exchange rate request (upsert operation)
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="rate">Exchange rate value</param>
    /// <param name="effectiveDate">Effective date</param>
    /// <param name="expiryDate">Expiry date (optional)</param>
    /// <param name="source">Source of the rate</param>
    /// <param name="externalReferenceId">External reference ID (optional)</param>
    /// <param name="notes">Additional notes (optional)</param>
    /// <returns>Created or updated exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> ProcessCreateOrUpdateAsync(
        Guid fromCurrencyId, 
        Guid toCurrencyId, 
        decimal rate, 
        DateTime effectiveDate, 
        DateTime? expiryDate = null, 
        ExchangeRateSource source = ExchangeRateSource.Manual,
        string? externalReferenceId = null,
        string? notes = null);

    /// <summary>
    /// Process bulk update exchange rates from external source
    /// </summary>
    /// <param name="exchangeRates">Exchange rates to update</param>
    /// <param name="source">Source of the exchange rates</param>
    /// <param name="deactivateOthers">Whether to deactivate existing rates not in this update</param>
    /// <returns>Updated exchange rates</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> ProcessBulkUpdateAsync(
        List<CreateExchangeRateDto> exchangeRates, 
        ExchangeRateSource source = ExchangeRateSource.API, 
        bool deactivateOthers = false);

    /// <summary>
    /// Process toggle exchange rate status request
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> ProcessToggleStatusAsync(Guid id, bool isActive);

    /// <summary>
    /// Process deactivate expired exchange rates request
    /// </summary>
    /// <param name="asOfDate">Date to check expiry against (optional)</param>
    /// <returns>Number of deactivated records</returns>
    Task<CustomResponseDto<int>> ProcessDeactivateExpiredAsync(DateTime? asOfDate = null);

    #endregion

    #region Query Operations

    /// <summary>
    /// Get all exchange rates
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <returns>List of exchange rates</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetAllAsync(bool tracking = false, bool activeOnly = false, bool validOnly = false);

    /// <summary>
    /// Get exchange rate by ID
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Exchange rate details</returns>
    new Task<CustomResponseDto<ExchangeRateDto>> GetByIdAsync(Guid id, bool tracking = false);

    /// <summary>
    /// Get exchange rates by currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <returns>List of exchange rates for the currency pair</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetByCurrencyPairAsync(
        Guid fromCurrencyId,
        Guid toCurrencyId,
        bool tracking = false,
        bool activeOnly = true,
        bool validOnly = true);

    /// <summary>
    /// Get exchange rates by currency (either as base or target currency)
    /// </summary>
    /// <param name="currencyId">Currency ID to filter by</param>
    /// <param name="asBaseCurrency">Whether to get rates where this currency is the base currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <returns>List of exchange rates for the currency</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetByCurrencyAsync(
        Guid currencyId,
        bool asBaseCurrency = true,
        bool tracking = false,
        bool activeOnly = true,
        bool validOnly = true);

    /// <summary>
    /// Get current exchange rate for currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="asOfDate">Date for which to get the rate (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Current exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> GetCurrentRateAsync(
        Guid fromCurrencyId, 
        Guid toCurrencyId, 
        DateTime? asOfDate = null, 
        bool tracking = false);

    /// <summary>
    /// Get current exchange rate by currency codes
    /// </summary>
    /// <param name="fromCurrencyCode">Base currency code</param>
    /// <param name="toCurrencyCode">Target currency code</param>
    /// <param name="asOfDate">Date for which to get the rate (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Current exchange rate</returns>
    Task<CustomResponseDto<ExchangeRateDto>> GetCurrentRateByCurrencyCodesAsync(
        string fromCurrencyCode, 
        string toCurrencyCode, 
        DateTime? asOfDate = null, 
        bool tracking = false);

    /// <summary>
    /// Get exchange rates by source
    /// </summary>
    /// <param name="source">Source of the exchange rates</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <returns>List of exchange rates from the specified source</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetBySourceAsync(
        ExchangeRateSource source, 
        bool tracking = false, 
        bool activeOnly = true, 
        bool validOnly = true);

    /// <summary>
    /// Get exchange rates by date range
    /// </summary>
    /// <param name="startDate">Start date (inclusive)</param>
    /// <param name="endDate">End date (inclusive)</param>
    /// <param name="fromCurrencyId">Base currency ID (optional)</param>
    /// <param name="toCurrencyId">Target currency ID (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <returns>List of exchange rates in the date range</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetByDateRangeAsync(
        DateTime startDate, 
        DateTime endDate, 
        Guid? fromCurrencyId = null, 
        Guid? toCurrencyId = null, 
        bool tracking = false, 
        bool activeOnly = true);

    /// <summary>
    /// Get expired exchange rates
    /// </summary>
    /// <param name="asOfDate">Date to check expiry against (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <returns>List of expired exchange rates</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetExpiredRatesAsync(
        DateTime? asOfDate = null, 
        bool tracking = false, 
        bool activeOnly = true);

    /// <summary>
    /// Get exchange rate history for currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="daysBack">Number of days to look back (optional)</param>
    /// <param name="maxRecords">Maximum number of records to return (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Exchange rate history</returns>
    Task<CustomResponseDto<List<ExchangeRateDto>>> GetHistoryAsync(
        Guid fromCurrencyId, 
        Guid toCurrencyId, 
        int? daysBack = 30, 
        int? maxRecords = 100, 
        bool tracking = false);

    /// <summary>
    /// Get exchange rate summary (for dashboard/overview)
    /// </summary>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Exchange rate summary</returns>
    Task<CustomResponseDto<List<ExchangeRateSummaryDto>>> GetSummaryAsync(
        bool activeOnly = true, 
        bool validOnly = true, 
        bool tracking = false);

    #endregion

    #region Conversion Operations

    /// <summary>
    /// Convert currency amount
    /// </summary>
    /// <param name="amount">Amount to convert</param>
    /// <param name="fromCurrencyCode">Source currency code</param>
    /// <param name="toCurrencyCode">Target currency code</param>
    /// <param name="conversionDate">Date for conversion (optional)</param>
    /// <returns>Currency conversion result</returns>
    Task<CustomResponseDto<CurrencyConversionResultDto>> ConvertCurrencyAsync(
        decimal amount, 
        string fromCurrencyCode, 
        string toCurrencyCode, 
        DateTime? conversionDate = null);

    #endregion

    #region Validation Operations

    /// <summary>
    /// Check if exchange rate exists for currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="asOfDate">Date to check (optional)</param>
    /// <returns>True if exchange rate exists</returns>
    Task<bool> ExchangeRateExistsAsync(Guid fromCurrencyId, Guid toCurrencyId, DateTime? asOfDate = null);

    /// <summary>
    /// Check if exchange rate can be deleted
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <returns>True if can be deleted</returns>
    Task<bool> CanDeleteExchangeRateAsync(Guid id);

    #endregion
}
