using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;

namespace EtyraCommerce.Application.Services.Inventory
{
    /// <summary>
    /// Inventory process service interface for business logic operations
    /// </summary>
    public interface IInventoryProcessService
    {
        #region Inventory Management

        /// <summary>
        /// Processes inventory creation
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> ProcessCreateInventoryAsync(CreateInventoryDto createDto);

        /// <summary>
        /// Processes inventory update
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> ProcessUpdateInventoryAsync(UpdateInventoryDto updateDto);

        /// <summary>
        /// Processes get inventory by ID
        /// </summary>
        Task<CustomResponseDto<InventoryDto>> ProcessGetInventoryByIdAsync(Guid id);

        /// <summary>
        /// Processes get inventory by product
        /// </summary>
        Task<CustomResponseDto<List<InventoryDto>>> ProcessGetInventoryByProductAsync(Guid productId, Guid? warehouseId = null, bool activeWarehousesOnly = true);

        /// <summary>
        /// Processes get stock status
        /// </summary>
        Task<CustomResponseDto<StockStatusDto>> ProcessGetStockStatusAsync(Guid productId, bool activeWarehousesOnly = true);

        /// <summary>
        /// Processes get low stock items
        /// </summary>
        Task<CustomResponseDto<List<LowStockItemDto>>> ProcessGetLowStockItemsAsync(Guid? warehouseId = null, bool activeWarehousesOnly = true, int? maxItems = null);

        #endregion

        #region Stock Operations

        /// <summary>
        /// Checks if stock is available for reservation
        /// </summary>
        Task<bool> CheckStockAvailabilityAsync(Guid productId, int quantity, Guid? warehouseId = null);

        /// <summary>
        /// Processes stock reservation
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessReserveStockAsync(Guid productId, Guid? warehouseId, int quantity, string reference, string? reason = null, Guid? userId = null);

        /// <summary>
        /// Processes stock allocation
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessAllocateStockAsync(Guid productId, Guid? warehouseId, int quantity, string reference, string? reason = null, Guid? userId = null);

        /// <summary>
        /// Processes reservation release
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessReleaseReservationAsync(string reference, string? reason = null, Guid? userId = null);

        /// <summary>
        /// Processes stock adjustment
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessAdjustStockAsync(Guid inventoryId, int newQuantity, string reason, string? notes = null, Guid? userId = null);

        /// <summary>
        /// Processes stock transfer
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessTransferStockAsync(Guid productId, Guid fromWarehouseId, Guid toWarehouseId, int quantity, string? reference = null, string? reason = null, Guid? userId = null);

        #endregion

        #region Warehouse Management

        /// <summary>
        /// Processes warehouse creation
        /// </summary>
        Task<CustomResponseDto<WarehouseDto>> ProcessCreateWarehouseAsync(CreateWarehouseDto createDto);

        /// <summary>
        /// Processes warehouse update
        /// </summary>
        Task<CustomResponseDto<WarehouseDto>> ProcessUpdateWarehouseAsync(UpdateWarehouseDto updateDto);

        /// <summary>
        /// Processes warehouse deletion
        /// </summary>
        Task<CustomResponseDto<bool>> ProcessDeleteWarehouseAsync(Guid id);

        /// <summary>
        /// Processes get all warehouses
        /// </summary>
        Task<CustomResponseDto<PagedResult<WarehouseDto>>> ProcessGetAllWarehousesAsync(WarehouseFilterDto filterDto);

        #endregion

        #region Transactions & Reporting

        /// <summary>
        /// Processes get inventory transactions
        /// </summary>
        Task<CustomResponseDto<PagedResult<InventoryTransactionDto>>> ProcessGetInventoryTransactionsAsync(InventoryTransactionFilterDto filterDto);

        /// <summary>
        /// Creates inventory transaction record
        /// </summary>
        Task CreateInventoryTransactionAsync(CreateInventoryTransactionDto transactionDto);

        #endregion
    }
}
