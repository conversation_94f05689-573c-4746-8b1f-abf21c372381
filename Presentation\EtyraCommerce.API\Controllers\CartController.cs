using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Shopping cart management controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CartController : ControllerBase
    {
        private readonly ICartService _cartService;
        private readonly ILogger<CartController> _logger;

        public CartController(ICartService cartService, ILogger<CartController> logger)
        {
            _cartService = cartService;
            _logger = logger;
        }

        #region Helper Methods

        /// <summary>
        /// Gets the current user ID from JWT token
        /// </summary>
        /// <returns>User ID if authenticated, null otherwise</returns>
        private Guid? GetCurrentUserId()
        {
            if (User.Identity?.IsAuthenticated != true)
                return null;

            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier) ??
                             User.FindFirst("sub") ??
                             User.FindFirst("userId");

            if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return userId;
            }

            return null;
        }

        #endregion

        #region Cart Management

        /// <summary>
        /// Adds an item to the shopping cart
        /// </summary>
        /// <param name="addToCartDto">Item to add to cart</param>
        /// <returns>Updated cart</returns>
        [HttpPost("add-item")]
        public async Task<IActionResult> AddToCart([FromBody] AddToCartDto addToCartDto)
        {
            try
            {
                _logger.LogInformation("Adding item to cart for ProductId: {ProductId}, Quantity: {Quantity}",
                    addToCartDto.ProductId, addToCartDto.Quantity);

                // Get user ID from JWT token if authenticated
                var customerId = GetCurrentUserId();

                var result = await _cartService.AddToCartAsync(addToCartDto, customerId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while adding item to cart for ProductId: {ProductId}",
                    addToCartDto.ProductId);
                return StatusCode(500, CustomResponseDto<CartDto>.InternalServerError("An error occurred while adding item to cart"));
            }
        }

        /// <summary>
        /// Updates cart item quantity
        /// </summary>
        /// <param name="updateCartItemDto">Item update data</param>
        /// <returns>Updated cart</returns>
        [HttpPut("update-item")]
        public async Task<IActionResult> UpdateCartItem([FromBody] UpdateCartItemDto updateCartItemDto)
        {
            try
            {
                _logger.LogInformation("Updating cart item for ProductId: {ProductId}, Quantity: {Quantity}",
                    updateCartItemDto.ProductId, updateCartItemDto.Quantity);

                // Get user ID and session ID
                Guid? customerId = null;
                string? sessionId = null;

                if (User.Identity?.IsAuthenticated == true)
                {
                    customerId = GetCurrentUserId();
                }
                else
                {
                    // For guest users, get session ID from header or generate one
                    sessionId = Request.Headers["X-Session-Id"].FirstOrDefault();
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        return BadRequest(CustomResponseDto<CartDto>.BadRequest("Session ID is required for guest users"));
                    }
                }

                var result = await _cartService.UpdateCartItemAsync(updateCartItemDto, customerId, sessionId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating cart item for ProductId: {ProductId}",
                    updateCartItemDto.ProductId);
                return StatusCode(500, CustomResponseDto<CartDto>.InternalServerError("An error occurred while updating cart item"));
            }
        }

        /// <summary>
        /// Removes an item from the cart
        /// </summary>
        /// <param name="removeFromCartDto">Item to remove</param>
        /// <returns>Success response</returns>
        [HttpDelete("remove-item")]
        public async Task<IActionResult> RemoveFromCart([FromBody] RemoveFromCartDto removeFromCartDto)
        {
            try
            {
                _logger.LogInformation("Removing item from cart for ProductId: {ProductId}",
                    removeFromCartDto.ProductId);

                // Get user ID and session ID
                Guid? customerId = null;
                string? sessionId = null;

                if (User.Identity?.IsAuthenticated == true)
                {
                    customerId = GetCurrentUserId();
                }
                else
                {
                    sessionId = Request.Headers["X-Session-Id"].FirstOrDefault();
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        return BadRequest(CustomResponseDto<NoContentDto>.BadRequest("Session ID is required for guest users"));
                    }
                }

                var result = await _cartService.RemoveFromCartAsync(removeFromCartDto, customerId, sessionId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while removing item from cart for ProductId: {ProductId}",
                    removeFromCartDto.ProductId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while removing item from cart"));
            }
        }

        /// <summary>
        /// Clears all items from the cart
        /// </summary>
        /// <returns>Success response</returns>
        [HttpDelete("clear")]
        public async Task<IActionResult> ClearCart()
        {
            try
            {
                _logger.LogInformation("Clearing cart");

                // Get user ID and session ID
                Guid? customerId = null;
                string? sessionId = null;

                if (User.Identity?.IsAuthenticated == true)
                {
                    customerId = GetCurrentUserId();
                }
                else
                {
                    sessionId = Request.Headers["X-Session-Id"].FirstOrDefault();
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        return BadRequest(CustomResponseDto<NoContentDto>.BadRequest("Session ID is required for guest users"));
                    }
                }

                var result = await _cartService.ClearCartAsync(customerId, sessionId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while clearing cart");
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while clearing cart"));
            }
        }

        /// <summary>
        /// Merges guest cart to user cart (called after login)
        /// </summary>
        /// <param name="mergeCartDto">Merge cart data</param>
        /// <returns>Merged cart</returns>
        [HttpPost("merge")]
        [Authorize]
        public async Task<IActionResult> MergeCart([FromBody] MergeCartDto mergeCartDto)
        {
            try
            {
                _logger.LogInformation("Merging cart for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    mergeCartDto.SessionId, mergeCartDto.CustomerId);

                // Validate that the customer ID matches the authenticated user
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(CustomResponseDto<CartDto>.Unauthorized("Invalid user token"));
                }

                if (mergeCartDto.CustomerId != userId.Value)
                {
                    return Forbid();
                }

                var result = await _cartService.MergeCartAsync(mergeCartDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while merging cart for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    mergeCartDto.SessionId, mergeCartDto.CustomerId);
                return StatusCode(500, CustomResponseDto<CartDto>.InternalServerError("An error occurred while merging cart"));
            }
        }

        #endregion

        #region Cart Retrieval

        /// <summary>
        /// Gets the current user's or guest's cart
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive carts</param>
        /// <param name="includeExpired">Whether to include expired carts</param>
        /// <returns>Cart with all items</returns>
        [HttpGet]
        public async Task<IActionResult> GetCart(
            [FromQuery] bool includeInactive = false,
            [FromQuery] bool includeExpired = false)
        {
            try
            {
                _logger.LogInformation("Getting cart");

                // Get user ID and session ID
                Guid? customerId = null;
                string? sessionId = null;

                if (User.Identity?.IsAuthenticated == true)
                {
                    customerId = GetCurrentUserId();
                }
                else
                {
                    sessionId = Request.Headers["X-Session-Id"].FirstOrDefault();
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        return BadRequest(CustomResponseDto<CartDto>.BadRequest("Session ID is required for guest users"));
                    }
                }

                var result = await _cartService.GetCartAsync(customerId, sessionId, includeInactive, includeExpired);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting cart");
                return StatusCode(500, CustomResponseDto<CartDto>.InternalServerError("An error occurred while retrieving cart"));
            }
        }

        /// <summary>
        /// Gets cart summary (without detailed items)
        /// </summary>
        /// <returns>Cart summary</returns>
        [HttpGet("summary")]
        public async Task<IActionResult> GetCartSummary()
        {
            try
            {
                _logger.LogInformation("Getting cart summary");

                // Get user ID and session ID
                Guid? customerId = null;
                string? sessionId = null;

                if (User.Identity?.IsAuthenticated == true)
                {
                    customerId = GetCurrentUserId();
                }
                else
                {
                    sessionId = Request.Headers["X-Session-Id"].FirstOrDefault();
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        return BadRequest(CustomResponseDto<CartSummaryDto>.BadRequest("Session ID is required for guest users"));
                    }
                }

                var result = await _cartService.GetCartSummaryAsync(customerId, sessionId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting cart summary");
                return StatusCode(500, CustomResponseDto<CartSummaryDto>.InternalServerError("An error occurred while retrieving cart summary"));
            }
        }

        /// <summary>
        /// Gets current user's cart (requires authentication)
        /// </summary>
        /// <returns>User's cart</returns>
        [HttpGet("my-cart")]
        [Authorize]
        public async Task<IActionResult> GetMyCart()
        {
            try
            {
                // Get user ID from JWT token
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(CustomResponseDto<CartDto>.Unauthorized("Invalid user token"));
                }

                _logger.LogInformation("Getting cart for user: {UserId}", userId);

                var result = await _cartService.GetCartAsync(userId.Value);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user cart");
                return StatusCode(500, CustomResponseDto<CartDto>.InternalServerError("An error occurred while retrieving user cart"));
            }
        }

        #endregion
    }
}
