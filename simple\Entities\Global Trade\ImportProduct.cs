﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Global_Trade;

public class ImportProduct : BaseEntity
{
    public HsCode? HsCode { get; set; }
    public int HsCodeId { get; set; }
    public Category? Category { get; set; }
    public int CategoryId { get; set; }

    public Import Import { get; set; }
    public int ImportId { get; set; }

    [MaxLength(250)]
    public string? ProductName { get; set; }
    [MaxLength(150)]
    public int? ProductId { get; set; }
    public decimal? Weight { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? EstimatedSellingPrice { get; set; }
    public decimal? Total { get; set; }
    public int Quantity { get; set; }
    public string? Link { get; set; }
    public decimal? FreightCost { get; set; }
    public decimal? TotalWeight { get; set; }
    public decimal? CustomsDuty { get; set; }
    public decimal? Tva { get; set; }
}