namespace EtyraCommerce.Domain.Enums.Shipping
{
    /// <summary>
    /// Types of shipping rate calculation methods
    /// </summary>
    public enum RateCalculationType
    {
        /// <summary>
        /// Flat rate regardless of weight or order amount
        /// </summary>
        Flat = 1,

        /// <summary>
        /// Rate based on total weight of the order
        /// </summary>
        WeightBased = 2,

        /// <summary>
        /// Rate based on order total amount
        /// </summary>
        OrderAmountBased = 3
    }
}
