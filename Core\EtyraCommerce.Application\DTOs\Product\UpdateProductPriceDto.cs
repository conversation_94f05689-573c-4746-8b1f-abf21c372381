using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for updating product price
    /// </summary>
    public class UpdateProductPriceDto
    {
        /// <summary>
        /// New base price amount
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Base price must be greater than 0")]
        public decimal? BasePrice { get; set; }

        /// <summary>
        /// Base price currency
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? BasePriceCurrency { get; set; }

        /// <summary>
        /// New cost price amount
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Cost must be non-negative")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// Cost price currency
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? CostCurrency { get; set; }

        /// <summary>
        /// New sale price amount
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Sale price must be greater than 0")]
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// Sale price currency
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? SalePriceCurrency { get; set; }

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Reason for price change
        /// </summary>
        [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
        public string? Reason { get; set; }

        /// <summary>
        /// Price adjustment type
        /// </summary>
        public PriceAdjustmentType AdjustmentType { get; set; } = PriceAdjustmentType.Set;

        #region Validation

        /// <summary>
        /// Custom validation for price dates
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (SaleStartDate.HasValue && SaleEndDate.HasValue && SaleStartDate > SaleEndDate)
            {
                yield return new ValidationResult(
                    "Sale start date cannot be after sale end date",
                    new[] { nameof(SaleStartDate), nameof(SaleEndDate) });
            }

            if (SalePrice.HasValue && BasePrice.HasValue && SalePrice >= BasePrice)
            {
                yield return new ValidationResult(
                    "Sale price must be less than base price",
                    new[] { nameof(SalePrice) });
            }

            if (Cost.HasValue && BasePrice.HasValue && Cost > BasePrice)
            {
                yield return new ValidationResult(
                    "Cost should not exceed base price",
                    new[] { nameof(Cost) });
            }
        }

        #endregion
    }

    /// <summary>
    /// Price adjustment type enumeration
    /// </summary>
    public enum PriceAdjustmentType
    {
        /// <summary>
        /// Set price to exact value
        /// </summary>
        Set = 0,

        /// <summary>
        /// Increase price by percentage
        /// </summary>
        IncreaseByPercentage = 1,

        /// <summary>
        /// Decrease price by percentage
        /// </summary>
        DecreaseByPercentage = 2,

        /// <summary>
        /// Increase price by amount
        /// </summary>
        IncreaseByAmount = 3,

        /// <summary>
        /// Decrease price by amount
        /// </summary>
        DecreaseByAmount = 4
    }
}
