﻿using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Application.UnitOfWork
{
    /// <summary>
    /// Unit of Work interface for managing transactions and repository access
    /// Autofac DI container handles resource disposal automatically
    /// </summary>
    public interface IUnitOfWork
    {
        /// <summary>
        /// Gets read repository for entity type T
        /// </summary>
        IReadRepository<T> ReadRepository<T>() where T : BaseEntity;

        /// <summary>
        /// Gets write repository for entity type T
        /// </summary>
        IWriteRepository<T> WriteRepository<T>() where T : BaseEntity;

        /// <summary>
        /// Saves all changes asynchronously
        /// </summary>
        Task<int> CommitAsync();

        /// <summary>
        /// Saves all changes asynchronously (alias for CommitAsync)
        /// </summary>
        Task<int> SaveChangesAsync() => CommitAsync();

        /// <summary>
        /// Saves all changes synchronously
        /// </summary>
        int Commit();

        /// <summary>
        /// Begins a new transaction
        /// </summary>
        Task BeginTransactionAsync();

        /// <summary>
        /// Commits the current transaction
        /// </summary>
        Task CommitTransactionAsync();

        /// <summary>
        /// Rolls back the current transaction
        /// </summary>
        Task RollbackTransactionAsync();

        /// <summary>
        /// Checks if there's an active transaction
        /// </summary>
        bool HasActiveTransaction { get; }

        /// <summary>
        /// Discards all changes without saving
        /// </summary>
        void RejectChanges();
    }
}
