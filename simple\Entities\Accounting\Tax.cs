using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class Tax : BaseEntity
{
    [MaxLength(100)]
    public string? Name { get; set; }
    
    public decimal Rate { get; set; }
    
    public bool IsDefault { get; set; }
    
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    
    public ICollection<InvoiceTax> InvoiceTaxes { get; set; }

    public bool IsActive { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    [MaxLength(50)]
    public string? Code { get; set; }

    [MaxLength(50)]
    public string? CountryCode { get; set; }



    public TransactionType TransactionType { get; set; }

}