using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Shipping.Handlers.Commands
{
    /// <summary>
    /// Handler for creating a new country
    /// Delegates business logic to CountryProcessService
    /// </summary>
    public class CreateCountryCommandHandler : IRequestHandler<CreateCountryCommand, CreateCountryResponse>
    {
        private readonly ICountryProcessService _countryProcessService;
        private readonly ILogger<CreateCountryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CreateCountryCommandHandler(
            ICountryProcessService countryProcessService,
            ILogger<CreateCountryCommandHandler> logger)
        {
            _countryProcessService = countryProcessService ?? throw new ArgumentNullException(nameof(countryProcessService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the create country command
        /// </summary>
        public async Task<CreateCountryResponse> Handle(CreateCountryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing create country command for: {CountryName} ({CountryCode})", request.Name, request.Code);

                // Validation
                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    _logger.LogWarning("Create country command failed: Country name is required");
                    throw new ArgumentException("Country name is required");
                }

                if (string.IsNullOrWhiteSpace(request.Code))
                {
                    _logger.LogWarning("Create country command failed: Country code is required");
                    throw new ArgumentException("Country code is required");
                }

                if (string.IsNullOrWhiteSpace(request.IsoCode))
                {
                    _logger.LogWarning("Create country command failed: ISO code is required");
                    throw new ArgumentException("ISO code is required");
                }

                if (string.IsNullOrWhiteSpace(request.CurrencyCode))
                {
                    _logger.LogWarning("Create country command failed: Currency code is required");
                    throw new ArgumentException("Currency code is required");
                }

                // Delegate to CountryProcessService for business logic
                var result = await _countryProcessService.ProcessCreateCountryAsync(request, cancellationToken);

                if (result != null)
                {
                    _logger.LogInformation("Country creation successful for: {CountryName} ({CountryCode}), CountryId: {CountryId}",
                        request.Name, request.Code, result.Id);
                }
                else
                {
                    _logger.LogWarning("Country creation failed for: {CountryName} ({CountryCode})",
                        request.Name, request.Code);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing create country command for: {CountryName} ({CountryCode})", request.Name, request.Code);
                throw;
            }
        }
    }
}
