using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.User.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Commands
{
    /// <summary>
    /// Handler for ActivateUserCommand - Validates and delegates to UserProcessService
    /// </summary>
    public class ActivateUserCommandHandler : IRequestHandler<ActivateUserCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<ActivateUserCommandHandler> _logger;

        public ActivateUserCommandHandler(
            IUserProcessService userProcessService,
            ILogger<ActivateUserCommandHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(ActivateUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing user activation request for UserId: {UserId}", request.UserId);

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessActivateUserAsync(request.UserId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("User activation successful for UserId: {UserId}. Activated by: {ActivatedBy}",
                        request.UserId, request.ActivatedByUserId);

                    // TODO: Send activation notification email
                    // TODO: Log admin action if activated by admin
                    // TODO: Trigger user activation events
                }
                else
                {
                    _logger.LogWarning("User activation failed for UserId: {UserId}. Reason: {Message}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing user activation command for UserId: {UserId}", request.UserId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred during user activation");
            }
        }
    }
}
