using System;
using EtyraApp.Domain.Entities.Common;

namespace EtyraApp.Domain.Entities.EmailSystem
{
    public class Email : BaseEntity
    {
        public int EmailAccountId { get; set; } // E-posta hesabı ID'si
        public string Sender { get; set; } // Gönderen e-posta adresi
        public string Recipient { get; set; } // Alıcı e-posta adresi
        public string Subject { get; set; } // E-posta konusu
        public string Body { get; set; } // E-posta içeriği
        public DateTime SentDate { get; set; } // Gönderim tarihi
        public bool IsRead { get; set; } // Okunma durumu
        public bool IsDeleted { get; set; } // Silinme durumu
        public string? Status { get; set; } // E-posta durumu
        public bool IsSpam { get; set; } // Spam durumu
        public string? MessageId { get; set; } // E-posta mesaj kimliği
        public string? InReplyTo { get; set; } // Yanıt verilen e-postanın MessageId'si
        
        // Navigation property
        public EmailAccount EmailAccount { get; set; }
        public int UserId { get; set; }

        public List<Attachments>? Attachments { get; set; }

    }


    public class Attachments : BaseEntity
    {
        public int EmailId { get; set; }
        public string? Name { get; set; }
        public string? Url { get; set; }
    }
}
