using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Commands
{
    /// <summary>
    /// Command to add item to shopping cart
    /// </summary>
    public class AddToCartCommand : IRequest<CustomResponseDto<CartDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Product ID to add
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Quantity to add
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Product variant information (optional)
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special notes for this item (optional)
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Currency code for new carts
        /// </summary>
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Creates command from DTO
        /// </summary>
        public static AddToCartCommand FromDto(AddToCartDto dto, Guid? customerId = null)
        {
            return new AddToCartCommand
            {
                CustomerId = customerId,
                SessionId = dto.SessionId,
                ProductId = dto.ProductId,
                Quantity = dto.Quantity,
                VariantInfo = dto.VariantInfo,
                Notes = dto.Notes
            };
        }
    }
}
