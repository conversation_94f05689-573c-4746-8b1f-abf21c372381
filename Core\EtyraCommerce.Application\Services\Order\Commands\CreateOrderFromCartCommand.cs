using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.Services.Order.Commands
{
    /// <summary>
    /// Command to create an order from shopping cart
    /// </summary>
    public class CreateOrderFromCartCommand : IRequest<CustomResponseDto<OrderDto>>
    {
        /// <summary>
        /// Customer ID (null for guest orders)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer last name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CustomerLastName { get; set; } = string.Empty;

        /// <summary>
        /// Customer email
        /// </summary>
        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number (optional)
        /// </summary>
        [MaxLength(20)]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        [Required]
        public CreateAddressDto BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address (if different from billing)
        /// </summary>
        [Required]
        public CreateAddressDto ShippingAddress { get; set; } = null!;

        /// <summary>
        /// Payment method
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string PaymentMethod { get; set; } = string.Empty;

        /// <summary>
        /// Shipping method (optional)
        /// </summary>
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Customer notes (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Currency code (will be taken from cart if not specified)
        /// </summary>
        [MaxLength(3)]
        public string? Currency { get; set; }

        /// <summary>
        /// Whether to clear the cart after creating the order
        /// </summary>
        public bool ClearCartAfterOrder { get; set; } = true;

        /// <summary>
        /// Creates command from DTO
        /// </summary>
        public static CreateOrderFromCartCommand FromDto(CreateOrderFromCartDto dto, Guid? customerId = null)
        {
            return new CreateOrderFromCartCommand
            {
                CustomerId = customerId,
                SessionId = dto.SessionId,
                CustomerFirstName = dto.CustomerFirstName,
                CustomerLastName = dto.CustomerLastName,
                CustomerEmail = dto.CustomerEmail,
                CustomerPhone = dto.CustomerPhone,
                BillingAddress = dto.BillingAddress,
                ShippingAddress = dto.ShippingAddress,
                PaymentMethod = dto.PaymentMethod,
                ShippingMethod = dto.ShippingMethod,
                Notes = dto.Notes,
                Currency = dto.Currency,
                ClearCartAfterOrder = dto.ClearCartAfterOrder
            };
        }
    }
}
