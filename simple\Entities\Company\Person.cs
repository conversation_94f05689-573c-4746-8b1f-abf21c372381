﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Accounting;

namespace EtyraApp.Domain.Entities.Company;

public class Person : BaseEntity
{
    [MaxLength(5)]
    public string? Title { get; set; }

    [MaxLength(50)]
    public string? FirstName { get; set; }

    [MaxLength(50)]
    public string? LastName { get; set; }

    [MaxLength(200)]
    public string? Address { get; set; }

    [MaxLength(40)]
    public string? TelNo { get; set; }

    [MaxLength(40)]
    public string? Email { get; set; }

    [MaxLength(40)]
    public string? Skype { get; set; }

    [MaxLength(100)]
    public string? CustomContact { get; set; }

    [MaxLength(200)]
    public string? Comment { get; set; }

    [MaxLength(200)]
    public string? Image { get; set; }

    public int? SupplierId { get; set; }
    public Supplier? Supplier { get; set; }

    public int? ManufacturerId { get; set; }
    public Manufacturer? Manufacturer { get; set; }

}