﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Emag;

public class EmagCharacteristics : BaseEntity
{
    public int? ParentCategoryId { get; set; }
    public int EmagCharacteristicsId { get; set; }

    [MaxLength(50)]
    public string CharacteristicsGlobalName { get; set; }

    [MaxLength(50)]
    public string CharacteristicsROName { get; set; }
    public int? ParentCharacteristicsId { get; set; }
    public bool Status { get; set; }
    public ICollection<ProductEmagCharacteristic>? ProductEmagCharacteristics { get; set; }

}


