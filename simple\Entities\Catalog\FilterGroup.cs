﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Integrations;

namespace EtyraApp.Domain.Entities.Catalog;

public class FilterGroup : BaseEntity
{
    public int? AiPlatformId { get; set; }

    public ICollection<Filter> Filter { get; set; }
    public ICollection<FilterGroupDescription> FilterGroupDescriptions { get; set; }
    public ICollection<IntegrationStoreFilter>? IntegrationStoreFilters { get; set; }

}