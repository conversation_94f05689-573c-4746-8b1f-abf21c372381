using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Queries;

/// <summary>
/// Query to get a currency by ID
/// </summary>
public class GetCurrencyByIdQuery : IRequest<CustomResponseDto<CurrencyDto>>
{
    /// <summary>
    /// Currency ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetCurrencyByIdQuery(Guid id, bool tracking = false)
    {
        Id = id;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetCurrencyByIdQuery() { }
}

/// <summary>
/// Query to get a currency by code
/// </summary>
public class GetCurrencyByCodeQuery : IRequest<CustomResponseDto<CurrencyDto>>
{
    /// <summary>
    /// Currency code (e.g., USD, EUR, TRY)
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="tracking">Whether to track changes</param>
    public GetCurrencyByCodeQuery(string code, bool tracking = false)
    {
        Code = code;
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetCurrencyByCodeQuery() { }
}
