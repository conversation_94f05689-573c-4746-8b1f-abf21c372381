using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.Services.Cart.Commands;
using EtyraCommerce.Application.Services.Cart.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Cart
{
    /// <summary>
    /// Cart service implementation for MediatR command/query dispatch
    /// </summary>
    public class CartService : ICartService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CartService> _logger;

        public CartService(IMediator mediator, ILogger<CartService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        #region Cart Management

        public async Task<CustomResponseDto<CartDto>> AddToCartAsync(AddToCartDto addToCartDto, Guid? customerId = null)
        {
            _logger.LogInformation("CartService: Adding item to cart for ProductId: {ProductId}, CustomerId: {CustomerId}",
                addToCartDto.ProductId, customerId);

            var command = AddToCartCommand.FromDto(addToCartDto, customerId);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CartService: Add to cart completed for ProductId: {ProductId}, Success: {IsSuccess}",
                addToCartDto.ProductId, result.IsSuccess);

            return result;
        }

        public async Task<CustomResponseDto<CartDto>> UpdateCartItemAsync(UpdateCartItemDto updateCartItemDto, Guid? customerId = null, string? sessionId = null)
        {
            _logger.LogInformation("CartService: Updating cart item for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                updateCartItemDto.ProductId, customerId, sessionId);

            var command = UpdateCartItemCommand.FromDto(updateCartItemDto, customerId, sessionId);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CartService: Update cart item completed for ProductId: {ProductId}, Success: {IsSuccess}",
                updateCartItemDto.ProductId, result.IsSuccess);

            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> RemoveFromCartAsync(RemoveFromCartDto removeFromCartDto, Guid? customerId = null, string? sessionId = null)
        {
            _logger.LogInformation("CartService: Removing item from cart for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                removeFromCartDto.ProductId, customerId, sessionId);

            var command = RemoveFromCartCommand.FromDto(removeFromCartDto, customerId, sessionId);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CartService: Remove from cart completed for ProductId: {ProductId}, Success: {IsSuccess}",
                removeFromCartDto.ProductId, result.IsSuccess);

            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> ClearCartAsync(Guid? customerId = null, string? sessionId = null)
        {
            _logger.LogInformation("CartService: Clearing cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                customerId, sessionId);

            var command = customerId.HasValue
                ? ClearCartCommand.ForUser(customerId.Value)
                : ClearCartCommand.ForGuest(sessionId!);

            var result = await _mediator.Send(command);

            _logger.LogInformation("CartService: Clear cart completed for CustomerId: {CustomerId}, SessionId: {SessionId}, Success: {IsSuccess}",
                customerId, sessionId, result.IsSuccess);

            return result;
        }

        public async Task<CustomResponseDto<CartDto>> MergeCartAsync(MergeCartDto mergeCartDto)
        {
            _logger.LogInformation("CartService: Merging cart for SessionId: {SessionId}, CustomerId: {CustomerId}",
                mergeCartDto.SessionId, mergeCartDto.CustomerId);

            var command = MergeCartCommand.FromDto(mergeCartDto);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CartService: Merge cart completed for SessionId: {SessionId}, CustomerId: {CustomerId}, Success: {IsSuccess}",
                mergeCartDto.SessionId, mergeCartDto.CustomerId, result.IsSuccess);

            return result;
        }

        #endregion

        #region Cart Retrieval

        public async Task<CustomResponseDto<CartDto>> GetCartAsync(Guid? customerId = null, string? sessionId = null, bool includeInactive = false, bool includeExpired = false)
        {
            _logger.LogInformation("CartService: Getting cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                customerId, sessionId);

            var query = customerId.HasValue
                ? GetCartQuery.ForUser(customerId.Value, includeInactive, includeExpired)
                : GetCartQuery.ForGuest(sessionId!, includeInactive, includeExpired);

            var result = await _mediator.Send(query);

            _logger.LogInformation("CartService: Get cart completed for CustomerId: {CustomerId}, SessionId: {SessionId}, Success: {IsSuccess}",
                customerId, sessionId, result.IsSuccess);

            return result;
        }

        public async Task<CustomResponseDto<CartSummaryDto>> GetCartSummaryAsync(Guid? customerId = null, string? sessionId = null)
        {
            _logger.LogInformation("CartService: Getting cart summary for CustomerId: {CustomerId}, SessionId: {SessionId}",
                customerId, sessionId);

            var query = customerId.HasValue
                ? GetCartSummaryQuery.ForUser(customerId.Value)
                : GetCartSummaryQuery.ForGuest(sessionId!);

            var result = await _mediator.Send(query);

            _logger.LogInformation("CartService: Get cart summary completed for CustomerId: {CustomerId}, SessionId: {SessionId}, Success: {IsSuccess}",
                customerId, sessionId, result.IsSuccess);

            return result;
        }

        #endregion
    }
}
