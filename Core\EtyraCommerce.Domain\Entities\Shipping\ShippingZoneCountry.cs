using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Shipping zone country mapping entity
    /// Maps countries to shipping zones for zone-based shipping calculations
    /// </summary>
    public class ShippingZoneCountry : BaseEntity
    {
        #region Foreign Keys

        /// <summary>
        /// Reference to the shipping zone
        /// </summary>
        public Guid ShippingZoneId { get; set; }

        /// <summary>
        /// Reference to the country
        /// </summary>
        public Guid CountryId { get; set; }

        #endregion

        #region Properties

        /// <summary>
        /// Indicates if this country mapping is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for country within the zone
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Override minimum delivery days for this specific country
        /// </summary>
        public int? OverrideMinDeliveryDays { get; set; }

        /// <summary>
        /// Override maximum delivery days for this specific country
        /// </summary>
        public int? OverrideMaxDeliveryDays { get; set; }

        /// <summary>
        /// Additional shipping notes for this country within the zone
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Shipping zone
        /// </summary>
        public virtual ShippingZone ShippingZone { get; set; } = null!;

        /// <summary>
        /// Country
        /// </summary>
        public virtual Country Country { get; set; } = null!;

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected ShippingZoneCountry() { }

        /// <summary>
        /// Constructor for creating a new shipping zone country mapping
        /// </summary>
        public ShippingZoneCountry(Guid shippingZoneId, Guid countryId)
        {
            ShippingZoneId = shippingZoneId;
            CountryId = countryId;
            IsActive = true;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Activates this country mapping
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates this country mapping
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets override delivery times for this country
        /// </summary>
        public void SetOverrideDeliveryTimes(int? minDays, int? maxDays)
        {
            if (minDays.HasValue && minDays < 0)
                throw new ArgumentException("Minimum delivery days cannot be negative");
            
            if (maxDays.HasValue && maxDays < 0)
                throw new ArgumentException("Maximum delivery days cannot be negative");
            
            if (minDays.HasValue && maxDays.HasValue && minDays > maxDays)
                throw new ArgumentException("Minimum delivery days cannot be greater than maximum");

            OverrideMinDeliveryDays = minDays;
            OverrideMaxDeliveryDays = maxDays;
            MarkAsUpdated();
        }

        /// <summary>
        /// Clears override delivery times (use zone defaults)
        /// </summary>
        public void ClearOverrideDeliveryTimes()
        {
            OverrideMinDeliveryDays = null;
            OverrideMaxDeliveryDays = null;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for country within zone
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates notes for this country mapping
        /// </summary>
        public void UpdateNotes(string? notes)
        {
            Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim();
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets effective minimum delivery days (override or zone default)
        /// </summary>
        public int GetEffectiveMinDeliveryDays()
        {
            return OverrideMinDeliveryDays ?? ShippingZone?.DefaultMinDeliveryDays ?? 1;
        }

        /// <summary>
        /// Gets effective maximum delivery days (override or zone default)
        /// </summary>
        public int GetEffectiveMaxDeliveryDays()
        {
            return OverrideMaxDeliveryDays ?? ShippingZone?.DefaultMaxDeliveryDays ?? 7;
        }

        /// <summary>
        /// Gets delivery time range text for this country
        /// </summary>
        public string GetDeliveryTimeText()
        {
            var minDays = GetEffectiveMinDeliveryDays();
            var maxDays = GetEffectiveMaxDeliveryDays();

            if (minDays == maxDays)
                return $"{minDays} day{(minDays > 1 ? "s" : "")}";
            
            return $"{minDays}-{maxDays} days";
        }

        /// <summary>
        /// Checks if this country mapping can be used for shipping
        /// </summary>
        public bool CanShip() => IsActive && !IsDeleted && 
                                 ShippingZone?.CanShip() == true && 
                                 Country?.CanShipTo() == true;

        /// <summary>
        /// Checks if delivery times are overridden
        /// </summary>
        public bool HasOverrideDeliveryTimes() => 
            OverrideMinDeliveryDays.HasValue || OverrideMaxDeliveryDays.HasValue;

        #endregion

        #region Validation

        /// <summary>
        /// Validates shipping zone country mapping data
        /// </summary>
        public bool IsValid()
        {
            return ShippingZoneId != Guid.Empty &&
                   CountryId != Guid.Empty &&
                   (!OverrideMinDeliveryDays.HasValue || OverrideMinDeliveryDays >= 0) &&
                   (!OverrideMaxDeliveryDays.HasValue || OverrideMaxDeliveryDays >= 0) &&
                   (!OverrideMinDeliveryDays.HasValue || !OverrideMaxDeliveryDays.HasValue || 
                    OverrideMinDeliveryDays <= OverrideMaxDeliveryDays);
        }

        #endregion
    }
}
