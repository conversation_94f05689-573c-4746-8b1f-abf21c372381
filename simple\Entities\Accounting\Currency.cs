using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class Currency : BaseEntity
{
    [MaxLength(3)]
    public string Code { get; set; } // RON, EUR, USD
    
    [MaxLength(50)]
    public string Name { get; set; }
    
    [MaxLength(5)]
    public string Symbol { get; set; }
    
    public bool IsDefault { get; set; }
    
    public ICollection<ExchangeRate> FromExchangeRates { get; set; }
    public ICollection<ExchangeRate> ToExchangeRates { get; set; }

    public decimal Value { get; set; }

} 