using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for creating inventory items
    /// </summary>
    public class CreateInventoryCommandHandler : IRequestHandler<CreateInventoryCommand, CustomResponseDto<InventoryDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<CreateInventoryCommandHandler> _logger;

        public CreateInventoryCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<CreateInventoryCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<InventoryDto>> Handle(CreateInventoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling create inventory command for ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                    request.ProductId, request.WarehouseId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<InventoryDto>.BadRequest("Product ID is required");

                if (request.WarehouseId == Guid.Empty)
                    return CustomResponseDto<InventoryDto>.BadRequest("Warehouse ID is required");

                if (request.AvailableQuantity < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Available quantity cannot be negative");

                if (request.MinStockLevel < 0 || request.MaxStockLevel < 0 || request.ReorderPoint < 0 || request.ReorderQuantity < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Stock levels cannot be negative");

                if (request.MaxStockLevel > 0 && request.MinStockLevel > request.MaxStockLevel)
                    return CustomResponseDto<InventoryDto>.BadRequest("Minimum stock level cannot be greater than maximum");

                // Create DTO
                var createDto = new CreateInventoryDto
                {
                    ProductId = request.ProductId,
                    WarehouseId = request.WarehouseId,
                    AvailableQuantity = request.AvailableQuantity,
                    MinStockLevel = request.MinStockLevel,
                    MaxStockLevel = request.MaxStockLevel,
                    ReorderPoint = request.ReorderPoint,
                    ReorderQuantity = request.ReorderQuantity,
                    LocationCode = request.LocationCode,
                    SupplierReference = request.SupplierReference,
                    LeadTimeDays = request.LeadTimeDays,
                    Notes = request.Notes
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessCreateInventoryAsync(createDto);

                _logger.LogInformation("Create inventory command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling create inventory command for ProductId: {ProductId}, WarehouseId: {WarehouseId}",
                    request.ProductId, request.WarehouseId);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while creating inventory");
            }
        }
    }
}
