using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// DTO for updating an existing user
    /// </summary>
    public class UpdateUserDto
    {
        /// <summary>
        /// User's first name
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (optional)
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User's preferred language/culture
        /// </summary>
        [StringLength(10, ErrorMessage = "Culture cannot exceed 10 characters")]
        public string Culture { get; set; } = "en-US";

        /// <summary>
        /// User's timezone
        /// </summary>
        [StringLength(50, ErrorMessage = "TimeZone cannot exceed 50 characters")]
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// Indicates if the user account is enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Additional notes about the user (admin use)
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        /// <summary>
        /// Gets the user's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
}
