using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting inventory by product
    /// </summary>
    public class GetInventoryByProductQueryHandler : IRequestHandler<GetInventoryByProductQuery, CustomResponseDto<List<InventoryDto>>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetInventoryByProductQueryHandler> _logger;

        public GetInventoryByProductQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetInventoryByProductQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<InventoryDto>>> Handle(GetInventoryByProductQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get inventory by product query for ProductId: {ProductId}", request.ProductId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<List<InventoryDto>>.BadRequest("Product ID is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetInventoryByProductAsync(
                    request.ProductId,
                    request.WarehouseId,
                    request.ActiveWarehousesOnly);

                _logger.LogInformation("Get inventory by product query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get inventory by product query for ProductId: {ProductId}", request.ProductId);
                return CustomResponseDto<List<InventoryDto>>.InternalServerError("An error occurred while retrieving inventory");
            }
        }
    }
}
