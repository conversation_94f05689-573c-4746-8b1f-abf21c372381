namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Currency value object representing a monetary currency
    /// </summary>
    public class Currency : IEquatable<Currency>
    {
        public string Code { get; private set; }
        public string Name { get; private set; }
        public string Symbol { get; private set; }
        public int DecimalPlaces { get; private set; }

        // Parameterless constructor for EF Core
        private Currency()
        {
            Code = "USD";
            Name = "US Dollar";
            Symbol = "$";
            DecimalPlaces = 2;
        }

        private Currency(string code, string name, string symbol, int decimalPlaces)
        {
            Code = code;
            Name = name;
            Symbol = symbol;
            DecimalPlaces = decimalPlaces;
        }

        /// <summary>
        /// Creates currency from ISO currency code
        /// </summary>
        public static Currency FromCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                throw new ArgumentException("Currency code cannot be null or empty", nameof(code));

            var upperCode = code.ToUpperInvariant();

            return upperCode switch
            {
                "USD" => USD,
                "EUR" => EUR,
                "TRY" => TRY,
                "GBP" => GBP,
                "JPY" => JPY,
                "CAD" => CAD,
                "AUD" => AUD,
                "CHF" => CHF,
                "CNY" => CNY,
                "SEK" => SEK,
                "NOK" => NOK,
                "DKK" => DKK,
                _ => throw new ArgumentException($"Unsupported currency code: {code}", nameof(code))
            };
        }

        /// <summary>
        /// Formats amount with currency symbol and proper decimal places
        /// </summary>
        public string Format(decimal amount)
        {
            var rounded = Math.Round(amount, DecimalPlaces);

            return Code switch
            {
                "USD" => $"${rounded:F2}",
                "EUR" => $"€{rounded:F2}",
                "TRY" => $"{rounded:F2} ₺",
                "GBP" => $"£{rounded:F2}",
                "JPY" => $"¥{rounded:F0}",
                "CAD" => $"C${rounded:F2}",
                "AUD" => $"A${rounded:F2}",
                "CHF" => $"CHF {rounded:F2}",
                "CNY" => $"¥{rounded:F2}",
                "SEK" => $"{rounded:F2} kr",
                "NOK" => $"{rounded:F2} kr",
                "DKK" => $"{rounded:F2} kr",
                _ => $"{rounded:F(DecimalPlaces)} {Code}"
            };
        }

        /// <summary>
        /// Gets all supported currencies
        /// </summary>
        public static IReadOnlyList<Currency> GetSupportedCurrencies()
        {
            return new List<Currency>
            {
                USD, EUR, TRY, GBP, JPY, CAD, AUD, CHF, CNY, SEK, NOK, DKK
            }.AsReadOnly();
        }

        /// <summary>
        /// Checks if currency code is supported
        /// </summary>
        public static bool IsSupported(string code)
        {
            try
            {
                FromCode(code);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #region Predefined Currencies

        public static readonly Currency USD = new("USD", "US Dollar", "$", 2);
        public static readonly Currency EUR = new("EUR", "Euro", "€", 2);
        public static readonly Currency TRY = new("TRY", "Turkish Lira", "₺", 2);
        public static readonly Currency GBP = new("GBP", "British Pound", "£", 2);
        public static readonly Currency JPY = new("JPY", "Japanese Yen", "¥", 0);
        public static readonly Currency CAD = new("CAD", "Canadian Dollar", "C$", 2);
        public static readonly Currency AUD = new("AUD", "Australian Dollar", "A$", 2);
        public static readonly Currency CHF = new("CHF", "Swiss Franc", "CHF", 2);
        public static readonly Currency CNY = new("CNY", "Chinese Yuan", "¥", 2);
        public static readonly Currency SEK = new("SEK", "Swedish Krona", "kr", 2);
        public static readonly Currency NOK = new("NOK", "Norwegian Krone", "kr", 2);
        public static readonly Currency DKK = new("DKK", "Danish Krone", "kr", 2);

        #endregion

        #region Equality

        public bool Equals(Currency? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Code == other.Code;
        }

        public override bool Equals(object? obj)
        {
            return obj is Currency other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Code.GetHashCode();
        }

        public static bool operator ==(Currency? left, Currency? right)
        {
            if (left is null && right is null) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(Currency? left, Currency? right) => !(left == right);

        #endregion

        public override string ToString() => $"{Code} ({Name})";
    }
}
