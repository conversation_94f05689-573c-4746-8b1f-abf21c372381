using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for adjusting stock
    /// </summary>
    public class AdjustStockCommandHandler : IRequestHandler<AdjustStockCommand, CustomResponseDto<bool>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<AdjustStockCommandHandler> _logger;

        public AdjustStockCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<AdjustStockCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(AdjustStockCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling adjust stock command for InventoryId: {InventoryId}, NewQuantity: {NewQuantity}",
                    request.InventoryId, request.NewQuantity);

                // Validation
                if (request.InventoryId == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("Inventory ID is required");

                if (request.NewQuantity < 0)
                    return CustomResponseDto<bool>.BadRequest("New quantity cannot be negative");

                if (string.IsNullOrEmpty(request.Reason))
                    return CustomResponseDto<bool>.BadRequest("Reason is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessAdjustStockAsync(
                    request.InventoryId,
                    request.NewQuantity,
                    request.Reason,
                    request.Notes,
                    request.UserId);

                _logger.LogInformation("Adjust stock command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling adjust stock command for InventoryId: {InventoryId}", request.InventoryId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while adjusting stock");
            }
        }
    }
}
