using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Queries
{
    /// <summary>
    /// Query to get orders for a specific user
    /// </summary>
    public class GetUserOrdersQuery : IRequest<CustomResponseDto<PagedResult<OrderDto>>>
    {
        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Order status filter
        /// </summary>
        public OrderStatus? Status { get; set; }

        /// <summary>
        /// Payment status filter
        /// </summary>
        public PaymentStatus? PaymentStatus { get; set; }

        /// <summary>
        /// Start date filter
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date filter
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Include order items in response
        /// </summary>
        public bool IncludeItems { get; set; } = false;

        /// <summary>
        /// Sort field
        /// </summary>
        public OrderSortField SortBy { get; set; } = OrderSortField.CreatedAt;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;
    }
}
