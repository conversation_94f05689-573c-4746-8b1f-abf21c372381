using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Repositories.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Currency;
using EtyraCommerce.Persistence.Services;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Currency;

/// <summary>
/// Currency process service implementation for business logic operations
/// </summary>
public class CurrencyProcessService : Service<Domain.Entities.Currency.Currency, CurrencyDto>, ICurrencyProcessService
{
    private readonly ICurrencyReadRepository _currencyReadRepository;
    private readonly ICurrencyWriteRepository _currencyWriteRepository;

    public CurrencyProcessService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CurrencyProcessService> logger,
        ICurrencyReadRepository currencyReadRepository,
        ICurrencyWriteRepository currencyWriteRepository)
        : base(unitOfWork, mapper, logger)
    {
        _currencyReadRepository = currencyReadRepository;
        _currencyWriteRepository = currencyWriteRepository;
    }

    #region Command Operations

    /// <summary>
    /// Process create currency request
    /// </summary>
    /// <param name="createDto">Create currency DTO</param>
    /// <returns>Created currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> ProcessCreateAsync(CreateCurrencyDto createDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { createDto });

            // Validation
            if (string.IsNullOrWhiteSpace(createDto.Name))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency name is required");
            }

            if (string.IsNullOrWhiteSpace(createDto.Code))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency code is required");
            }

            if (string.IsNullOrWhiteSpace(createDto.Symbol))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency symbol is required");
            }

            // Validate currency code format (ISO 4217)
            var currencyCode = createDto.Code.Trim().ToUpper();
            if (currencyCode.Length != 3)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency code must be exactly 3 characters (ISO 4217 format)");
            }

            // Check if code already exists
            var codeExists = await _currencyReadRepository.CodeExistsAsync(currencyCode);
            if (codeExists)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest($"Currency code '{currencyCode}' already exists");
            }

            // Validate decimal places
            if (createDto.DecimalPlaces < 0 || createDto.DecimalPlaces > 8)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Decimal places must be between 0 and 8");
            }

            // Create currency entity
            var currency = new Domain.Entities.Currency.Currency
            {
                Code = currencyCode,
                Name = createDto.Name.Trim(),
                Symbol = createDto.Symbol.Trim(),
                DecimalPlaces = createDto.DecimalPlaces,
                IsActive = createDto.IsActive,
                DisplayOrder = createDto.DisplayOrder,
                FormatPattern = createDto.FormatPattern?.Trim()
            };

            // Handle default currency logic
            if (createDto.IsDefault)
            {
                // If this is set as default, unset all other defaults first
                await UnsetAllDefaultCurrenciesAsync();
                currency.SetAsDefault();
            }

            // Save to database
            var createdCurrency = await _currencyWriteRepository.AddAsync(currency);
            await _unitOfWork.CommitAsync();

            var currencyDto = _mapper.Map<CurrencyDto>(createdCurrency);

            _logger.LogInformation("Currency {CurrencyId} created successfully with code {Code}", 
                createdCurrency.Id, currencyCode);

            return CustomResponseDto<CurrencyDto>.Created(currencyDto, "Currency created successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    /// <summary>
    /// Process update currency request
    /// </summary>
    /// <param name="updateDto">Update currency DTO</param>
    /// <returns>Updated currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> ProcessUpdateAsync(UpdateCurrencyDto updateDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { updateDto });

            // Validation
            if (updateDto.Id == Guid.Empty)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency ID is required");
            }

            if (string.IsNullOrWhiteSpace(updateDto.Name))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency name is required");
            }

            if (string.IsNullOrWhiteSpace(updateDto.Code))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency code is required");
            }

            if (string.IsNullOrWhiteSpace(updateDto.Symbol))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency symbol is required");
            }

            // Validate currency code format (ISO 4217)
            var currencyCode = updateDto.Code.Trim().ToUpper();
            if (currencyCode.Length != 3)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency code must be exactly 3 characters (ISO 4217 format)");
            }

            // Get existing currency
            var existingCurrency = await _currencyReadRepository.GetByIdAsync(updateDto.Id, tracking: true);
            if (existingCurrency == null)
            {
                return CustomResponseDto<CurrencyDto>.NotFound("Currency not found");
            }

            // Check if code already exists (excluding current currency)
            var codeExists = await _currencyReadRepository.CodeExistsAsync(currencyCode, updateDto.Id);
            if (codeExists)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest($"Currency code '{currencyCode}' already exists");
            }

            // Validate decimal places
            if (updateDto.DecimalPlaces < 0 || updateDto.DecimalPlaces > 8)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Decimal places must be between 0 and 8");
            }

            // Update currency properties
            existingCurrency.Code = currencyCode;
            existingCurrency.Name = updateDto.Name.Trim();
            existingCurrency.Symbol = updateDto.Symbol.Trim();
            existingCurrency.DecimalPlaces = updateDto.DecimalPlaces;
            existingCurrency.IsActive = updateDto.IsActive;
            existingCurrency.DisplayOrder = updateDto.DisplayOrder;
            existingCurrency.FormatPattern = updateDto.FormatPattern?.Trim();

            // Handle default currency logic
            if (updateDto.IsDefault && !existingCurrency.IsDefault)
            {
                // If this is being set as default, unset all other defaults first
                await UnsetAllDefaultCurrenciesAsync();
                existingCurrency.SetAsDefault();
            }
            else if (!updateDto.IsDefault && existingCurrency.IsDefault)
            {
                // Cannot unset default currency without setting another as default
                return CustomResponseDto<CurrencyDto>.BadRequest("Cannot unset default currency. Please set another currency as default first");
            }

            // Save changes
            var updatedCurrency = await _currencyWriteRepository.UpdateAsync(existingCurrency);
            await _unitOfWork.CommitAsync();

            var currencyDto = _mapper.Map<CurrencyDto>(updatedCurrency);

            _logger.LogInformation("Currency {CurrencyId} updated successfully with code {Code}", 
                updateDto.Id, currencyCode);

            return CustomResponseDto<CurrencyDto>.Success(currencyDto, "Currency updated successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    /// <summary>
    /// Process delete currency request
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<NoContentDto>.BadRequest("Currency ID is required");
            }

            // Get existing currency
            var existingCurrency = await _currencyReadRepository.GetByIdAsync(id, tracking: true);
            if (existingCurrency == null)
            {
                return CustomResponseDto<NoContentDto>.NotFound("Currency not found");
            }

            // Check if currency can be deleted
            var canDelete = await _currencyReadRepository.CanDeleteAsync(id);
            if (!canDelete)
            {
                return CustomResponseDto<NoContentDto>.BadRequest("Currency cannot be deleted because it is either the default currency or is being used in orders, products, or payment methods");
            }

            // Delete currency
            var deleted = await _currencyWriteRepository.RemoveByIdAsync(id);
            if (!deleted)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError("Failed to delete currency");
            }

            await _unitOfWork.CommitAsync();

            _logger.LogInformation("Currency {CurrencyId} deleted successfully", id);

            return CustomResponseDto<NoContentDto>.Success("Currency deleted successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<NoContentDto>(ex);
        }
    }

    /// <summary>
    /// Process set default currency request
    /// </summary>
    /// <param name="id">Currency ID to set as default</param>
    /// <returns>Updated default currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> ProcessSetDefaultAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency ID is required");
            }

            // Get existing currency
            var existingCurrency = await _currencyReadRepository.GetByIdAsync(id, tracking: true);
            if (existingCurrency == null)
            {
                return CustomResponseDto<CurrencyDto>.NotFound("Currency not found");
            }

            if (!existingCurrency.IsActive)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Cannot set inactive currency as default");
            }

            // Set as default using repository method
            var updatedCurrency = await _currencyWriteRepository.SetAsDefaultAsync(id);
            if (updatedCurrency == null)
            {
                return CustomResponseDto<CurrencyDto>.InternalServerError("Failed to set currency as default");
            }

            await _unitOfWork.CommitAsync();

            var currencyDto = _mapper.Map<CurrencyDto>(updatedCurrency);

            _logger.LogInformation("Currency {CurrencyId} set as default successfully", id);

            return CustomResponseDto<CurrencyDto>.Success(currencyDto, "Currency set as default successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    #endregion

    #endregion

    #region Query Operations

    /// <summary>
    /// Get all currencies
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of currencies</returns>
    public async Task<CustomResponseDto<List<CurrencyDto>>> GetAllAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var currencies = await _currencyReadRepository.GetAllAsync(tracking);
            var currencyList = currencies.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name).ToList();
            var currencyDtos = _mapper.Map<List<CurrencyDto>>(currencyList);

            return CustomResponseDto<List<CurrencyDto>>.Success(currencyDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<CurrencyDto>>(ex);
        }
    }

    /// <summary>
    /// Get active currencies
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies</returns>
    public async Task<CustomResponseDto<List<CurrencyDto>>> GetActiveCurrenciesAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var currencies = await _currencyReadRepository.GetActiveCurrenciesAsync(tracking);
            var currencyDtos = _mapper.Map<List<CurrencyDto>>(currencies);

            return CustomResponseDto<List<CurrencyDto>>.Success(currencyDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<CurrencyDto>>(ex);
        }
    }

    /// <summary>
    /// Get active currencies summary
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active currencies summary</returns>
    public async Task<CustomResponseDto<List<CurrencySummaryDto>>> GetActiveCurrenciesSummaryAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var currencies = await _currencyReadRepository.GetActiveCurrenciesAsync(tracking);
            var currencySummaryDtos = _mapper.Map<List<CurrencySummaryDto>>(currencies);

            return CustomResponseDto<List<CurrencySummaryDto>>.Success(currencySummaryDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<CurrencySummaryDto>>(ex);
        }
    }

    /// <summary>
    /// Get currency by ID
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency details</returns>
    public async Task<CustomResponseDto<CurrencyDto>> GetByIdAsync(Guid id, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, tracking });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency ID is required");
            }

            var currency = await _currencyReadRepository.GetByIdAsync(id, tracking);
            if (currency == null)
            {
                return CustomResponseDto<CurrencyDto>.NotFound("Currency not found");
            }

            var currencyDto = _mapper.Map<CurrencyDto>(currency);

            return CustomResponseDto<CurrencyDto>.Success(currencyDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    /// <summary>
    /// Get currency by code
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Currency details</returns>
    public async Task<CustomResponseDto<CurrencyDto>> GetByCodeAsync(string code, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { code, tracking });

            if (string.IsNullOrWhiteSpace(code))
            {
                return CustomResponseDto<CurrencyDto>.BadRequest("Currency code is required");
            }

            var currency = await _currencyReadRepository.GetByCodeAsync(code.Trim().ToUpper(), tracking);
            if (currency == null)
            {
                return CustomResponseDto<CurrencyDto>.NotFound("Currency not found");
            }

            var currencyDto = _mapper.Map<CurrencyDto>(currency);

            return CustomResponseDto<CurrencyDto>.Success(currencyDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    /// <summary>
    /// Get default currency
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Default currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> GetDefaultCurrencyAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var currency = await _currencyReadRepository.GetDefaultCurrencyAsync(tracking);
            if (currency == null)
            {
                return CustomResponseDto<CurrencyDto>.NotFound("No default currency found");
            }

            var currencyDto = _mapper.Map<CurrencyDto>(currency);

            return CustomResponseDto<CurrencyDto>.Success(currencyDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyDto>(ex);
        }
    }

    #endregion

    #region Validation Operations

    /// <summary>
    /// Check if currency code exists
    /// </summary>
    /// <param name="code">Currency code</param>
    /// <param name="excludeId">Currency ID to exclude from check</param>
    /// <returns>True if exists</returns>
    public async Task<bool> CurrencyCodeExistsAsync(string code, Guid? excludeId = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { code, excludeId });

            if (string.IsNullOrWhiteSpace(code))
            {
                return false;
            }

            return await _currencyReadRepository.CodeExistsAsync(code.Trim().ToUpper(), excludeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if currency code exists: {Code}", code);
            return false;
        }
    }

    /// <summary>
    /// Check if currency can be deleted
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <returns>True if can be deleted</returns>
    public async Task<bool> CanDeleteCurrencyAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return false;
            }

            return await _currencyReadRepository.CanDeleteAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if currency can be deleted: {CurrencyId}", id);
            return false;
        }
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Unset all default currencies
    /// </summary>
    private async Task UnsetAllDefaultCurrenciesAsync()
    {
        var allCurrencies = await _currencyReadRepository.GetAllAsync(tracking: true);
        var defaultCurrencies = allCurrencies.Result.Where(c => c.IsDefault).ToList();

        foreach (var currency in defaultCurrencies)
        {
            currency.IsDefault = false;
        }

        if (defaultCurrencies.Any())
        {
            await _currencyWriteRepository.BulkUpdateAsync(defaultCurrencies);
        }
    }

    #endregion
}
