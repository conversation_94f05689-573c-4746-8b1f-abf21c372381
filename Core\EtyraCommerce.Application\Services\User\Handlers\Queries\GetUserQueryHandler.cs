using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserQuery - Validates and delegates to UserProcessService
    /// </summary>
    public class GetUserQueryHandler : IRequestHandler<GetUserQuery, CustomResponseDto<UserDto?>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<GetUserQueryHandler> _logger;

        public GetUserQueryHandler(
            IUserProcessService userProcessService,
            ILogger<GetUserQueryHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto?>> Handle(GetUserQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get user query for UserId: {UserId}", request.UserId);

                // Validation
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("Get user query failed: UserId is required");
                    return CustomResponseDto<UserDto?>.BadRequest("UserId is required");
                }

                // Use inherited generic service method from Service<User, UserDto>
                var result = await _userProcessService.GetByIdAsync(request.UserId, request.Tracking);

                if (result.IsSuccess && result.Data != null)
                {
                    _logger.LogDebug("User found for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<UserDto?>.Success(result.Data);
                }
                else if (result.IsSuccess && result.Data == null)
                {
                    _logger.LogDebug("User not found for UserId: {UserId}", request.UserId);
                    return CustomResponseDto<UserDto?>.Success(null, "User not found");
                }
                else
                {
                    _logger.LogWarning("Error getting user for UserId: {UserId}. Reason: {Message}",
                        request.UserId, result.Message);
                    return CustomResponseDto<UserDto?>.Failure(500, result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get user query for UserId: {UserId}", request.UserId);
                return CustomResponseDto<UserDto?>.InternalServerError("An error occurred while retrieving user");
            }
        }
    }
}
