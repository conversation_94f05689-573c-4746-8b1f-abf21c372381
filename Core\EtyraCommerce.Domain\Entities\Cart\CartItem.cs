using EtyraCommerce.Domain.ValueObjects;
using ProductEntity = EtyraCommerce.Domain.Entities.Product;

namespace EtyraCommerce.Domain.Entities.Cart
{
    /// <summary>
    /// Cart item entity representing an item in a shopping cart
    /// </summary>
    public class CartItem : BaseEntity
    {
        #region Basic Information

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Navigation property to Product
        /// </summary>
        public virtual ProductEntity.Product Product { get; set; } = null!;

        /// <summary>
        /// Product name at the time of adding to cart
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// Product SKU at the time of adding to cart
        /// </summary>
        public string ProductSku { get; set; } = string.Empty;

        /// <summary>
        /// Unit price at the time of adding to cart
        /// </summary>
        public Money UnitPrice { get; set; } = null!;

        /// <summary>
        /// Quantity of this item in cart
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Total price for this line item (UnitPrice * Quantity)
        /// </summary>
        public Money TotalPrice { get; set; } = null!;

        #endregion

        #region Optional Information

        /// <summary>
        /// Product variant information (e.g., "Size: L, Color: Red")
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Product image URL for display
        /// </summary>
        public string? ProductImageUrl { get; set; }

        /// <summary>
        /// Special notes for this item
        /// </summary>
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Shopping cart this item belongs to
        /// </summary>
        public ShoppingCart ShoppingCart { get; set; } = null!;

        /// <summary>
        /// Shopping cart ID
        /// </summary>
        public Guid ShoppingCartId { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public CartItem()
        {
        }

        /// <summary>
        /// Creates a new cart item
        /// </summary>
        public CartItem(Guid productId, string productName, string productSku, Money unitPrice, int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be greater than zero", nameof(quantity));

            // Id'yi set etme - EF Core otomatik generate edecek
            ProductId = productId;
            ProductName = productName ?? throw new ArgumentNullException(nameof(productName));
            ProductSku = productSku ?? throw new ArgumentNullException(nameof(productSku));
            UnitPrice = unitPrice ?? throw new ArgumentNullException(nameof(unitPrice));
            Quantity = quantity;

            CalculateTotalPrice();
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates the quantity and recalculates total price
        /// </summary>
        public void UpdateQuantity(int newQuantity)
        {
            if (newQuantity <= 0)
                throw new ArgumentException("Quantity must be greater than zero", nameof(newQuantity));

            Quantity = newQuantity;
            CalculateTotalPrice();
        }

        /// <summary>
        /// Updates the unit price and recalculates total price
        /// </summary>
        public void UpdateUnitPrice(Money newUnitPrice)
        {
            UnitPrice = newUnitPrice ?? throw new ArgumentNullException(nameof(newUnitPrice));
            CalculateTotalPrice();
        }

        /// <summary>
        /// Sets variant information
        /// </summary>
        public void SetVariantInfo(string variantInfo)
        {
            VariantInfo = variantInfo;
        }

        /// <summary>
        /// Sets product image URL
        /// </summary>
        public void SetProductImageUrl(string imageUrl)
        {
            ProductImageUrl = imageUrl;
        }

        /// <summary>
        /// Sets notes for this item
        /// </summary>
        public void SetNotes(string notes)
        {
            Notes = notes;
        }

        /// <summary>
        /// Calculates the total price for this line item
        /// </summary>
        private void CalculateTotalPrice()
        {
            var totalAmount = UnitPrice.Amount * Quantity;
            TotalPrice = new Money(totalAmount, UnitPrice.Currency);
        }

        /// <summary>
        /// Gets a display name for the item including variant info
        /// </summary>
        public string GetDisplayName()
        {
            if (string.IsNullOrEmpty(VariantInfo))
                return ProductName;

            return $"{ProductName} ({VariantInfo})";
        }

        /// <summary>
        /// Validates the cart item
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(ProductName) &&
                   !string.IsNullOrWhiteSpace(ProductSku) &&
                   UnitPrice != null &&
                   UnitPrice.Amount > 0 &&
                   Quantity > 0 &&
                   TotalPrice != null &&
                   TotalPrice.Amount > 0;
        }

        /// <summary>
        /// Gets item summary
        /// </summary>
        public string GetSummary()
        {
            return $"{GetDisplayName()} - Qty: {Quantity}, Unit: {UnitPrice.Amount:C}, Total: {TotalPrice.Amount:C}";
        }

        /// <summary>
        /// Checks if this item represents the same product (for merging)
        /// </summary>
        public bool IsSameProduct(Guid productId, string? variantInfo = null)
        {
            return ProductId == productId &&
                   string.Equals(VariantInfo, variantInfo, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Creates a copy of this cart item
        /// </summary>
        public CartItem Clone()
        {
            return new CartItem(ProductId, ProductName, ProductSku, UnitPrice, Quantity)
            {
                VariantInfo = VariantInfo,
                ProductImageUrl = ProductImageUrl,
                Notes = Notes
            };
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the unit price formatted as string
        /// </summary>
        public string FormattedUnitPrice => $"{UnitPrice.Amount:C} {UnitPrice.Currency.Code}";

        /// <summary>
        /// Gets the total price formatted as string
        /// </summary>
        public string FormattedTotalPrice => $"{TotalPrice.Amount:C} {TotalPrice.Currency.Code}";

        /// <summary>
        /// Gets the savings per unit if there's a discount (placeholder for future discount feature)
        /// </summary>
        public Money? SavingsPerUnit => null; // TODO: Implement when discount system is added

        #endregion
    }
}
