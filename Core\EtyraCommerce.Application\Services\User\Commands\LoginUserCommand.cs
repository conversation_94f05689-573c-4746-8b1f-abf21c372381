using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for user login authentication
    /// </summary>
    public class LoginUserCommand : IRequest<CustomResponseDto<UserDto>>
    {
        /// <summary>
        /// Email or username for login
        /// </summary>
        public string EmailOrUsername { get; set; } = string.Empty;

        /// <summary>
        /// User password
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Remember me option for extended session
        /// </summary>
        public bool RememberMe { get; set; } = false;

        /// <summary>
        /// IP address of the login attempt
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the browser
        /// </summary>
        public string? UserAgent { get; set; }

        public LoginUserCommand() { }

        public LoginUserCommand(string emailOrUsername, string password, bool rememberMe = false)
        {
            EmailOrUsername = emailOrUsername;
            Password = password;
            RememberMe = rememberMe;
        }

        public LoginUserCommand(UserLoginDto loginDto)
        {
            EmailOrUsername = loginDto.EmailOrUsername;
            Password = loginDto.Password;
            RememberMe = loginDto.RememberMe;
        }
    }
}
