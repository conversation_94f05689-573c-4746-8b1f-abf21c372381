﻿using System.Text.Json.Serialization;

namespace EtyraCommerce.Application.DTOs.CustomResponse
{
    /// <summary>
    /// Custom response wrapper for service layer operations
    /// </summary>
    /// <typeparam name="T">Type of data in the response</typeparam>
    public class CustomResponseDto<T>
    {
        /// <summary>
        /// Response data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// List of error messages
        /// </summary>
        public List<string> ErrorList { get; set; } = new();

        /// <summary>
        /// Success indicator
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// HTTP status code
        /// </summary>
        [JsonIgnore]
        public int StatusCode { get; set; }

        /// <summary>
        /// Response timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Validation errors (field-specific)
        /// </summary>
        public Dictionary<string, List<string>> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Checks if response has any errors
        /// </summary>
        [JsonIgnore]
        public bool HasErrors => ErrorList.Any() || ValidationErrors.Any();

        /// <summary>
        /// Checks if response has validation errors
        /// </summary>
        [JsonIgnore]
        public bool HasValidationErrors => ValidationErrors.Any();

        #region Success Factory Methods

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        public static CustomResponseDto<T> Success(int statusCode, T data, string message = "")
        {
            return new CustomResponseDto<T>
            {
                Data = data,
                StatusCode = statusCode,
                IsSuccess = true,
                Message = message
            };
        }

        /// <summary>
        /// Creates a successful response without data
        /// </summary>
        public static CustomResponseDto<T> Success(int statusCode, string message = "")
        {
            return new CustomResponseDto<T>
            {
                StatusCode = statusCode,
                IsSuccess = true,
                Message = message
            };
        }

        /// <summary>
        /// Creates a successful response with default 200 status
        /// </summary>
        public static CustomResponseDto<T> Success(T data, string message = "")
        {
            return Success(200, data, message);
        }

        /// <summary>
        /// Creates a successful response for created resource (201)
        /// </summary>
        public static CustomResponseDto<T> Created(T data, string message = "Resource created successfully")
        {
            return Success(201, data, message);
        }

        /// <summary>
        /// Creates a successful response for no content (204)
        /// </summary>
        public static CustomResponseDto<T> NoContent(string message = "")
        {
            return Success(204, message);
        }

        #endregion

        #region Failure Factory Methods

        /// <summary>
        /// Creates a failure response with error list
        /// </summary>
        public static CustomResponseDto<T> Failure(int statusCode, List<string> errorList, string message = "")
        {
            return new CustomResponseDto<T>
            {
                StatusCode = statusCode,
                ErrorList = errorList ?? new List<string>(),
                IsSuccess = false,
                Message = message
            };
        }

        /// <summary>
        /// Creates a failure response with single error
        /// </summary>
        public static CustomResponseDto<T> Failure(int statusCode, string error, string message = "")
        {
            return new CustomResponseDto<T>
            {
                StatusCode = statusCode,
                ErrorList = new List<string> { error },
                IsSuccess = false,
                Message = message
            };
        }

        /// <summary>
        /// Creates a validation failure response (400)
        /// </summary>
        public static CustomResponseDto<T> ValidationFailure(Dictionary<string, List<string>> validationErrors, string message = "Validation failed")
        {
            return new CustomResponseDto<T>
            {
                StatusCode = 400,
                ValidationErrors = validationErrors ?? new Dictionary<string, List<string>>(),
                IsSuccess = false,
                Message = message
            };
        }

        /// <summary>
        /// Creates a validation failure response with simple errors
        /// </summary>
        public static CustomResponseDto<T> ValidationFailure(List<string> errors, string message = "Validation failed")
        {
            return new CustomResponseDto<T>
            {
                StatusCode = 400,
                ErrorList = errors ?? new List<string>(),
                IsSuccess = false,
                Message = message
            };
        }

        /// <summary>
        /// Creates a not found response (404)
        /// </summary>
        public static CustomResponseDto<T> NotFound(string message = "Resource not found")
        {
            return Failure(404, message, message);
        }

        /// <summary>
        /// Creates a bad request response (400)
        /// </summary>
        public static CustomResponseDto<T> BadRequest(string message)
        {
            return Failure(400, message, message);
        }

        /// <summary>
        /// Creates an unauthorized response (401)
        /// </summary>
        public static CustomResponseDto<T> Unauthorized(string message = "Unauthorized")
        {
            return Failure(401, message, message);
        }

        /// <summary>
        /// Creates a forbidden response (403)
        /// </summary>
        public static CustomResponseDto<T> Forbidden(string message = "Forbidden")
        {
            return Failure(403, message, message);
        }

        /// <summary>
        /// Creates an internal server error response (500)
        /// </summary>
        public static CustomResponseDto<T> InternalServerError(string message = "Internal server error")
        {
            return Failure(500, message, message);
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Adds an error to the error list
        /// </summary>
        public CustomResponseDto<T> AddError(string error)
        {
            ErrorList.Add(error);
            IsSuccess = false;
            return this;
        }

        /// <summary>
        /// Adds multiple errors to the error list
        /// </summary>
        public CustomResponseDto<T> AddErrors(IEnumerable<string> errors)
        {
            ErrorList.AddRange(errors);
            IsSuccess = false;
            return this;
        }

        /// <summary>
        /// Adds a validation error
        /// </summary>
        public CustomResponseDto<T> AddValidationError(string field, string error)
        {
            if (!ValidationErrors.ContainsKey(field))
                ValidationErrors[field] = new List<string>();

            ValidationErrors[field].Add(error);
            IsSuccess = false;
            return this;
        }

        /// <summary>
        /// Gets all errors as a flat list
        /// </summary>
        public List<string> GetAllErrors()
        {
            var allErrors = new List<string>(ErrorList);

            foreach (var kvp in ValidationErrors)
            {
                allErrors.AddRange(kvp.Value.Select(error => $"{kvp.Key}: {error}"));
            }

            return allErrors;
        }

        /// <summary>
        /// Gets all errors as a single string
        /// </summary>
        public string GetAllErrorsAsString(string separator = "; ")
        {
            return string.Join(separator, GetAllErrors());
        }

        #endregion
    }
}