using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Shipping method entity for European carriers
    /// </summary>
    public class ShippingMethod : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Method name (e.g., "Express Delivery", "Standard Shipping")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Unique method code (e.g., "DHL_EXPRESS", "UPS_STANDARD")
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Carrier name (e.g., "DHL", "UPS", "Fan Courier")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CarrierName { get; set; } = string.Empty;

        /// <summary>
        /// Carrier code (e.g., "DHL", "UPS", "FC")
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string CarrierCode { get; set; } = string.Empty;

        /// <summary>
        /// Method description
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Shipping method type
        /// </summary>
        public ShippingMethodType Type { get; set; }

        /// <summary>
        /// Shipping zone this method belongs to
        /// </summary>
        public Guid ShippingZoneId { get; set; }

        /// <summary>
        /// Is method active for new shipments
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Minimum delivery days
        /// </summary>
        public int MinDeliveryDays { get; set; }

        /// <summary>
        /// Maximum delivery days
        /// </summary>
        public int MaxDeliveryDays { get; set; }

        /// <summary>
        /// Maximum package weight (kg)
        /// </summary>
        public decimal MaxWeight { get; set; }

        /// <summary>
        /// Maximum package length (cm)
        /// </summary>
        public decimal? MaxLength { get; set; }

        /// <summary>
        /// Maximum package width (cm)
        /// </summary>
        public decimal? MaxWidth { get; set; }

        /// <summary>
        /// Maximum package height (cm)
        /// </summary>
        public decimal? MaxHeight { get; set; }

        /// <summary>
        /// Has tracking capability
        /// </summary>
        public bool HasTracking { get; set; } = true;

        /// <summary>
        /// Has insurance option
        /// </summary>
        public bool HasInsurance { get; set; } = false;

        /// <summary>
        /// Supports cash on delivery
        /// </summary>
        public bool SupportsCashOnDelivery { get; set; } = false;

        /// <summary>
        /// API endpoint for carrier integration
        /// </summary>
        [MaxLength(500)]
        public string? ApiEndpoint { get; set; }

        /// <summary>
        /// API credentials (encrypted)
        /// </summary>
        [MaxLength(1000)]
        public string? ApiCredentials { get; set; }

        /// <summary>
        /// API configuration JSON
        /// </summary>
        public string? ApiConfiguration { get; set; }

        /// <summary>
        /// Display order for UI
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Additional notes
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Shipping zone
        /// </summary>
        public virtual ShippingZone ShippingZone { get; set; } = null!;

        /// <summary>
        /// Shipping rates for this method
        /// </summary>
        public virtual ICollection<ShippingRate> ShippingRates { get; set; } = new List<ShippingRate>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public ShippingMethod() { }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public ShippingMethod(
            string name,
            string code,
            string carrierName,
            string carrierCode,
            ShippingMethodType type,
            Guid shippingZoneId,
            int minDeliveryDays,
            int maxDeliveryDays,
            decimal maxWeight)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Code = code?.Trim() ?? throw new ArgumentNullException(nameof(code));
            CarrierName = carrierName?.Trim() ?? throw new ArgumentNullException(nameof(carrierName));
            CarrierCode = carrierCode?.Trim() ?? throw new ArgumentNullException(nameof(carrierCode));
            Type = type;
            ShippingZoneId = shippingZoneId;
            MinDeliveryDays = minDeliveryDays;
            MaxDeliveryDays = maxDeliveryDays;
            MaxWeight = maxWeight;
            IsActive = true;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates method information
        /// </summary>
        public void UpdateInfo(string name, string description = null, string notes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Description = string.IsNullOrWhiteSpace(description) ? null : description.Trim();
            Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets delivery time range
        /// </summary>
        public void SetDeliveryTimes(int minDays, int maxDays)
        {
            if (minDays < 0 || maxDays < 0)
                throw new ArgumentException("Delivery days cannot be negative");

            if (minDays > maxDays)
                throw new ArgumentException("Minimum delivery days cannot be greater than maximum");

            MinDeliveryDays = minDays;
            MaxDeliveryDays = maxDays;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets weight and dimension limits
        /// </summary>
        public void SetLimits(decimal maxWeight, decimal? maxLength = null, decimal? maxWidth = null, decimal? maxHeight = null)
        {
            if (maxWeight <= 0)
                throw new ArgumentException("Maximum weight must be positive");

            MaxWeight = maxWeight;
            MaxLength = maxLength;
            MaxWidth = maxWidth;
            MaxHeight = maxHeight;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets service features
        /// </summary>
        public void SetFeatures(bool hasTracking, bool hasInsurance, bool supportsCashOnDelivery)
        {
            HasTracking = hasTracking;
            HasInsurance = hasInsurance;
            SupportsCashOnDelivery = supportsCashOnDelivery;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets API integration configuration
        /// </summary>
        public void SetApiConfiguration(string? endpoint, string? credentials, string? configuration)
        {
            ApiEndpoint = string.IsNullOrWhiteSpace(endpoint) ? null : endpoint.Trim();
            ApiCredentials = string.IsNullOrWhiteSpace(credentials) ? null : credentials.Trim();
            ApiConfiguration = string.IsNullOrWhiteSpace(configuration) ? null : configuration.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Activates the shipping method
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the shipping method
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        #endregion

        #region Display Methods

        /// <summary>
        /// Gets display name with carrier and delivery time
        /// </summary>
        public string GetDisplayName() => $"{CarrierName} - {Name} ({MinDeliveryDays}-{MaxDeliveryDays} days)";

        /// <summary>
        /// Gets delivery time text
        /// </summary>
        public string GetDeliveryTimeText()
        {
            if (MinDeliveryDays == MaxDeliveryDays)
                return $"{MinDeliveryDays} day{(MinDeliveryDays > 1 ? "s" : "")}";

            return $"{MinDeliveryDays}-{MaxDeliveryDays} days";
        }

        /// <summary>
        /// Gets features text (tracking, insurance, COD)
        /// </summary>
        public string GetFeaturesText()
        {
            var features = new List<string>();

            if (HasTracking)
                features.Add("Tracking");

            if (HasInsurance)
                features.Add("Insurance");

            if (SupportsCashOnDelivery)
                features.Add("Cash on Delivery");

            return features.Count > 0 ? string.Join(", ", features) : "Standard";
        }

        /// <summary>
        /// Gets method type display name
        /// </summary>
        public string GetTypeDisplayName()
        {
            return Type switch
            {
                ShippingMethodType.Express => "Express",
                ShippingMethodType.Overnight => "Overnight",
                ShippingMethodType.Standard => "Standard",
                ShippingMethodType.Economy => "Economy",
                _ => Type.ToString()
            };
        }

        #endregion

        #region Validation

        /// <summary>
        /// Checks if method can ship to given weight
        /// </summary>
        public bool CanShipWeight(decimal weight) => weight <= MaxWeight && weight > 0;

        /// <summary>
        /// Checks if method supports given weight
        /// </summary>
        public bool SupportsWeight(decimal weight) => weight <= MaxWeight;

        /// <summary>
        /// Checks if method is valid for shipping
        /// </summary>
        public bool IsValidForShipping() => !IsDeleted && MinDeliveryDays > 0 && MaxDeliveryDays >= MinDeliveryDays && MaxWeight > 0;

        /// <summary>
        /// Validates shipping method data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Code) &&
                   !string.IsNullOrWhiteSpace(CarrierName) &&
                   !string.IsNullOrWhiteSpace(CarrierCode) &&
                   MinDeliveryDays >= 0 &&
                   MaxDeliveryDays >= MinDeliveryDays &&
                   MaxWeight > 0;
        }

        #endregion
    }

    /// <summary>
    /// Shipping method types for European market
    /// </summary>
    public enum ShippingMethodType
    {
        /// <summary>
        /// Standard shipping (2-5 days)
        /// </summary>
        Standard = 1,

        /// <summary>
        /// Express shipping (1-2 days)
        /// </summary>
        Express = 2,

        /// <summary>
        /// Overnight shipping (next day)
        /// </summary>
        Overnight = 3,

        /// <summary>
        /// Economy shipping (5-10 days)
        /// </summary>
        Economy = 4
    }
}