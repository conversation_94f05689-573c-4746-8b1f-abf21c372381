using AutoMapper;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Services.User;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Cryptography;
using System.Text;

namespace EtyraCommerce.Tests.Services
{
    public class UserProcessServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<UserProcessService>> _mockLogger;
        private readonly Mock<IReadRepository<User>> _mockReadRepository;
        private readonly Mock<IWriteRepository<User>> _mockWriteRepository;
        private readonly UserProcessService _service;

        public UserProcessServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<UserProcessService>>();
            _mockReadRepository = new Mock<IReadRepository<User>>();
            _mockWriteRepository = new Mock<IWriteRepository<User>>();

            _mockUnitOfWork.Setup(x => x.ReadRepository<User>()).Returns(_mockReadRepository.Object);
            _mockUnitOfWork.Setup(x => x.WriteRepository<User>()).Returns(_mockWriteRepository.Object);

            _service = new UserProcessService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object);
        }

        #region ProcessLoginAsync Tests

        [Fact]
        public async Task ProcessLoginAsync_ValidCredentials_ReturnsSuccessWithUserDto()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "ValidPassword123!";
            var passwordSalt = "testSalt";
            var passwordHash = CreatePasswordHash(password, passwordSalt);

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(email),
                Username = "testuser",
                FirstName = "Test",
                LastName = "User",
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsEnabled = true,
                IsEmailConfirmed = true,
                FailedLoginAttempts = 0,
                LockedUntil = null
            };

            var userDto = new UserDto
            {
                Id = user.Id,
                Email = email,
                Username = user.Username,
                FirstName = user.FirstName,
                LastName = user.LastName,
                IsActive = user.IsActive,
                IsEmailConfirmed = user.IsEmailConfirmed
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { user }.AsQueryable());

            _mockMapper
                .Setup(x => x.Map<UserDto>(user))
                .Returns(userDto);

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(user.Id);
            result.Data.Email.Should().Be(email);
            result.Message.Should().Be("Login successful");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(user), Times.Once);
        }

        [Fact]
        public async Task ProcessLoginAsync_UserNotFound_ReturnsBadRequest()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "ValidPassword123!";

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User>().AsQueryable());

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Invalid email/username or password");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Never);
        }

        [Fact]
        public async Task ProcessLoginAsync_InvalidPassword_ReturnsBadRequest()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "WrongPassword";
            var correctPassword = "ValidPassword123!";
            var passwordSalt = "testSalt";
            var passwordHash = CreatePasswordHash(correctPassword, passwordSalt);

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(email),
                Username = "testuser",
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsEnabled = true,
                IsEmailConfirmed = true,
                FailedLoginAttempts = 0
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { user }.AsQueryable());

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Invalid email/username or password");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Never);
        }

        [Fact]
        public async Task ProcessLoginAsync_InactiveUser_ReturnsBadRequest()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "ValidPassword123!";
            var passwordSalt = "testSalt";
            var passwordHash = CreatePasswordHash(password, passwordSalt);

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(email),
                Username = "testuser",
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsEnabled = false, // User is inactive
                IsEmailConfirmed = true,
                FailedLoginAttempts = 0
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { user }.AsQueryable());

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Account is deactivated");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Never);
        }

        [Fact]
        public async Task ProcessLoginAsync_EmailNotConfirmed_ReturnsBadRequest()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "ValidPassword123!";
            var passwordSalt = "testSalt";
            var passwordHash = CreatePasswordHash(password, passwordSalt);

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(email),
                Username = "testuser",
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsEnabled = true,
                IsEmailConfirmed = false, // Email not confirmed
                FailedLoginAttempts = 0
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { user }.AsQueryable());

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email address is not confirmed");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Never);
        }

        [Fact]
        public async Task ProcessLoginAsync_AccountLocked_ReturnsBadRequest()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "ValidPassword123!";
            var passwordSalt = "testSalt";
            var passwordHash = CreatePasswordHash(password, passwordSalt);

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(email),
                Username = "testuser",
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsEnabled = true,
                IsEmailConfirmed = true,
                FailedLoginAttempts = 5,
                LockedUntil = DateTime.UtcNow.AddMinutes(30) // Account locked
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { user }.AsQueryable());

            // Act
            var result = await _service.ProcessLoginAsync(email, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Contain("Account is locked");

            _mockReadRepository.Verify(x => x.GetAllAsync(false), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Never);
        }

        #endregion

        #region ProcessRegisterAsync Tests

        [Fact]
        public async Task ProcessRegisterAsync_ValidData_ReturnsCreatedWithUserDto()
        {
            // Arrange
            var createUserDto = new CreateUserDto
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                PhoneNumber = "+**********",
                AcceptTerms = true,
                Culture = "en-US",
                TimeZone = "UTC"
            };

            var userId = Guid.NewGuid();
            var user = new User
            {
                Id = userId,
                Email = new Email(createUserDto.Email),
                Username = createUserDto.Username,
                FirstName = createUserDto.FirstName,
                LastName = createUserDto.LastName,
                IsEnabled = true,
                IsEmailConfirmed = false
            };

            var userDto = new UserDto
            {
                Id = userId,
                Email = createUserDto.Email,
                Username = createUserDto.Username,
                FirstName = createUserDto.FirstName,
                LastName = createUserDto.LastName,
                IsEnabled = true,
                IsEmailConfirmed = false
            };

            // Setup empty repository for email/username checks
            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User>().AsQueryable());

            _mockWriteRepository
                .Setup(x => x.AddAsync(It.IsAny<User>()))
                .ReturnsAsync(user);

            _mockMapper
                .Setup(x => x.Map<UserDto>(It.IsAny<User>()))
                .Returns(userDto);

            // Act
            var result = await _service.ProcessRegisterAsync(createUserDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
            result.Data.Email.Should().Be(createUserDto.Email);
            result.Data.Username.Should().Be(createUserDto.Username);
            result.Message.Should().Be("User registered successfully. Please check your email for confirmation.");

            _mockWriteRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.CommitAsync(), Times.Once);
            _mockMapper.Verify(x => x.Map<UserDto>(It.IsAny<User>()), Times.Once);
        }

        [Fact]
        public async Task ProcessRegisterAsync_EmailAlreadyExists_ReturnsBadRequest()
        {
            // Arrange
            var createUserDto = new CreateUserDto
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            var existingUser = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(createUserDto.Email),
                Username = "existinguser"
            };

            _mockReadRepository
                .Setup(x => x.GetAllAsync(false))
                .ReturnsAsync(new List<User> { existingUser }.AsQueryable());

            // Act
            var result = await _service.ProcessRegisterAsync(createUserDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Email already exists");

            _mockWriteRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Never);
            _mockUnitOfWork.Verify(x => x.CommitAsync(), Times.Never);
        }

        #endregion

        #region Helper Methods

        private string CreatePasswordHash(string password, string salt)
        {
            using var hmac = new HMACSHA512(Convert.FromBase64String(salt));
            return Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
        }

        #endregion
    }
}
