using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Queries
{
    /// <summary>
    /// Query to get stock status for a product
    /// </summary>
    public class GetStockStatusQuery : IRequest<CustomResponseDto<StockStatusDto>>
    {
        public Guid ProductId { get; set; }
        public bool ActiveWarehousesOnly { get; set; } = true;
    }
}
