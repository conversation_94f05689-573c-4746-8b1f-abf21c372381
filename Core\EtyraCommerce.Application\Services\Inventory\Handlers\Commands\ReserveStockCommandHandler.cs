using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for reserving stock
    /// </summary>
    public class ReserveStockCommandHandler : IRequestHandler<ReserveStockCommand, CustomResponseDto<bool>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<ReserveStockCommandHandler> _logger;

        public ReserveStockCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<ReserveStockCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(ReserveStockCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling reserve stock command for ProductId: {ProductId}, Quantity: {Quantity}, Reference: {Reference}",
                    request.ProductId, request.Quantity, request.Reference);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("Product ID is required");

                if (request.Quantity <= 0)
                    return CustomResponseDto<bool>.BadRequest("Quantity must be positive");

                if (string.IsNullOrEmpty(request.Reference))
                    return CustomResponseDto<bool>.BadRequest("Reference is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessReserveStockAsync(
                    request.ProductId,
                    request.WarehouseId,
                    request.Quantity,
                    request.Reference,
                    request.Reason,
                    request.UserId);

                _logger.LogInformation("Reserve stock command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling reserve stock command for ProductId: {ProductId}, Reference: {Reference}",
                    request.ProductId, request.Reference);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while reserving stock");
            }
        }
    }
}
