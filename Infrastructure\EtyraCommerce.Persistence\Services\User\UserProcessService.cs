using AutoMapper;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace EtyraCommerce.Persistence.Services.User
{
    /// <summary>
    /// User Process Service Implementation - Contains business logic
    /// Called by CQRS Handlers for actual business processing
    /// </summary>
    public class UserProcessService : Service<Domain.Entities.User.User, UserDto>, IUserProcessService
    {
        public UserProcessService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<UserProcessService> logger)
            : base(unitOfWork, mapper, logger)
        {
        }

        #region Authentication & Authorization

        public async Task<CustomResponseDto<UserDto>> ProcessLoginAsync(string emailOrUsername, string password)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { emailOrUsername });

                // Validate input
                if (string.IsNullOrWhiteSpace(emailOrUsername))
                {
                    return CustomResponseDto<UserDto>.BadRequest("Email or username is required");
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    return CustomResponseDto<UserDto>.BadRequest("Password is required");
                }

                // Find user by email or username
                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var users = await readRepo.GetAllAsync(tracking: false);
                var user = users.FirstOrDefault(u =>
                    u.Email.Value.ToLower() == emailOrUsername.ToLower() ||
                    u.Username.ToLower() == emailOrUsername.ToLower());

                if (user == null)
                {
                    _logger.LogWarning("Login attempt failed: User not found for {EmailOrUsername}", emailOrUsername);
                    return CustomResponseDto<UserDto>.BadRequest("Invalid email/username or password");
                }

                // Check if user can login
                if (!user.IsEnabled)
                {
                    _logger.LogWarning("Login attempt failed: User {UserId} is disabled", user.Id);
                    return CustomResponseDto<UserDto>.BadRequest("Account is deactivated");
                }

                if (user.IsLocked)
                {
                    _logger.LogWarning("Login attempt failed: User {UserId} is locked until {LockedUntil}", user.Id, user.LockedUntil);
                    return CustomResponseDto<UserDto>.BadRequest($"Account is locked until {user.LockedUntil:yyyy-MM-dd HH:mm}");
                }

                if (!user.IsEmailConfirmed)
                {
                    _logger.LogWarning("Login attempt failed: User {UserId} email not confirmed", user.Id);
                    return CustomResponseDto<UserDto>.BadRequest("Email address is not confirmed");
                }

                // Verify password
                if (!VerifyPassword(password, user.PasswordHash, user.PasswordSalt))
                {
                    // Increment failed login attempts
                    await IncrementFailedLoginAttemptsAsync(user);

                    _logger.LogWarning("Login attempt failed: Invalid password for user {UserId}", user.Id);
                    return CustomResponseDto<UserDto>.BadRequest("Invalid email/username or password");
                }

                // Reset failed login attempts on successful login
                await ResetFailedLoginAttemptsAsync(user);

                // Update last login
                user.UpdateLastLogin();

                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                writeRepo.UpdateAsync(user);
                await _unitOfWork.CommitAsync();

                var userDto = _mapper.Map<UserDto>(user);

                _logger.LogInformation("User {UserId} logged in successfully", user.Id);
                return CustomResponseDto<UserDto>.Success(StatusCodes.Status200OK, userDto, "Login successful");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<UserDto>(ex);
            }
        }

        public async Task<CustomResponseDto<Domain.Entities.User.User>> ProcessLoginForTokenAsync(string emailOrUsername, string password)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { emailOrUsername });

                // Validate input
                if (string.IsNullOrWhiteSpace(emailOrUsername))
                {
                    return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Email or username is required");
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Password is required");
                }

                // Find user by email or username
                var userRepository = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var allUsersQuery = await userRepository.GetAllAsync(tracking: false);
                var allUsers = await allUsersQuery.ToListAsync();
                var user = allUsers.FirstOrDefault(u =>
                    u.Email.Value.Equals(emailOrUsername, StringComparison.OrdinalIgnoreCase) ||
                    u.Username.Equals(emailOrUsername, StringComparison.OrdinalIgnoreCase));

                if (user == null)
                {
                    _logger.LogWarning("Login attempt with invalid credentials: {EmailOrUsername}", emailOrUsername);
                    return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Invalid email/username or password");
                }

                // Check if user can login
                if (!user.CanLogin)
                {
                    if (!user.IsEnabled)
                    {
                        return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Account is deactivated");
                    }
                    if (user.IsLocked)
                    {
                        return CustomResponseDto<Domain.Entities.User.User>.BadRequest($"Account is locked until {user.LockedUntil:yyyy-MM-dd HH:mm}");
                    }
                    if (!user.IsEmailConfirmed)
                    {
                        return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Email address is not confirmed");
                    }
                }

                // Verify password
                if (!VerifyPassword(password, user.PasswordHash, user.PasswordSalt))
                {
                    user.RecordFailedLogin();
                    await _unitOfWork.CommitAsync();

                    _logger.LogWarning("Failed login attempt for user: {EmailOrUsername}", emailOrUsername);
                    return CustomResponseDto<Domain.Entities.User.User>.BadRequest("Invalid email/username or password");
                }

                // Record successful login
                user.RecordSuccessfulLogin();
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("User {UserId} logged in successfully", user.Id);
                return CustomResponseDto<Domain.Entities.User.User>.Success(StatusCodes.Status200OK, user, "Login successful");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<Domain.Entities.User.User>(ex);
            }
        }

        public async Task<CustomResponseDto<UserDto>> ProcessRegisterAsync(CreateUserDto registerDto)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { registerDto.Email });

                // Validate input
                if (string.IsNullOrWhiteSpace(registerDto.Email))
                {
                    return CustomResponseDto<UserDto>.BadRequest("Email is required");
                }

                if (string.IsNullOrWhiteSpace(registerDto.Username))
                {
                    return CustomResponseDto<UserDto>.BadRequest("Username is required");
                }

                if (string.IsNullOrWhiteSpace(registerDto.Password))
                {
                    return CustomResponseDto<UserDto>.BadRequest("Password is required");
                }

                if (registerDto.Password != registerDto.ConfirmPassword)
                {
                    return CustomResponseDto<UserDto>.BadRequest("Password and confirmation password do not match");
                }

                if (!registerDto.AcceptTerms)
                {
                    return CustomResponseDto<UserDto>.BadRequest("You must accept the terms and conditions");
                }

                // Check if email already exists
                var emailExists = await ProcessEmailExistsAsync(registerDto.Email);
                if (emailExists.Data)
                {
                    return CustomResponseDto<UserDto>.BadRequest("Email already exists");
                }

                // Check if username already exists
                var usernameExists = await ProcessUsernameExistsAsync(registerDto.Username);
                if (usernameExists.Data)
                {
                    return CustomResponseDto<UserDto>.BadRequest("Username already exists");
                }

                // Create password hash and salt
                var (passwordHash, passwordSalt) = CreatePasswordHash(registerDto.Password);

                // Create user entity
                var user = new Domain.Entities.User.User
                {
                    FirstName = registerDto.FirstName?.Trim() ?? string.Empty,
                    LastName = registerDto.LastName?.Trim() ?? string.Empty,
                    Email = new Email(registerDto.Email.Trim()),
                    PhoneNumber = !string.IsNullOrWhiteSpace(registerDto.PhoneNumber)
                        ? new PhoneNumber(registerDto.PhoneNumber.Trim())
                        : null,
                    Username = registerDto.Username.Trim(),
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsEnabled = true,
                    IsEmailConfirmed = false, // Will be confirmed via email
                    IsPhoneConfirmed = false,
                    EmailConfirmationToken = GenerateToken(),
                    EmailConfirmationTokenExpiry = DateTime.UtcNow.AddHours(24),
                    Culture = registerDto.Culture ?? "en-US",
                    TimeZone = registerDto.TimeZone ?? "UTC",
                    FailedLoginAttempts = 0
                };

                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                var createdUser = await writeRepo.AddAsync(user);
                await _unitOfWork.CommitAsync();

                var userDto = _mapper.Map<UserDto>(createdUser);

                _logger.LogInformation("User {UserId} registered successfully with email {Email}", createdUser.Id, registerDto.Email);

                // TODO: Send email confirmation email

                return CustomResponseDto<UserDto>.Created(userDto, "User registered successfully. Please check your email for confirmation.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<UserDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessChangePasswordAsync(Guid userId, string currentPassword, string newPassword)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId });

                // Validate input
                if (string.IsNullOrWhiteSpace(currentPassword))
                {
                    return CustomResponseDto<NoContentDto>.BadRequest("Current password is required");
                }

                if (string.IsNullOrWhiteSpace(newPassword))
                {
                    return CustomResponseDto<NoContentDto>.BadRequest("New password is required");
                }

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await readRepo.GetByIdAsync(userId);

                if (user == null)
                {
                    return CustomResponseDto<NoContentDto>.NotFound("User not found");
                }

                // Verify current password
                if (!VerifyPassword(currentPassword, user.PasswordHash, user.PasswordSalt))
                {
                    return CustomResponseDto<NoContentDto>.BadRequest("Current password is incorrect");
                }

                // Create new password hash
                var (newPasswordHash, newPasswordSalt) = CreatePasswordHash(newPassword);

                user.ChangePassword(newPasswordHash, newPasswordSalt);

                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                writeRepo.UpdateAsync(user);
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("Password changed successfully for user {UserId}", userId);
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Password changed successfully");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessResetPasswordAsync(string email, string token, string newPassword)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { email });
                // TODO: Implement password reset logic
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Password reset will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessGeneratePasswordResetTokenAsync(string email)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { email });
                // TODO: Implement password reset token generation
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Password reset token generation will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        #endregion

        #region Account Management

        public async Task<CustomResponseDto<NoContentDto>> ProcessActivateUserAsync(Guid userId)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId });

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await readRepo.GetByIdAsync(userId);

                if (user == null)
                {
                    return CustomResponseDto<NoContentDto>.NotFound("User not found");
                }

                if (user.IsEnabled)
                {
                    return CustomResponseDto<NoContentDto>.BadRequest("User is already active");
                }

                user.Activate();

                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                writeRepo.UpdateAsync(user);
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("User {UserId} activated successfully", userId);
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "User activated successfully");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessDeactivateUserAsync(Guid userId)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId });

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await readRepo.GetByIdAsync(userId);

                if (user == null)
                {
                    return CustomResponseDto<NoContentDto>.NotFound("User not found");
                }

                if (!user.IsEnabled)
                {
                    return CustomResponseDto<NoContentDto>.BadRequest("User is already inactive");
                }

                user.Deactivate();

                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                writeRepo.UpdateAsync(user);
                await _unitOfWork.CommitAsync();

                _logger.LogInformation("User {UserId} deactivated successfully", userId);
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "User deactivated successfully");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessConfirmEmailAsync(Guid userId, string token)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId, token });
                // TODO: Implement email confirmation
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Email confirmation will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessConfirmPhoneAsync(Guid userId, string token)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId, token });
                // TODO: Implement phone confirmation
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Phone confirmation will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessLockUserAsync(Guid userId, DateTime lockUntil, string reason)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId, lockUntil, reason });
                // TODO: Implement user locking
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "User locking will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ProcessUnlockUserAsync(Guid userId)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId });
                // TODO: Implement user unlocking
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "User unlocking will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        #endregion

        #region User Queries

        public async Task<CustomResponseDto<UserDto?>> ProcessGetUserByEmailAsync(string email)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { email });

                if (string.IsNullOrWhiteSpace(email))
                {
                    return CustomResponseDto<UserDto?>.BadRequest("Email is required");
                }

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var users = await readRepo.GetAllAsync(tracking: false);
                var user = users.FirstOrDefault(u => u.Email.Value.ToLower() == email.ToLower());

                if (user == null)
                {
                    return CustomResponseDto<UserDto?>.Success(StatusCodes.Status200OK, null, "User not found");
                }

                var userDto = _mapper.Map<UserDto>(user);
                return CustomResponseDto<UserDto?>.Success(StatusCodes.Status200OK, userDto);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<UserDto?>(ex);
            }
        }

        public async Task<CustomResponseDto<UserDto?>> ProcessGetUserByUsernameAsync(string username)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { username });

                if (string.IsNullOrWhiteSpace(username))
                {
                    return CustomResponseDto<UserDto?>.BadRequest("Username is required");
                }

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var users = await readRepo.GetAllAsync(tracking: false);
                var user = users.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());

                if (user == null)
                {
                    return CustomResponseDto<UserDto?>.Success(StatusCodes.Status200OK, null, "User not found");
                }

                var userDto = _mapper.Map<UserDto>(user);
                return CustomResponseDto<UserDto?>.Success(StatusCodes.Status200OK, userDto);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<UserDto?>(ex);
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessEmailExistsAsync(string email, Guid? excludeUserId = null)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { email, excludeUserId });

                if (string.IsNullOrWhiteSpace(email))
                {
                    return CustomResponseDto<bool>.BadRequest("Email is required");
                }

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var users = await readRepo.GetAllAsync(tracking: false);
                var exists = users.Any(u => u.Email.Value.ToLower() == email.ToLower() &&
                                          (excludeUserId == null || u.Id != excludeUserId));

                return CustomResponseDto<bool>.Success(StatusCodes.Status200OK, exists);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<bool>(ex);
            }
        }

        public async Task<CustomResponseDto<bool>> ProcessUsernameExistsAsync(string username, Guid? excludeUserId = null)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { username, excludeUserId });

                if (string.IsNullOrWhiteSpace(username))
                {
                    return CustomResponseDto<bool>.BadRequest("Username is required");
                }

                var readRepo = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var users = await readRepo.GetAllAsync(tracking: false);
                var exists = users.Any(u => u.Username.ToLower() == username.ToLower() &&
                                          (excludeUserId == null || u.Id != excludeUserId));

                return CustomResponseDto<bool>.Success(StatusCodes.Status200OK, exists);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<bool>(ex);
            }
        }

        public async Task<CustomResponseDto<PagedResult<UserDto>>> ProcessSearchUsersAsync(UserSearchDto searchDto)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { searchDto });
                // TODO: Implement user search
                var emptyResult = new PagedResult<UserDto>
                {
                    Items = new List<UserDto>(),
                    TotalCount = 0,
                    PageNumber = searchDto.PageNumber,
                    PageSize = searchDto.PageSize
                };
                return CustomResponseDto<PagedResult<UserDto>>.Success(StatusCodes.Status200OK, emptyResult, "User search will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<PagedResult<UserDto>>(ex);
            }
        }

        #endregion

        #region Statistics & Analytics

        public async Task<CustomResponseDto<UserStatisticsDto>> ProcessGetUserStatisticsAsync()
        {
            try
            {
                LogMethodEntry();
                // TODO: Implement user statistics
                var stats = new UserStatisticsDto();
                return CustomResponseDto<UserStatisticsDto>.Success(StatusCodes.Status200OK, stats, "User statistics will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<UserStatisticsDto>(ex);
            }
        }

        public async Task<CustomResponseDto<PagedResult<UserLoginHistoryDto>>> ProcessGetUserLoginHistoryAsync(Guid userId, int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { userId, pageNumber, pageSize });
                // TODO: Implement login history
                var emptyResult = new PagedResult<UserLoginHistoryDto>
                {
                    Items = new List<UserLoginHistoryDto>(),
                    TotalCount = 0,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
                return CustomResponseDto<PagedResult<UserLoginHistoryDto>>.Success(StatusCodes.Status200OK, emptyResult, "Login history will be implemented");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<PagedResult<UserLoginHistoryDto>>(ex);
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Creates password hash and salt using HMACSHA512
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>Tuple of (hash, salt)</returns>
        private (string hash, string salt) CreatePasswordHash(string password)
        {
            using var hmac = new HMACSHA512();
            var salt = Convert.ToBase64String(hmac.Key);
            var hash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
            return (hash, salt);
        }

        /// <summary>
        /// Verifies password against stored hash and salt
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="hash">Stored password hash</param>
        /// <param name="salt">Stored password salt</param>
        /// <returns>True if password matches</returns>
        private bool VerifyPassword(string password, string hash, string salt)
        {
            var saltBytes = Convert.FromBase64String(salt);
            using var hmac = new HMACSHA512(saltBytes);
            var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));
            var computedHashString = Convert.ToBase64String(computedHash);
            return computedHashString == hash;
        }

        /// <summary>
        /// Generates a random token for email/phone confirmation
        /// </summary>
        /// <returns>Random token string</returns>
        private string GenerateToken()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// Increments failed login attempts and locks account if necessary
        /// </summary>
        /// <param name="user">User entity</param>
        private async Task IncrementFailedLoginAttemptsAsync(Domain.Entities.User.User user)
        {
            user.IncrementFailedLoginAttempts();

            // Lock account after 5 failed attempts
            if (user.FailedLoginAttempts >= 5)
            {
                user.Lock(DateTime.UtcNow.AddMinutes(30), "Too many failed login attempts");
                _logger.LogWarning("User {UserId} locked due to too many failed login attempts", user.Id);
            }

            var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
            writeRepo.UpdateAsync(user);
            await _unitOfWork.CommitAsync();
        }

        /// <summary>
        /// Resets failed login attempts to zero
        /// </summary>
        /// <param name="user">User entity</param>
        private async Task ResetFailedLoginAttemptsAsync(Domain.Entities.User.User user)
        {
            if (user.FailedLoginAttempts > 0)
            {
                user.ResetFailedLoginAttempts();
                var writeRepo = _unitOfWork.WriteRepository<Domain.Entities.User.User>();
                writeRepo.UpdateAsync(user);
                await _unitOfWork.CommitAsync();
            }
        }

        #endregion
    }
}
