using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Order
{
    /// <summary>
    /// Order entity representing a customer order
    /// </summary>
    public class Order : AuditableBaseEntity
    {
        #region Basic Information

        /// <summary>
        /// Unique order number for customer reference
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// Customer ID who placed the order (null for guest orders)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Customer email at the time of order
        /// </summary>
        public Email CustomerEmail { get; set; } = null!;

        /// <summary>
        /// Customer phone number at the time of order (optional)
        /// </summary>
        public PhoneNumber? CustomerPhone { get; set; }

        /// <summary>
        /// Customer's first name at the time of order
        /// </summary>
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer's last name at the time of order
        /// </summary>
        public string CustomerLastName { get; set; } = string.Empty;

        #endregion

        #region Order Status

        /// <summary>
        /// Current order status
        /// </summary>
        public OrderStatus Status { get; set; } = OrderStatus.Draft;

        /// <summary>
        /// Current payment status
        /// </summary>
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;

        /// <summary>
        /// Current shipping status
        /// </summary>
        public ShippingStatus ShippingStatus { get; set; } = ShippingStatus.NotShipped;

        #endregion

        #region Addresses

        /// <summary>
        /// Billing address
        /// </summary>
        public Address BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address (can be different from billing)
        /// </summary>
        public Address ShippingAddress { get; set; } = null!;

        #endregion

        #region Financial Information

        /// <summary>
        /// Subtotal amount (sum of all order items)
        /// </summary>
        public Money Subtotal { get; set; } = null!;

        /// <summary>
        /// Tax amount
        /// </summary>
        public Money TaxAmount { get; set; } = null!;

        /// <summary>
        /// Shipping cost
        /// </summary>
        public Money ShippingCost { get; set; } = null!;

        /// <summary>
        /// Discount amount applied to the order
        /// </summary>
        public Money DiscountAmount { get; set; } = null!;

        /// <summary>
        /// Total order amount (Subtotal + Tax + Shipping - Discount)
        /// </summary>
        public Money Total { get; set; } = null!;

        /// <summary>
        /// Currency used for this order
        /// </summary>
        public Currency Currency { get; set; } = null!;

        #endregion

        #region Order Items

        /// <summary>
        /// Items in this order
        /// </summary>
        public ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

        #endregion

        #region Additional Information

        /// <summary>
        /// Customer notes/comments for the order
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Internal notes for staff
        /// </summary>
        public string? InternalNotes { get; set; }

        /// <summary>
        /// Shipping method selected
        /// </summary>
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Payment method used
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Tracking number for shipment
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// Expected delivery date
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// Actual delivery date
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Navigation property to User entity
        /// </summary>
        public User.User Customer { get; set; } = null!;

        #endregion

        #region Constructors

        // Parameterless constructor for EF Core
        public Order() { }

        /// <summary>
        /// Creates a new order
        /// </summary>
        public Order(Guid? customerId, Email customerEmail, string customerFirstName, string customerLastName,
                    Address billingAddress, Address shippingAddress, Currency currency)
        {
            CustomerId = customerId;
            CustomerEmail = customerEmail;
            CustomerFirstName = customerFirstName;
            CustomerLastName = customerLastName;
            BillingAddress = billingAddress;
            ShippingAddress = shippingAddress;
            Currency = currency;

            // Initialize money values with zero amounts
            Subtotal = Money.Zero(currency);
            TaxAmount = Money.Zero(currency);
            ShippingCost = Money.Zero(currency);
            DiscountAmount = Money.Zero(currency);
            Total = Money.Zero(currency);

            // Generate order number
            OrderNumber = GenerateOrderNumber();

            Status = OrderStatus.Draft;
            PaymentStatus = PaymentStatus.Pending;
            ShippingStatus = ShippingStatus.NotShipped;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Generates a unique order number
        /// </summary>
        private string GenerateOrderNumber()
        {
            return $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
        }

        /// <summary>
        /// Adds an item to the order
        /// </summary>
        public void AddItem(Guid productId, string productName, string productSku, Money unitPrice, int quantity)
        {
            if (Status != OrderStatus.Draft)
                throw new InvalidOperationException("Cannot add items to a confirmed order");

            var existingItem = OrderItems.FirstOrDefault(x => x.ProductId == productId);
            if (existingItem != null)
            {
                existingItem.UpdateQuantity(existingItem.Quantity + quantity);
            }
            else
            {
                var orderItem = new OrderItem(productId, productName, productSku, unitPrice, quantity);
                OrderItems.Add(orderItem);
            }

            RecalculateTotal();
        }

        /// <summary>
        /// Removes an item from the order
        /// </summary>
        public void RemoveItem(Guid productId)
        {
            if (Status != OrderStatus.Draft)
                throw new InvalidOperationException("Cannot remove items from a confirmed order");

            var item = OrderItems.FirstOrDefault(x => x.ProductId == productId);
            if (item != null)
            {
                OrderItems.Remove(item);
                RecalculateTotal();
            }
        }

        /// <summary>
        /// Updates item quantity
        /// </summary>
        public void UpdateItemQuantity(Guid productId, int newQuantity)
        {
            if (Status != OrderStatus.Draft)
                throw new InvalidOperationException("Cannot update items in a confirmed order");

            var item = OrderItems.FirstOrDefault(x => x.ProductId == productId);
            if (item != null)
            {
                if (newQuantity <= 0)
                {
                    RemoveItem(productId);
                }
                else
                {
                    item.UpdateQuantity(newQuantity);
                    RecalculateTotal();
                }
            }
        }

        /// <summary>
        /// Recalculates order totals
        /// </summary>
        public void RecalculateTotal()
        {
            var itemsTotal = OrderItems.Sum(item => item.TotalPrice.Amount);
            Subtotal = new Money(itemsTotal, Currency);

            // Total = Subtotal + Tax + Shipping - Discount
            var totalAmount = Subtotal.Amount + TaxAmount.Amount + ShippingCost.Amount - DiscountAmount.Amount;
            Total = new Money(Math.Max(0, totalAmount), Currency);
        }

        /// <summary>
        /// Sets tax amount
        /// </summary>
        public void SetTaxAmount(Money taxAmount)
        {
            if (!taxAmount.Currency.Equals(Currency))
                throw new ArgumentException("Tax amount currency must match order currency");

            TaxAmount = taxAmount;
            RecalculateTotal();
        }

        /// <summary>
        /// Sets shipping cost
        /// </summary>
        public void SetShippingCost(Money shippingCost, string? shippingMethod = null)
        {
            if (!shippingCost.Currency.Equals(Currency))
                throw new ArgumentException("Shipping cost currency must match order currency");

            ShippingCost = shippingCost;
            if (!string.IsNullOrEmpty(shippingMethod))
                ShippingMethod = shippingMethod;

            RecalculateTotal();
        }

        /// <summary>
        /// Applies discount to the order
        /// </summary>
        public void ApplyDiscount(Money discountAmount)
        {
            if (!discountAmount.Currency.Equals(Currency))
                throw new ArgumentException("Discount amount currency must match order currency");

            DiscountAmount = discountAmount;
            RecalculateTotal();
        }

        /// <summary>
        /// Confirms the order
        /// </summary>
        public void Confirm()
        {
            if (Status != OrderStatus.Draft)
                throw new InvalidOperationException("Only draft orders can be confirmed");

            if (!OrderItems.Any())
                throw new InvalidOperationException("Cannot confirm order without items");

            Status = OrderStatus.Confirmed;
            MarkAsUpdated();
        }

        /// <summary>
        /// Cancels the order
        /// </summary>
        public void Cancel(string? reason = null)
        {
            if (Status == OrderStatus.Delivered || Status == OrderStatus.Shipped)
                throw new InvalidOperationException("Cannot cancel delivered or shipped orders");

            Status = OrderStatus.Cancelled;
            if (!string.IsNullOrEmpty(reason))
                InternalNotes = $"Cancelled: {reason}";

            MarkAsUpdated();
        }

        /// <summary>
        /// Marks order as shipped
        /// </summary>
        public void MarkAsShipped(string? trackingNumber = null, DateTime? expectedDeliveryDate = null)
        {
            if (Status != OrderStatus.Processing)
                throw new InvalidOperationException("Only processing orders can be marked as shipped");

            Status = OrderStatus.Shipped;
            ShippingStatus = ShippingStatus.InTransit;

            if (!string.IsNullOrEmpty(trackingNumber))
                TrackingNumber = trackingNumber;

            if (expectedDeliveryDate.HasValue)
                ExpectedDeliveryDate = expectedDeliveryDate;

            MarkAsUpdated();
        }

        /// <summary>
        /// Marks order as delivered
        /// </summary>
        public void MarkAsDelivered(DateTime? deliveryDate = null)
        {
            if (Status != OrderStatus.Shipped)
                throw new InvalidOperationException("Only shipped orders can be marked as delivered");

            Status = OrderStatus.Delivered;
            ShippingStatus = ShippingStatus.Delivered;
            ActualDeliveryDate = deliveryDate ?? DateTime.UtcNow;

            MarkAsUpdated();
        }

        /// <summary>
        /// Updates payment status
        /// </summary>
        public void UpdatePaymentStatus(PaymentStatus newStatus)
        {
            PaymentStatus = newStatus;

            // Auto-update order status based on payment status
            if (newStatus == PaymentStatus.Completed && Status == OrderStatus.Confirmed)
            {
                Status = OrderStatus.Processing;
            }
            else if (newStatus == PaymentStatus.Failed && Status == OrderStatus.Confirmed)
            {
                Status = OrderStatus.Failed;
            }

            MarkAsUpdated();
        }

        /// <summary>
        /// Gets customer full name
        /// </summary>
        public string GetCustomerFullName()
        {
            return $"{CustomerFirstName} {CustomerLastName}".Trim();
        }

        /// <summary>
        /// Checks if order can be modified
        /// </summary>
        public bool CanBeModified()
        {
            return Status == OrderStatus.Draft;
        }

        /// <summary>
        /// Checks if order can be cancelled
        /// </summary>
        public bool CanBeCancelled()
        {
            return Status != OrderStatus.Delivered &&
                   Status != OrderStatus.Shipped &&
                   Status != OrderStatus.Cancelled &&
                   Status != OrderStatus.Refunded;
        }

        #endregion
    }
}
