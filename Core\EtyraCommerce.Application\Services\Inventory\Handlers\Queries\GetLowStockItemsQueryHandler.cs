using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting low stock items
    /// </summary>
    public class GetLowStockItemsQueryHandler : IRequestHandler<GetLowStockItemsQuery, CustomResponseDto<List<LowStockItemDto>>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetLowStockItemsQueryHandler> _logger;

        public GetLowStockItemsQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetLowStockItemsQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<LowStockItemDto>>> Handle(GetLowStockItemsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get low stock items query");

                // Validation
                if (request.MaxItems.HasValue && request.MaxItems.Value <= 0)
                    return CustomResponseDto<List<LowStockItemDto>>.BadRequest("Max items must be positive");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetLowStockItemsAsync(
                    request.WarehouseId,
                    request.ActiveWarehousesOnly,
                    request.MaxItems);

                _logger.LogInformation("Get low stock items query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get low stock items query");
                return CustomResponseDto<List<LowStockItemDto>>.InternalServerError("An error occurred while retrieving low stock items");
            }
        }
    }
}
