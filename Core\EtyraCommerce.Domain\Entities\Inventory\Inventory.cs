namespace EtyraCommerce.Domain.Entities.Inventory
{
    /// <summary>
    /// Inventory entity for product stock management per warehouse
    /// </summary>
    public class Inventory : AuditableBaseEntity
    {
        #region Properties

        /// <summary>
        /// Product ID reference
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Warehouse ID reference
        /// </summary>
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// Available quantity in stock
        /// </summary>
        public int AvailableQuantity { get; set; } = 0;

        /// <summary>
        /// Reserved quantity (for pending orders)
        /// </summary>
        public int ReservedQuantity { get; set; } = 0;

        /// <summary>
        /// Allocated quantity (for confirmed orders)
        /// </summary>
        public int AllocatedQuantity { get; set; } = 0;

        /// <summary>
        /// Minimum stock level for reorder alerts
        /// </summary>
        public int MinStockLevel { get; set; } = 0;

        /// <summary>
        /// Maximum stock level capacity
        /// </summary>
        public int MaxStockLevel { get; set; } = 0;

        /// <summary>
        /// Reorder point threshold
        /// </summary>
        public int ReorderPoint { get; set; } = 0;

        /// <summary>
        /// Reorder quantity when restocking
        /// </summary>
        public int ReorderQuantity { get; set; } = 0;

        /// <summary>
        /// Location code within warehouse (bin, shelf, etc.)
        /// </summary>
        public string? LocationCode { get; set; }

        /// <summary>
        /// Supplier reference for this inventory item
        /// </summary>
        public string? SupplierReference { get; set; }

        /// <summary>
        /// Lead time in days for restocking
        /// </summary>
        public int? LeadTimeDays { get; set; }

        /// <summary>
        /// Last stock update timestamp
        /// </summary>
        public DateTime? LastStockUpdate { get; set; }

        /// <summary>
        /// Last physical count timestamp
        /// </summary>
        public DateTime? LastPhysicalCount { get; set; }

        /// <summary>
        /// Current inventory status
        /// </summary>
        public InventoryStatus Status { get; set; } = InventoryStatus.InStock;

        /// <summary>
        /// Notes or comments about this inventory item
        /// </summary>
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Product reference
        /// </summary>
        public virtual Product.Product Product { get; set; } = null!;

        /// <summary>
        /// Warehouse reference
        /// </summary>
        public virtual Warehouse Warehouse { get; set; } = null!;

        /// <summary>
        /// Inventory transactions for this item
        /// </summary>
        public virtual ICollection<InventoryTransaction> Transactions { get; set; } = new List<InventoryTransaction>();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Total quantity (available + reserved + allocated)
        /// </summary>
        public int TotalQuantity => AvailableQuantity + ReservedQuantity + AllocatedQuantity;

        /// <summary>
        /// Free quantity (available - reserved)
        /// </summary>
        public int FreeQuantity => Math.Max(0, AvailableQuantity - ReservedQuantity);

        /// <summary>
        /// Whether stock is low (below reorder point)
        /// </summary>
        public bool IsLowStock => TotalQuantity <= ReorderPoint && ReorderPoint > 0;

        /// <summary>
        /// Whether stock is out (no available quantity)
        /// </summary>
        public bool IsOutOfStock => AvailableQuantity <= 0;

        /// <summary>
        /// Whether stock is overstocked (above max level)
        /// </summary>
        public bool IsOverstocked => MaxStockLevel > 0 && TotalQuantity > MaxStockLevel;

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public Inventory() { }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public Inventory(Guid productId, Guid warehouseId, int availableQuantity = 0,
            int minStockLevel = 0, int reorderPoint = 0, int reorderQuantity = 0)
        {
            ProductId = productId;
            WarehouseId = warehouseId;
            AvailableQuantity = availableQuantity;
            MinStockLevel = minStockLevel;
            ReorderPoint = reorderPoint;
            ReorderQuantity = reorderQuantity;
            LastStockUpdate = DateTime.UtcNow;
            UpdateStatus();
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates available quantity
        /// </summary>
        public void UpdateAvailableQuantity(int newQuantity, string? reason = null)
        {
            if (newQuantity < 0)
                throw new ArgumentException("Available quantity cannot be negative");

            AvailableQuantity = newQuantity;
            LastStockUpdate = DateTime.UtcNow;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Adds stock to available quantity
        /// </summary>
        public void AddStock(int quantity, string? reason = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            AvailableQuantity += quantity;
            LastStockUpdate = DateTime.UtcNow;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes stock from available quantity
        /// </summary>
        public void RemoveStock(int quantity, string? reason = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (AvailableQuantity < quantity)
                throw new InvalidOperationException("Insufficient available stock");

            AvailableQuantity -= quantity;
            LastStockUpdate = DateTime.UtcNow;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Reserves stock for pending orders
        /// </summary>
        public void ReserveStock(int quantity, string? reference = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (FreeQuantity < quantity)
                throw new InvalidOperationException("Insufficient free stock for reservation");

            ReservedQuantity += quantity;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Releases reserved stock
        /// </summary>
        public void ReleaseReservation(int quantity, string? reference = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (ReservedQuantity < quantity)
                throw new InvalidOperationException("Cannot release more than reserved quantity");

            ReservedQuantity -= quantity;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Allocates reserved stock for confirmed orders
        /// </summary>
        public void AllocateStock(int quantity, string? reference = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (ReservedQuantity < quantity)
                throw new InvalidOperationException("Cannot allocate more than reserved quantity");

            ReservedQuantity -= quantity;
            AllocatedQuantity += quantity;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Ships allocated stock (removes from inventory)
        /// </summary>
        public void ShipStock(int quantity, string? reference = null)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (AllocatedQuantity < quantity)
                throw new InvalidOperationException("Cannot ship more than allocated quantity");

            AllocatedQuantity -= quantity;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates stock levels configuration
        /// </summary>
        public void UpdateStockLevels(int minStockLevel, int maxStockLevel, int reorderPoint, int reorderQuantity)
        {
            if (minStockLevel < 0 || maxStockLevel < 0 || reorderPoint < 0 || reorderQuantity < 0)
                throw new ArgumentException("Stock levels cannot be negative");

            if (maxStockLevel > 0 && minStockLevel > maxStockLevel)
                throw new ArgumentException("Minimum stock level cannot be greater than maximum");

            MinStockLevel = minStockLevel;
            MaxStockLevel = maxStockLevel;
            ReorderPoint = reorderPoint;
            ReorderQuantity = reorderQuantity;
            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates location and supplier information
        /// </summary>
        public void UpdateLocationInfo(string? locationCode, string? supplierReference, int? leadTimeDays)
        {
            LocationCode = locationCode;
            SupplierReference = supplierReference;
            LeadTimeDays = leadTimeDays;
            MarkAsUpdated();
        }

        /// <summary>
        /// Records physical count
        /// </summary>
        public void RecordPhysicalCount(int countedQuantity, string? notes = null)
        {
            var difference = countedQuantity - AvailableQuantity;
            AvailableQuantity = countedQuantity;
            LastPhysicalCount = DateTime.UtcNow;
            LastStockUpdate = DateTime.UtcNow;

            if (!string.IsNullOrEmpty(notes))
                Notes = notes;

            UpdateStatus();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates inventory status based on current quantities
        /// </summary>
        private void UpdateStatus()
        {
            if (IsOutOfStock)
                Status = InventoryStatus.OutOfStock;
            else if (IsLowStock)
                Status = InventoryStatus.LowStock;
            else if (IsOverstocked)
                Status = InventoryStatus.Overstocked;
            else
                Status = InventoryStatus.InStock;
        }

        #endregion

        public override string ToString()
        {
            return $"Inventory [ProductId: {ProductId}, WarehouseId: {WarehouseId}, Available: {AvailableQuantity}, Status: {Status}]";
        }
    }

    /// <summary>
    /// Inventory status enumeration
    /// </summary>
    public enum InventoryStatus
    {
        InStock = 0,
        LowStock = 1,
        OutOfStock = 2,
        Overstocked = 3,
        Discontinued = 4
    }
}
