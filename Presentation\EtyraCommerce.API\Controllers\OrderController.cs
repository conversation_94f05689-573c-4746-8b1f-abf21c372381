using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Order management API controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [EnableCors("AllowAll")]
    public class OrderController : ControllerBase
    {
        private readonly IOrderService _orderService;
        private readonly IMediator _mediator;
        private readonly ILogger<OrderController> _logger;

        public OrderController(IOrderService orderService, IMediator mediator, ILogger<OrderController> logger)
        {
            _orderService = orderService;
            _mediator = mediator;
            _logger = logger;
        }

        #region Order Management

        /// <summary>
        /// Creates a new order
        /// </summary>
        /// <param name="createOrderDto">Order creation data</param>
        /// <returns>Created order</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto createOrderDto)
        {
            try
            {
                _logger.LogInformation("Creating order for customer: {CustomerId}", createOrderDto.CustomerId);

                var result = await _orderService.CreateOrderAsync(createOrderDto);

                if (result.IsSuccess)
                {
                    return CreatedAtAction(nameof(GetOrderById), new { orderId = result.Data!.Id }, result);
                }

                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating order for customer: {CustomerId}", createOrderDto.CustomerId);
                return StatusCode(500, CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating the order"));
            }
        }

        /// <summary>
        /// Creates an order from shopping cart
        /// </summary>
        /// <param name="createOrderFromCartDto">Order creation data from cart</param>
        /// <returns>Created order</returns>
        [HttpPost("from-cart")]
        public async Task<IActionResult> CreateOrderFromCart([FromBody] CreateOrderFromCartDto createOrderFromCartDto)
        {
            try
            {
                // Get customer ID from JWT token if authenticated
                Guid? customerId = null;
                if (User.Identity?.IsAuthenticated == true)
                {
                    var customerIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    if (Guid.TryParse(customerIdClaim, out var parsedCustomerId))
                    {
                        customerId = parsedCustomerId;
                    }
                }

                _logger.LogInformation("Creating order from cart for CustomerId: {CustomerId}, SessionId: {SessionId}, PaymentMethod: {PaymentMethod}",
                    customerId, createOrderFromCartDto.SessionId, createOrderFromCartDto.PaymentMethod);

                // Create command
                var command = CreateOrderFromCartCommand.FromDto(createOrderFromCartDto, customerId);

                // Send command
                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    return CreatedAtAction(nameof(GetOrderById), new { orderId = result.Data!.Id }, result);
                }

                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating order from cart for CustomerId: {CustomerId}, SessionId: {SessionId}",
                    User.FindFirst(ClaimTypes.NameIdentifier)?.Value, createOrderFromCartDto.SessionId);
                return StatusCode(500, CustomResponseDto<OrderDto>.InternalServerError("An error occurred while creating order from cart"));
            }
        }

        /// <summary>
        /// Gets an order by ID
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="includeItems">Whether to include order items</param>
        /// <param name="includeCustomer">Whether to include customer information</param>
        /// <param name="languageCode">Language code for localized content</param>
        /// <returns>Order details</returns>
        [HttpGet("{orderId:guid}")]
        [Authorize]
        public async Task<IActionResult> GetOrderById(
            Guid orderId,
            [FromQuery] bool includeItems = true,
            [FromQuery] bool includeCustomer = false,
            [FromQuery] string? languageCode = null)
        {
            try
            {
                _logger.LogInformation("Getting order by ID: {OrderId}", orderId);

                var result = await _orderService.GetOrderByIdAsync(orderId, includeItems, includeCustomer, languageCode);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order"));
            }
        }

        /// <summary>
        /// Gets an order by order number
        /// </summary>
        /// <param name="orderNumber">Order number</param>
        /// <param name="includeItems">Whether to include order items</param>
        /// <returns>Order details</returns>
        [HttpGet("by-number/{orderNumber}")]
        [Authorize]
        public async Task<IActionResult> GetOrderByNumber(
            string orderNumber,
            [FromQuery] bool includeItems = true)
        {
            try
            {
                _logger.LogInformation("Getting order by number: {OrderNumber}", orderNumber);

                var result = await _orderService.GetOrderByOrderNumberAsync(orderNumber, includeItems);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting order by number: {OrderNumber}", orderNumber);
                return StatusCode(500, CustomResponseDto<OrderDto>.InternalServerError("An error occurred while retrieving the order"));
            }
        }

        /// <summary>
        /// Searches orders with filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for order number, customer email, or name</param>
        /// <param name="customerId">Filter by customer ID</param>
        /// <param name="status">Filter by order status</param>
        /// <param name="paymentStatus">Filter by payment status</param>
        /// <param name="shippingStatus">Filter by shipping status</param>
        /// <param name="minTotal">Minimum order total</param>
        /// <param name="maxTotal">Maximum order total</param>
        /// <param name="currency">Currency code</param>
        /// <param name="startDate">Start date for date range filter</param>
        /// <param name="endDate">End date for date range filter</param>
        /// <param name="paymentMethod">Filter by payment method</param>
        /// <param name="shippingMethod">Filter by shipping method</param>
        /// <param name="includeItems">Whether to include order items</param>
        /// <param name="includeCustomer">Whether to include customer information</param>
        /// <param name="sortBy">Sort field</param>
        /// <param name="sortDirection">Sort direction</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Page size (max 100)</param>
        /// <returns>Paginated order results</returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> SearchOrders(
            [FromQuery] string? searchTerm = null,
            [FromQuery] Guid? customerId = null,
            [FromQuery] OrderStatus? status = null,
            [FromQuery] PaymentStatus? paymentStatus = null,
            [FromQuery] ShippingStatus? shippingStatus = null,
            [FromQuery] decimal? minTotal = null,
            [FromQuery] decimal? maxTotal = null,
            [FromQuery] string? currency = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? shippingMethod = null,
            [FromQuery] bool includeItems = false,
            [FromQuery] bool includeCustomer = false,
            [FromQuery] OrderSortField sortBy = OrderSortField.CreatedAt,
            [FromQuery] SortDirection sortDirection = SortDirection.Descending,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var searchDto = new OrderSearchDto
                {
                    SearchTerm = searchTerm,
                    CustomerId = customerId,
                    Status = status,
                    PaymentStatus = paymentStatus,
                    ShippingStatus = shippingStatus,
                    MinTotal = minTotal,
                    MaxTotal = maxTotal,
                    Currency = currency,
                    StartDate = startDate,
                    EndDate = endDate,
                    PaymentMethod = paymentMethod,
                    ShippingMethod = shippingMethod,
                    IncludeItems = includeItems,
                    IncludeCustomer = includeCustomer,
                    SortBy = sortBy,
                    SortDirection = sortDirection,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                _logger.LogInformation("Searching orders with term: {SearchTerm}, Status: {Status}", searchTerm ?? "None", status?.ToString() ?? "All");

                var result = await _orderService.SearchOrdersAsync(searchDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while searching orders");
                return StatusCode(500, CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while searching orders"));
            }
        }

        #endregion

        #region Customer Orders

        /// <summary>
        /// Gets orders for a specific customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Page size (max 100)</param>
        /// <returns>Customer's orders</returns>
        [HttpGet("customer/{customerId:guid}")]
        [Authorize]
        public async Task<IActionResult> GetCustomerOrders(
            Guid customerId,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting orders for customer: {CustomerId}", customerId);

                var result = await _orderService.GetOrdersByCustomerIdAsync(customerId, pageNumber, pageSize);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting orders for customer: {CustomerId}", customerId);
                return StatusCode(500, CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while retrieving customer orders"));
            }
        }

        /// <summary>
        /// Gets current user's orders (requires authentication)
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Page size (max 100)</param>
        /// <returns>Current user's orders</returns>
        [HttpGet("my-orders")]
        [Authorize]
        public async Task<IActionResult> GetMyOrders(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // Get user ID from JWT token claims
                var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized(CustomResponseDto<PagedResult<OrderDto>>.Unauthorized("Invalid user token"));
                }

                _logger.LogInformation("Getting orders for current user: {UserId}", userId);

                var result = await _orderService.GetOrdersByCustomerIdAsync(userId, pageNumber, pageSize);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting current user's orders");
                return StatusCode(500, CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while retrieving your orders"));
            }
        }

        #endregion

        #region Order Statistics

        /// <summary>
        /// Gets order statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="customerId">Filter by specific customer</param>
        /// <param name="currency">Filter by currency</param>
        /// <param name="topCustomersCount">Number of top customers to include</param>
        /// <param name="recentOrdersCount">Number of recent orders to include</param>
        /// <returns>Order statistics</returns>
        [HttpGet("statistics")]
        [Authorize]
        public async Task<IActionResult> GetOrderStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] Guid? customerId = null,
            [FromQuery] string? currency = null,
            [FromQuery] int topCustomersCount = 10,
            [FromQuery] int recentOrdersCount = 5)
        {
            try
            {
                _logger.LogInformation("Getting order statistics from {StartDate} to {EndDate}",
                    startDate?.ToString("yyyy-MM-dd") ?? "beginning",
                    endDate?.ToString("yyyy-MM-dd") ?? "now");

                var result = await _orderService.GetOrderStatisticsAsync(startDate, endDate, customerId, currency, topCustomersCount, recentOrdersCount);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting order statistics");
                return StatusCode(500, CustomResponseDto<OrderStatisticsDto>.InternalServerError("An error occurred while retrieving order statistics"));
            }
        }

        #endregion

        #region Order Status Management

        /// <summary>
        /// Updates order status
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="updateStatusDto">Status update data</param>
        /// <returns>Success response</returns>
        [HttpPut("{orderId:guid}/status")]
        [Authorize]
        public async Task<IActionResult> UpdateOrderStatus(Guid orderId, [FromBody] UpdateOrderStatusDto updateStatusDto)
        {
            try
            {
                _logger.LogInformation("Updating order status for order: {OrderId} to {Status}", orderId, updateStatusDto.Status);

                var result = await _orderService.UpdateOrderStatusAsync(orderId, updateStatusDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating order status for order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the order status"));
            }
        }

        /// <summary>
        /// Updates payment status
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="updatePaymentDto">Payment status update data</param>
        /// <returns>Success response</returns>
        [HttpPut("{orderId:guid}/payment-status")]
        [Authorize]
        public async Task<IActionResult> UpdatePaymentStatus(Guid orderId, [FromBody] UpdatePaymentStatusDto updatePaymentDto)
        {
            try
            {
                _logger.LogInformation("Updating payment status for order: {OrderId} to {PaymentStatus}", orderId, updatePaymentDto.PaymentStatus);

                var result = await _orderService.UpdatePaymentStatusAsync(orderId, updatePaymentDto);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating payment status for order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while updating the payment status"));
            }
        }

        /// <summary>
        /// Confirms an order
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <returns>Success response</returns>
        [HttpPost("{orderId:guid}/confirm")]
        [Authorize]
        public async Task<IActionResult> ConfirmOrder(Guid orderId)
        {
            try
            {
                _logger.LogInformation("Confirming order: {OrderId}", orderId);

                var result = await _orderService.ConfirmOrderAsync(orderId);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while confirming order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while confirming the order"));
            }
        }

        /// <summary>
        /// Cancels an order
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="cancelOrderDto">Cancellation data</param>
        /// <returns>Success response</returns>
        [HttpPost("{orderId:guid}/cancel")]
        [Authorize]
        public async Task<IActionResult> CancelOrder(Guid orderId, [FromBody] CancelOrderDto? cancelOrderDto = null)
        {
            try
            {
                _logger.LogInformation("Cancelling order: {OrderId}", orderId);

                var result = await _orderService.CancelOrderAsync(orderId, cancelOrderDto?.Reason, cancelOrderDto?.CancelledBy);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while cancelling order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while cancelling the order"));
            }
        }

        /// <summary>
        /// Marks order as shipped
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="shipOrderDto">Shipping data</param>
        /// <returns>Success response</returns>
        [HttpPost("{orderId:guid}/ship")]
        [Authorize]
        public async Task<IActionResult> ShipOrder(Guid orderId, [FromBody] ShipOrderDto? shipOrderDto = null)
        {
            try
            {
                _logger.LogInformation("Shipping order: {OrderId}", orderId);

                var result = await _orderService.ShipOrderAsync(orderId, shipOrderDto?.TrackingNumber, shipOrderDto?.ExpectedDeliveryDate);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while shipping order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while shipping the order"));
            }
        }

        /// <summary>
        /// Marks order as delivered
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="deliverOrderDto">Delivery data</param>
        /// <returns>Success response</returns>
        [HttpPost("{orderId:guid}/deliver")]
        [Authorize]
        public async Task<IActionResult> DeliverOrder(Guid orderId, [FromBody] DeliverOrderDto? deliverOrderDto = null)
        {
            try
            {
                _logger.LogInformation("Delivering order: {OrderId}", orderId);

                var result = await _orderService.DeliverOrderAsync(orderId, deliverOrderDto?.DeliveryDate);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while delivering order: {OrderId}", orderId);
                return StatusCode(500, CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while delivering the order"));
            }
        }

        #endregion

        #region Order Validation

        /// <summary>
        /// Checks if an order exists
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <returns>Boolean result</returns>
        [HttpGet("{orderId:guid}/exists")]
        [Authorize]
        public async Task<IActionResult> OrderExists(Guid orderId)
        {
            try
            {
                _logger.LogInformation("Checking if order exists: {OrderId}", orderId);

                var exists = await _orderService.OrderExistsAsync(orderId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking order existence: {OrderId}", orderId);
                return StatusCode(500, new { error = "An error occurred while checking order existence" });
            }
        }

        /// <summary>
        /// Checks if an order belongs to a specific customer
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <param name="customerId">Customer ID</param>
        /// <returns>Boolean result</returns>
        [HttpGet("{orderId:guid}/belongs-to/{customerId:guid}")]
        [Authorize]
        public async Task<IActionResult> OrderBelongsToCustomer(Guid orderId, Guid customerId)
        {
            try
            {
                _logger.LogInformation("Checking if order {OrderId} belongs to customer {CustomerId}", orderId, customerId);

                var belongs = await _orderService.OrderBelongsToCustomerAsync(orderId, customerId);
                return Ok(new { belongs });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking order ownership: {OrderId}, {CustomerId}", orderId, customerId);
                return StatusCode(500, new { error = "An error occurred while checking order ownership" });
            }
        }

        #endregion
    }
}
