version: '3.8'

services:
  # PostgreSQL Database
  etyra-postgres:
    image: postgres:15-alpine
    container_name: etyra-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: etyracommerce
      POSTGRES_USER: etyra
      POSTGRES_PASSWORD: EtyraCommerce2024!
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - etyra-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U etyra -d etyracommerce"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  etyra-redis:
    image: redis:7-alpine
    container_name: etyra-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass EtyraRedis2024!
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - etyra-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin (PostgreSQL Management - Optional)
  etyra-pgadmin:
    image: dpage/pgadmin4:latest
    container_name: etyra-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: EtyraAdmin2024!
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - etyra-network
    depends_on:
      - etyra-postgres

  # Redis Commander (Redis Management - Optional)
  etyra-redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: etyra-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:etyra-redis:6379:0:EtyraRedis2024!
      HTTP_USER: admin
      HTTP_PASSWORD: EtyraRedis2024!
    ports:
      - "8081:8081"
    networks:
      - etyra-network
    depends_on:
      - etyra-redis

# Volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

# Network for inter-container communication
networks:
  etyra-network:
    driver: bridge
