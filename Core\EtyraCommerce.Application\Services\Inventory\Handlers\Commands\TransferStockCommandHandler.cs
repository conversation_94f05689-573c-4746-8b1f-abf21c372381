using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for transferring stock between warehouses
    /// </summary>
    public class TransferStockCommandHandler : IRequestHandler<TransferStockCommand, CustomResponseDto<bool>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<TransferStockCommandHandler> _logger;

        public TransferStockCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<TransferStockCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(TransferStockCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling transfer stock command for ProductId: {ProductId}, From: {FromWarehouse}, To: {ToWarehouse}, Quantity: {Quantity}",
                    request.ProductId, request.FromWarehouseId, request.ToWarehouseId, request.Quantity);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("Product ID is required");

                if (request.FromWarehouseId == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("From warehouse ID is required");

                if (request.ToWarehouseId == Guid.Empty)
                    return CustomResponseDto<bool>.BadRequest("To warehouse ID is required");

                if (request.FromWarehouseId == request.ToWarehouseId)
                    return CustomResponseDto<bool>.BadRequest("Source and destination warehouses cannot be the same");

                if (request.Quantity <= 0)
                    return CustomResponseDto<bool>.BadRequest("Quantity must be positive");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessTransferStockAsync(
                    request.ProductId,
                    request.FromWarehouseId,
                    request.ToWarehouseId,
                    request.Quantity,
                    request.Reference,
                    request.Reason,
                    request.UserId);

                _logger.LogInformation("Transfer stock command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling transfer stock command for ProductId: {ProductId}", request.ProductId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while transferring stock");
            }
        }
    }
}
