using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingZoneCountry entity
    /// </summary>
    public class ShippingZoneCountryConfiguration : IEntityTypeConfiguration<ShippingZoneCountry>
    {
        public void Configure(EntityTypeBuilder<ShippingZoneCountry> builder)
        {
            // Table configuration
            builder.ToTable("ShippingZoneCountries", "etyra_shipping");

            // Primary key
            builder.HasKey(szc => szc.Id);

            // Properties
            builder.Property(szc => szc.CostModifierPercentage)
                .HasPrecision(5, 2)
                .HasDefaultValue(0);

            builder.Property(szc => szc.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            // Money value object configuration
            builder.OwnsOne(szc => szc.CostModifierAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("CostModifierAmount")
                    .HasPrecision(18, 2)
                    .HasDefaultValue(0);

                money.Property(m => m.Currency)
                    .HasColumnName("CostModifierCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => new Currency(code, code, code, 2))
                    .HasMaxLength(3)
                    .HasDefaultValue("EUR");
            });

            // Foreign key relationships
            builder.HasOne(szc => szc.ShippingZone)
                .WithMany(sz => sz.Countries)
                .HasForeignKey(szc => szc.ShippingZoneId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(szc => szc.Country)
                .WithMany(c => c.ShippingZones)
                .HasForeignKey(szc => szc.CountryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint
            builder.HasIndex(szc => new { szc.ShippingZoneId, szc.CountryId })
                .IsUnique()
                .HasDatabaseName("IX_ShippingZoneCountries_Zone_Country_Unique");

            // Other indexes
            builder.HasIndex(szc => szc.ShippingZoneId)
                .HasDatabaseName("IX_ShippingZoneCountries_ShippingZoneId");

            builder.HasIndex(szc => szc.CountryId)
                .HasDatabaseName("IX_ShippingZoneCountries_CountryId");

            builder.HasIndex(szc => szc.IsActive)
                .HasDatabaseName("IX_ShippingZoneCountries_IsActive");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneCountries_CostModifierPercentage",
                "\"CostModifierPercentage\" >= -100 AND \"CostModifierPercentage\" <= 1000"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneCountries_CostModifierAmount",
                "\"CostModifierAmount\" >= -1000000 AND \"CostModifierAmount\" <= 1000000"));

            // Table comment
            builder.ToTable(t => t.HasComment("Mapping between shipping zones and countries with cost modifiers"));
        }
    }
}
