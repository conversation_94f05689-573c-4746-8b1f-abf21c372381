using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Commands;

/// <summary>
/// Command to delete a currency
/// </summary>
public class DeleteCurrencyCommand : IRequest<CustomResponseDto<NoContentDto>>
{
    /// <summary>
    /// Currency ID to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Currency ID</param>
    public DeleteCurrencyCommand(Guid id)
    {
        Id = id;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public DeleteCurrencyCommand() { }
}
