using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Commands
{
    /// <summary>
    /// Command to create a new order
    /// </summary>
    public class CreateOrderCommand : IRequest<CustomResponseDto<OrderDto>>
    {
        /// <summary>
        /// Customer ID
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Customer email
        /// </summary>
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number (optional)
        /// </summary>
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer last name
        /// </summary>
        public string CustomerLastName { get; set; } = string.Empty;

        /// <summary>
        /// Billing address
        /// </summary>
        public CreateAddressDto BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address
        /// </summary>
        public CreateAddressDto ShippingAddress { get; set; } = null!;

        /// <summary>
        /// Order items
        /// </summary>
        public List<CreateOrderItemDto> OrderItems { get; set; } = new();

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Customer notes (optional)
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Shipping method (optional)
        /// </summary>
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Payment method (optional)
        /// </summary>
        public string? PaymentMethod { get; set; }
    }
}
