using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Repositories.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Services;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Payment;

/// <summary>
/// Payment method process service implementation for business logic operations
/// </summary>
public class PaymentMethodProcessService : Service<PaymentMethod, PaymentMethodDto>, IPaymentMethodProcessService
{
    private readonly IPaymentMethodReadRepository _paymentMethodReadRepository;
    private readonly IPaymentMethodWriteRepository _paymentMethodWriteRepository;

    public PaymentMethodProcessService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<PaymentMethodProcessService> logger,
        IPaymentMethodReadRepository paymentMethodReadRepository,
        IPaymentMethodWriteRepository paymentMethodWriteRepository)
        : base(unitOfWork, mapper, logger)
    {
        _paymentMethodReadRepository = paymentMethodReadRepository;
        _paymentMethodWriteRepository = paymentMethodWriteRepository;
    }

    /// <summary>
    /// Process create payment method request
    /// </summary>
    /// <param name="createDto">Create payment method DTO</param>
    /// <returns>Created payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> ProcessCreateAsync(CreatePaymentMethodDto createDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { createDto });

            // Validation
            if (string.IsNullOrWhiteSpace(createDto.Name))
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method name is required");
            }

            if (string.IsNullOrWhiteSpace(createDto.Code))
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method code is required");
            }

            // Check if code already exists
            var codeExists = await _paymentMethodReadRepository.CodeExistsAsync(createDto.Code);
            if (codeExists)
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest($"Payment method code '{createDto.Code}' already exists");
            }

            // Create payment method entity
            var paymentMethod = new PaymentMethod(
                name: createDto.Name.Trim(),
                code: createDto.Code.Trim().ToUpper(),
                type: createDto.Type,
                description: createDto.Description?.Trim())
            {
                DisplayOrder = createDto.DisplayOrder,
                Instructions = createDto.Instructions?.Trim()
            };

            // Set fee if provided
            if (createDto.FeeCalculationType != FeeCalculationType.None)
            {
                Currency? feeCurrency = null;
                if (!string.IsNullOrWhiteSpace(createDto.FeeCurrencyCode))
                {
                    feeCurrency = new Currency(
                        createDto.FeeCurrencyCode,
                        createDto.FeeCurrencyName ?? createDto.FeeCurrencyCode,
                        createDto.FeeCurrencySymbol ?? createDto.FeeCurrencyCode);
                }

                paymentMethod.SetFee(createDto.FeeCalculationType, createDto.FeeValue, feeCurrency);
            }

            // Set order amount limits if provided
            if (createDto.MinimumOrderAmount.HasValue && !string.IsNullOrWhiteSpace(createDto.MinimumOrderCurrency))
            {
                var minCurrency = new Currency(createDto.MinimumOrderCurrency, createDto.MinimumOrderCurrency, createDto.MinimumOrderCurrency);
                paymentMethod.SetOrderLimits(minimumAmount: new Money(createDto.MinimumOrderAmount.Value, minCurrency));
            }

            if (createDto.MaximumOrderAmount.HasValue && !string.IsNullOrWhiteSpace(createDto.MaximumOrderCurrency))
            {
                var maxCurrency = new Currency(createDto.MaximumOrderCurrency, createDto.MaximumOrderCurrency, createDto.MaximumOrderCurrency);
                paymentMethod.SetOrderLimits(maximumAmount: new Money(createDto.MaximumOrderAmount.Value, maxCurrency));
            }

            // Save to database
            var createdPaymentMethod = await _paymentMethodWriteRepository.AddAsync(paymentMethod);
            await _unitOfWork.CommitAsync();

            var paymentMethodDto = _mapper.Map<PaymentMethodDto>(createdPaymentMethod);

            _logger.LogInformation("Payment method {PaymentMethodId} created successfully with code {Code}", 
                createdPaymentMethod.Id, createDto.Code);

            return CustomResponseDto<PaymentMethodDto>.Created(paymentMethodDto, "Payment method created successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<PaymentMethodDto>(ex);
        }
    }

    /// <summary>
    /// Process update payment method request
    /// </summary>
    /// <param name="updateDto">Update payment method DTO</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> ProcessUpdateAsync(UpdatePaymentMethodDto updateDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { updateDto });

            // Validation
            if (updateDto.Id == Guid.Empty)
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method ID is required");
            }

            if (string.IsNullOrWhiteSpace(updateDto.Name))
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method name is required");
            }

            if (string.IsNullOrWhiteSpace(updateDto.Code))
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method code is required");
            }

            // Get existing payment method
            var existingPaymentMethod = await _paymentMethodReadRepository.GetByIdAsync(updateDto.Id, tracking: true);
            if (existingPaymentMethod == null)
            {
                return CustomResponseDto<PaymentMethodDto>.NotFound("Payment method not found");
            }

            // Check if code already exists (excluding current payment method)
            var codeExists = await _paymentMethodReadRepository.CodeExistsAsync(updateDto.Code.Trim().ToUpper(), updateDto.Id);
            if (codeExists)
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest($"Payment method code '{updateDto.Code}' already exists");
            }

            // Update payment method properties
            existingPaymentMethod.Name = updateDto.Name.Trim();
            existingPaymentMethod.Code = updateDto.Code.Trim().ToUpper();
            existingPaymentMethod.Description = updateDto.Description?.Trim();
            existingPaymentMethod.Instructions = updateDto.Instructions?.Trim();
            existingPaymentMethod.IsActive = updateDto.IsActive;
            existingPaymentMethod.DisplayOrder = updateDto.DisplayOrder;
            existingPaymentMethod.Type = updateDto.Type;
            existingPaymentMethod.UpdatedAt = DateTime.UtcNow;

            // Update fee if provided
            if (updateDto.FeeCalculationType != FeeCalculationType.None)
            {
                Currency? feeCurrency = null;
                if (!string.IsNullOrWhiteSpace(updateDto.FeeCurrencyCode))
                {
                    feeCurrency = new Currency(
                        updateDto.FeeCurrencyCode,
                        updateDto.FeeCurrencyName ?? updateDto.FeeCurrencyCode,
                        updateDto.FeeCurrencySymbol ?? updateDto.FeeCurrencyCode,
                        updateDto.FeeCurrencyDecimalPlaces ?? 2);
                }

                existingPaymentMethod.SetFee(updateDto.FeeCalculationType, updateDto.FeeValue, feeCurrency);
            }
            else
            {
                existingPaymentMethod.SetFee(FeeCalculationType.None, 0, null);
            }

            // Update order amount limits
            if (updateDto.MinimumOrderAmount.HasValue && !string.IsNullOrWhiteSpace(updateDto.MinimumOrderCurrency))
            {
                var minCurrency = new Currency(updateDto.MinimumOrderCurrency, updateDto.MinimumOrderCurrency, updateDto.MinimumOrderCurrency);
                existingPaymentMethod.SetOrderLimits(minimumAmount: new Money(updateDto.MinimumOrderAmount.Value, minCurrency));
            }
            else
            {
                existingPaymentMethod.SetOrderLimits(minimumAmount: null);
            }

            if (updateDto.MaximumOrderAmount.HasValue && !string.IsNullOrWhiteSpace(updateDto.MaximumOrderCurrency))
            {
                var maxCurrency = new Currency(updateDto.MaximumOrderCurrency, updateDto.MaximumOrderCurrency, updateDto.MaximumOrderCurrency);
                existingPaymentMethod.SetOrderLimits(maximumAmount: new Money(updateDto.MaximumOrderAmount.Value, maxCurrency));
            }
            else
            {
                existingPaymentMethod.SetOrderLimits(maximumAmount: null);
            }

            // Save changes
            await _unitOfWork.CommitAsync();

            var paymentMethodDto = _mapper.Map<PaymentMethodDto>(existingPaymentMethod);

            _logger.LogInformation("Payment method {PaymentMethodId} updated successfully", updateDto.Id);

            return CustomResponseDto<PaymentMethodDto>.Success(paymentMethodDto, "Payment method updated successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<PaymentMethodDto>(ex);
        }
    }

    /// <summary>
    /// Process delete payment method request
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<NoContentDto>.BadRequest("Payment method ID is required");
            }

            // Get existing payment method
            var existingPaymentMethod = await _paymentMethodReadRepository.GetByIdAsync(id, tracking: true);
            if (existingPaymentMethod == null)
            {
                return CustomResponseDto<NoContentDto>.NotFound("Payment method not found");
            }

            // TODO: Check if payment method is used in any orders
            // For now, we'll allow deletion

            // Soft delete
            await _paymentMethodWriteRepository.RemoveAsync(existingPaymentMethod);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("Payment method {PaymentMethodId} deleted successfully", id);

            return CustomResponseDto<NoContentDto>.Success("Payment method deleted successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<NoContentDto>(ex);
        }
    }

    /// <summary>
    /// Process toggle payment method status request
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> ProcessToggleStatusAsync(Guid id, bool isActive)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, isActive });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<PaymentMethodDto>.BadRequest("Payment method ID is required");
            }

            // Update status
            var updatedPaymentMethod = await _paymentMethodWriteRepository.UpdateStatusAsync(id, isActive);
            if (updatedPaymentMethod == null)
            {
                return CustomResponseDto<PaymentMethodDto>.NotFound("Payment method not found");
            }

            await _unitOfWork.CommitAsync();

            var paymentMethodDto = _mapper.Map<PaymentMethodDto>(updatedPaymentMethod);

            _logger.LogInformation("Payment method {PaymentMethodId} status updated to {IsActive}", id, isActive);

            return CustomResponseDto<PaymentMethodDto>.Success(paymentMethodDto, "Payment method status updated successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<PaymentMethodDto>(ex);
        }
    }

    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    public async Task<CustomResponseDto<PaymentMethodDto?>> GetByIdAsync(Guid id, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, tracking });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<PaymentMethodDto?>.BadRequest("Payment method ID is required");
            }

            var paymentMethod = await _paymentMethodReadRepository.GetByIdAsync(id, tracking);
            var paymentMethodDto = _mapper.Map<PaymentMethodDto?>(paymentMethod);

            return CustomResponseDto<PaymentMethodDto?>.Success(paymentMethodDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<PaymentMethodDto?>(ex);
        }
    }

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    public async Task<CustomResponseDto<PaymentMethodDto?>> GetByCodeAsync(string code, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { code, tracking });

            if (string.IsNullOrWhiteSpace(code))
            {
                return CustomResponseDto<PaymentMethodDto?>.BadRequest("Payment method code is required");
            }

            var paymentMethod = await _paymentMethodReadRepository.GetByCodeAsync(code.Trim().ToUpper(), tracking);
            var paymentMethodDto = _mapper.Map<PaymentMethodDto?>(paymentMethod);

            return CustomResponseDto<PaymentMethodDto?>.Success(paymentMethodDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<PaymentMethodDto?>(ex);
        }
    }

    /// <summary>
    /// Get all payment methods
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of all payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var paymentMethods = await _paymentMethodReadRepository.GetAllAsync(tracking);
            var paymentMethodDtos = _mapper.Map<List<PaymentMethodDto>>(paymentMethods);

            return CustomResponseDto<List<PaymentMethodDto>>.Success(paymentMethodDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<PaymentMethodDto>>(ex);
        }
    }

    /// <summary>
    /// Get active payment methods
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsAsync(bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking });

            var paymentMethods = await _paymentMethodReadRepository.GetActivePaymentMethodsAsync(tracking);
            var paymentMethodDtos = _mapper.Map<List<PaymentMethodDto>>(paymentMethods);

            return CustomResponseDto<List<PaymentMethodDto>>.Success(paymentMethodDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<PaymentMethodDto>>(ex);
        }
    }

    /// <summary>
    /// Get payment methods by type
    /// </summary>
    /// <param name="type">Payment method type</param>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetByTypeAsync(PaymentMethodType type, bool onlyActive = true, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { type, onlyActive, tracking });

            var paymentMethods = await _paymentMethodReadRepository.GetByTypeAsync(type, onlyActive, tracking);
            var paymentMethodDtos = _mapper.Map<List<PaymentMethodDto>>(paymentMethods);

            return CustomResponseDto<List<PaymentMethodDto>>.Success(paymentMethodDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<PaymentMethodDto>>(ex);
        }
    }

    /// <summary>
    /// Get payment methods available for order amount with calculated fees
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of available payment methods with calculated fees</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetAvailableForAmountAsync(decimal orderAmount, string orderCurrency, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { orderAmount, orderCurrency, tracking });

            if (orderAmount <= 0)
            {
                return CustomResponseDto<List<PaymentMethodDto>>.BadRequest("Order amount must be greater than zero");
            }

            if (string.IsNullOrWhiteSpace(orderCurrency))
            {
                return CustomResponseDto<List<PaymentMethodDto>>.BadRequest("Order currency is required");
            }

            var paymentMethods = await _paymentMethodReadRepository.GetAvailableForAmountAsync(orderAmount, orderCurrency.Trim().ToUpper(), tracking);
            var paymentMethodDtos = _mapper.Map<List<PaymentMethodDto>>(paymentMethods);

            // Calculate fees for each payment method
            var orderMoney = new Money(orderAmount, new Currency(orderCurrency.Trim().ToUpper(), orderCurrency, orderCurrency));

            foreach (var dto in paymentMethodDtos)
            {
                var paymentMethod = paymentMethods.First(pm => pm.Id == dto.Id);
                var calculatedFee = paymentMethod.CalculateFee(orderMoney);
                dto.CalculatedFee = calculatedFee?.Amount ?? 0;
                dto.IsAvailableForAmount = paymentMethod.IsAvailableForAmount(orderMoney);
            }

            return CustomResponseDto<List<PaymentMethodDto>>.Success(paymentMethodDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<PaymentMethodDto>>(ex);
        }
    }

    /// <summary>
    /// Check if payment method code exists
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    public async Task<CustomResponseDto<bool>> CodeExistsAsync(string code, Guid? excludeId = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { code, excludeId });

            if (string.IsNullOrWhiteSpace(code))
            {
                return CustomResponseDto<bool>.BadRequest("Payment method code is required");
            }

            var exists = await _paymentMethodReadRepository.CodeExistsAsync(code.Trim().ToUpper(), excludeId);

            return CustomResponseDto<bool>.Success(exists);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<bool>(ex);
        }
    }

    /// <summary>
    /// Calculate fee for payment method and order amount
    /// </summary>
    /// <param name="paymentMethodId">Payment method ID</param>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <returns>Calculated fee amount</returns>
    public async Task<CustomResponseDto<decimal>> CalculateFeeAsync(Guid paymentMethodId, decimal orderAmount, string orderCurrency)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { paymentMethodId, orderAmount, orderCurrency });

            if (paymentMethodId == Guid.Empty)
            {
                return CustomResponseDto<decimal>.BadRequest("Payment method ID is required");
            }

            if (orderAmount <= 0)
            {
                return CustomResponseDto<decimal>.BadRequest("Order amount must be greater than zero");
            }

            if (string.IsNullOrWhiteSpace(orderCurrency))
            {
                return CustomResponseDto<decimal>.BadRequest("Order currency is required");
            }

            var paymentMethod = await _paymentMethodReadRepository.GetByIdAsync(paymentMethodId);
            if (paymentMethod == null)
            {
                return CustomResponseDto<decimal>.NotFound("Payment method not found");
            }

            var orderMoney = new Money(orderAmount, new Currency(orderCurrency.Trim().ToUpper(), orderCurrency, orderCurrency));
            var calculatedFee = paymentMethod.CalculateFee(orderMoney);

            return CustomResponseDto<decimal>.Success(calculatedFee?.Amount ?? 0);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<decimal>(ex);
        }
    }
}
