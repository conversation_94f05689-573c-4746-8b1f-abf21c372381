using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;

namespace EtyraCommerce.Application.Services.User
{
    /// <summary>
    /// User Service Interface - CQRS Orchestration Layer
    /// Pure API orchestration - delegates to MediatR handlers
    /// No generic service inheritance - only business API methods
    /// </summary>
    public interface IUserService
    {
        #region Authentication & Authorization

        /// <summary>
        /// Authenticate user with email/username and password
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>User data with authentication result</returns>
        Task<CustomResponseDto<UserDto>> LoginAsync(UserLoginDto loginDto);

        /// <summary>
        /// Register a new user account
        /// </summary>
        /// <param name="registerDto">Registration data</param>
        /// <returns>Created user data</returns>
        Task<CustomResponseDto<UserDto>> RegisterAsync(CreateUserDto registerDto);

        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="changePasswordDto">Password change data</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ChangePasswordAsync(Guid userId, ChangePasswordDto changePasswordDto);

        /// <summary>
        /// Reset user password with token
        /// </summary>
        /// <param name="resetPasswordDto">Password reset data</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);

        /// <summary>
        /// Generate password reset token
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>Success result (token sent via email)</returns>
        Task<CustomResponseDto<NoContentDto>> GeneratePasswordResetTokenAsync(string email);

        #endregion

        #region User Data Management

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User data</returns>
        Task<CustomResponseDto<UserDto>> GetByIdAsync(Guid userId);

        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>User data</returns>
        Task<CustomResponseDto<UserDto>> GetByEmailAsync(string email);

        /// <summary>
        /// Get user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User data</returns>
        Task<CustomResponseDto<UserDto>> GetByUsernameAsync(string username);

        #endregion

        #region Account Management

        /// <summary>
        /// Activate user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ActivateUserAsync(Guid userId);

        /// <summary>
        /// Deactivate user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> DeactivateUserAsync(Guid userId);

        /// <summary>
        /// Confirm user email with token
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="token">Email confirmation token</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ConfirmEmailAsync(Guid userId, string token);

        /// <summary>
        /// Confirm user phone number with token
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="token">Phone confirmation token</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> ConfirmPhoneAsync(Guid userId, string token);

        /// <summary>
        /// Lock user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="lockUntil">Lock until date</param>
        /// <param name="reason">Lock reason</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> LockUserAsync(Guid userId, DateTime lockUntil, string reason);

        /// <summary>
        /// Unlock user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<CustomResponseDto<NoContentDto>> UnlockUserAsync(Guid userId);

        #endregion

        #region User Queries

        /// <summary>
        /// Get user by email address
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>User data or null</returns>
        Task<CustomResponseDto<UserDto?>> GetUserByEmailAsync(string email);

        /// <summary>
        /// Get user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User data or null</returns>
        Task<CustomResponseDto<UserDto?>> GetUserByUsernameAsync(string username);

        /// <summary>
        /// Check if email exists
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>True if exists</returns>
        Task<CustomResponseDto<bool>> EmailExistsAsync(string email);

        /// <summary>
        /// Check if username exists
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>True if exists</returns>
        Task<CustomResponseDto<bool>> UsernameExistsAsync(string username);

        /// <summary>
        /// Search users with advanced filters
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>Paginated search results</returns>
        Task<CustomResponseDto<PagedResult<UserDto>>> SearchUsersAsync(UserSearchDto searchDto);

        #endregion

        #region Statistics & Analytics

        /// <summary>
        /// Get user statistics for admin dashboard
        /// </summary>
        /// <returns>User statistics</returns>
        Task<CustomResponseDto<UserStatisticsDto>> GetUserStatisticsAsync();

        /// <summary>
        /// Get user login history
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated login history</returns>
        Task<CustomResponseDto<PagedResult<UserLoginHistoryDto>>> GetUserLoginHistoryAsync(Guid userId, int pageNumber = 1, int pageSize = 20);

        #endregion
    }
}
