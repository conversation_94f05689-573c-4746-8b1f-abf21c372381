namespace EtyraCommerce.Application.Repositories.User
{
    /// <summary>
    /// User-specific write repository interface
    /// Extends generic write repository with user-specific modification methods
    /// </summary>
    public interface IUserWriteRepository : IWriteRepository<Domain.Entities.User.User>
    {
        /// <summary>
        /// Updates user's last login timestamp
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateLastLoginAsync(Guid userId);

        /// <summary>
        /// Increments failed login attempts for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Current failed attempts count</returns>
        Task<int> IncrementFailedLoginAttemptsAsync(Guid userId);

        /// <summary>
        /// Resets failed login attempts for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> ResetFailedLoginAttemptsAsync(Guid userId);

        /// <summary>
        /// Locks user account until specified date
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="lockUntil">Lock until date</param>
        /// <param name="reason">Lock reason</param>
        /// <returns>True if successful</returns>
        Task<bool> LockUserAsync(Guid userId, DateTime lockUntil, string reason);

        /// <summary>
        /// Unlocks user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> UnlockUserAsync(Guid userId);

        /// <summary>
        /// Activates user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> ActivateUserAsync(Guid userId);

        /// <summary>
        /// Deactivates user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeactivateUserAsync(Guid userId);

        /// <summary>
        /// Confirms user email
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> ConfirmEmailAsync(Guid userId);

        /// <summary>
        /// Confirms user phone number
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> ConfirmPhoneAsync(Guid userId);

        /// <summary>
        /// Updates user password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="passwordHash">New password hash</param>
        /// <param name="passwordSalt">New password salt</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdatePasswordAsync(Guid userId, string passwordHash, string passwordSalt);

        /// <summary>
        /// Sets password reset token for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="token">Reset token</param>
        /// <param name="expiry">Token expiry</param>
        /// <returns>True if successful</returns>
        Task<bool> SetPasswordResetTokenAsync(Guid userId, string token, DateTime expiry);

        /// <summary>
        /// Clears password reset token for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        Task<bool> ClearPasswordResetTokenAsync(Guid userId);

        /// <summary>
        /// Bulk activates multiple users
        /// </summary>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of users activated</returns>
        Task<int> BulkActivateUsersAsync(IEnumerable<Guid> userIds);

        /// <summary>
        /// Bulk deactivates multiple users
        /// </summary>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of users deactivated</returns>
        Task<int> BulkDeactivateUsersAsync(IEnumerable<Guid> userIds);

        /// <summary>
        /// Deletes users that have been soft deleted for more than specified days
        /// </summary>
        /// <param name="daysOld">Number of days</param>
        /// <returns>Number of users permanently deleted</returns>
        Task<int> PermanentlyDeleteOldUsersAsync(int daysOld = 30);
    }
}
