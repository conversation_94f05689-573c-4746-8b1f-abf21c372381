[{"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "CreateCategory", "RelativePath": "api/Category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCategoryDto", "Type": "EtyraCommerce.Application.DTOs.Category.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategories", "RelativePath": "api/Category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "showInMenu", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "parentCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "level", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "UpdateCategory", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateCategoryDto", "Type": "EtyraCommerce.Application.DTOs.Category.UpdateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "DeleteCategory", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "forceDelete", "Type": "System.Boolean", "IsRequired": false}, {"Name": "deleteC<PERSON><PERSON>n", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoryById", "RelativePath": "api/Category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeParent", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoriesByParent", "RelativePath": "api/Category/by-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDescriptions", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetMenuCategories", "RelativePath": "api/Category/menu", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "max<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetRootCategories", "RelativePath": "api/Category/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetAllCategories", "RelativePath": "api/Category/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchDto", "Type": "EtyraCommerce.Application.DTOs.Category.CategorySearchDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.CategoryController", "Method": "GetCategoryTree", "RelativePath": "api/Category/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "rootCategoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "max<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "activeOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "menuOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "languageCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "EtyraCommerce.Application.DTOs.User.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/User/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "EtyraCommerce.Application.DTOs.User.UserLoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "LogoutAll", "RelativePath": "api/User/logout-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "GetProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "RefreshToken", "RelativePath": "api/User/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "EtyraCommerce.Application.DTOs.Authentication.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.Authentication.TokenResponseDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "Register", "RelativePath": "api/User/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "EtyraCommerce.Application.DTOs.User.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.User.UserDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "EtyraCommerce.API.Controllers.UserController", "Method": "RevokeToken", "RelativePath": "api/User/revoke-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "revokeTokenDto", "Type": "EtyraCommerce.Application.DTOs.Authentication.RevokeTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtyraCommerce.Application.DTOs.CustomResponse.CustomResponseDto`1[[EtyraCommerce.Application.DTOs.CustomResponse.NoContentDto, EtyraCommerce.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}]}, {"ContainingType": "EtyraCommerce.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[EtyraCommerce.API.WeatherForecast, EtyraCommerce.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]