using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Queries
{
    /// <summary>
    /// Handler for getting the default address for a user
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class GetDefaultAddressQueryHandler : IRequestHandler<GetDefaultAddressQuery, CustomResponseDto<UserAddressDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<GetDefaultAddressQueryHandler> _logger;

        public GetDefaultAddressQueryHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<GetDefaultAddressQueryHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserAddressDto>> Handle(GetDefaultAddressQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling GetDefaultAddressQuery for UserId: {UserId}, AddressType: {AddressType}",
                    request.UserId, request.AddressType);

                // Validate request
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetDefaultAddressQuery received with empty UserId");
                    return CustomResponseDto<UserAddressDto>.BadRequest("User ID is required");
                }

                if (request.AddressType.HasValue && (request.AddressType < 1 || request.AddressType > 3))
                {
                    _logger.LogWarning("GetDefaultAddressQuery received with invalid AddressType: {AddressType} for UserId: {UserId}",
                        request.AddressType, request.UserId);
                    return CustomResponseDto<UserAddressDto>.BadRequest("Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessGetDefaultAddressAsync(request.UserId, request.AddressType);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("GetDefaultAddressQuery handled successfully for UserId: {UserId}", request.UserId);
                }
                else
                {
                    _logger.LogWarning("GetDefaultAddressQuery failed for UserId: {UserId}, Error: {Error}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling GetDefaultAddressQuery for UserId: {UserId}", request.UserId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the default address");
            }
        }
    }
}
