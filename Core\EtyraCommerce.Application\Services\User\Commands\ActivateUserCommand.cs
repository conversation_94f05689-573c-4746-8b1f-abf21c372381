using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for activating a user account
    /// </summary>
    public class ActivateUserCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// User ID to activate
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Reason for activation (optional)
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// ID of the admin performing the activation
        /// </summary>
        public Guid? ActivatedByUserId { get; set; }

        public ActivateUserCommand() { }

        public ActivateUserCommand(Guid userId, string? reason = null, Guid? activatedByUserId = null)
        {
            UserId = userId;
            Reason = reason;
            ActivatedByUserId = activatedByUserId;
        }
    }
}
