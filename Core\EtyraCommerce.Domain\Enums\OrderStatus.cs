namespace EtyraCommerce.Domain.Enums
{
    /// <summary>
    /// Order status enumeration representing the lifecycle of an order
    /// </summary>
    public enum OrderStatus
    {
        /// <summary>
        /// Order is being created but not yet confirmed
        /// </summary>
        Draft = 0,

        /// <summary>
        /// Order has been confirmed and payment is pending
        /// </summary>
        Confirmed = 1,

        /// <summary>
        /// Payment has been received and order is being processed
        /// </summary>
        Processing = 2,

        /// <summary>
        /// Order has been shipped to customer
        /// </summary>
        Shipped = 3,

        /// <summary>
        /// Order has been delivered to customer
        /// </summary>
        Delivered = 4,

        /// <summary>
        /// Order has been cancelled
        /// </summary>
        Cancelled = 5,

        /// <summary>
        /// Order has been returned by customer
        /// </summary>
        Returned = 6,

        /// <summary>
        /// Order has been refunded
        /// </summary>
        Refunded = 7,

        /// <summary>
        /// Order is on hold (payment issues, stock issues, etc.)
        /// </summary>
        OnHold = 8,

        /// <summary>
        /// Order failed (payment failed, validation failed, etc.)
        /// </summary>
        Failed = 9
    }

    /// <summary>
    /// Payment status enumeration
    /// </summary>
    public enum PaymentStatus
    {
        /// <summary>
        /// Payment is pending
        /// </summary>
        Pending = 0,

        /// <summary>
        /// Payment has been completed successfully
        /// </summary>
        Completed = 1,

        /// <summary>
        /// Payment has failed
        /// </summary>
        Failed = 2,

        /// <summary>
        /// Payment has been cancelled
        /// </summary>
        Cancelled = 3,

        /// <summary>
        /// Payment has been refunded
        /// </summary>
        Refunded = 4,

        /// <summary>
        /// Partial payment received
        /// </summary>
        Partial = 5,

        /// <summary>
        /// Payment is being processed
        /// </summary>
        Processing = 6
    }

    /// <summary>
    /// Shipping status enumeration
    /// </summary>
    public enum ShippingStatus
    {
        /// <summary>
        /// Not yet shipped
        /// </summary>
        NotShipped = 0,

        /// <summary>
        /// Preparing for shipment
        /// </summary>
        Preparing = 1,

        /// <summary>
        /// Shipped and in transit
        /// </summary>
        InTransit = 2,

        /// <summary>
        /// Out for delivery
        /// </summary>
        OutForDelivery = 3,

        /// <summary>
        /// Successfully delivered
        /// </summary>
        Delivered = 4,

        /// <summary>
        /// Delivery failed
        /// </summary>
        DeliveryFailed = 5,

        /// <summary>
        /// Returned to sender
        /// </summary>
        ReturnedToSender = 6
    }
}
