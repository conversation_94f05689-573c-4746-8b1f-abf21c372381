﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;

namespace EtyraApp.Domain.Entities.Integrations;

public class IntegrationStoreCategory : BaseEntity
{
    public int? CategoryId { get; set; }
    public Category? Category { get; set; }
    public int? StoreId { get; set; }
    public Store? Store { get; set; }
    public int? RemoteCategoryId { get; set; }
}