using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Queries
{
    /// <summary>
    /// Query to get shopping cart summary (without detailed items)
    /// </summary>
    public class GetCartSummaryQuery : IRequest<CustomResponseDto<CartSummaryDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Creates query for user cart
        /// </summary>
        public static GetCartSummaryQuery ForUser(Guid customerId)
        {
            return new GetCartSummaryQuery
            {
                CustomerId = customerId
            };
        }

        /// <summary>
        /// Creates query for guest cart
        /// </summary>
        public static GetCartSummaryQuery ForGuest(string sessionId)
        {
            return new GetCartSummaryQuery
            {
                SessionId = sessionId
            };
        }
    }
}
