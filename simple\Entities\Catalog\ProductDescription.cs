﻿using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.Settings;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Catalog;

public class ProductDescription
{
    [MaxLength(120)]
    public string Name { get; set; }

    public string Description { get; set; }


    [MaxLength(120)]
    public string MetaTitle { get; set; }

    [MaxLength(350)]
    public string? MetaDescription { get; set; }

    [MaxLength(200)]
    public string? MetaKeywords { get; set; }

    [MaxLength(200)]
    public string? Tags { get; set; }

    [MaxLength(150)]
    public string? Seo { get; set; }



    //Relationships

    // PRODUCT - LANGUAGE - STORE
    public int ProductId { get; set; }

    public Product Product { get; set; }

    public Language Language { get; set; }
    public int LanguageId { get; set; }

    public Store Store { get; set; }
    public int StoreId { get; set; }
}