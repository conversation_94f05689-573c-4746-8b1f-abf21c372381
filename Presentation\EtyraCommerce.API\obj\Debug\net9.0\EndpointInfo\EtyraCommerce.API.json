{"openapi": "3.0.1", "info": {"title": "EtyraCommerce API", "description": "EtyraCommerce E-commerce Platform API", "version": "v1"}, "paths": {"/api/Category": {"post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Category"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "showInMenu", "in": "query", "schema": {"type": "boolean"}}, {"name": "parentCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "level", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "schema": {"$ref": "#/components/schemas/CategorySortField"}}, {"name": "sortDirection", "in": "query", "schema": {"$ref": "#/components/schemas/SortDirection"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/{categoryId}": {"put": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "forceDelete", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "deleteC<PERSON><PERSON>n", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeParent", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/search": {"post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Category/by-parent": {"get": {"tags": ["Category"], "parameters": [{"name": "parentCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/root": {"get": {"tags": ["Category"], "parameters": [{"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/tree": {"get": {"tags": ["Category"], "parameters": [{"name": "rootCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "max<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "menuOnly", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/menu": {"get": {"tags": ["Category"], "parameters": [{"name": "max<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 3}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/health": {"get": {"tags": ["EtyraCommerce.API"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/api/User/login": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}}}}, "/api/User/register": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/refresh-token": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}}}}, "/api/User/revoke-token": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/logout-all": {"post": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"CategorySearchDto": {"type": "object", "properties": {"searchTerm": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "showInMenu": {"type": "boolean", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "level": {"maximum": 10, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "includeChildren": {"type": "boolean"}, "sortBy": {"$ref": "#/components/schemas/CategorySortField"}, "sortDirection": {"$ref": "#/components/schemas/SortDirection"}, "pageNumber": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategorySortField": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "ChangePasswordDto": {"required": ["confirmNewPassword", "currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string", "format": "password"}, "newPassword": {"maxLength": 100, "minLength": 8, "type": "string", "format": "password"}, "confirmNewPassword": {"minLength": 1, "type": "string", "format": "password"}}, "additionalProperties": false}, "CreateCategoryDescriptionDto": {"required": ["languageCode", "name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 2000, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "languageCode": {"maxLength": 10, "minLength": 2, "type": "string"}, "storeId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "showInMenu": {"type": "boolean"}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/CreateCategoryDescriptionDto"}, "nullable": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["confirmPassword", "email", "firstName", "lastName", "password", "username"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "email": {"maxLength": 254, "minLength": 0, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "username": {"maxLength": 50, "minLength": 3, "pattern": "^[a-zA-Z0-9_.-]+$", "type": "string"}, "password": {"maxLength": 100, "minLength": 8, "type": "string", "format": "password"}, "confirmPassword": {"minLength": 1, "type": "string", "format": "password"}, "culture": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "timeZone": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "skipEmailConfirmation": {"type": "boolean"}, "notes": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "acceptTerms": {"type": "boolean"}, "subscribeToNewsletter": {"type": "boolean"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "NoContentDto": {"type": "object", "additionalProperties": false}, "NoContentDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/NoContentDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "RefreshTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokeTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}, "reason": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SortDirection": {"enum": [0, 1], "type": "integer", "format": "int32"}, "TokenResponseDto": {"required": ["accessToken", "expiresAt", "refreshToken", "refreshTokenExpiresAt", "tokenType"], "type": "object", "properties": {"accessToken": {"minLength": 1, "type": "string"}, "refreshToken": {"minLength": 1, "type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "refreshTokenExpiresAt": {"type": "string", "format": "date-time"}, "tokenType": {"minLength": 1, "type": "string"}, "expiresIn": {"type": "integer", "format": "int32"}, "user": {"$ref": "#/components/schemas/TokenUserDto"}, "scopes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "claims": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "TokenResponseDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TokenResponseDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "TokenUserDto": {"required": ["email", "id", "username"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string"}, "fullName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "showInMenu": {"type": "boolean"}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "deletedBy": {"type": "string", "format": "uuid", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}, "deletedByName": {"type": "string", "nullable": true}, "createdByEmail": {"type": "string", "nullable": true}, "updatedByEmail": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "creatorDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "updaterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "deleterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "isSelfUpdated": {"type": "boolean", "readOnly": true}, "timeSinceDeletion": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isDeletedToday": {"type": "boolean", "readOnly": true}, "isRecentlyDeleted": {"type": "boolean", "readOnly": true}, "formattedDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "isEmailConfirmed": {"type": "boolean"}, "isPhoneConfirmed": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "failedLoginAttempts": {"type": "integer", "format": "int32"}, "lockedUntil": {"type": "string", "format": "date-time", "nullable": true}, "culture": {"type": "string", "nullable": true}, "timeZone": {"type": "string", "nullable": true}, "profilePictureUrl": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "isLocked": {"type": "boolean", "readOnly": true}, "canLogin": {"type": "boolean", "readOnly": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}, "maskedEmail": {"type": "string", "nullable": true, "readOnly": true}, "maskedPhoneNumber": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "UserLoginDto": {"required": ["emailOrUsername", "password"], "type": "object", "properties": {"emailOrUsername": {"maxLength": 254, "minLength": 0, "type": "string"}, "password": {"minLength": 1, "type": "string", "format": "password"}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}