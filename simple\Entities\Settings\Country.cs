﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Settings;

public class Country : BaseEntity
{
    [MaxLength(30)]
    public string Name { get; set; }
    [MaxLength(3)]
    public string Code { get; set; }

    public decimal TaxValue { get; set; }

    [MaxLength(3)]
    public string FlagCode { get; set; }

    [MaxLength(3)]
    public string? Currency { get; set; }



    //Relationships

    //public ICollection<Supplier> Suppliers { get; set; }
    //public ICollection<Store> Stores { get; set; }
    //public ICollection<WareHouse> WareHouse { get; set; }
}