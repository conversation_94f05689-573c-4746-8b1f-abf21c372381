using AutoMapper;
using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.Entities.Product;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Services.Cart;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Services
{
    public class CartProcessServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<CartProcessService>> _mockLogger;
        private readonly Mock<IReadRepository<ShoppingCart>> _mockCartReadRepository;
        private readonly Mock<IWriteRepository<ShoppingCart>> _mockCartWriteRepository;
        private readonly Mock<IReadRepository<Product>> _mockProductReadRepository;
        private readonly CartProcessService _service;

        public CartProcessServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<CartProcessService>>();
            _mockCartReadRepository = new Mock<IReadRepository<ShoppingCart>>();
            _mockCartWriteRepository = new Mock<IWriteRepository<ShoppingCart>>();
            _mockProductReadRepository = new Mock<IReadRepository<Product>>();

            _mockUnitOfWork.Setup(x => x.ReadRepository<ShoppingCart>()).Returns(_mockCartReadRepository.Object);
            _mockUnitOfWork.Setup(x => x.WriteRepository<ShoppingCart>()).Returns(_mockCartWriteRepository.Object);
            _mockUnitOfWork.Setup(x => x.ReadRepository<Product>()).Returns(_mockProductReadRepository.Object);

            _service = new CartProcessService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object);
        }

        #region ProcessAddToCartAsync Tests

        [Fact]
        public async Task ProcessAddToCartAsync_ValidProduct_ReturnsSuccessWithCartDto()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var addToCartDto = new AddToCartDto
            {
                ProductId = productId,
                Quantity = 2,
                VariantInfo = "Size: L",
                Notes = "Test notes"
            };

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency),
                Status = ProductStatus.Published
            };

            var cart = new ShoppingCart(customerId, currency);
            var cartDto = new CartDto { Id = cart.Id, CustomerId = customerId };

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            var products = new List<Product> { product }.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(products);

            _mockMapper.Setup(x => x.Map<CartDto>(It.IsAny<ShoppingCart>())).Returns(cartDto);

            // Act
            var result = await _service.ProcessAddToCartAsync(addToCartDto, customerId, currency.Code);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(cart.Id);

            _mockCartWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<ShoppingCart>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessAddToCartAsync_ProductNotFound_ReturnsNotFound()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var addToCartDto = new AddToCartDto
            {
                ProductId = productId,
                Quantity = 1
            };

            var cart = new ShoppingCart(customerId, currency);

            // Setup mocks - empty product list
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            var products = new List<Product>().AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(products);

            // Act
            var result = await _service.ProcessAddToCartAsync(addToCartDto, customerId, currency.Code);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Contain("Product not found");
        }

        [Fact]
        public async Task ProcessAddToCartAsync_ProductNotPublished_ReturnsBadRequest()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var addToCartDto = new AddToCartDto
            {
                ProductId = productId,
                Quantity = 1
            };

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency),
                Status = ProductStatus.Draft // Not published
            };

            var cart = new ShoppingCart(customerId, currency);

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            var products = new List<Product> { product }.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(products);

            // Act
            var result = await _service.ProcessAddToCartAsync(addToCartDto, customerId, currency.Code);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Product is not available");
        }

        #endregion

        #region ProcessUpdateCartItemAsync Tests

        [Fact]
        public async Task ProcessUpdateCartItemAsync_ValidUpdate_ReturnsSuccessWithCartDto()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var updateCartItemDto = new UpdateCartItemDto
            {
                ProductId = productId,
                Quantity = 3,
                Notes = "Updated notes"
            };

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency),
                Status = ProductStatus.Published
            };

            var cart = new ShoppingCart(customerId, currency);
            cart.AddItem(productId, product.Name, product.SKU, product.BasePrice, 1);

            var cartDto = new CartDto { Id = cart.Id, CustomerId = customerId };

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            var products = new List<Product> { product }.AsQueryable();
            _mockProductReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(products);

            _mockMapper.Setup(x => x.Map<CartDto>(It.IsAny<ShoppingCart>())).Returns(cartDto);

            // Act
            var result = await _service.ProcessUpdateCartItemAsync(updateCartItemDto, customerId, null);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();

            _mockCartWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<ShoppingCart>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessUpdateCartItemAsync_ZeroQuantity_RemovesItem()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var updateCartItemDto = new UpdateCartItemDto
            {
                ProductId = productId,
                Quantity = 0 // This should remove the item
            };

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency),
                Status = ProductStatus.Published
            };

            var cart = new ShoppingCart(customerId, currency);
            cart.AddItem(productId, product.Name, product.SKU, product.BasePrice, 2);

            var cartDto = new CartDto { Id = cart.Id, CustomerId = customerId };

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            _mockMapper.Setup(x => x.Map<CartDto>(It.IsAny<ShoppingCart>())).Returns(cartDto);

            // Act
            var result = await _service.ProcessUpdateCartItemAsync(updateCartItemDto, customerId, null);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            cart.CartItems.Should().BeEmpty(); // Item should be removed

            _mockCartWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<ShoppingCart>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        #endregion

        #region ProcessRemoveFromCartAsync Tests

        [Fact]
        public async Task ProcessRemoveFromCartAsync_ValidProduct_ReturnsSuccess()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var removeFromCartDto = new RemoveFromCartDto
            {
                ProductId = productId
            };

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency)
            };

            var cart = new ShoppingCart(customerId, currency);
            cart.AddItem(productId, product.Name, product.SKU, product.BasePrice, 1);

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            // Act
            var result = await _service.ProcessRemoveFromCartAsync(removeFromCartDto, customerId, null);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            cart.CartItems.Should().BeEmpty(); // Item should be removed

            _mockCartWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<ShoppingCart>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessRemoveFromCartAsync_CartNotFound_ReturnsNotFound()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();

            var removeFromCartDto = new RemoveFromCartDto
            {
                ProductId = productId
            };

            // Setup mocks - empty cart list
            var carts = new List<ShoppingCart>().AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            // Act
            var result = await _service.ProcessRemoveFromCartAsync(removeFromCartDto, customerId, null);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Contain("Cart not found");
        }

        #endregion

        #region ProcessClearCartAsync Tests

        [Fact]
        public async Task ProcessClearCartAsync_ValidCart_ReturnsSuccess()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var currency = Currency.USD;

            var product = new Product
            {
                Id = productId,
                Name = "Test Product",
                SKU = "TEST-001",
                BasePrice = new Money(100m, currency)
            };

            var cart = new ShoppingCart(customerId, currency);
            cart.AddItem(productId, product.Name, product.SKU, product.BasePrice, 2);

            // Setup mocks
            var carts = new List<ShoppingCart> { cart }.AsQueryable();
            _mockCartReadRepository.Setup(x => x.GetAllAsync(false)).ReturnsAsync(carts);

            // Act
            var result = await _service.ProcessClearCartAsync(customerId, null);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            cart.CartItems.Should().BeEmpty(); // All items should be removed

            _mockCartWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<ShoppingCart>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        #endregion
    }
}
