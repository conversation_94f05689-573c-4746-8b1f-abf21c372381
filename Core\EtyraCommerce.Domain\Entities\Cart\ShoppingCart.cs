using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Cart
{
    /// <summary>
    /// Shopping cart entity representing a customer's cart
    /// </summary>
    public class ShoppingCart : AuditableBaseEntity
    {
        #region Basic Information

        /// <summary>
        /// Customer ID who owns the cart (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Currency used for this cart
        /// </summary>
        public Currency Currency { get; set; } = null!;

        /// <summary>
        /// Cart expiration date
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Whether this cart is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        #endregion

        #region Cart Items

        /// <summary>
        /// Items in this cart
        /// </summary>
        public ICollection<CartItem> CartItems { get; set; } = new List<CartItem>();

        #endregion

        #region Calculated Properties

        /// <summary>
        /// Subtotal amount (sum of all item totals)
        /// </summary>
        public Money Subtotal { get; private set; } = null!;

        /// <summary>
        /// Total number of items in cart
        /// </summary>
        public int TotalItems => CartItems.Sum(x => x.Quantity);

        /// <summary>
        /// Total number of unique products in cart
        /// </summary>
        public int UniqueItems => CartItems.Count;

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public ShoppingCart()
        {
            Currency = Currency.USD; // Default currency
            Subtotal = Money.Zero(Currency);
            ExpiresAt = DateTime.UtcNow.AddDays(30); // Default 30 days expiration
        }

        /// <summary>
        /// Creates a new shopping cart for registered user
        /// </summary>
        public ShoppingCart(Guid customerId, Currency currency)
        {
            CustomerId = customerId;
            Currency = currency ?? throw new ArgumentNullException(nameof(currency));
            Subtotal = Money.Zero(currency);
            ExpiresAt = DateTime.UtcNow.AddDays(30); // 30 days for registered users
            IsActive = true;
        }

        /// <summary>
        /// Creates a new shopping cart for guest user
        /// </summary>
        public ShoppingCart(string sessionId, Currency currency)
        {
            SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
            Currency = currency ?? throw new ArgumentNullException(nameof(currency));
            Subtotal = Money.Zero(currency);
            ExpiresAt = DateTime.UtcNow.AddDays(7); // 7 days for guest users
            IsActive = true;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Adds an item to the cart
        /// </summary>
        public void AddItem(Guid productId, string productName, string productSku, Money unitPrice, int quantity)
        {
            if (!IsActive)
                throw new InvalidOperationException("Cannot add items to inactive cart");

            if (IsExpired())
                throw new InvalidOperationException("Cannot add items to expired cart");

            var existingItem = CartItems.FirstOrDefault(x => x.ProductId == productId);
            if (existingItem != null)
            {
                existingItem.UpdateQuantity(existingItem.Quantity + quantity);
            }
            else
            {
                var cartItem = new CartItem(productId, productName, productSku, unitPrice, quantity);
                // ShoppingCartId'yi set etme - EF navigation property ile halleder
                CartItems.Add(cartItem);
            }

            RecalculateSubtotal();
            ExtendExpiration();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates item quantity in the cart
        /// </summary>
        public void UpdateItemQuantity(Guid productId, int newQuantity)
        {
            if (!IsActive)
                throw new InvalidOperationException("Cannot update items in inactive cart");

            if (IsExpired())
                throw new InvalidOperationException("Cannot update items in expired cart");

            var item = CartItems.FirstOrDefault(x => x.ProductId == productId);
            if (item == null)
                throw new ArgumentException("Product not found in cart", nameof(productId));

            if (newQuantity <= 0)
            {
                RemoveItem(productId);
                return;
            }

            item.UpdateQuantity(newQuantity);
            RecalculateSubtotal();
            ExtendExpiration();
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes an item from the cart
        /// </summary>
        public void RemoveItem(Guid productId)
        {
            if (!IsActive)
                throw new InvalidOperationException("Cannot remove items from inactive cart");

            var item = CartItems.FirstOrDefault(x => x.ProductId == productId);
            if (item != null)
            {
                CartItems.Remove(item);
                RecalculateSubtotal();
                ExtendExpiration();
                MarkAsUpdated();
            }
        }

        /// <summary>
        /// Clears all items from the cart
        /// </summary>
        public void Clear()
        {
            CartItems.Clear();
            RecalculateSubtotal();
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the cart
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Activates the cart
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            ExtendExpiration();
            MarkAsUpdated();
        }

        /// <summary>
        /// Checks if cart is expired
        /// </summary>
        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpiresAt;
        }

        /// <summary>
        /// Extends cart expiration
        /// </summary>
        public void ExtendExpiration()
        {
            var extensionDays = CustomerId.HasValue ? 30 : 7; // 30 days for users, 7 for guests
            ExpiresAt = DateTime.UtcNow.AddDays(extensionDays);
        }

        /// <summary>
        /// Converts guest cart to user cart
        /// </summary>
        public void ConvertToUserCart(Guid customerId)
        {
            if (CustomerId.HasValue)
                throw new InvalidOperationException("Cart is already associated with a user");

            CustomerId = customerId;
            SessionId = null;
            ExtendExpiration(); // Extend to 30 days for registered user
            MarkAsUpdated();
        }

        /// <summary>
        /// Recalculates cart subtotal
        /// </summary>
        private void RecalculateSubtotal()
        {
            var totalAmount = CartItems.Sum(item => item.TotalPrice.Amount);
            Subtotal = new Money(totalAmount, Currency);
        }

        /// <summary>
        /// Validates the cart
        /// </summary>
        public bool IsValid()
        {
            return IsActive &&
                   !IsExpired() &&
                   (CustomerId.HasValue || !string.IsNullOrEmpty(SessionId)) &&
                   Currency != null;
        }

        /// <summary>
        /// Gets cart summary information
        /// </summary>
        public string GetSummary()
        {
            return $"Cart: {UniqueItems} unique items, {TotalItems} total items, {Subtotal.Amount:C} {Currency.Code}";
        }

        #endregion
    }
}
