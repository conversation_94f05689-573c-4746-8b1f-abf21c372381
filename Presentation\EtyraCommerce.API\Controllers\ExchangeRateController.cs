using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using EtyraCommerce.Application.Services.Currency.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers;

/// <summary>
/// Exchange rate management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ExchangeRateController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ExchangeRateController> _logger;

    public ExchangeRateController(
        IMediator mediator,
        ILogger<ExchangeRateController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all exchange rates
    /// </summary>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates (active and not expired)</param>
    /// <returns>List of exchange rates</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetAllExchangeRates(
        [FromQuery] bool tracking = false,
        [FromQuery] bool activeOnly = false,
        [FromQuery] bool validOnly = false)
    {
        try
        {
            _logger.LogInformation("Getting all exchange rates with filters - Tracking: {Tracking}, ActiveOnly: {ActiveOnly}, ValidOnly: {ValidOnly}",
                tracking, activeOnly, validOnly);

            var query = new GetAllExchangeRatesQuery
            {
                Tracking = tracking,
                ActiveOnly = activeOnly,
                ValidOnly = validOnly
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} exchange rates successfully", result.Data?.Count ?? 0);
                return Ok(result);
            }

            _logger.LogWarning("Failed to retrieve exchange rates: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all exchange rates");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get exchange rate by ID
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>Exchange rate details</returns>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetExchangeRateById(
        Guid id,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting exchange rate by ID: {ExchangeRateId}", id);

            var query = new GetExchangeRateByIdQuery
            {
                Id = id,
                Tracking = tracking
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved exchange rate {ExchangeRateId} successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Exchange rate {ExchangeRateId} not found: {Message}", id, result.Message);
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting exchange rate {ExchangeRateId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get current exchange rate for currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="asOfDate">Date for which to get the rate (optional)</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>Current exchange rate</returns>
    [HttpGet("current")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCurrentExchangeRate(
        [FromQuery] Guid fromCurrencyId,
        [FromQuery] Guid toCurrencyId,
        [FromQuery] DateTime? asOfDate = null,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting current exchange rate from {FromCurrencyId} to {ToCurrencyId} as of {AsOfDate}",
                fromCurrencyId, toCurrencyId, asOfDate);

            var query = new GetCurrentExchangeRateQuery
            {
                FromCurrencyId = fromCurrencyId,
                ToCurrencyId = toCurrencyId,
                AsOfDate = asOfDate,
                Tracking = tracking
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved current exchange rate successfully");
                return Ok(result);
            }

            _logger.LogWarning("Current exchange rate not found: {Message}", result.Message);
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting current exchange rate");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Get exchange rates for a specific currency
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <param name="asBase">Whether to get rates where this currency is the base currency</param>
    /// <param name="tracking">Whether to track changes for EF Core</param>
    /// <returns>List of exchange rates</returns>
    [HttpGet("by-currency/{currencyId:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetExchangeRatesByCurrency(
        Guid currencyId,
        [FromQuery] bool asBase = true,
        [FromQuery] bool tracking = false)
    {
        try
        {
            _logger.LogInformation("Getting exchange rates for currency {CurrencyId} as {Role}",
                currencyId, asBase ? "base" : "target");

            var query = new GetExchangeRatesByCurrencyQuery
            {
                CurrencyId = currencyId,
                AsBaseCurrency = asBase,
                Tracking = tracking
            };

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} exchange rates for currency {CurrencyId}",
                    result.Data?.Count ?? 0, currencyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to retrieve exchange rates for currency {CurrencyId}: {Message}",
                currencyId, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting exchange rates for currency {CurrencyId}", currencyId);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Create a new exchange rate
    /// </summary>
    /// <param name="command">Create exchange rate command</param>
    /// <returns>Created exchange rate</returns>
    [HttpPost]
    public async Task<IActionResult> CreateExchangeRate([FromBody] CreateExchangeRateCommand command)
    {
        try
        {
            _logger.LogInformation("Creating new exchange rate from {FromCurrencyId} to {ToCurrencyId} with rate {Rate}",
                command.FromCurrencyId, command.ToCurrencyId, command.Rate);

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Exchange rate created successfully with ID: {ExchangeRateId}",
                    result.Data?.Id);
                return CreatedAtAction(nameof(GetExchangeRateById), new { id = result.Data?.Id }, result);
            }

            _logger.LogWarning("Failed to create exchange rate: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating exchange rate");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Update an existing exchange rate
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="command">Update exchange rate command</param>
    /// <returns>Updated exchange rate</returns>
    [HttpPut("{id:guid}")]
    public async Task<IActionResult> UpdateExchangeRate(Guid id, [FromBody] UpdateExchangeRateCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                _logger.LogWarning("Exchange rate ID mismatch: URL ID {UrlId} vs Command ID {CommandId}", id, command.Id);
                return BadRequest("Exchange rate ID mismatch");
            }

            _logger.LogInformation("Updating exchange rate: {ExchangeRateId}", id);

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Exchange rate {ExchangeRateId} updated successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update exchange rate {ExchangeRateId}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating exchange rate {ExchangeRateId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Delete an exchange rate
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <returns>No content if successful</returns>
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteExchangeRate(Guid id)
    {
        try
        {
            _logger.LogInformation("Deleting exchange rate: {ExchangeRateId}", id);

            var command = new DeleteExchangeRateCommand { Id = id };
            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Exchange rate {ExchangeRateId} deleted successfully", id);
                return NoContent();
            }

            _logger.LogWarning("Failed to delete exchange rate {ExchangeRateId}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting exchange rate {ExchangeRateId}", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Toggle exchange rate status (active/inactive)
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated exchange rate</returns>
    [HttpPost("{id:guid}/toggle-status")]
    public async Task<IActionResult> ToggleExchangeRateStatus(Guid id, [FromQuery] bool isActive)
    {
        try
        {
            _logger.LogInformation("Toggling exchange rate {ExchangeRateId} status to {IsActive}", id, isActive);

            var command = new ToggleExchangeRateStatusCommand
            {
                Id = id,
                IsActive = isActive
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Exchange rate {ExchangeRateId} status toggled successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to toggle exchange rate {ExchangeRateId} status: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while toggling exchange rate {ExchangeRateId} status", id);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Convert currency amount
    /// </summary>
    /// <param name="amount">Amount to convert</param>
    /// <param name="fromCurrencyCode">Source currency code</param>
    /// <param name="toCurrencyCode">Target currency code</param>
    /// <param name="conversionDate">Date for conversion (optional)</param>
    /// <returns>Currency conversion result</returns>
    [HttpPost("convert")]
    [AllowAnonymous]
    public async Task<IActionResult> ConvertCurrency(
        [FromQuery] decimal amount,
        [FromQuery] string fromCurrencyCode,
        [FromQuery] string toCurrencyCode,
        [FromQuery] DateTime? conversionDate = null)
    {
        try
        {
            _logger.LogInformation("Converting {Amount} from {FromCurrency} to {ToCurrency} as of {ConversionDate}",
                amount, fromCurrencyCode, toCurrencyCode, conversionDate);

            var command = new ConvertCurrencyCommand
            {
                Amount = amount,
                FromCurrencyCode = fromCurrencyCode,
                ToCurrencyCode = toCurrencyCode,
                ConversionDate = conversionDate
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency conversion completed successfully");
                return Ok(result);
            }

            _logger.LogWarning("Currency conversion failed: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while converting currency");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Bulk update exchange rates from external source
    /// </summary>
    /// <param name="command">Bulk update exchange rates command</param>
    /// <returns>List of updated exchange rates</returns>
    [HttpPost("bulk-update")]
    public async Task<IActionResult> BulkUpdateExchangeRates([FromBody] BulkUpdateExchangeRatesCommand command)
    {
        try
        {
            _logger.LogInformation("Bulk updating {Count} exchange rates from {Source}",
                command.ExchangeRates?.Count ?? 0, command.Source);

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Bulk update completed successfully, updated {Count} exchange rates",
                    result.Data?.Count ?? 0);
                return Ok(result);
            }

            _logger.LogWarning("Bulk update failed: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while bulk updating exchange rates");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Deactivate expired exchange rates
    /// </summary>
    /// <param name="asOfDate">Date to check expiry against (optional)</param>
    /// <returns>Number of deactivated rates</returns>
    [HttpPost("deactivate-expired")]
    public async Task<IActionResult> DeactivateExpiredExchangeRates([FromQuery] DateTime? asOfDate = null)
    {
        try
        {
            _logger.LogInformation("Deactivating expired exchange rates as of {AsOfDate}", asOfDate);

            var command = new DeactivateExpiredExchangeRatesCommand
            {
                AsOfDate = asOfDate
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Deactivated {Count} expired exchange rates", result.Data);
                return Ok(result);
            }

            _logger.LogWarning("Failed to deactivate expired exchange rates: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deactivating expired exchange rates");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
}
