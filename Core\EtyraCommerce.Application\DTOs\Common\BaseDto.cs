namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Base DTO class that provides common properties for all DTOs
    /// </summary>
    public abstract class BaseDto
    {
        /// <summary>
        /// Unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Date and time when the entity was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Date and time when the entity was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Indicates if the entity is active (not soft deleted)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Version number for optimistic concurrency control
        /// </summary>
        public byte[]? RowVersion { get; set; }

        /// <summary>
        /// Gets the age of the entity (time since creation)
        /// </summary>
        public TimeSpan Age => DateTime.UtcNow - CreatedAt;

        /// <summary>
        /// Gets the time since last update
        /// </summary>
        public TimeSpan? TimeSinceUpdate => UpdatedAt.HasValue ? DateTime.UtcNow - UpdatedAt.Value : null;

        /// <summary>
        /// Checks if the entity was created today
        /// </summary>
        public bool IsCreatedToday => CreatedAt.Date == DateTime.UtcNow.Date;

        /// <summary>
        /// Checks if the entity was updated today
        /// </summary>
        public bool IsUpdatedToday => UpdatedAt?.Date == DateTime.UtcNow.Date;

        /// <summary>
        /// Checks if the entity was recently created (within last hour)
        /// </summary>
        public bool IsRecentlyCreated => Age.TotalHours <= 1;

        /// <summary>
        /// Checks if the entity was recently updated (within last hour)
        /// </summary>
        public bool IsRecentlyUpdated => TimeSinceUpdate?.TotalHours <= 1;

        /// <summary>
        /// Gets formatted creation date
        /// </summary>
        public string FormattedCreatedAt => CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// Gets formatted update date
        /// </summary>
        public string FormattedUpdatedAt => UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets relative time since creation (e.g., "2 hours ago")
        /// </summary>
        public string RelativeCreatedAt => GetRelativeTime(CreatedAt);

        /// <summary>
        /// Gets relative time since update (e.g., "30 minutes ago")
        /// </summary>
        public string RelativeUpdatedAt => UpdatedAt.HasValue ? GetRelativeTime(UpdatedAt.Value) : "Never";

        /// <summary>
        /// Converts a DateTime to relative time string
        /// </summary>
        private static string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;

            if (timeSpan.TotalDays >= 365)
                return $"{(int)(timeSpan.TotalDays / 365)} year{((int)(timeSpan.TotalDays / 365) == 1 ? "" : "s")} ago";

            if (timeSpan.TotalDays >= 30)
                return $"{(int)(timeSpan.TotalDays / 30)} month{((int)(timeSpan.TotalDays / 30) == 1 ? "" : "s")} ago";

            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays == 1 ? "" : "s")} ago";

            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours == 1 ? "" : "s")} ago";

            if (timeSpan.TotalMinutes >= 1)
                return $"{(int)timeSpan.TotalMinutes} minute{((int)timeSpan.TotalMinutes == 1 ? "" : "s")} ago";

            return "Just now";
        }

        /// <summary>
        /// Creates a copy of the DTO with updated timestamp
        /// </summary>
        public virtual T WithUpdatedTimestamp<T>() where T : BaseDto
        {
            var copy = (T)MemberwiseClone();
            copy.UpdatedAt = DateTime.UtcNow;
            return copy;
        }

        /// <summary>
        /// Validates the DTO
        /// </summary>
        public virtual ValidationResult Validate()
        {
            var errors = new Dictionary<string, List<string>>();

            if (Id == Guid.Empty)
                errors.Add(nameof(Id), new List<string> { "Id cannot be empty" });

            if (CreatedAt == default)
                errors.Add(nameof(CreatedAt), new List<string> { "CreatedAt cannot be default" });

            if (CreatedAt > DateTime.UtcNow)
                errors.Add(nameof(CreatedAt), new List<string> { "CreatedAt cannot be in the future" });

            if (UpdatedAt.HasValue && UpdatedAt.Value < CreatedAt)
                errors.Add(nameof(UpdatedAt), new List<string> { "UpdatedAt cannot be before CreatedAt" });

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }

        /// <summary>
        /// Gets a summary of the DTO for logging/debugging
        /// </summary>
        public virtual string GetSummary()
        {
            return $"{GetType().Name} [Id: {Id}, Created: {RelativeCreatedAt}, Active: {IsActive}]";
        }

        /// <summary>
        /// Equality comparison based on Id
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is not BaseDto other)
                return false;

            if (ReferenceEquals(this, other))
                return true;

            if (GetType() != other.GetType())
                return false;

            return Id == other.Id && Id != Guid.Empty;
        }

        /// <summary>
        /// Hash code based on Id
        /// </summary>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        /// <summary>
        /// String representation
        /// </summary>
        public override string ToString()
        {
            return GetSummary();
        }
    }
}
