﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Customer;

public class Address : BaseEntity
{
    public Customer Customer { get; set; }
    public int CustomerId { get; set; }
    public int? StoreId { get; set; } = 0;
    public int? BazaarId { get; set; } = 0;

    [MaxLength(500)]
    public string? AddressInfo { get; set; }
    [MaxLength(50)]
    public string? City { get; set; }
    [MaxLength(50)]
    public string? Zone { get; set; }
    [MaxLength(50)]
    public string? Country { get; set; }
    [MaxLength(10)]
    public string? PostalCode { get; set; }
    [MaxLength(20)]
    public string? Telephone { get; set; }

    public int CustomerType { get; set; } = 1;
    [MaxLength(100)]
    public string? Company { get; set; }
    [MaxLength(20)]
    public string? VatNumber { get; set; }


}