using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for resetting user password with token
    /// </summary>
    public class ResetPasswordCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Email address of the user
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Password reset token
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// New password
        /// </summary>
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// New password confirmation
        /// </summary>
        public string ConfirmNewPassword { get; set; } = string.Empty;

        /// <summary>
        /// IP address of the password reset request
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the browser
        /// </summary>
        public string? UserAgent { get; set; }

        public ResetPasswordCommand() { }

        public ResetPasswordCommand(ResetPasswordDto resetPasswordDto)
        {
            Email = resetPasswordDto.Email;
            Token = resetPasswordDto.Token;
            NewPassword = resetPasswordDto.NewPassword;
            ConfirmNewPassword = resetPasswordDto.ConfirmNewPassword;
        }
    }
}
