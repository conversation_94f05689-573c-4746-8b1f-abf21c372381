using EtyraCommerce.Domain.Entities.Inventory;

namespace EtyraCommerce.Application.DTOs.Inventory
{
    /// <summary>
    /// Inventory data transfer object
    /// </summary>
    public class InventoryDto
    {
        public Guid Id { get; set; }
        public Guid ProductId { get; set; }
        public Guid WarehouseId { get; set; }
        public int AvailableQuantity { get; set; }
        public int ReservedQuantity { get; set; }
        public int AllocatedQuantity { get; set; }
        public int MinStockLevel { get; set; }
        public int MaxStockLevel { get; set; }
        public int ReorderPoint { get; set; }
        public int ReorderQuantity { get; set; }
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public int? LeadTimeDays { get; set; }
        public DateTime? LastStockUpdate { get; set; }
        public DateTime? LastPhysicalCount { get; set; }
        public InventoryStatus Status { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Computed properties
        public int TotalQuantity { get; set; }
        public int FreeQuantity { get; set; }
        public bool IsLowStock { get; set; }
        public bool IsOutOfStock { get; set; }
        public bool IsOverstocked { get; set; }

        // Navigation properties
        public string? ProductName { get; set; }
        public string? ProductModel { get; set; }
        public string? WarehouseName { get; set; }
        public string? WarehouseCode { get; set; }
    }

    /// <summary>
    /// Create inventory DTO
    /// </summary>
    public class CreateInventoryDto
    {
        public Guid ProductId { get; set; }
        public Guid WarehouseId { get; set; }
        public int AvailableQuantity { get; set; } = 0;
        public int MinStockLevel { get; set; } = 0;
        public int MaxStockLevel { get; set; } = 0;
        public int ReorderPoint { get; set; } = 0;
        public int ReorderQuantity { get; set; } = 0;
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public int? LeadTimeDays { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Update inventory DTO
    /// </summary>
    public class UpdateInventoryDto
    {
        public Guid Id { get; set; }
        public int? AvailableQuantity { get; set; }
        public int? MinStockLevel { get; set; }
        public int? MaxStockLevel { get; set; }
        public int? ReorderPoint { get; set; }
        public int? ReorderQuantity { get; set; }
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public int? LeadTimeDays { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Stock movement DTO
    /// </summary>
    public class StockMovementDto
    {
        public Guid InventoryId { get; set; }
        public int Quantity { get; set; }
        public string? Reference { get; set; }
        public string? Reason { get; set; }
        public decimal? UnitCost { get; set; }
        public string? ExternalReference { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
    }

    /// <summary>
    /// Stock reservation DTO
    /// </summary>
    public class StockReservationDto
    {
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public int Quantity { get; set; }
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Stock allocation DTO
    /// </summary>
    public class StockAllocationDto
    {
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public int Quantity { get; set; }
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Release reservation request DTO
    /// </summary>
    public class ReleaseReservationRequest
    {
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
        public Guid? UserId { get; set; }
    }

    /// <summary>
    /// Stock transfer DTO
    /// </summary>
    public class StockTransferDto
    {
        public Guid ProductId { get; set; }
        public Guid FromWarehouseId { get; set; }
        public Guid ToWarehouseId { get; set; }
        public int Quantity { get; set; }
        public string? Reference { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Stock adjustment DTO
    /// </summary>
    public class StockAdjustmentDto
    {
        public Guid InventoryId { get; set; }
        public int NewQuantity { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Physical count DTO
    /// </summary>
    public class PhysicalCountDto
    {
        public Guid InventoryId { get; set; }
        public int CountedQuantity { get; set; }
        public string? Notes { get; set; }
        public Guid? CountedByUserId { get; set; }
    }

    /// <summary>
    /// Low stock item DTO
    /// </summary>
    public class LowStockItemDto
    {
        public Guid InventoryId { get; set; }
        public Guid ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductModel { get; set; } = string.Empty;
        public Guid WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public int AvailableQuantity { get; set; }
        public int ReorderPoint { get; set; }
        public int ReorderQuantity { get; set; }
        public InventoryStatus Status { get; set; }
        public int? LeadTimeDays { get; set; }
        public string? SupplierReference { get; set; }
    }

    /// <summary>
    /// Stock status DTO
    /// </summary>
    public class StockStatusDto
    {
        public Guid ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int TotalAvailable { get; set; }
        public int TotalReserved { get; set; }
        public int TotalAllocated { get; set; }
        public bool IsInStock { get; set; }
        public bool IsLowStock { get; set; }
        public List<WarehouseStockDto> WarehouseStocks { get; set; } = new List<WarehouseStockDto>();
    }

    /// <summary>
    /// Warehouse stock DTO
    /// </summary>
    public class WarehouseStockDto
    {
        public Guid WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public string WarehouseCode { get; set; } = string.Empty;
        public int AvailableQuantity { get; set; }
        public int ReservedQuantity { get; set; }
        public int AllocatedQuantity { get; set; }
        public InventoryStatus Status { get; set; }
        public string? LocationCode { get; set; }
    }

    /// <summary>
    /// Inventory filter DTO
    /// </summary>
    public class InventoryFilterDto
    {
        public Guid? ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public InventoryStatus? Status { get; set; }
        public bool? IsLowStock { get; set; }
        public bool? IsOutOfStock { get; set; }
        public string? LocationCode { get; set; }
        public string? SupplierReference { get; set; }
        public DateTime? LastStockUpdateFrom { get; set; }
        public DateTime? LastStockUpdateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "ProductName";
        public string? SortDirection { get; set; } = "asc";
    }
}
