using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for City entity
    /// </summary>
    public class CityConfiguration : IEntityTypeConfiguration<City>
    {
        public void Configure(EntityTypeBuilder<City> builder)
        {
            // Table configuration
            builder.ToTable("Cities", "etyra_shipping");

            // Primary key
            builder.HasKey(c => c.Id);

            // Properties
            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.PostalCode)
                .HasMaxLength(20);

            builder.Property(c => c.Latitude)
                .HasPrecision(10, 7);

            builder.Property(c => c.Longitude)
                .HasPrecision(10, 7);

            builder.Property(c => c.IsShippingEnabled)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(c => c.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Foreign key relationships
            builder.HasOne(c => c.Region)
                .WithMany(r => r.Cities)
                .HasForeignKey(c => c.RegionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Navigation properties
            builder.HasMany(c => c.Districts)
                .WithOne(d => d.City)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(c => c.RegionId)
                .HasDatabaseName("IX_Cities_RegionId");

            builder.HasIndex(c => new { c.Name, c.RegionId })
                .IsUnique()
                .HasDatabaseName("IX_Cities_Name_Region_Unique");

            builder.HasIndex(c => c.PostalCode)
                .HasDatabaseName("IX_Cities_PostalCode");

            builder.HasIndex(c => c.IsShippingEnabled)
                .HasDatabaseName("IX_Cities_IsShippingEnabled");

            builder.HasIndex(c => c.DisplayOrder)
                .HasDatabaseName("IX_Cities_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Cities_Latitude",
                "\"Latitude\" IS NULL OR (\"Latitude\" >= -90 AND \"Latitude\" <= 90)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Cities_Longitude",
                "\"Longitude\" IS NULL OR (\"Longitude\" >= -180 AND \"Longitude\" <= 180)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Cities_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Cities within regions for detailed shipping address management"));
        }
    }
}
