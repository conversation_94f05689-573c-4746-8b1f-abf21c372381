using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Analysis;

public class AiAgentResult : BaseEntity
{
    public int AiAgentId { get; set; }
    public AiAgent AiAgent { get; set; }
    
    [MaxLength(100)]
    public string Title { get; set; }
    
    [MaxLength(5000)]
    public string Content { get; set; }
    
    [MaxLength(50)]
    public string ResultType { get; set; } // Analysis, Recommendation, Alert, etc.
    
    public int? RelatedProductId { get; set; }
    
    public int? RelatedCompetitorId { get; set; }
    
    public int? RelatedCategoryId { get; set; }
    
    public bool IsRead { get; set; } = false;
    
    public bool IsFavorite { get; set; } = false;
    
    public int? Priority { get; set; } = 0; // 0: Normal, 1: Important, 2: Urgent
} 