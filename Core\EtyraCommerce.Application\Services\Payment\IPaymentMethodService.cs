using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment.Commands;
using EtyraCommerce.Application.Services.Payment.Queries;
using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.Services.Payment;

/// <summary>
/// Payment method service interface for CQRS operations
/// </summary>
public interface IPaymentMethodService
{
    // Command operations
    /// <summary>
    /// Create a new payment method
    /// </summary>
    /// <param name="command">Create payment method command</param>
    /// <returns>Created payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> CreatePaymentMethodAsync(CreatePaymentMethodCommand command);

    /// <summary>
    /// Update an existing payment method
    /// </summary>
    /// <param name="command">Update payment method command</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> UpdatePaymentMethodAsync(UpdatePaymentMethodCommand command);

    /// <summary>
    /// Delete a payment method
    /// </summary>
    /// <param name="command">Delete payment method command</param>
    /// <returns>Success result</returns>
    Task<CustomResponseDto<NoContentDto>> DeletePaymentMethodAsync(DeletePaymentMethodCommand command);

    /// <summary>
    /// Toggle payment method status
    /// </summary>
    /// <param name="command">Toggle payment method status command</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> TogglePaymentMethodStatusAsync(TogglePaymentMethodStatusCommand command);

    // Query operations
    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="query">Get payment method by ID query</param>
    /// <returns>Payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByIdAsync(GetPaymentMethodByIdQuery query);

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="query">Get payment method by code query</param>
    /// <returns>Payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByCodeAsync(GetPaymentMethodByCodeQuery query);

    /// <summary>
    /// Get all payment methods with optional filters
    /// </summary>
    /// <param name="query">Get all payment methods query</param>
    /// <returns>List of payment methods</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllPaymentMethodsAsync(GetAllPaymentMethodsQuery query);

    /// <summary>
    /// Get active payment methods for checkout
    /// </summary>
    /// <param name="query">Get active payment methods query</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsAsync(GetActivePaymentMethodsQuery query);

    // Convenience methods
    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByIdAsync(Guid id, bool tracking = false);

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByCodeAsync(string code, bool tracking = false);

    /// <summary>
    /// Get all payment methods
    /// </summary>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="type">Filter by payment method type</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllPaymentMethodsAsync(bool? onlyActive = null, PaymentMethodType? type = null, bool tracking = false);

    /// <summary>
    /// Get active payment methods for order amount
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsForOrderAsync(decimal orderAmount, string orderCurrency = "TRY");

    /// <summary>
    /// Create payment method from DTO
    /// </summary>
    /// <param name="createDto">Create payment method DTO</param>
    /// <returns>Created payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> CreatePaymentMethodAsync(CreatePaymentMethodDto createDto);

    /// <summary>
    /// Update payment method from DTO
    /// </summary>
    /// <param name="updateDto">Update payment method DTO</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> UpdatePaymentMethodAsync(UpdatePaymentMethodDto updateDto);

    /// <summary>
    /// Delete payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Success result</returns>
    Task<CustomResponseDto<NoContentDto>> DeletePaymentMethodAsync(Guid id);

    /// <summary>
    /// Toggle payment method status
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> TogglePaymentMethodStatusAsync(Guid id, bool isActive);
}
