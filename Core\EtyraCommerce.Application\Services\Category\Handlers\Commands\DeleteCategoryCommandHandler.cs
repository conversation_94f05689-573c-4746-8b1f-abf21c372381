using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Commands
{
    /// <summary>
    /// Handler for DeleteCategoryCommand
    /// </summary>
    public class DeleteCategoryCommandHandler : IRequestHandler<DeleteCategoryCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<DeleteCategoryCommandHandler> _logger;

        public DeleteCategoryCommandHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<DeleteCategoryCommandHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(DeleteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing delete category command for category ID: {CategoryId}", request.CategoryId);

                // Validation
                if (request.CategoryId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Category ID is required");

                // Delegate to process service
                var result = await _categoryProcessService.ProcessDeleteCategoryAsync(
                    request.CategoryId,
                    request.ForceDelete,
                    request.DeleteChildren);

                _logger.LogInformation("Delete category command processed successfully for category ID: {CategoryId}", request.CategoryId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing delete category command for category ID: {CategoryId}", request.CategoryId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the category");
            }
        }
    }
}
