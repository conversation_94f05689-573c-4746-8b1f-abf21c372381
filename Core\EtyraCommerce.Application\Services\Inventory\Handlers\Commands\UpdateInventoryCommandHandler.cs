using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for updating inventory items
    /// </summary>
    public class UpdateInventoryCommandHandler : IRequestHandler<UpdateInventoryCommand, CustomResponseDto<InventoryDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<UpdateInventoryCommandHandler> _logger;

        public UpdateInventoryCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<UpdateInventoryCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<InventoryDto>> Handle(UpdateInventoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling update inventory command for ID: {InventoryId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                    return CustomResponseDto<InventoryDto>.BadRequest("Inventory ID is required");

                if (request.AvailableQuantity.HasValue && request.AvailableQuantity.Value < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Available quantity cannot be negative");

                if (request.MinStockLevel.HasValue && request.MinStockLevel.Value < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Minimum stock level cannot be negative");

                if (request.MaxStockLevel.HasValue && request.MaxStockLevel.Value < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Maximum stock level cannot be negative");

                if (request.ReorderPoint.HasValue && request.ReorderPoint.Value < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Reorder point cannot be negative");

                if (request.ReorderQuantity.HasValue && request.ReorderQuantity.Value < 0)
                    return CustomResponseDto<InventoryDto>.BadRequest("Reorder quantity cannot be negative");

                if (request.MaxStockLevel.HasValue && request.MinStockLevel.HasValue &&
                    request.MaxStockLevel.Value > 0 && request.MinStockLevel.Value > request.MaxStockLevel.Value)
                    return CustomResponseDto<InventoryDto>.BadRequest("Minimum stock level cannot be greater than maximum");

                // Create DTO
                var updateDto = new UpdateInventoryDto
                {
                    Id = request.Id,
                    AvailableQuantity = request.AvailableQuantity,
                    MinStockLevel = request.MinStockLevel,
                    MaxStockLevel = request.MaxStockLevel,
                    ReorderPoint = request.ReorderPoint,
                    ReorderQuantity = request.ReorderQuantity,
                    LocationCode = request.LocationCode,
                    SupplierReference = request.SupplierReference,
                    LeadTimeDays = request.LeadTimeDays,
                    Notes = request.Notes
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessUpdateInventoryAsync(updateDto);

                _logger.LogInformation("Update inventory command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling update inventory command for ID: {InventoryId}", request.Id);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while updating inventory");
            }
        }
    }
}
