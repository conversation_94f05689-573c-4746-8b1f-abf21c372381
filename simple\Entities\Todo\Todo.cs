﻿using EtyraApp.Domain.Entities.Account;
using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Customer;
using EtyraApp.Domain.Entities.Orders;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities
{
    public class Todo : BaseEntity
    {
        [MaxLength(500)] public string? Title { get; set; }
        public string? Description { get; set; }
        public bool IsCompleted { get; set; } = false;
        public DateTime? DueDate { get; set; }
        public int CreatedByUserId { get; set; }
        public User? CreatedByUser { get; set; }
        public ICollection<TodoUser>? AssignedToUser { get; set; }
        [MaxLength(20)] public string? Priority { get; set; }
        [MaxLength(20)] public string? Category { get; set; }
        public int? RecurrenceType { get; set; }
        public int? RecurrenceInterval { get; set; }
        public DateTime? RecurrenceEndDate { get; set; }
        public bool IsPrivate { get; set; } = false;
        public int? Status { get; set; }
    //    public Order? Orders { get; set; }
        public int? OrderId { get; set; }
      //  public Customer.Customer? Customers { get; set; }
       // public int? CustomerId { get; set; }
        [MaxLength(50)] public string? Company { get; set; }
        [MaxLength(50)] public string? Customer { get; set; }

        [MaxLength(50)] public string? Telephone { get; set; }
        [MaxLength(50)] public string? Email { get; set; }
        //   public PrivateCustomer? PrivateCustomers { get; set; }
        // public int? PrivateCustomerId { get; set; }

        //   public Product? Products { get; set; }
        public int? ProductId { get; set; }
        [MaxLength(150)] public string? ProductName { get; set; }

        public ICollection<TodoAndTag>? TodoAndTags { get; set; }
        public ICollection<TodoNotes>? TodoNotes { get; set; }



    }
}
