using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingZoneLocation entity
    /// </summary>
    public class ShippingZoneLocationConfiguration : IEntityTypeConfiguration<ShippingZoneLocation>
    {
        public void Configure(EntityTypeBuilder<ShippingZoneLocation> builder)
        {
            // Table configuration
            builder.ToTable("ShippingZoneLocations", "etyra_shipping");

            // Primary key
            builder.HasKey(szl => szl.Id);

            // Properties
            builder.Property(szl => szl.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(szl => szl.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(szl => szl.OverrideMinDeliveryDays);

            builder.Property(szl => szl.OverrideMaxDeliveryDays);

            builder.Property(szl => szl.Notes)
                .HasMaxLength(500);

            // Enum conversions
            builder.Property(szl => szl.LocationType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Foreign key relationships
            builder.HasOne(szl => szl.ShippingZone)
                .WithMany(sz => sz.Locations)
                .HasForeignKey(szl => szl.ShippingZoneId)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint
            builder.HasIndex(szl => new { szl.ShippingZoneId, szl.CountryId, szl.RegionId, szl.CityId, szl.DistrictId, szl.LocationType })
                .IsUnique()
                .HasDatabaseName("IX_ShippingZoneLocations_Zone_Location_Type_Unique");

            // Other indexes
            builder.HasIndex(szl => szl.ShippingZoneId)
                .HasDatabaseName("IX_ShippingZoneLocations_ShippingZoneId");

            builder.HasIndex(szl => szl.CountryId)
                .HasDatabaseName("IX_ShippingZoneLocations_CountryId");

            builder.HasIndex(szl => szl.LocationType)
                .HasDatabaseName("IX_ShippingZoneLocations_LocationType");

            builder.HasIndex(szl => szl.IsActive)
                .HasDatabaseName("IX_ShippingZoneLocations_IsActive");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneLocations_CostModifierPercentage",
                "\"CostModifierPercentage\" IS NULL OR (\"CostModifierPercentage\" >= -100 AND \"CostModifierPercentage\" <= 1000)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneLocations_CostModifierFixed",
                "\"CostModifierFixed\" IS NULL OR (\"CostModifierFixed\" >= -1000000 AND \"CostModifierFixed\" <= 1000000)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneLocations_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Mapping between shipping zones and specific locations (regions, cities, districts) with cost modifiers"));
        }
    }
}
