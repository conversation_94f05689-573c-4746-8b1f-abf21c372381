using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingZoneLocation entity
    /// </summary>
    public class ShippingZoneLocationConfiguration : IEntityTypeConfiguration<ShippingZoneLocation>
    {
        public void Configure(EntityTypeBuilder<ShippingZoneLocation> builder)
        {
            // Table configuration
            builder.ToTable("ShippingZoneLocations", "etyra_shipping");

            // Primary key
            builder.HasKey(szl => szl.Id);

            // Properties
            builder.Property(szl => szl.LocationId)
                .IsRequired();

            builder.Property(szl => szl.CostModifierPercentage)
                .HasPrecision(5, 2)
                .HasDefaultValue(0);

            builder.Property(szl => szl.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            // Money value object configuration
            builder.OwnsOne(szl => szl.CostModifierAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("CostModifierAmount")
                    .HasPrecision(18, 2)
                    .HasDefaultValue(0);

                money.Property(m => m.Currency)
                    .HasColumnName("CostModifierCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => new Currency(code, code, code, 2))
                    .HasMaxLength(3)
                    .HasDefaultValue("EUR");
            });

            // Enum conversions
            builder.Property(szl => szl.LocationType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Foreign key relationships
            builder.HasOne(szl => szl.ShippingZone)
                .WithMany(sz => sz.Locations)
                .HasForeignKey(szl => szl.ShippingZoneId)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint
            builder.HasIndex(szl => new { szl.ShippingZoneId, szl.LocationId, szl.LocationType })
                .IsUnique()
                .HasDatabaseName("IX_ShippingZoneLocations_Zone_Location_Type_Unique");

            // Other indexes
            builder.HasIndex(szl => szl.ShippingZoneId)
                .HasDatabaseName("IX_ShippingZoneLocations_ShippingZoneId");

            builder.HasIndex(szl => szl.LocationId)
                .HasDatabaseName("IX_ShippingZoneLocations_LocationId");

            builder.HasIndex(szl => szl.LocationType)
                .HasDatabaseName("IX_ShippingZoneLocations_LocationType");

            builder.HasIndex(szl => szl.IsActive)
                .HasDatabaseName("IX_ShippingZoneLocations_IsActive");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneLocations_CostModifierPercentage",
                "\"CostModifierPercentage\" >= -100 AND \"CostModifierPercentage\" <= 1000"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingZoneLocations_CostModifierAmount",
                "\"CostModifierAmount\" >= -1000000 AND \"CostModifierAmount\" <= 1000000"));

            // Table comment
            builder.ToTable(t => t.HasComment("Mapping between shipping zones and specific locations (regions, cities, districts) with cost modifiers"));
        }
    }
}
