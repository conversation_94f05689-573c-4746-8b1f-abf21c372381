using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for PaymentMethod entity
    /// </summary>
    public class PaymentMethodConfiguration : IEntityTypeConfiguration<PaymentMethod>
    {
        public void Configure(EntityTypeBuilder<PaymentMethod> builder)
        {
            // Table configuration
            builder.ToTable("payment_methods", "etyra_core");

            // Primary key
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id)
                .HasColumnName("id")
                .IsRequired();

            // Base entity properties
            builder.Property(x => x.CreatedAt)
                .HasColumnName("created_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired();

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("updated_at")
                .HasColumnType("timestamp with time zone");

            builder.Property(x => x.IsDeleted)
                .HasColumnName("is_deleted")
                .HasDefaultValue(false)
                .IsRequired();

            builder.Property(x => x.DeletedAt)
                .HasColumnName("deleted_at")
                .HasColumnType("timestamp with time zone");

            builder.Property(x => x.RowVersion)
                .HasColumnName("row_version")
                .IsRowVersion();

            // Payment method properties
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(x => x.Code)
                .HasColumnName("code")
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasMaxLength(500);

            builder.Property(x => x.IsActive)
                .HasColumnName("is_active")
                .HasDefaultValue(true)
                .IsRequired();

            builder.Property(x => x.DisplayOrder)
                .HasColumnName("display_order")
                .HasDefaultValue(0)
                .IsRequired();

            builder.Property(x => x.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();

            builder.Property(x => x.FeeCalculationType)
                .HasColumnName("fee_calculation_type")
                .HasConversion<int>()
                .HasDefaultValue(FeeCalculationType.None)
                .IsRequired();

            builder.Property(x => x.FeeValue)
                .HasColumnName("fee_value")
                .HasColumnType("decimal(18,4)")
                .HasDefaultValue(0)
                .IsRequired();

            builder.Property(x => x.Instructions)
                .HasColumnName("instructions")
                .HasMaxLength(1000);

            // Fee Currency Value Object (Optional)
            builder.OwnsOne(x => x.FeeCurrency, currency =>
            {
                currency.Property(c => c.Code)
                    .HasColumnName("fee_currency_code")
                    .HasMaxLength(3);

                currency.Property(c => c.Name)
                    .HasColumnName("fee_currency_name")
                    .HasMaxLength(100);

                currency.Property(c => c.Symbol)
                    .HasColumnName("fee_currency_symbol")
                    .HasMaxLength(10);

                currency.Property(c => c.DecimalPlaces)
                    .HasColumnName("fee_currency_decimal_places")
                    .HasDefaultValue(2);
            });

            // Minimum Order Amount Value Object (Optional)
            builder.OwnsOne(x => x.MinimumOrderAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("minimum_order_amount")
                    .HasColumnType("decimal(18,4)");

                money.Property(m => m.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("minimum_order_currency")
                    .HasMaxLength(3);
            });

            // Maximum Order Amount Value Object (Optional)
            builder.OwnsOne(x => x.MaximumOrderAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("maximum_order_amount")
                    .HasColumnType("decimal(18,4)");

                money.Property(m => m.Currency)
                    .HasConversion(ValueObjectConversions.CurrencyConverter)
                    .HasColumnName("maximum_order_currency")
                    .HasMaxLength(3);
            });

            // Indexes
            builder.HasIndex(x => x.Code)
                .IsUnique()
                .HasDatabaseName("ix_payment_methods_code");

            builder.HasIndex(x => x.IsActive)
                .HasDatabaseName("ix_payment_methods_is_active");

            builder.HasIndex(x => x.DisplayOrder)
                .HasDatabaseName("ix_payment_methods_display_order");

            builder.HasIndex(x => x.Type)
                .HasDatabaseName("ix_payment_methods_type");

            // Global query filter for soft delete
            builder.HasQueryFilter(x => !x.IsDeleted);

            // Check constraints
            builder.HasCheckConstraint("ck_payment_methods_name_not_empty", 
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("ck_payment_methods_code_not_empty", 
                "LENGTH(TRIM(code)) > 0");

            builder.HasCheckConstraint("ck_payment_methods_fee_value_valid", 
                "fee_value >= -999999.9999 AND fee_value <= 999999.9999");

            builder.HasCheckConstraint("ck_payment_methods_display_order_valid", 
                "display_order >= 0");

            builder.HasCheckConstraint("ck_payment_methods_minimum_maximum_order", 
                "minimum_order_amount IS NULL OR maximum_order_amount IS NULL OR minimum_order_amount <= maximum_order_amount");

            // Table comment
            builder.HasComment("Payment methods available for orders");
        }
    }
}
