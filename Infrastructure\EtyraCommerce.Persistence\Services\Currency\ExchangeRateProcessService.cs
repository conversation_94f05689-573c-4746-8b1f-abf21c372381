using AutoMapper;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Repositories.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities.Currency;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Persistence.Services;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Currency;

/// <summary>
/// Exchange rate process service implementation for business logic operations
/// </summary>
public class ExchangeRateProcessService : Service<ExchangeRate, ExchangeRateDto>, IExchangeRateProcessService
{
    private readonly IExchangeRateReadRepository _exchangeRateReadRepository;
    private readonly IExchangeRateWriteRepository _exchangeRateWriteRepository;
    private readonly ICurrencyReadRepository _currencyReadRepository;

    public ExchangeRateProcessService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<ExchangeRateProcessService> logger,
        IExchangeRateReadRepository exchangeRateReadRepository,
        IExchangeRateWriteRepository exchangeRateWriteRepository,
        ICurrencyReadRepository currencyReadRepository)
        : base(unitOfWork, mapper, logger)
    {
        _exchangeRateReadRepository = exchangeRateReadRepository;
        _exchangeRateWriteRepository = exchangeRateWriteRepository;
        _currencyReadRepository = currencyReadRepository;
    }

    #region Command Operations

    /// <summary>
    /// Process create exchange rate request
    /// </summary>
    /// <param name="createDto">Create exchange rate DTO</param>
    /// <returns>Created exchange rate</returns>
    public async Task<CustomResponseDto<ExchangeRateDto>> ProcessCreateAsync(CreateExchangeRateDto createDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { createDto });

            // Validation
            if (createDto.FromCurrencyId == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("From currency ID is required");
            }

            if (createDto.ToCurrencyId == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("To currency ID is required");
            }

            if (createDto.FromCurrencyId == createDto.ToCurrencyId)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("From and To currencies cannot be the same");
            }

            if (createDto.Rate <= 0)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Exchange rate must be greater than zero");
            }

            // Validate currencies exist
            var fromCurrency = await _currencyReadRepository.GetByIdAsync(createDto.FromCurrencyId);
            if (fromCurrency == null)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("From currency not found");
            }

            var toCurrency = await _currencyReadRepository.GetByIdAsync(createDto.ToCurrencyId);
            if (toCurrency == null)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("To currency not found");
            }

            // Validate effective date
            if (createDto.EffectiveDate == default)
            {
                createDto.EffectiveDate = DateTime.UtcNow;
            }

            // Validate expiry date
            if (createDto.ExpiryDate.HasValue && createDto.ExpiryDate <= createDto.EffectiveDate)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Expiry date must be after effective date");
            }

            // Parse source enum
            if (!Enum.TryParse<ExchangeRateSource>(createDto.Source, true, out var sourceEnum))
            {
                sourceEnum = ExchangeRateSource.Manual;
            }

            // Create exchange rate entity
            var exchangeRate = new ExchangeRate
            {
                FromCurrencyId = createDto.FromCurrencyId,
                ToCurrencyId = createDto.ToCurrencyId,
                Rate = createDto.Rate,
                EffectiveDate = createDto.EffectiveDate,
                ExpiryDate = createDto.ExpiryDate,
                Source = sourceEnum,
                ExternalReferenceId = createDto.ExternalReferenceId?.Trim(),
                Notes = createDto.Notes?.Trim(),
                IsActive = true
            };

            // Save to database
            var createdExchangeRate = await _exchangeRateWriteRepository.AddAsync(exchangeRate);
            await _unitOfWork.CommitAsync();

            // Load with navigation properties for DTO mapping
            var exchangeRateWithNavigation = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(createdExchangeRate.Id);
            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRateWithNavigation);

            _logger.LogInformation("Exchange rate {ExchangeRateId} created successfully for {FromCurrency}/{ToCurrency} with rate {Rate}", 
                createdExchangeRate.Id, fromCurrency.Code, toCurrency.Code, createDto.Rate);

            return CustomResponseDto<ExchangeRateDto>.Created(exchangeRateDto, "Exchange rate created successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Process update exchange rate request
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="updateDto">Update exchange rate DTO</param>
    /// <returns>Updated exchange rate</returns>
    public async Task<CustomResponseDto<ExchangeRateDto>> ProcessUpdateAsync(Guid id, UpdateExchangeRateDto updateDto)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, updateDto });

            // Validation
            if (id == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Exchange rate ID is required");
            }

            if (updateDto.Rate <= 0)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Exchange rate must be greater than zero");
            }

            // Get existing exchange rate
            var existingExchangeRate = await _exchangeRateReadRepository.GetByIdAsync(id, tracking: true);
            if (existingExchangeRate == null)
            {
                return CustomResponseDto<ExchangeRateDto>.NotFound("Exchange rate not found");
            }

            // Validate effective date
            if (updateDto.EffectiveDate == default)
            {
                updateDto.EffectiveDate = DateTime.UtcNow;
            }

            // Validate expiry date
            if (updateDto.ExpiryDate.HasValue && updateDto.ExpiryDate <= updateDto.EffectiveDate)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Expiry date must be after effective date");
            }

            // Parse source enum
            if (!Enum.TryParse<ExchangeRateSource>(updateDto.Source, true, out var sourceEnum))
            {
                sourceEnum = ExchangeRateSource.Manual;
            }

            // Update exchange rate properties
            existingExchangeRate.Rate = updateDto.Rate;
            existingExchangeRate.EffectiveDate = updateDto.EffectiveDate;
            existingExchangeRate.ExpiryDate = updateDto.ExpiryDate;
            existingExchangeRate.IsActive = updateDto.IsActive;
            existingExchangeRate.Source = sourceEnum;
            existingExchangeRate.ExternalReferenceId = updateDto.ExternalReferenceId?.Trim();
            existingExchangeRate.Notes = updateDto.Notes?.Trim();

            // Save changes
            var updatedExchangeRate = await _exchangeRateWriteRepository.UpdateAsync(existingExchangeRate);
            await _unitOfWork.CommitAsync();

            // Load with navigation properties for DTO mapping
            var exchangeRateWithNavigation = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(updatedExchangeRate.Id);
            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRateWithNavigation);

            _logger.LogInformation("Exchange rate {ExchangeRateId} updated successfully with rate {Rate}", 
                id, updateDto.Rate);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto, "Exchange rate updated successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Process delete exchange rate request
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<NoContentDto>.BadRequest("Exchange rate ID is required");
            }

            // Get existing exchange rate
            var existingExchangeRate = await _exchangeRateReadRepository.GetByIdAsync(id, tracking: true);
            if (existingExchangeRate == null)
            {
                return CustomResponseDto<NoContentDto>.NotFound("Exchange rate not found");
            }

            // Check if exchange rate can be deleted
            var canDelete = await CanDeleteExchangeRateAsync(id);
            if (!canDelete)
            {
                return CustomResponseDto<NoContentDto>.BadRequest("Exchange rate cannot be deleted because it may be referenced by other entities");
            }

            // Delete exchange rate
            var deleted = await _exchangeRateWriteRepository.RemoveByIdAsync(id);
            if (!deleted)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError("Failed to delete exchange rate");
            }

            await _unitOfWork.CommitAsync();

            _logger.LogInformation("Exchange rate {ExchangeRateId} deleted successfully", id);

            return CustomResponseDto<NoContentDto>.Success(200, "Exchange rate deleted successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<NoContentDto>(ex);
        }
    }

    /// <summary>
    /// Process create or update exchange rate request (upsert operation)
    /// </summary>
    public async Task<CustomResponseDto<ExchangeRateDto>> ProcessCreateOrUpdateAsync(
        Guid fromCurrencyId, 
        Guid toCurrencyId, 
        decimal rate, 
        DateTime effectiveDate, 
        DateTime? expiryDate = null, 
        ExchangeRateSource source = ExchangeRateSource.Manual,
        string? externalReferenceId = null,
        string? notes = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyId, toCurrencyId, rate, effectiveDate, expiryDate, source, externalReferenceId, notes });

            // Use repository method for create or update
            var exchangeRate = await _exchangeRateWriteRepository.CreateOrUpdateRateAsync(
                fromCurrencyId, toCurrencyId, rate, effectiveDate, expiryDate, source);

            // Update additional properties if provided
            if (!string.IsNullOrWhiteSpace(externalReferenceId))
            {
                exchangeRate.ExternalReferenceId = externalReferenceId.Trim();
            }

            if (!string.IsNullOrWhiteSpace(notes))
            {
                exchangeRate.Notes = notes.Trim();
            }

            await _unitOfWork.CommitAsync();

            // Load with navigation properties for DTO mapping
            var exchangeRateWithNavigation = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(exchangeRate.Id);
            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRateWithNavigation);

            _logger.LogInformation("Exchange rate {ExchangeRateId} created or updated successfully for currencies {FromCurrencyId}/{ToCurrencyId} with rate {Rate}", 
                exchangeRate.Id, fromCurrencyId, toCurrencyId, rate);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto, "Exchange rate created or updated successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Process bulk update exchange rates from external source
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> ProcessBulkUpdateAsync(
        List<CreateExchangeRateDto> exchangeRates,
        ExchangeRateSource source = ExchangeRateSource.API,
        bool deactivateOthers = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { exchangeRates.Count, source, deactivateOthers });

            if (!exchangeRates.Any())
            {
                return CustomResponseDto<List<ExchangeRateDto>>.BadRequest("No exchange rates provided for bulk update");
            }

            var updatedRates = new List<ExchangeRate>();

            foreach (var rateDto in exchangeRates)
            {
                // Validate each rate
                if (rateDto.FromCurrencyId == Guid.Empty || rateDto.ToCurrencyId == Guid.Empty)
                {
                    continue; // Skip invalid rates
                }

                if (rateDto.Rate <= 0)
                {
                    continue; // Skip invalid rates
                }

                // Create or update rate
                var exchangeRate = await _exchangeRateWriteRepository.CreateOrUpdateRateAsync(
                    rateDto.FromCurrencyId,
                    rateDto.ToCurrencyId,
                    rateDto.Rate,
                    rateDto.EffectiveDate == default ? DateTime.UtcNow : rateDto.EffectiveDate,
                    rateDto.ExpiryDate,
                    source);

                // Update additional properties
                if (!string.IsNullOrWhiteSpace(rateDto.ExternalReferenceId))
                {
                    exchangeRate.ExternalReferenceId = rateDto.ExternalReferenceId.Trim();
                }

                if (!string.IsNullOrWhiteSpace(rateDto.Notes))
                {
                    exchangeRate.Notes = rateDto.Notes.Trim();
                }

                updatedRates.Add(exchangeRate);
            }

            // Deactivate other rates if requested
            if (deactivateOthers)
            {
                var allRates = await _exchangeRateReadRepository.GetBySourceAsync(source, tracking: true);
                var ratesToDeactivate = allRates.Where(r => !updatedRates.Any(ur => ur.Id == r.Id)).ToList();

                foreach (var rate in ratesToDeactivate)
                {
                    rate.IsActive = false;
                }

                if (ratesToDeactivate.Any())
                {
                    await _exchangeRateWriteRepository.BulkUpdateAsync(ratesToDeactivate);
                }
            }

            await _unitOfWork.CommitAsync();

            // Load with navigation properties for DTO mapping
            var exchangeRateDtos = new List<ExchangeRateDto>();
            foreach (var rate in updatedRates)
            {
                var rateWithNavigation = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(rate.Id);
                var dto = _mapper.Map<ExchangeRateDto>(rateWithNavigation);
                exchangeRateDtos.Add(dto);
            }

            _logger.LogInformation("Bulk updated {Count} exchange rates from source {Source}",
                updatedRates.Count, source);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos, $"Successfully updated {updatedRates.Count} exchange rates");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Process toggle exchange rate status request
    /// </summary>
    public async Task<CustomResponseDto<ExchangeRateDto>> ProcessToggleStatusAsync(Guid id, bool isActive)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, isActive });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Exchange rate ID is required");
            }

            // Update status
            var updatedExchangeRate = await _exchangeRateWriteRepository.UpdateStatusAsync(id, isActive);
            if (updatedExchangeRate == null)
            {
                return CustomResponseDto<ExchangeRateDto>.NotFound("Exchange rate not found");
            }

            await _unitOfWork.CommitAsync();

            // Load with navigation properties for DTO mapping
            var exchangeRateWithNavigation = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(updatedExchangeRate.Id);
            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRateWithNavigation);

            _logger.LogInformation("Exchange rate {ExchangeRateId} status updated to {IsActive}",
                id, isActive);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto, $"Exchange rate {(isActive ? "activated" : "deactivated")} successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Process deactivate expired exchange rates request
    /// </summary>
    public async Task<CustomResponseDto<int>> ProcessDeactivateExpiredAsync(DateTime? asOfDate = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { asOfDate });

            var deactivatedCount = await _exchangeRateWriteRepository.DeactivateExpiredRatesAsync(asOfDate);

            if (deactivatedCount > 0)
            {
                await _unitOfWork.CommitAsync();
            }

            _logger.LogInformation("Deactivated {Count} expired exchange rates", deactivatedCount);

            return CustomResponseDto<int>.Success(deactivatedCount, $"Successfully deactivated {deactivatedCount} expired exchange rates");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<int>(ex);
        }
    }

    #endregion

    #region Query Operations

    /// <summary>
    /// Get all exchange rates
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetAllAsync(bool tracking = false, bool activeOnly = false, bool validOnly = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { tracking, activeOnly, validOnly });

            var exchangeRates = await _exchangeRateReadRepository.GetAllWithCurrenciesAsync(tracking, activeOnly, validOnly);
            var exchangeRateDtos = _mapper.Map<List<ExchangeRateDto>>(exchangeRates);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Get exchange rate by ID
    /// </summary>
    public async Task<CustomResponseDto<ExchangeRateDto>> GetByIdAsync(Guid id, bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id, tracking });

            if (id == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Exchange rate ID is required");
            }

            var exchangeRate = await _exchangeRateReadRepository.GetByIdWithCurrenciesAsync(id, tracking);
            if (exchangeRate == null)
            {
                return CustomResponseDto<ExchangeRateDto>.NotFound("Exchange rate not found");
            }

            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRate);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Get exchange rates by currency pair
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetByCurrencyPairAsync(
        Guid fromCurrencyId,
        Guid toCurrencyId,
        bool tracking = false,
        bool activeOnly = true,
        bool validOnly = true)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyId, toCurrencyId, tracking, activeOnly, validOnly });

            if (fromCurrencyId == Guid.Empty || toCurrencyId == Guid.Empty)
            {
                return CustomResponseDto<List<ExchangeRateDto>>.BadRequest("Currency IDs are required");
            }

            var exchangeRates = await _exchangeRateReadRepository.GetExchangeRateAsync(fromCurrencyId, toCurrencyId, tracking: tracking);
            var exchangeRatesList = exchangeRates != null ? new List<ExchangeRate> { exchangeRates } : new List<ExchangeRate>();
            var exchangeRateDtos = _mapper.Map<List<ExchangeRateDto>>(exchangeRatesList);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Get current exchange rate for currency pair
    /// </summary>
    public async Task<CustomResponseDto<ExchangeRateDto>> GetCurrentRateAsync(
        Guid fromCurrencyId,
        Guid toCurrencyId,
        DateTime? asOfDate = null,
        bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyId, toCurrencyId, asOfDate, tracking });

            if (fromCurrencyId == Guid.Empty || toCurrencyId == Guid.Empty)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Currency IDs are required");
            }

            var exchangeRate = await _exchangeRateReadRepository.GetCurrentRateAsync(fromCurrencyId, toCurrencyId, asOfDate, tracking);
            if (exchangeRate == null)
            {
                return CustomResponseDto<ExchangeRateDto>.NotFound("Exchange rate not found for the specified currency pair");
            }

            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRate);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Get current exchange rate by currency codes
    /// </summary>
    public async Task<CustomResponseDto<ExchangeRateDto>> GetCurrentRateByCurrencyCodesAsync(
        string fromCurrencyCode,
        string toCurrencyCode,
        DateTime? asOfDate = null,
        bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyCode, toCurrencyCode, asOfDate, tracking });

            if (string.IsNullOrWhiteSpace(fromCurrencyCode) || string.IsNullOrWhiteSpace(toCurrencyCode))
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest("Currency codes are required");
            }

            // Get currencies by codes
            var fromCurrency = await _currencyReadRepository.GetByCodeAsync(fromCurrencyCode.Trim().ToUpper());
            if (fromCurrency == null)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest($"From currency '{fromCurrencyCode}' not found");
            }

            var toCurrency = await _currencyReadRepository.GetByCodeAsync(toCurrencyCode.Trim().ToUpper());
            if (toCurrency == null)
            {
                return CustomResponseDto<ExchangeRateDto>.BadRequest($"To currency '{toCurrencyCode}' not found");
            }

            // Get exchange rate
            var exchangeRate = await _exchangeRateReadRepository.GetCurrentRateAsync(fromCurrency.Id, toCurrency.Id, asOfDate, tracking);
            if (exchangeRate == null)
            {
                return CustomResponseDto<ExchangeRateDto>.NotFound($"Exchange rate not found for {fromCurrencyCode}/{toCurrencyCode}");
            }

            var exchangeRateDto = _mapper.Map<ExchangeRateDto>(exchangeRate);

            return CustomResponseDto<ExchangeRateDto>.Success(exchangeRateDto);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<ExchangeRateDto>(ex);
        }
    }

    /// <summary>
    /// Get exchange rates by source
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetBySourceAsync(
        ExchangeRateSource source,
        bool tracking = false,
        bool activeOnly = true,
        bool validOnly = true)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { source, tracking, activeOnly, validOnly });

            var exchangeRates = await _exchangeRateReadRepository.GetBySourceAsync(source, activeOnly, tracking);
            var exchangeRateDtos = _mapper.Map<List<ExchangeRateDto>>(exchangeRates);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Convert currency amount
    /// </summary>
    public async Task<CustomResponseDto<CurrencyConversionResultDto>> ConvertCurrencyAsync(
        decimal amount,
        string fromCurrencyCode,
        string toCurrencyCode,
        DateTime? conversionDate = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { amount, fromCurrencyCode, toCurrencyCode, conversionDate });

            if (amount < 0)
            {
                return CustomResponseDto<CurrencyConversionResultDto>.BadRequest("Amount cannot be negative");
            }

            if (string.IsNullOrWhiteSpace(fromCurrencyCode) || string.IsNullOrWhiteSpace(toCurrencyCode))
            {
                return CustomResponseDto<CurrencyConversionResultDto>.BadRequest("Currency codes are required");
            }

            // If same currency, return original amount
            if (fromCurrencyCode.Trim().Equals(toCurrencyCode.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                var sameCurrencyResult = new CurrencyConversionResultDto
                {
                    OriginalAmount = amount,
                    ConvertedAmount = amount,
                    FromCurrencyCode = fromCurrencyCode.Trim().ToUpper(),
                    ToCurrencyCode = toCurrencyCode.Trim().ToUpper(),
                    ExchangeRate = 1.0m,
                    ConversionDate = conversionDate ?? DateTime.UtcNow,
                    IsSuccessful = true
                };

                return CustomResponseDto<CurrencyConversionResultDto>.Success(sameCurrencyResult, "Currency conversion completed (same currency)");
            }

            // Get exchange rate
            var exchangeRateResponse = await GetCurrentRateByCurrencyCodesAsync(fromCurrencyCode, toCurrencyCode, conversionDate);
            if (!exchangeRateResponse.IsSuccessful || exchangeRateResponse.Data == null)
            {
                var failedResult = new CurrencyConversionResultDto
                {
                    OriginalAmount = amount,
                    ConvertedAmount = 0,
                    FromCurrencyCode = fromCurrencyCode.Trim().ToUpper(),
                    ToCurrencyCode = toCurrencyCode.Trim().ToUpper(),
                    ExchangeRate = 0,
                    ConversionDate = conversionDate ?? DateTime.UtcNow,
                    IsSuccessful = false,
                    ErrorMessage = exchangeRateResponse.Message
                };

                return CustomResponseDto<CurrencyConversionResultDto>.BadRequest("Currency conversion failed");
            }

            // Perform conversion
            var rate = exchangeRateResponse.Data.Rate;
            var convertedAmount = amount * rate;

            var conversionResult = new CurrencyConversionResultDto
            {
                OriginalAmount = amount,
                ConvertedAmount = convertedAmount,
                FromCurrencyCode = fromCurrencyCode.Trim().ToUpper(),
                ToCurrencyCode = toCurrencyCode.Trim().ToUpper(),
                ExchangeRate = rate,
                ConversionDate = conversionDate ?? DateTime.UtcNow,
                IsSuccessful = true
            };

            _logger.LogInformation("Currency conversion completed: {Amount} {FromCurrency} = {ConvertedAmount} {ToCurrency} (Rate: {Rate})",
                amount, fromCurrencyCode, convertedAmount, toCurrencyCode, rate);

            return CustomResponseDto<CurrencyConversionResultDto>.Success(conversionResult, "Currency conversion completed successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<CurrencyConversionResultDto>(ex);
        }
    }

    /// <summary>
    /// Get exchange rates by date range
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        Guid? fromCurrencyId = null,
        Guid? toCurrencyId = null,
        bool tracking = false,
        bool activeOnly = true)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { startDate, endDate, fromCurrencyId, toCurrencyId, tracking, activeOnly });

            if (startDate > endDate)
            {
                return CustomResponseDto<List<ExchangeRateDto>>.BadRequest("Start date cannot be after end date");
            }

            // For now, use a simple implementation - in a real scenario you'd add this method to repository
            var allRates = await _exchangeRateReadRepository.GetAllWithCurrenciesAsync(tracking, activeOnly);

            var filteredRates = allRates.Where(er =>
                er.EffectiveDate >= startDate &&
                er.EffectiveDate <= endDate &&
                (fromCurrencyId == null || er.FromCurrencyId == fromCurrencyId) &&
                (toCurrencyId == null || er.ToCurrencyId == toCurrencyId))
                .ToList();

            var exchangeRateDtos = _mapper.Map<List<ExchangeRateDto>>(filteredRates);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Get expired exchange rates
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetExpiredRatesAsync(
        DateTime? asOfDate = null,
        bool tracking = false,
        bool activeOnly = true)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { asOfDate, tracking, activeOnly });

            var expiredRates = await _exchangeRateReadRepository.GetExpiredRatesAsync(asOfDate, tracking);
            var exchangeRateDtos = _mapper.Map<List<ExchangeRateDto>>(expiredRates);

            return CustomResponseDto<List<ExchangeRateDto>>.Success(exchangeRateDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Get exchange rate history for currency pair
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateDto>>> GetHistoryAsync(
        Guid fromCurrencyId,
        Guid toCurrencyId,
        int? daysBack = 30,
        int? maxRecords = 100,
        bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyId, toCurrencyId, daysBack, maxRecords, tracking });

            if (fromCurrencyId == Guid.Empty || toCurrencyId == Guid.Empty)
            {
                return CustomResponseDto<List<ExchangeRateDto>>.BadRequest("Currency IDs are required");
            }

            var startDate = DateTime.UtcNow.AddDays(-(daysBack ?? 30));
            var endDate = DateTime.UtcNow;

            var historyResponse = await GetByDateRangeAsync(startDate, endDate, fromCurrencyId, toCurrencyId, tracking);

            if (historyResponse.IsSuccessful && historyResponse.Data != null)
            {
                var limitedHistory = historyResponse.Data
                    .OrderByDescending(er => er.EffectiveDate)
                    .Take(maxRecords ?? 100)
                    .ToList();

                return CustomResponseDto<List<ExchangeRateDto>>.Success(limitedHistory);
            }

            return historyResponse;
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateDto>>(ex);
        }
    }

    /// <summary>
    /// Get exchange rate summary (for dashboard/overview)
    /// </summary>
    public async Task<CustomResponseDto<List<ExchangeRateSummaryDto>>> GetSummaryAsync(
        bool activeOnly = true,
        bool validOnly = true,
        bool tracking = false)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { activeOnly, validOnly, tracking });

            var exchangeRates = await _exchangeRateReadRepository.GetAllWithCurrenciesAsync(tracking, activeOnly, validOnly);
            var exchangeRateSummaryDtos = _mapper.Map<List<ExchangeRateSummaryDto>>(exchangeRates);

            return CustomResponseDto<List<ExchangeRateSummaryDto>>.Success(exchangeRateSummaryDtos);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse<List<ExchangeRateSummaryDto>>(ex);
        }
    }

    #endregion

    #region Validation Operations

    /// <summary>
    /// Check if exchange rate exists for currency pair
    /// </summary>
    public async Task<bool> ExchangeRateExistsAsync(Guid fromCurrencyId, Guid toCurrencyId, DateTime? asOfDate = null)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { fromCurrencyId, toCurrencyId, asOfDate });

            if (fromCurrencyId == Guid.Empty || toCurrencyId == Guid.Empty)
            {
                return false;
            }

            var exchangeRate = await _exchangeRateReadRepository.GetCurrentRateAsync(fromCurrencyId, toCurrencyId, asOfDate);
            return exchangeRate != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if exchange rate exists for currencies {FromCurrencyId}/{ToCurrencyId}",
                fromCurrencyId, toCurrencyId);
            return false;
        }
    }

    /// <summary>
    /// Check if exchange rate can be deleted
    /// </summary>
    public async Task<bool> CanDeleteExchangeRateAsync(Guid id)
    {
        try
        {
            LogMethodEntry(parameters: new object[] { id });

            if (id == Guid.Empty)
            {
                return false;
            }

            // For now, allow deletion of any exchange rate
            // In the future, you might want to check if it's referenced by orders, etc.
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if exchange rate can be deleted: {ExchangeRateId}", id);
            return false;
        }
    }

    #endregion
}
