using EtyraCommerce.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Base entity configuration for common properties
    /// </summary>
    public class BaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : BaseEntity
    {
        public virtual void Configure(EntityTypeBuilder<T> builder)
        {
            // Primary Key
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id)
                .HasColumnName("id")
                .IsRequired()
                .ValueGeneratedOnAdd();

            // Audit Fields
            builder.Property(x => x.CreatedAt)
                .HasColumnName("created_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired()
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("updated_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Soft Delete Fields
            builder.Property(x => x.IsDeleted)
                .HasColumnName("is_deleted")
                .HasDefaultValue(false)
                .IsRequired();

            builder.Property(x => x.DeletedAt)
                .HasColumnName("deleted_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false);

            // Concurrency Control
            builder.Property(x => x.RowVersion)
                .HasColumnName("row_version")
                .IsRowVersion()
                .IsConcurrencyToken();

            // Indexes for Performance
            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName($"ix_{GetTableName()}_created_at");

            builder.HasIndex(x => x.IsDeleted)
                .HasDatabaseName($"ix_{GetTableName()}_is_deleted");

            builder.HasIndex(x => new { x.IsDeleted, x.CreatedAt })
                .HasDatabaseName($"ix_{GetTableName()}_is_deleted_created_at");

            // Global Query Filter (Soft Delete)
            // This is already handled in DbContext.ApplyGlobalQueryFilters()
            // but we can add additional filters here if needed
        }

        /// <summary>
        /// Gets the table name for index naming
        /// Override this in derived configurations
        /// </summary>
        protected virtual string GetTableName()
        {
            return typeof(T).Name.ToLowerInvariant();
        }
    }
}
