using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Currency
{
    /// <summary>
    /// Currency entity for dynamic currency management
    /// </summary>
    public class Currency : BaseEntity
    {
        #region Properties

        /// <summary>
        /// ISO currency code (e.g., USD, EUR, TRY)
        /// </summary>
        public string Code { get; set; } = null!;

        /// <summary>
        /// Currency name (e.g., US Dollar, Euro, Turkish Lira)
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Currency symbol (e.g., $, €, ₺)
        /// </summary>
        public string Symbol { get; set; } = null!;

        /// <summary>
        /// Number of decimal places for this currency
        /// </summary>
        public int DecimalPlaces { get; set; } = 2;

        /// <summary>
        /// Whether this currency is active and can be used
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether this is the default currency for the system
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Display order for currency selection
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Currency format pattern for display
        /// </summary>
        public string? FormatPattern { get; set; }

        /// <summary>
        /// Additional notes about this currency
        /// </summary>
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Exchange rates where this currency is the base currency
        /// </summary>
        public ICollection<ExchangeRate> FromExchangeRates { get; set; } = new List<ExchangeRate>();

        /// <summary>
        /// Exchange rates where this currency is the target currency
        /// </summary>
        public ICollection<ExchangeRate> ToExchangeRates { get; set; } = new List<ExchangeRate>();

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public Currency()
        {
        }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        public Currency(string code, string name, string symbol, int decimalPlaces = 2)
        {
            Code = code?.ToUpperInvariant() ?? throw new ArgumentNullException(nameof(code));
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
            DecimalPlaces = decimalPlaces;
            IsActive = true;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Formats amount with currency symbol and proper decimal places
        /// </summary>
        public string FormatAmount(decimal amount)
        {
            var rounded = Math.Round(amount, DecimalPlaces);

            // Use custom format pattern if available
            if (!string.IsNullOrEmpty(FormatPattern))
            {
                return string.Format(FormatPattern, rounded, Symbol, Code);
            }

            // Default formatting based on currency code
            return Code switch
            {
                "USD" => $"${rounded:F2}",
                "EUR" => $"€{rounded:F2}",
                "TRY" => $"{rounded:F2} ₺",
                "GBP" => $"£{rounded:F2}",
                "JPY" => $"¥{rounded:F0}",
                "CAD" => $"C${rounded:F2}",
                "AUD" => $"A${rounded:F2}",
                "CHF" => $"CHF {rounded:F2}",
                "CNY" => $"¥{rounded:F2}",
                "SEK" => $"{rounded:F2} kr",
                "NOK" => $"{rounded:F2} kr",
                "DKK" => $"{rounded:F2} kr",
                _ => $"{rounded.ToString($"F{DecimalPlaces}")} {Symbol}"
            };
        }

        /// <summary>
        /// Converts this entity to Currency Value Object
        /// </summary>
        public ValueObjects.Currency ToValueObject()
        {
            return ValueObjects.Currency.Create(Code, Name, Symbol, DecimalPlaces);
        }

        /// <summary>
        /// Activates the currency
        /// </summary>
        public void Activate()
        {
            IsActive = true;
        }

        /// <summary>
        /// Deactivates the currency
        /// </summary>
        public void Deactivate()
        {
            if (IsDefault)
                throw new InvalidOperationException("Cannot deactivate the default currency");
            
            IsActive = false;
        }

        /// <summary>
        /// Sets this currency as default
        /// </summary>
        public void SetAsDefault()
        {
            if (!IsActive)
                throw new InvalidOperationException("Cannot set inactive currency as default");
            
            IsDefault = true;
        }

        /// <summary>
        /// Removes default status from this currency
        /// </summary>
        public void RemoveDefaultStatus()
        {
            IsDefault = false;
        }

        /// <summary>
        /// Updates currency information
        /// </summary>
        public void UpdateInfo(string name, string symbol, int decimalPlaces, string? formatPattern = null, string? notes = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
            DecimalPlaces = decimalPlaces;
            FormatPattern = formatPattern;
            Notes = notes;
        }

        /// <summary>
        /// Sets display order
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates currency data
        /// </summary>
        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Code))
                errors.Add("Currency code is required");
            else if (Code.Length != 3)
                errors.Add("Currency code must be exactly 3 characters");

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("Currency name is required");

            if (string.IsNullOrWhiteSpace(Symbol))
                errors.Add("Currency symbol is required");

            if (DecimalPlaces < 0 || DecimalPlaces > 4)
                errors.Add("Decimal places must be between 0 and 4");

            return errors.Count == 0;
        }

        #endregion
    }
}
