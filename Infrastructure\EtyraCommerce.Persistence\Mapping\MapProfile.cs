﻿using AutoMapper;
using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.Entities.Inventory;
using EtyraCommerce.Domain.Entities.Order;
using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Persistence.Mapping;

public class MapProfile : Profile
{
    public MapProfile()
    {
        CreateUserMappings();
        CreateProductMappings();
        CreateCategoryMappings();
        CreateOrderMappings();
        CreateInventoryMappings();
        CreateCartMappings();
    }

    private void CreateUserMappings()
    {
        CreateUserAddressMappings();
        // User Entity to UserDto
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email.Value))
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => src.PhoneNumber != null ? src.PhoneNumber.Value : null))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => !src.IsDeleted)); // BaseEntity.IsActive

        // CreateUserDto to User Entity
        CreateMap<CreateUserDto, User>()
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => new Email(src.Email)))
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PhoneNumber) ? new PhoneNumber(src.PhoneNumber) : null))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordHash, opt => opt.Ignore()) // Will be set by service
            .ForMember(dest => dest.PasswordSalt, opt => opt.Ignore()) // Will be set by service
            .ForMember(dest => dest.IsEmailConfirmed, opt => opt.MapFrom(src => src.SkipEmailConfirmation))
            .ForMember(dest => dest.IsPhoneConfirmed, opt => opt.Ignore())
            .ForMember(dest => dest.LastLoginAt, opt => opt.Ignore())
            .ForMember(dest => dest.FailedLoginAttempts, opt => opt.Ignore())
            .ForMember(dest => dest.LockedUntil, opt => opt.Ignore())
            .ForMember(dest => dest.EmailConfirmationToken, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordResetToken, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordResetTokenExpiry, opt => opt.Ignore())
            .ForMember(dest => dest.ProfilePictureUrl, opt => opt.Ignore());

        // UpdateUserDto to User Entity (for updating existing user)
        CreateMap<UpdateUserDto, User>()
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PhoneNumber) ? new PhoneNumber(src.PhoneNumber) : null))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Email, opt => opt.Ignore()) // Email cannot be updated via UpdateUserDto
            .ForMember(dest => dest.Username, opt => opt.Ignore()) // Username cannot be updated via UpdateUserDto
            .ForMember(dest => dest.PasswordHash, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordSalt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.IsEmailConfirmed, opt => opt.Ignore())
            .ForMember(dest => dest.IsPhoneConfirmed, opt => opt.Ignore()) // Will be reset if phone changes
            .ForMember(dest => dest.LastLoginAt, opt => opt.Ignore())
            .ForMember(dest => dest.FailedLoginAttempts, opt => opt.Ignore())
            .ForMember(dest => dest.LockedUntil, opt => opt.Ignore())
            .ForMember(dest => dest.EmailConfirmationToken, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordResetToken, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordResetTokenExpiry, opt => opt.Ignore())
            .ForMember(dest => dest.ProfilePictureUrl, opt => opt.Ignore());
    }

    private void CreateProductMappings()
    {
        // Product Entity to ProductDto
        CreateMap<Domain.Entities.Product.Product, Application.DTOs.Product.ProductDto>()
            .ForMember(dest => dest.BasePrice, opt => opt.MapFrom(src => src.BasePrice.Amount))
            .ForMember(dest => dest.BasePriceCurrency, opt => opt.MapFrom(src => src.BasePrice.Currency.Code))
            .ForMember(dest => dest.Cost, opt => opt.MapFrom(src => src.Cost != null ? src.Cost.Amount : (decimal?)null))
            .ForMember(dest => dest.CostCurrency, opt => opt.MapFrom(src => src.Cost != null ? src.Cost.Currency.Code : null))
            .ForMember(dest => dest.SalePrice, opt => opt.MapFrom(src => src.SalePrice != null ? src.SalePrice.Amount : (decimal?)null))
            .ForMember(dest => dest.SalePriceCurrency, opt => opt.MapFrom(src => src.SalePrice != null ? src.SalePrice.Currency.Code : null))
            .ForMember(dest => dest.DefaultCurrency, opt => opt.MapFrom(src => src.DefaultCurrency.Code))
            .ForMember(dest => dest.EffectivePrice, opt => opt.MapFrom(src => src.EffectivePrice.Amount))
            .ForMember(dest => dest.EffectivePriceCurrency, opt => opt.MapFrom(src => src.EffectivePrice.Currency.Code))
            .ForMember(dest => dest.IsOnSale, opt => opt.MapFrom(src => src.IsOnSale))
            .ForMember(dest => dest.DiscountPercentage, opt => opt.MapFrom(src => src.DiscountPercentage))
            .ForMember(dest => dest.ProfitMargin, opt => opt.MapFrom(src => src.ProfitMargin))
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.DisplayName))
            .ForMember(dest => dest.FormattedPrice, opt => opt.MapFrom(src => $"{src.EffectivePrice.Amount:C} {src.EffectivePrice.Currency.Code}"))
            .ForMember(dest => dest.FormattedSalePrice, opt => opt.MapFrom(src => src.SalePrice != null ? $"{src.SalePrice.Amount:C} {src.SalePrice.Currency.Code}" : null))
            .ForMember(dest => dest.Categories, opt => opt.MapFrom(src => src.ProductCategories.Select(pc => pc.Category)))
            .ForMember(dest => dest.PrimaryCategory, opt => opt.MapFrom(src => src.ProductCategories.FirstOrDefault(pc => pc.IsPrimary) != null ? src.ProductCategories.FirstOrDefault(pc => pc.IsPrimary)!.Category : null));

        // ProductDimensions to ProductDimensionsDto
        CreateMap<ProductDimensions, Application.DTOs.Product.ProductDimensionsDto>()
            .ForMember(dest => dest.Volume, opt => opt.MapFrom(src => src.Volume))
            .ForMember(dest => dest.VolumetricWeight, opt => opt.MapFrom(src => src.VolumetricWeight))
            .ForMember(dest => dest.FormattedDimensions, opt => opt.MapFrom(src => src.Format()));
    }

    private void CreateCategoryMappings()
    {
        // Category Entity to CategoryDto
        CreateMap<Domain.Entities.Category.Category, Application.DTOs.Category.CategoryDto>()
            .ForMember(dest => dest.Level, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.FullPath, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.Breadcrumbs, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.ParentCategory, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.ChildCategories, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.Descriptions, opt => opt.Ignore()) // Will be set manually
            .ForMember(dest => dest.ProductCount, opt => opt.Ignore()); // Will be calculated

        // CreateCategoryDto to Category Entity
        CreateMap<Application.DTOs.Category.CreateCategoryDto, Domain.Entities.Category.Category>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.ParentCategory, opt => opt.Ignore())
            .ForMember(dest => dest.ChildCategories, opt => opt.Ignore())
            .ForMember(dest => dest.ProductCategories, opt => opt.Ignore())
            .ForMember(dest => dest.Descriptions, opt => opt.Ignore())
            .ForMember(dest => dest.ProductCount, opt => opt.Ignore());

        // UpdateCategoryDto to Category Entity
        CreateMap<Application.DTOs.Category.UpdateCategoryDto, Domain.Entities.Category.Category>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.ParentCategory, opt => opt.Ignore())
            .ForMember(dest => dest.ChildCategories, opt => opt.Ignore())
            .ForMember(dest => dest.ProductCategories, opt => opt.Ignore())
            .ForMember(dest => dest.Descriptions, opt => opt.Ignore())
            .ForMember(dest => dest.ProductCount, opt => opt.Ignore());

        // CategoryDescription Entity to CategoryDescriptionDto
        CreateMap<Domain.Entities.Category.CategoryDescription, Application.DTOs.Category.CategoryDescriptionDto>();

        // CreateCategoryDescriptionDto to CategoryDescription Entity
        CreateMap<Application.DTOs.Category.CreateCategoryDescriptionDto, Domain.Entities.Category.CategoryDescription>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.Category, opt => opt.Ignore())
            .ForMember(dest => dest.CategoryId, opt => opt.Ignore());

        // UpdateCategoryDescriptionDto to CategoryDescription Entity
        CreateMap<Application.DTOs.Category.UpdateCategoryDescriptionDto, Domain.Entities.Category.CategoryDescription>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.LanguageCode, opt => opt.Ignore()) // Language cannot be changed
            .ForMember(dest => dest.StoreId, opt => opt.Ignore()) // Store cannot be changed
            .ForMember(dest => dest.Category, opt => opt.Ignore())
            .ForMember(dest => dest.CategoryId, opt => opt.Ignore());
    }

    private void CreateOrderMappings()
    {
        // Order Entity to OrderDto
        CreateMap<Order, OrderDto>()
            .ForMember(dest => dest.CustomerEmail, opt => opt.MapFrom(src => src.CustomerEmail.Value))
            .ForMember(dest => dest.CustomerPhone, opt => opt.MapFrom(src => src.CustomerPhone != null ? src.CustomerPhone.Value : null))
            .ForMember(dest => dest.Subtotal, opt => opt.MapFrom(src => src.Subtotal.Amount))
            .ForMember(dest => dest.TaxAmount, opt => opt.MapFrom(src => src.TaxAmount.Amount))
            .ForMember(dest => dest.ShippingCost, opt => opt.MapFrom(src => src.ShippingCost.Amount))
            .ForMember(dest => dest.DiscountAmount, opt => opt.MapFrom(src => src.DiscountAmount.Amount))
            .ForMember(dest => dest.Total, opt => opt.MapFrom(src => src.Total.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency.Code));

        // OrderItem Entity to OrderItemDto
        CreateMap<OrderItem, OrderItemDto>()
            .ForMember(dest => dest.UnitPrice, opt => opt.MapFrom(src => src.UnitPrice.Amount))
            .ForMember(dest => dest.TotalPrice, opt => opt.MapFrom(src => src.TotalPrice.Amount))
            .ForMember(dest => dest.DiscountAmount, opt => opt.MapFrom(src => src.DiscountAmount != null ? src.DiscountAmount.Amount : (decimal?)null))
            .ForMember(dest => dest.TaxAmount, opt => opt.MapFrom(src => src.TaxAmount != null ? src.TaxAmount.Amount : (decimal?)null));

        // Address Value Object to AddressDto
        CreateMap<Address, AddressDto>();

        // CreateOrderDto to Order Entity
        CreateMap<CreateOrderDto, Order>()
            .ForMember(dest => dest.CustomerEmail, opt => opt.MapFrom(src => new Email(src.CustomerEmail)))
            .ForMember(dest => dest.CustomerPhone, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.CustomerPhone) ? new PhoneNumber(src.CustomerPhone) : null))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => Currency.FromCode(src.Currency)))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.OrderNumber, opt => opt.Ignore()) // Generated by entity
            .ForMember(dest => dest.Status, opt => opt.Ignore()) // Set by entity
            .ForMember(dest => dest.PaymentStatus, opt => opt.Ignore()) // Set by entity
            .ForMember(dest => dest.ShippingStatus, opt => opt.Ignore()) // Set by entity
            .ForMember(dest => dest.Subtotal, opt => opt.Ignore()) // Calculated by entity
            .ForMember(dest => dest.TaxAmount, opt => opt.Ignore()) // Set by business logic
            .ForMember(dest => dest.ShippingCost, opt => opt.Ignore()) // Set by business logic
            .ForMember(dest => dest.DiscountAmount, opt => opt.Ignore()) // Set by business logic
            .ForMember(dest => dest.Total, opt => opt.Ignore()) // Calculated by entity
            .ForMember(dest => dest.OrderItems, opt => opt.Ignore()) // Handled separately
            .ForMember(dest => dest.Customer, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.InternalNotes, opt => opt.Ignore())
            .ForMember(dest => dest.TrackingNumber, opt => opt.Ignore())
            .ForMember(dest => dest.ExpectedDeliveryDate, opt => opt.Ignore())
            .ForMember(dest => dest.ActualDeliveryDate, opt => opt.Ignore());

        // CreateAddressDto to Address Value Object
        CreateMap<CreateAddressDto, Address>()
            .ConstructUsing(src => new Address(src.Street, src.City, src.State, src.PostalCode, src.Country, src.AddressLine2));

        // UpdateAddressDto to Address Value Object
        CreateMap<UpdateAddressDto, Address>()
            .ConstructUsing(src => new Address(src.Street, src.City, src.State, src.PostalCode, src.Country, src.AddressLine2));

        // CreateOrderItemDto to OrderItem Entity
        CreateMap<CreateOrderItemDto, OrderItem>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.ProductName, opt => opt.Ignore()) // Set from Product entity
            .ForMember(dest => dest.ProductSku, opt => opt.Ignore()) // Set from Product entity
            .ForMember(dest => dest.UnitPrice, opt => opt.Ignore()) // Set from Product entity
            .ForMember(dest => dest.TotalPrice, opt => opt.Ignore()) // Calculated by entity
            .ForMember(dest => dest.DiscountAmount, opt => opt.Ignore()) // Set by business logic
            .ForMember(dest => dest.TaxAmount, opt => opt.Ignore()) // Set by business logic
            .ForMember(dest => dest.OrderId, opt => opt.Ignore())
            .ForMember(dest => dest.Order, opt => opt.Ignore())
            .ForMember(dest => dest.Product, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore());
    }

    private void CreateInventoryMappings()
    {
        // Inventory Entity to InventoryDto
        CreateMap<Inventory, InventoryDto>()
            .ForMember(dest => dest.TotalQuantity, opt => opt.MapFrom(src => src.TotalQuantity))
            .ForMember(dest => dest.FreeQuantity, opt => opt.MapFrom(src => src.FreeQuantity))
            .ForMember(dest => dest.IsLowStock, opt => opt.MapFrom(src => src.IsLowStock))
            .ForMember(dest => dest.IsOutOfStock, opt => opt.MapFrom(src => src.IsOutOfStock))
            .ForMember(dest => dest.IsOverstocked, opt => opt.MapFrom(src => src.IsOverstocked))
            .ForMember(dest => dest.ProductName, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.ProductModel, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.WarehouseName, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.WarehouseCode, opt => opt.Ignore()); // Set manually

        // CreateInventoryDto to Inventory Entity
        CreateMap<CreateInventoryDto, Inventory>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Product, opt => opt.Ignore())
            .ForMember(dest => dest.Warehouse, opt => opt.Ignore())
            .ForMember(dest => dest.Transactions, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore()) // Calculated automatically
            .ForMember(dest => dest.LastStockUpdate, opt => opt.Ignore()) // Set automatically
            .ForMember(dest => dest.LastPhysicalCount, opt => opt.Ignore());

        // Warehouse Entity to WarehouseDto
        CreateMap<Warehouse, WarehouseDto>()
            .ForMember(dest => dest.TotalProducts, opt => opt.Ignore()) // Set manually if needed
            .ForMember(dest => dest.LowStockItems, opt => opt.Ignore()) // Set manually if needed
            .ForMember(dest => dest.OutOfStockItems, opt => opt.Ignore()); // Set manually if needed

        // CreateWarehouseDto to Warehouse Entity
        CreateMap<CreateWarehouseDto, Warehouse>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.InventoryItems, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore());

        // InventoryTransaction Entity to InventoryTransactionDto
        CreateMap<InventoryTransaction, InventoryTransactionDto>()
            .ForMember(dest => dest.ProductName, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.ProductModel, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.WarehouseName, opt => opt.Ignore()) // Set manually
            .ForMember(dest => dest.UserName, opt => opt.Ignore()); // Set manually

        // CreateInventoryTransactionDto to InventoryTransaction Entity
        CreateMap<CreateInventoryTransactionDto, InventoryTransaction>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Inventory, opt => opt.Ignore())
            .ForMember(dest => dest.User, opt => opt.Ignore())
            .ForMember(dest => dest.TransactionDate, opt => opt.Ignore()) // Set automatically
            .ForMember(dest => dest.TotalCost, opt => opt.Ignore()) // Calculated if UnitCost provided
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore());
    }

    private void CreateUserAddressMappings()
    {
        // UserAddress Entity to UserAddressDto
        CreateMap<UserAddress, UserAddressDto>()
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => src.PhoneNumber != null ? src.PhoneNumber.Value : null))
            .ForMember(dest => dest.Street, opt => opt.MapFrom(src => src.Address.Street))
            .ForMember(dest => dest.AddressLine2, opt => opt.MapFrom(src => src.Address.AddressLine2))
            .ForMember(dest => dest.City, opt => opt.MapFrom(src => src.Address.City))
            .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.Address.State))
            .ForMember(dest => dest.PostalCode, opt => opt.MapFrom(src => src.Address.PostalCode))
            .ForMember(dest => dest.Country, opt => opt.MapFrom(src => src.Address.Country))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => (int)src.Type));

        // CreateUserAddressDto to UserAddress Entity
        CreateMap<CreateUserAddressDto, UserAddress>()
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PhoneNumber) ? new PhoneNumber(src.PhoneNumber) : null))
            .ForMember(dest => dest.Address, opt => opt.MapFrom(src => new Address(src.Street, src.City, src.State, src.PostalCode, src.Country, src.AddressLine2)))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => (AddressType)src.Type))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.UserId, opt => opt.Ignore()) // Set by service
            .ForMember(dest => dest.User, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore());

        // UpdateUserAddressDto to UserAddress Entity (for updating existing address)
        CreateMap<UpdateUserAddressDto, UserAddress>()
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PhoneNumber) ? new PhoneNumber(src.PhoneNumber) : null))
            .ForMember(dest => dest.Address, opt => opt.MapFrom(src => new Address(src.Street, src.City, src.State, src.PostalCode, src.Country, src.AddressLine2)))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => (AddressType)src.Type))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.UserId, opt => opt.Ignore()) // Cannot change user
            .ForMember(dest => dest.User, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore());
    }

    private void CreateCartMappings()
    {
        // ShoppingCart Entity to CartDto
        CreateMap<ShoppingCart, CartDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency.Code))
            .ForMember(dest => dest.Subtotal, opt => opt.MapFrom(src => src.Subtotal.Amount))
            .ForMember(dest => dest.TotalItems, opt => opt.MapFrom(src => src.CartItems.Sum(x => x.Quantity)))
            .ForMember(dest => dest.UniqueItems, opt => opt.MapFrom(src => src.CartItems.Count));

        // CartItem Entity to CartItemDto
        CreateMap<CartItem, CartItemDto>()
            .ForMember(dest => dest.UnitPrice, opt => opt.MapFrom(src => src.UnitPrice.Amount))
            .ForMember(dest => dest.TotalPrice, opt => opt.MapFrom(src => src.TotalPrice.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.UnitPrice.Currency.Code));

        // ShoppingCart Entity to CartSummaryDto
        CreateMap<ShoppingCart, CartSummaryDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency.Code))
            .ForMember(dest => dest.Subtotal, opt => opt.MapFrom(src => src.Subtotal.Amount))
            .ForMember(dest => dest.TotalItems, opt => opt.MapFrom(src => src.CartItems.Sum(x => x.Quantity)))
            .ForMember(dest => dest.UniqueItems, opt => opt.MapFrom(src => src.CartItems.Count))
            .ForMember(dest => dest.IsExpired, opt => opt.MapFrom(src => DateTime.UtcNow > src.ExpiresAt))
            .ForMember(dest => dest.CartType, opt => opt.MapFrom(src => src.CustomerId.HasValue ? "User" : "Guest"));

        // AddToCartDto to CartItem Entity (for creating new cart items)
        CreateMap<AddToCartDto, CartItem>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.ProductName, opt => opt.Ignore()) // Will be set from Product entity
            .ForMember(dest => dest.ProductSku, opt => opt.Ignore()) // Will be set from Product entity
            .ForMember(dest => dest.UnitPrice, opt => opt.Ignore()) // Will be set from Product entity
            .ForMember(dest => dest.TotalPrice, opt => opt.Ignore()) // Will be calculated
            .ForMember(dest => dest.ProductImageUrl, opt => opt.Ignore()) // Will be set from Product entity
            .ForMember(dest => dest.ShoppingCart, opt => opt.Ignore())
            .ForMember(dest => dest.ShoppingCartId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore());
    }
}