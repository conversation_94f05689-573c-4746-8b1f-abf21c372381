﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Competitor;

public class CompetitorProduct : BaseEntity
{
    public int CompetitorId { get; set; }
    public Competitor Competitor { get; set; } // ✅ Eksik ilişki eklendi

    [MaxLength(250)]
    public string ProductName { get; set; }

    public int ProductCompetitorId { get; set; }

    [MaxLength(250)]
    public string ProductLink { get; set; }

    public ICollection<CompetitorProductPriceAndQuantityChange>? CompetitorProductPriceAndQuantityChanges { get; set; }
    public ICollection<IntegrationCompetitorProduct>? IntegrationCompetitorProducts { get; set; }

    public string? MatchGroupId { get; set; }
    public int Status { get; set; }
}
