﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddUserAddress : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "user_addresses",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    user_id = table.Column<Guid>(type: "uuid", nullable: false, comment: "Reference to the user who owns this address"),
                    address_type = table.Column<int>(type: "integer", nullable: false, comment: "Address type: 1=Billing, 2=Shipping, 3=Both"),
                    is_default = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false, comment: "Indicates if this is the default address for the user"),
                    address_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    address_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    address_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_line2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    first_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "First name of the person at this address"),
                    last_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "Last name of the person at this address"),
                    phone_number = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    label = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "User-friendly label for this address (e.g., Home, Office)"),
                    delivery_instructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "Additional delivery instructions for this address"),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true, comment: "Indicates if this address is active and can be used"),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_addresses", x => x.id);
                    table.CheckConstraint("ck_user_addresses_address_type", "address_type IN (1, 2, 3)");
                    table.CheckConstraint("ck_user_addresses_first_name_not_empty", "LENGTH(TRIM(first_name)) > 0");
                    table.CheckConstraint("ck_user_addresses_label_not_empty", "label IS NULL OR LENGTH(TRIM(label)) > 0");
                    table.CheckConstraint("ck_user_addresses_last_name_not_empty", "LENGTH(TRIM(last_name)) > 0");
                    table.ForeignKey(
                        name: "fk_user_addresses_user_id",
                        column: x => x.user_id,
                        principalSchema: "etyra_core",
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "User addresses for billing and shipping");

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_created_at",
                schema: "etyra_core",
                table: "user_addresses",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_is_deleted",
                schema: "etyra_core",
                table: "user_addresses",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_is_deleted_created_at",
                schema: "etyra_core",
                table: "user_addresses",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_user_id",
                schema: "etyra_core",
                table: "user_addresses",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_user_id_active_type",
                schema: "etyra_core",
                table: "user_addresses",
                columns: new[] { "user_id", "is_active", "address_type" });

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_user_id_is_active",
                schema: "etyra_core",
                table: "user_addresses",
                columns: new[] { "user_id", "is_active" });

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_user_id_is_default",
                schema: "etyra_core",
                table: "user_addresses",
                columns: new[] { "user_id", "is_default" });

            migrationBuilder.CreateIndex(
                name: "ix_user_addresses_user_id_type",
                schema: "etyra_core",
                table: "user_addresses",
                columns: new[] { "user_id", "address_type" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "user_addresses",
                schema: "etyra_core");
        }
    }
}
