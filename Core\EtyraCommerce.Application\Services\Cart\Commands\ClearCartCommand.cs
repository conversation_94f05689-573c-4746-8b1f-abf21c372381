using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Commands
{
    /// <summary>
    /// Command to clear all items from shopping cart
    /// </summary>
    public class ClearCartCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Creates command for user cart
        /// </summary>
        public static ClearCartCommand ForUser(Guid customerId)
        {
            return new ClearCartCommand
            {
                CustomerId = customerId
            };
        }

        /// <summary>
        /// Creates command for guest cart
        /// </summary>
        public static ClearCartCommand ForGuest(string sessionId)
        {
            return new ClearCartCommand
            {
                SessionId = sessionId
            };
        }
    }
}
