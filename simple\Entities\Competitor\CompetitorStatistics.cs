﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Competitor;

public class CompetitorSalesStatistics : BaseEntity
{
    public decimal? TotalSalesAmount { get; set; }
    public DateTime? StartDateTime { get; set; }
    public DateTime? EndDateTime { get; set; }
    public int TotalSalesProductQuantity { get; set; }
    public Competitor Competitor { get; set; }
    public int CompetitorId { get; set; }
    [MaxLength(4)]
    public string? Currency { get; set; }

    public int Status { get; set; }
}

public class CompetitorStockStatistics : BaseEntity
{
    public int? Quantity { get; set; }
    public int Type { get; set; } // 1=> in Stock  2=> Out of Stock 
    public DateTime? CheckDateTime { get; set; }
    public Competitor Competitor { get; set; }
    public int CompetitorId { get; set; }
}

