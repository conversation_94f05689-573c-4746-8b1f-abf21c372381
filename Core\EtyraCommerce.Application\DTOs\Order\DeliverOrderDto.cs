using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// DTO for marking an order as delivered
    /// </summary>
    public class DeliverOrderDto
    {
        /// <summary>
        /// Actual delivery date
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// Delivery confirmation method
        /// </summary>
        [MaxLength(100)]
        public string? ConfirmationMethod { get; set; }

        /// <summary>
        /// Name of the person who received the delivery
        /// </summary>
        [MaxLength(100)]
        public string? ReceivedBy { get; set; }

        /// <summary>
        /// Delivery notes
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Delivery photo URL (if applicable)
        /// </summary>
        [MaxLength(500)]
        public string? DeliveryPhotoUrl { get; set; }

        /// <summary>
        /// Signature URL (if applicable)
        /// </summary>
        [MaxLength(500)]
        public string? SignatureUrl { get; set; }
    }
}
