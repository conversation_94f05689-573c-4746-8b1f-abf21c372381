using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for generating password reset token
    /// </summary>
    public class GeneratePasswordResetTokenCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Email address of the user requesting password reset
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// IP address of the request
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the browser
        /// </summary>
        public string? UserAgent { get; set; }

        public GeneratePasswordResetTokenCommand() { }

        public GeneratePasswordResetTokenCommand(string email)
        {
            Email = email;
        }
    }
}
