using EtyraCommerce.Application.DTOs.Common;

namespace EtyraCommerce.Application.DTOs.User
{
    /// <summary>
    /// User Data Transfer Object for API responses
    /// </summary>
    public class UserDto : AuditableDto
    {
        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (optional)
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Unique username for login
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the user account is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Indicates if the email address has been confirmed
        /// </summary>
        public bool IsEmailConfirmed { get; set; }

        /// <summary>
        /// Indicates if the phone number has been confirmed
        /// </summary>
        public bool IsPhoneConfirmed { get; set; }

        /// <summary>
        /// Date and time of the last login
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Number of failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; }

        /// <summary>
        /// Date and time when the account was locked (if applicable)
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// User's preferred language/culture
        /// </summary>
        public string Culture { get; set; } = "en-US";

        /// <summary>
        /// User's timezone
        /// </summary>
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// User's profile picture URL or path
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// Additional notes about the user (admin use)
        /// </summary>
        public string? Notes { get; set; }

        #region Computed Properties

        /// <summary>
        /// Gets the user's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Checks if the user account is locked
        /// </summary>
        public bool IsLocked => LockedUntil.HasValue && LockedUntil.Value > DateTime.UtcNow;

        /// <summary>
        /// Checks if the user can login (enabled, not locked, email confirmed, not deleted)
        /// </summary>
        public bool CanLogin => IsEnabled && !IsLocked && IsEmailConfirmed && IsActive;

        /// <summary>
        /// Gets the display name (FirstName or Username if FirstName is empty)
        /// </summary>
        public string DisplayName => !string.IsNullOrWhiteSpace(FirstName) ? FirstName : Username;

        /// <summary>
        /// Gets masked email for display (e.g., j***@example.com)
        /// </summary>
        public string MaskedEmail
        {
            get
            {
                if (string.IsNullOrEmpty(Email) || !Email.Contains('@'))
                    return Email;

                var parts = Email.Split('@');
                var localPart = parts[0];
                var domain = parts[1];

                if (localPart.Length <= 2)
                    return $"{localPart[0]}***@{domain}";

                var visibleChars = Math.Min(2, localPart.Length / 2);
                var maskedPart = localPart[..visibleChars] + new string('*', localPart.Length - visibleChars);
                return $"{maskedPart}@{domain}";
            }
        }

        /// <summary>
        /// Gets masked phone number for display
        /// </summary>
        public string? MaskedPhoneNumber
        {
            get
            {
                if (string.IsNullOrEmpty(PhoneNumber) || PhoneNumber.Length <= 6)
                    return PhoneNumber;

                var visibleStart = Math.Min(4, PhoneNumber.Length / 3);
                var visibleEnd = Math.Min(2, PhoneNumber.Length / 4);
                var maskedLength = PhoneNumber.Length - visibleStart - visibleEnd;

                return PhoneNumber[..visibleStart] + new string('*', maskedLength) + PhoneNumber[^visibleEnd..];
            }
        }

        #endregion
    }
}
