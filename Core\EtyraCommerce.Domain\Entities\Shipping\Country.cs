using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Country entity for European shipping system
    /// Represents countries in the European market with shipping capabilities
    /// </summary>
    public class Country : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Country name (e.g., "Romania", "Germany", "France")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// ISO 3166-1 alpha-2 country code (e.g., "RO", "DE", "FR")
        /// </summary>
        [Required]
        [MaxLength(2)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// ISO 3166-1 alpha-3 country code (e.g., "ROU", "DEU", "FRA")
        /// </summary>
        [Required]
        [MaxLength(3)]
        public string IsoCode { get; set; } = string.Empty;

        /// <summary>
        /// Currency code used in this country (e.g., "RON", "EUR", "USD")
        /// </summary>
        [Required]
        [MaxLength(3)]
        public string CurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// Country phone code (e.g., "+40", "+49", "+33")
        /// </summary>
        [MaxLength(10)]
        public string? PhoneCode { get; set; }

        /// <summary>
        /// Indicates if shipping is available to this country
        /// </summary>
        public bool IsShippingEnabled { get; set; } = true;

        /// <summary>
        /// Indicates if this country is in the European Union
        /// </summary>
        public bool IsEuMember { get; set; } = false;

        /// <summary>
        /// Display order for country selection lists
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Additional notes about shipping to this country
        /// </summary>
        [MaxLength(500)]
        public string? ShippingNotes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Regions/States/Counties within this country
        /// </summary>
        public virtual ICollection<Region> Regions { get; set; } = new List<Region>();

        /// <summary>
        /// Shipping zones that include this country
        /// </summary>
        public virtual ICollection<ShippingZoneCountry> ShippingZones { get; set; } = new List<ShippingZoneCountry>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected Country() { }

        /// <summary>
        /// Constructor for creating a new country
        /// </summary>
        public Country(string name, string code, string isoCode, string currencyCode, 
            bool isEuMember = false, string? phoneCode = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Code = code?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(code));
            IsoCode = isoCode?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(isoCode));
            CurrencyCode = currencyCode?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(currencyCode));
            IsEuMember = isEuMember;
            PhoneCode = phoneCode?.Trim();
            IsShippingEnabled = true;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Enables shipping to this country
        /// </summary>
        public void EnableShipping(string? notes = null)
        {
            IsShippingEnabled = true;
            if (!string.IsNullOrWhiteSpace(notes))
                ShippingNotes = notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Disables shipping to this country
        /// </summary>
        public void DisableShipping(string? reason = null)
        {
            IsShippingEnabled = false;
            if (!string.IsNullOrWhiteSpace(reason))
                ShippingNotes = reason.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates country information
        /// </summary>
        public void UpdateInfo(string name, string currencyCode, bool isEuMember, 
            string? phoneCode = null, string? shippingNotes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            CurrencyCode = currencyCode?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(currencyCode));
            IsEuMember = isEuMember;
            PhoneCode = string.IsNullOrWhiteSpace(phoneCode) ? null : phoneCode.Trim();
            ShippingNotes = string.IsNullOrWhiteSpace(shippingNotes) ? null : shippingNotes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for country lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets full country display name with code
        /// </summary>
        public string GetDisplayName() => $"{Name} ({Code})";

        /// <summary>
        /// Updates country details (excluding codes which are immutable)
        /// </summary>
        public void UpdateDetails(string name, string currencyCode, string phoneCode, bool isEuMember, bool isShippingEnabled, int displayOrder)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Country name cannot be empty", nameof(name));

            if (string.IsNullOrWhiteSpace(currencyCode))
                throw new ArgumentException("Currency code cannot be empty", nameof(currencyCode));

            if (string.IsNullOrWhiteSpace(phoneCode))
                throw new ArgumentException("Phone code cannot be empty", nameof(phoneCode));

            Name = name.Trim();
            CurrencyCode = currencyCode.Trim().ToUpper();
            PhoneCode = phoneCode.Trim();
            IsEuMember = isEuMember;
            IsShippingEnabled = isShippingEnabled;
            DisplayOrder = displayOrder;

            MarkAsUpdated();
        }

        /// <summary>
        /// Checks if country supports shipping
        /// </summary>
        public bool CanShipTo() => IsShippingEnabled && !IsDeleted;

        /// <summary>
        /// Gets formatted phone code
        /// </summary>
        public string GetFormattedPhoneCode() => 
            string.IsNullOrWhiteSpace(PhoneCode) ? string.Empty : 
            PhoneCode.StartsWith("+") ? PhoneCode : $"+{PhoneCode}";

        #endregion

        #region Validation

        /// <summary>
        /// Validates country data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Code) && Code.Length == 2 &&
                   !string.IsNullOrWhiteSpace(IsoCode) && IsoCode.Length == 3 &&
                   !string.IsNullOrWhiteSpace(CurrencyCode) && CurrencyCode.Length == 3;
        }

        #endregion
    }
}
