using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Payment.Commands;

/// <summary>
/// Command to update an existing payment method
/// </summary>
public class UpdatePaymentMethodCommand : IRequest<CustomResponseDto<PaymentMethodDto>>
{
    /// <summary>
    /// Payment method ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Payment method name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Payment method code for system identification
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Description of the payment method
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this payment method is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Display order for sorting payment methods
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Payment method type
    /// </summary>
    public PaymentMethodType Type { get; set; }

    /// <summary>
    /// Fee calculation type (Fixed amount or percentage)
    /// </summary>
    public FeeCalculationType FeeCalculationType { get; set; } = FeeCalculationType.None;

    /// <summary>
    /// Fee value (amount for fixed, percentage for percentage)
    /// </summary>
    public decimal FeeValue { get; set; } = 0;

    /// <summary>
    /// Currency code for fixed fee amounts
    /// </summary>
    public string? FeeCurrencyCode { get; set; }

    /// <summary>
    /// Currency name for fixed fee amounts
    /// </summary>
    public string? FeeCurrencyName { get; set; }

    /// <summary>
    /// Currency symbol for fixed fee amounts
    /// </summary>
    public string? FeeCurrencySymbol { get; set; }

    /// <summary>
    /// Currency decimal places for fixed fee amounts
    /// </summary>
    public int? FeeCurrencyDecimalPlaces { get; set; } = 2;

    /// <summary>
    /// Minimum order amount to use this payment method
    /// </summary>
    public decimal? MinimumOrderAmount { get; set; }

    /// <summary>
    /// Minimum order currency code
    /// </summary>
    public string? MinimumOrderCurrency { get; set; }

    /// <summary>
    /// Maximum order amount to use this payment method
    /// </summary>
    public decimal? MaximumOrderAmount { get; set; }

    /// <summary>
    /// Maximum order currency code
    /// </summary>
    public string? MaximumOrderCurrency { get; set; }

    /// <summary>
    /// Payment instructions for customers
    /// </summary>
    public string? Instructions { get; set; }
}
