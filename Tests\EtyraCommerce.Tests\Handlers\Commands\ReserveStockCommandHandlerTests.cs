using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory;
using EtyraCommerce.Application.Services.Inventory.Commands;
using EtyraCommerce.Application.Services.Inventory.Handlers.Commands;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class ReserveStockCommandHandlerTests
    {
        private readonly Mock<IInventoryProcessService> _mockInventoryProcessService;
        private readonly Mock<ILogger<ReserveStockCommandHandler>> _mockLogger;
        private readonly ReserveStockCommandHandler _handler;

        public ReserveStockCommandHandlerTests()
        {
            _mockInventoryProcessService = new Mock<IInventoryProcessService>();
            _mockLogger = new Mock<ILogger<ReserveStockCommandHandler>>();
            _handler = new ReserveStockCommandHandler(_mockInventoryProcessService.Object, _mockLogger.Object);
        }

        #region Handle Method Tests

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccess()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                Quantity = 5,
                Reference = "ORDER-12345",
                UserId = Guid.NewGuid(),
                Reason = "Stock reservation for order"
            };

            var expectedResponse = CustomResponseDto<bool>.Success(true, "Stock reserved successfully");

            _mockInventoryProcessService
                .Setup(x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().BeTrue();
            result.Message.Should().Be("Stock reserved successfully");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId
                ), Times.Once);
        }

        [Fact]
        public async Task Handle_InvalidProductId_ReturnsBadRequest()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.Empty, // Invalid
                WarehouseId = Guid.NewGuid(),
                Quantity = 5,
                Reference = "ORDER-12345",
                UserId = Guid.NewGuid()
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Product ID is required");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(It.IsAny<Guid>(), It.IsAny<Guid?>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<Guid?>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_InvalidQuantity_ReturnsBadRequest()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                Quantity = 0, // Invalid
                Reference = "ORDER-12345",
                UserId = Guid.NewGuid()
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Quantity must be greater than 0");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(It.IsAny<Guid>(), It.IsAny<Guid?>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<Guid?>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_EmptyReference_ReturnsBadRequest()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                Quantity = 5,
                Reference = "", // Invalid
                UserId = Guid.NewGuid()
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Reference is required");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(It.IsAny<Guid>(), It.IsAny<Guid?>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<Guid?>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_InsufficientStock_ReturnsError()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                Quantity = 100,
                Reference = "ORDER-12345",
                UserId = Guid.NewGuid(),
                Reason = "Large order reservation"
            };

            var errorResponse = CustomResponseDto<bool>.BadRequest("Insufficient stock available");

            _mockInventoryProcessService
                .Setup(x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId))
                .ReturnsAsync(errorResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Be("Insufficient stock available");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new ReserveStockCommand
            {
                ProductId = Guid.NewGuid(),
                WarehouseId = Guid.NewGuid(),
                Quantity = 5,
                Reference = "ORDER-12345",
                UserId = Guid.NewGuid()
            };

            _mockInventoryProcessService
                .Setup(x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("An error occurred while reserving stock");

            _mockInventoryProcessService.Verify(
                x => x.ProcessReserveStockAsync(
                    command.ProductId,
                    command.WarehouseId,
                    command.Quantity,
                    command.Reference,
                    command.Reason,
                    command.UserId),
                Times.Once);
        }

        #endregion
    }
}
