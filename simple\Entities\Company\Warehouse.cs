﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;
using EtyraApp.Domain.Entities.Settings;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Company;

public class Warehouse : BaseEntity
{
    [MaxLength(50)]
    public string Name { get; set; }

    [MaxLength(200)]
    public string? Address { get; set; }

    public ICollection<WarehouseProduct> WarehouseProducts { get; set; }
    public ICollection<IntegrationStoreProduct>? IntegrationStoreProducts { get; set; }

    public Country Country { get; set; }
    public int CountryId { get; set; }

    public StockStatus StockStatus { get; set; }
    public int StockStatusId { get; set; }

    public int Status { get; set; }

}