using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.User;
using EtyraCommerce.Application.Services.User.Commands;
using EtyraCommerce.Application.Services.User.Handlers.Commands;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class ChangePasswordCommandHandlerTests
    {
        private readonly Mock<IUserProcessService> _mockUserProcessService;
        private readonly Mock<ILogger<ChangePasswordCommandHandler>> _mockLogger;
        private readonly ChangePasswordCommandHandler _handler;

        public ChangePasswordCommandHandlerTests()
        {
            _mockUserProcessService = new Mock<IUserProcessService>();
            _mockLogger = new Mock<ILogger<ChangePasswordCommandHandler>>();
            _handler = new ChangePasswordCommandHandler(_mockUserProcessService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidPasswordChange_ReturnsSuccess()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var command = new ChangePasswordCommand
            {
                UserId = userId,
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(
                StatusCodes.Status200OK,
                "Password changed successfully"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Message.Should().Be("Password changed successfully");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_EmptyUserId_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.Empty,
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("UserId is required");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_EmptyCurrentPassword_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.NewGuid(),
                CurrentPassword = "",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Current password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_EmptyNewPassword_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.NewGuid(),
                CurrentPassword = "OldPassword123!",
                NewPassword = "",
                ConfirmNewPassword = "NewPassword456!"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("New password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_PasswordsDoNotMatch_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.NewGuid(),
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "DifferentPassword789!"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("New password and confirmation password do not match");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_InvalidCurrentPassword_ReturnsFailureFromProcessService()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var command = new ChangePasswordCommand
            {
                UserId = userId,
                CurrentPassword = "WrongPassword",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.BadRequest("Current password is incorrect");

            _mockUserProcessService
                .Setup(x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Current password is incorrect");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_UserNotFound_ReturnsFailureFromProcessService()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var command = new ChangePasswordCommand
            {
                UserId = userId,
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.NotFound("User not found");

            _mockUserProcessService
                .Setup(x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status404NotFound);
            result.Message.Should().Be("User not found");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var command = new ChangePasswordCommand
            {
                UserId = userId,
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword456!",
                ConfirmNewPassword = "NewPassword456!"
            };

            _mockUserProcessService
                .Setup(x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during password change");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(userId, command.CurrentPassword, command.NewPassword),
                Times.Once
            );
        }

        [Theory]
        [InlineData("OldPass123!", "NewPass456!")]
        [InlineData("CurrentPassword", "UpdatedPassword")]
        [InlineData("Temp@123", "Secure#789")]
        public async Task Handle_VariousValidPasswords_CallsProcessServiceWithCorrectParameters(
            string currentPassword, string newPassword)
        {
            // Arrange
            var userId = Guid.NewGuid();
            var command = new ChangePasswordCommand
            {
                UserId = userId,
                CurrentPassword = currentPassword,
                NewPassword = newPassword,
                ConfirmNewPassword = newPassword
            };

            var expectedResponse = CustomResponseDto<NoContentDto>.Success(
                StatusCodes.Status200OK,
                "Password changed successfully"
            );

            _mockUserProcessService
                .Setup(x => x.ProcessChangePasswordAsync(userId, currentPassword, newPassword))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(userId, currentPassword, newPassword),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_WhitespacePasswords_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.NewGuid(),
                CurrentPassword = "   ",
                NewPassword = "   ",
                ConfirmNewPassword = "   "
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Current password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_NullPasswords_ReturnsBadRequest()
        {
            // Arrange
            var command = new ChangePasswordCommand
            {
                UserId = Guid.NewGuid(),
                CurrentPassword = null!,
                NewPassword = null!,
                ConfirmNewPassword = null!
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Current password is required");

            _mockUserProcessService.Verify(
                x => x.ProcessChangePasswordAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Never
            );
        }
    }
}
