using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for creating currencies
/// </summary>
public class CreateCurrencyCommandHandler : IRequestHandler<CreateCurrencyCommand, CustomResponseDto<CurrencyDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateCurrencyCommandHandler> _logger;

    public CreateCurrencyCommandHandler(
        ICurrencyProcessService currencyProcessService,
        IMapper mapper,
        ILogger<CreateCurrencyCommandHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Handle create currency command
    /// </summary>
    /// <param name="request">Create currency command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created currency</returns>
    public async Task<CustomResponseDto<CurrencyDto>> Handle(CreateCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing CreateCurrencyCommand for currency: {Code}", request.Code);

            var createDto = _mapper.Map<CreateCurrencyDto>(request);
            var result = await _currencyProcessService.ProcessCreateAsync(createDto);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {Code} created successfully with ID: {CurrencyId}", 
                    request.Code, result.Data?.Id);
            }
            else
            {
                _logger.LogWarning("Failed to create currency {Code}. Errors: {Errors}", 
                    request.Code, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing CreateCurrencyCommand for currency: {Code}", request.Code);
            return CustomResponseDto<CurrencyDto>.InternalServerError("An error occurred while creating the currency");
        }
    }
}
