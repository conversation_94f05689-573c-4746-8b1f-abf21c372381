using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting stock status
    /// </summary>
    public class GetStockStatusQueryHandler : IRequestHandler<GetStockStatusQuery, CustomResponseDto<StockStatusDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetStockStatusQueryHandler> _logger;

        public GetStockStatusQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetStockStatusQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<StockStatusDto>> Handle(GetStockStatusQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get stock status query for ProductId: {ProductId}", request.ProductId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<StockStatusDto>.BadRequest("Product ID is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetStockStatusAsync(request.ProductId, request.ActiveWarehousesOnly);

                _logger.LogInformation("Get stock status query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get stock status query for ProductId: {ProductId}", request.ProductId);
                return CustomResponseDto<StockStatusDto>.InternalServerError("An error occurred while retrieving stock status");
            }
        }
    }
}
