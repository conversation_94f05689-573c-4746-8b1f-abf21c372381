using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Cart
{
    /// <summary>
    /// Shopping cart DTO for API responses
    /// </summary>
    public class CartDto
    {
        /// <summary>
        /// Cart ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Customer ID who owns the cart (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Cart expiration date
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Whether this cart is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Subtotal amount
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// Total number of items in cart
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Total number of unique products in cart
        /// </summary>
        public int UniqueItems { get; set; }

        /// <summary>
        /// Cart items
        /// </summary>
        public List<CartItemDto> CartItems { get; set; } = new();

        /// <summary>
        /// Cart creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Cart last update date
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Whether cart is expired
        /// </summary>
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;

        /// <summary>
        /// Cart type (User or Guest)
        /// </summary>
        public string CartType => CustomerId.HasValue ? "User" : "Guest";

        /// <summary>
        /// Formatted subtotal with currency
        /// </summary>
        public string FormattedSubtotal => $"{Subtotal:C} {Currency}";

        /// <summary>
        /// Cart summary
        /// </summary>
        public string Summary => $"{UniqueItems} unique items, {TotalItems} total items";
    }

    /// <summary>
    /// Cart item DTO for API responses
    /// </summary>
    public class CartItemDto
    {
        /// <summary>
        /// Cart item ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Product name
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// Product SKU
        /// </summary>
        public string ProductSku { get; set; } = string.Empty;

        /// <summary>
        /// Unit price
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Quantity
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Total price for this line item
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// Product variant information
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Product image URL
        /// </summary>
        public string? ProductImageUrl { get; set; }

        /// <summary>
        /// Special notes for this item
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Display name including variant info
        /// </summary>
        public string DisplayName => string.IsNullOrEmpty(VariantInfo) ? ProductName : $"{ProductName} ({VariantInfo})";

        /// <summary>
        /// Formatted unit price
        /// </summary>
        public string FormattedUnitPrice => $"{UnitPrice:C} {Currency}";

        /// <summary>
        /// Formatted total price
        /// </summary>
        public string FormattedTotalPrice => $"{TotalPrice:C} {Currency}";

        /// <summary>
        /// Item summary
        /// </summary>
        public string Summary => $"{DisplayName} - Qty: {Quantity}, Total: {FormattedTotalPrice}";
    }

    /// <summary>
    /// Add item to cart DTO
    /// </summary>
    public class AddToCartDto
    {
        /// <summary>
        /// Product ID to add
        /// </summary>
        [Required]
        public Guid ProductId { get; set; }

        /// <summary>
        /// Quantity to add
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// Product variant information (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special notes for this item (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Session ID for guest carts (optional, will be generated if not provided)
        /// </summary>
        [MaxLength(255)]
        public string? SessionId { get; set; }
    }

    /// <summary>
    /// Update cart item DTO
    /// </summary>
    public class UpdateCartItemDto
    {
        /// <summary>
        /// Product ID to update
        /// </summary>
        [Required]
        public Guid ProductId { get; set; }

        /// <summary>
        /// New quantity (set to 0 to remove item)
        /// </summary>
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "Quantity must be 0 or greater")]
        public int Quantity { get; set; }

        /// <summary>
        /// Updated variant information (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Updated notes (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Remove item from cart DTO
    /// </summary>
    public class RemoveFromCartDto
    {
        /// <summary>
        /// Product ID to remove
        /// </summary>
        [Required]
        public Guid ProductId { get; set; }
    }

    /// <summary>
    /// Cart summary DTO for quick overview
    /// </summary>
    public class CartSummaryDto
    {
        /// <summary>
        /// Cart ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Total number of items
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Total number of unique products
        /// </summary>
        public int UniqueItems { get; set; }

        /// <summary>
        /// Subtotal amount
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Whether cart is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether cart is expired
        /// </summary>
        public bool IsExpired { get; set; }

        /// <summary>
        /// Cart type (User or Guest)
        /// </summary>
        public string CartType { get; set; } = string.Empty;

        /// <summary>
        /// Formatted subtotal
        /// </summary>
        public string FormattedSubtotal => $"{Subtotal:C} {Currency}";
    }

    /// <summary>
    /// Merge cart DTO for converting guest cart to user cart
    /// </summary>
    public class MergeCartDto
    {
        /// <summary>
        /// Guest session ID to merge from
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// Target customer ID to merge to
        /// </summary>
        [Required]
        public Guid CustomerId { get; set; }
    }
}
