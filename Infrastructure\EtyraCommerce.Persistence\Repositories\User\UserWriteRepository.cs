using EtyraCommerce.Application.Repositories.User;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.User
{
    /// <summary>
    /// User-specific write repository implementation
    /// </summary>
    public class UserWriteRepository : WriteRepository<Domain.Entities.User.User>, IUserWriteRepository
    {
        public UserWriteRepository(EtyraCommerceDbContext context) : base(context)
        {
        }

        public async Task<bool> UpdateLastLoginAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.UpdateLastLogin();
            return true;
        }

        public async Task<int> IncrementFailedLoginAttemptsAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return 0;

            user.IncrementFailedLoginAttempts();
            return user.FailedLoginAttempts;
        }

        public async Task<bool> ResetFailedLoginAttemptsAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.ResetFailedLoginAttempts();
            return true;
        }

        public async Task<bool> LockUserAsync(Guid userId, DateTime lockUntil, string reason)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.Lock(lockUntil, reason);
            return true;
        }

        public async Task<bool> UnlockUserAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.Unlock();
            return true;
        }

        public async Task<bool> ActivateUserAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.Activate();
            return true;
        }

        public async Task<bool> DeactivateUserAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.Deactivate();
            return true;
        }

        public async Task<bool> ConfirmEmailAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.ConfirmEmail();
            return true;
        }

        public async Task<bool> ConfirmPhoneAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.ConfirmPhone();
            return true;
        }

        public async Task<bool> UpdatePasswordAsync(Guid userId, string passwordHash, string passwordSalt)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.ChangePassword(passwordHash, passwordSalt);
            return true;
        }

        public async Task<bool> SetPasswordResetTokenAsync(Guid userId, string token, DateTime expiry)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.GeneratePasswordResetToken(token, expiry);
            return true;
        }

        public async Task<bool> ClearPasswordResetTokenAsync(Guid userId)
        {
            var user = await Table.FindAsync(userId);
            if (user == null) return false;

            user.ClearPasswordResetToken();
            return true;
        }

        public async Task<int> BulkActivateUsersAsync(IEnumerable<Guid> userIds)
        {
            var users = await Table.Where(u => userIds.Contains(u.Id)).ToListAsync();

            foreach (var user in users)
            {
                user.Activate();
            }

            return users.Count;
        }

        public async Task<int> BulkDeactivateUsersAsync(IEnumerable<Guid> userIds)
        {
            var users = await Table.Where(u => userIds.Contains(u.Id)).ToListAsync();

            foreach (var user in users)
            {
                user.Deactivate();
            }

            return users.Count;
        }

        public async Task<int> PermanentlyDeleteOldUsersAsync(int daysOld = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);

            var usersToDelete = await Table
                .Where(u => u.IsDeleted && u.DeletedAt.HasValue && u.DeletedAt.Value <= cutoffDate)
                .ToListAsync();

            if (usersToDelete.Any())
            {
                Table.RemoveRange(usersToDelete);
            }

            return usersToDelete.Count;
        }
    }
}
