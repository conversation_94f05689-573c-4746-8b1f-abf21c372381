using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Seeds;

/// <summary>
/// Seeds initial payment methods data
/// </summary>
public static class PaymentMethodSeeder
{
    /// <summary>
    /// Seeds payment methods if they don't exist
    /// </summary>
    /// <param name="context">Database context</param>
    public static async Task SeedAsync(EtyraCommerceDbContext context)
    {
        // Check if payment methods already exist
        if (await context.PaymentMethods.AnyAsync())
            return;

        var paymentMethods = new List<PaymentMethod>
        {
            // Cash on Delivery with 5 TL fee
            new PaymentMethod(
                name: "Cash on Delivery",
                code: "COD",
                type: PaymentMethodType.CashOnDelivery,
                description: "Pay in cash when your order is delivered."
            )
            {
                DisplayOrder = 1,
                Instructions = "The courier will collect the payment amount in cash when delivering your order. Please have cash ready."
            },

            // Bank Transfer with 3% discount
            new PaymentMethod(
                name: "Bank Transfer",
                code: "BANK_TRANSFER",
                type: PaymentMethodType.BankTransfer,
                description: "Pay by bank transfer and get 3% discount."
            )
            {
                DisplayOrder = 2,
                Instructions = "Make a transfer to the bank details that will be sent to you after order confirmation. Send the transfer receipt photo via WhatsApp."
            },

            // Credit Card (no fee)
            new PaymentMethod(
                name: "Credit Card",
                code: "CREDIT_CARD",
                type: PaymentMethodType.CreditCard,
                description: "Secure payment with credit card."
            )
            {
                DisplayOrder = 3,
                Instructions = "You can make secure payments with your Visa, MasterCard and American Express cards."
            },

            // Free Payment (for promotional orders)
            new PaymentMethod(
                name: "Free",
                code: "FREE",
                type: PaymentMethodType.Free,
                description: "Free payment for promotional orders."
            )
            {
                DisplayOrder = 4,
                IsActive = false, // Initially inactive
                Instructions = "This order is free under the promotion."
            }
        };

        // Set fees for specific payment methods
        var turkishLira = Currency.TRY;

        // Cash on Delivery: 5 TL fee
        var codMethod = paymentMethods.First(p => p.Code == "COD");
        codMethod.SetFee(FeeCalculationType.Fixed, 5.00m, turkishLira);

        // Bank Transfer: 3% discount (negative fee)
        var bankTransferMethod = paymentMethods.First(p => p.Code == "BANK_TRANSFER");
        bankTransferMethod.SetFee(FeeCalculationType.Percentage, -3.00m, turkishLira);

        // Add to context
        await context.PaymentMethods.AddRangeAsync(paymentMethods);
        await context.SaveChangesAsync();
    }
}
