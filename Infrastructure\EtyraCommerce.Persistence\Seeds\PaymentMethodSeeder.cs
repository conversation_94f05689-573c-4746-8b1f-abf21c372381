using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Seeds;

/// <summary>
/// Seeds initial payment methods data
/// </summary>
public static class PaymentMethodSeeder
{
    /// <summary>
    /// Seeds payment methods if they don't exist
    /// </summary>
    /// <param name="context">Database context</param>
    public static async Task SeedAsync(EtyraCommerceDbContext context)
    {
        // Check if payment methods already exist
        if (await context.PaymentMethods.AnyAsync())
            return;

        var paymentMethods = new List<PaymentMethod>
        {
            // Cash on Delivery with 5 TL fee
            new PaymentMethod(
                name: "Kapıda Ödeme",
                code: "COD",
                type: PaymentMethodType.CashOnDelivery,
                description: "Siparişinizi teslim alırken nakit olarak ödeyebilirsiniz.",
                instructions: "Kurye siparişinizi teslim ederken ödeme tutarını nakit olarak tahsil edecektir. Lütfen hazır para bulundurunuz."
            )
            {
                DisplayOrder = 1
            },

            // Bank Transfer with 3% discount
            new PaymentMethod(
                name: "Banka Havalesi",
                code: "BANK_TRANSFER", 
                type: PaymentMethodType.BankTransfer,
                description: "Banka havalesi ile ödeme yaparak %3 indirim kazanın.",
                instructions: "Sipariş onayından sonra size gönderilecek banka bilgilerine havale yapınız. Havale dekontu fotoğrafını WhatsApp üzerinden gönderiniz."
            )
            {
                DisplayOrder = 2
            },

            // Credit Card (no fee)
            new PaymentMethod(
                name: "Kredi Kartı",
                code: "CREDIT_CARD",
                type: PaymentMethodType.CreditCard,
                description: "Kredi kartı ile güvenli ödeme.",
                instructions: "Visa, MasterCard ve American Express kartlarınızla güvenli ödeme yapabilirsiniz."
            )
            {
                DisplayOrder = 3
            },

            // Free Payment (for promotional orders)
            new PaymentMethod(
                name: "Ücretsiz",
                code: "FREE",
                type: PaymentMethodType.Free,
                description: "Promosyon siparişleri için ücretsiz ödeme.",
                instructions: "Bu sipariş promosyon kapsamında ücretsizdir."
            )
            {
                DisplayOrder = 4,
                IsActive = false // Initially inactive
            }
        };

        // Set fees for specific payment methods
        var turkishLira = new Currency("TRY", "Türk Lirası", "₺", 2);

        // Cash on Delivery: 5 TL fee
        var codMethod = paymentMethods.First(p => p.Code == "COD");
        codMethod.SetFee(FeeCalculationType.Fixed, 5.00m, turkishLira);

        // Bank Transfer: 3% discount (negative fee)
        var bankTransferMethod = paymentMethods.First(p => p.Code == "BANK_TRANSFER");
        bankTransferMethod.SetFee(FeeCalculationType.Percentage, -3.00m, turkishLira);

        // Add to context
        await context.PaymentMethods.AddRangeAsync(paymentMethods);
        await context.SaveChangesAsync();
    }
}
