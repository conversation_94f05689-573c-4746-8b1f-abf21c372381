using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Commands
{
    /// <summary>
    /// Command for updating an existing category
    /// </summary>
    public class UpdateCategoryCommand : IRequest<CustomResponseDto<CategoryDto>>
    {
        /// <summary>
        /// Category ID to update
        /// </summary>
        public Guid CategoryId { get; set; }

        /// <summary>
        /// Category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        public string? Slug { get; set; }

        /// <summary>
        /// Parent category ID (for hierarchical categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Category image URL
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Category icon (CSS class or icon name)
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether category is active/visible
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether to show category in menu
        /// </summary>
        public bool ShowInMenu { get; set; } = true;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Creates command from UpdateCategoryDto and ID
        /// </summary>
        public static UpdateCategoryCommand FromDto(Guid categoryId, UpdateCategoryDto dto)
        {
            return new UpdateCategoryCommand
            {
                CategoryId = categoryId,
                Name = dto.Name,
                Description = dto.Description,
                Slug = dto.Slug,
                ParentCategoryId = dto.ParentCategoryId,
                ImageUrl = dto.ImageUrl,
                Icon = dto.Icon,
                SortOrder = dto.SortOrder,
                IsActive = dto.IsActive,
                ShowInMenu = dto.ShowInMenu,
                MetaTitle = dto.MetaTitle,
                MetaDescription = dto.MetaDescription,
                MetaKeywords = dto.MetaKeywords
            };
        }
    }
}
