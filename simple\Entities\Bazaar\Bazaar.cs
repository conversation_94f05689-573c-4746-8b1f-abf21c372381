﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities;

public class Bazaar : BaseEntity
{
    [MaxLength(50)]
    public string Name { get; set; }

    [MaxLength(50)]
    public string? BaseName { get; set; }
    [MaxLength(200)]
    public string? Address { get; set; }

    [MaxLength(50)]
    public string? WebSite { get; set; }

    [MaxLength(100)]
    public string? BaseUrl { get; set; }

    [MaxLength(10)]
    public string? ButtonName { get; set; }
    [MaxLength(50)]
    public string? UserName { get; set; }

    [MaxLength(200)]
    public string? Password { get; set; }

    [MaxLength(200)]
    public string? ApiSecret { get; set; }

    [MaxLength(200)]
    public string? ApiKey { get; set; }

    public decimal PriceMultiplier { get; set; } = 1;

    public bool IntegrationStatus { get; set; } = false;

    public Warehouse? Warehouse { get; set; }

    public int WarehouseId { get; set; }

    [MaxLength(200)]
    public string? Image { get; set; }

    [MaxLength(200)]
    public string? InvoiceLogoImage { get; set; }
    public int? SiteId { get; set; }

    [MaxLength(50)]
    public string? Site { get; set; }

    public bool? FreeShipping { get; set; }

    [MaxLength(100)]
    public string? ReturnsWithinOption { get; set; }

    [MaxLength(100)]
    public string? ShippingCostPaidByReturns { get; set; }

    [MaxLength(100)]
    public decimal? ShippingServiceAdditionalCost { get; set; }

    [MaxLength(100)]
    public string? ShippingService { get; set; } = "UPSGround";

    public ICollection<BazaarLanguage> Languages { get; set; }
    //  public ICollection<CountryBazaar> CountryBazaars { get; set; }

    public ICollection<BazaarProduct>? BazaarProducts { get; set; }
    public ICollection<IntegrationBazaarCategory>? IntegrationBazaarCategories { get; set; }
    public ICollection<IntegrationBazaarProduct>? IntegrationBazaarProducts { get; set; }

    // EMAG currentPage ve itemPerPage

    public int currentPage { get; set; } = 0;
    public int itemPerPage { get; set; } = 0;
}