using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to reserve stock for an order
    /// </summary>
    public class ReserveStockCommand : IRequest<CustomResponseDto<bool>>
    {
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public int Quantity { get; set; }
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
        public Guid? UserId { get; set; }
    }
}
