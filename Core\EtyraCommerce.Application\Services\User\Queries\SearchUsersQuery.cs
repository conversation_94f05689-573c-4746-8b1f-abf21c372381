using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for searching users with advanced filters
    /// </summary>
    public class SearchUsersQuery : IRequest<CustomResponseDto<PagedResult<UserDto>>>
    {
        /// <summary>
        /// Search term to filter by name, email, or username
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by enabled status
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// Filter by email confirmation status
        /// </summary>
        public bool? IsEmailConfirmed { get; set; }

        /// <summary>
        /// Filter by phone confirmation status
        /// </summary>
        public bool? IsPhoneConfirmed { get; set; }

        /// <summary>
        /// Filter by locked status
        /// </summary>
        public bool? IsLocked { get; set; }

        /// <summary>
        /// Filter users created from this date
        /// </summary>
        public DateTime? CreatedFrom { get; set; }

        /// <summary>
        /// Filter users created until this date
        /// </summary>
        public DateTime? CreatedTo { get; set; }

        /// <summary>
        /// Filter users last logged in from this date
        /// </summary>
        public DateTime? LastLoginFrom { get; set; }

        /// <summary>
        /// Filter users last logged in until this date
        /// </summary>
        public DateTime? LastLoginTo { get; set; }

        /// <summary>
        /// Filter by culture/language
        /// </summary>
        public string? Culture { get; set; }

        /// <summary>
        /// Page number for pagination
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size for pagination
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// Sort field
        /// </summary>
        public UserSortField SortBy { get; set; } = UserSortField.CreatedAt;

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;

        /// <summary>
        /// Whether to include deleted users
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Whether to track entities for changes
        /// </summary>
        public bool Tracking { get; set; } = false;

        public SearchUsersQuery() { }

        public SearchUsersQuery(UserSearchDto searchDto)
        {
            SearchTerm = searchDto.SearchTerm;
            IsEnabled = searchDto.IsEnabled;
            IsEmailConfirmed = searchDto.IsEmailConfirmed;
            IsPhoneConfirmed = searchDto.IsPhoneConfirmed;
            CreatedFrom = searchDto.CreatedFrom;
            CreatedTo = searchDto.CreatedTo;
            LastLoginFrom = searchDto.LastLoginFrom;
            LastLoginTo = searchDto.LastLoginTo;
            Culture = searchDto.Culture;
            PageNumber = searchDto.PageNumber;
            PageSize = searchDto.PageSize;
            SortBy = searchDto.SortBy;
            SortDirection = searchDto.SortDirection;
        }
    }
}
