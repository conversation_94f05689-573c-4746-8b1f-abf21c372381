using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class Account : BaseEntity
{
    [MaxLength(100)]
    public string Name { get; set; }
    
    [MaxLength(50)]
    public string AccountNumber { get; set; }
    
    [MaxLength(100)]
    public string BankName { get; set; }
    
    [MaxLength(50)]
    public string IBAN { get; set; }
    
    [MaxLength(20)]
    public string SWIFT { get; set; }
    
    public int CurrencyId { get; set; }
    public Currency Currency { get; set; }
    
    public decimal Balance { get; set; }
    
    public int CompanyId { get; set; }
    public Company Company { get; set; }

    public ICollection<Transaction> Transactions { get; set; }

    public bool IsActive { get; set; }
    public string? BranchName { get; set; }

}