using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;
using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.RelationsTable;

namespace EtyraApp.Domain.Entities.Accounting;

public class Supplier : BaseEntity
{
    [MaxLength(100)]
    public string? Name { get; set; }
    
    [MaxLength(100)]
    public string? ContactPerson { get; set; }
    
    [MaxLength(100)]
    public string? Email { get; set; }
    
    [MaxLength(20)]
    public string? Phone { get; set; }
    
    [MaxLength(200)]
    public string Address { get; set; }
    
    [MaxLength(50)]
    public string? City { get; set; }
    
    [MaxLength(50)]
    public string? Country { get; set; }
    
    [MaxLength(50)]
    public string? TaxNumber { get; set; }

    [MaxLength(50)]
    public string? TaxOffice { get; set; }
   
    [MaxLength(10)]
    public string? PostalCode { get; set; }

    [MaxLength(50)]
    public string? RegistrationNumber { get; set; }
    
    public int CompanyId { get; set; }
    public Company Company { get; set; }

    [MaxLength(50)]
    public string? WebSite { get; set; }

    [MaxLength(200)]
    public string? Comment { get; set; }

    [MaxLength(200)]
    public string? Image { get; set; }

    public ICollection<Invoice> Invoices { get; set; }


    public ICollection<Person> Persons { get; set; }

    public ICollection<SupplierProduct> SuppliersProducts { get; set; }

    public bool IsActive { get; set; }
} 