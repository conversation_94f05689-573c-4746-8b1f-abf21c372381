using EtyraCommerce.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Auditable base entity configuration for user tracking properties
    /// </summary>
    public class AuditableBaseEntityConfiguration<T> : BaseEntityConfiguration<T> where T : AuditableBaseEntity
    {
        public override void Configure(EntityTypeBuilder<T> builder)
        {
            // Apply base configuration first
            base.Configure(builder);

            // User Audit Fields
            builder.Property(x => x.CreatedBy)
                .HasColumnName("created_by")
                .IsRequired(false);

            builder.Property(x => x.UpdatedBy)
                .HasColumnName("updated_by")
                .IsRequired(false);

            builder.Property(x => x.DeletedBy)
                .HasColumnName("deleted_by")
                .IsRequired(false);

            // Indexes for User Audit Fields
            builder.HasIndex(x => x.CreatedBy)
                .HasDatabaseName($"ix_{GetTableName()}_created_by");

            builder.HasIndex(x => x.UpdatedBy)
                .HasDatabaseName($"ix_{GetTableName()}_updated_by");

            builder.HasIndex(x => x.DeletedBy)
                .HasDatabaseName($"ix_{GetTableName()}_deleted_by");

            // Composite indexes for common queries
            builder.HasIndex(x => new { x.CreatedBy, x.CreatedAt })
                .HasDatabaseName($"ix_{GetTableName()}_created_by_created_at");

            builder.HasIndex(x => new { x.UpdatedBy, x.UpdatedAt })
                .HasDatabaseName($"ix_{GetTableName()}_updated_by_updated_at");

            // Foreign Key Relationships (will be configured when User entity is created)
            // builder.HasOne<User>()
            //     .WithMany()
            //     .HasForeignKey(x => x.CreatedBy)
            //     .OnDelete(DeleteBehavior.SetNull);
            //
            // builder.HasOne<User>()
            //     .WithMany()
            //     .HasForeignKey(x => x.UpdatedBy)
            //     .OnDelete(DeleteBehavior.SetNull);
            //
            // builder.HasOne<User>()
            //     .WithMany()
            //     .HasForeignKey(x => x.DeletedBy)
            //     .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
