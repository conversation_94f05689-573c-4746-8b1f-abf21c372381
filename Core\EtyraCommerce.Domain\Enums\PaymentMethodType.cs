namespace EtyraCommerce.Domain.Enums
{
    /// <summary>
    /// Payment method type enumeration
    /// </summary>
    public enum PaymentMethodType
    {
        /// <summary>
        /// Cash on delivery payment
        /// </summary>
        CashOnDelivery = 1,

        /// <summary>
        /// Bank transfer payment
        /// </summary>
        BankTransfer = 2,

        /// <summary>
        /// Credit card payment
        /// </summary>
        CreditCard = 3,

        /// <summary>
        /// Debit card payment
        /// </summary>
        DebitCard = 4,

        /// <summary>
        /// Digital wallet payment (PayPal, etc.)
        /// </summary>
        DigitalWallet = 5,

        /// <summary>
        /// Cryptocurrency payment
        /// </summary>
        Cryptocurrency = 6,

        /// <summary>
        /// Buy now, pay later services
        /// </summary>
        BuyNowPayLater = 7,

        /// <summary>
        /// Mobile payment
        /// </summary>
        MobilePayment = 8,

        /// <summary>
        /// Free payment (for free orders)
        /// </summary>
        Free = 9,

        /// <summary>
        /// Other payment methods
        /// </summary>
        Other = 99
    }

    /// <summary>
    /// Fee calculation type enumeration
    /// </summary>
    public enum FeeCalculationType
    {
        /// <summary>
        /// No fee applied
        /// </summary>
        None = 0,

        /// <summary>
        /// Fixed amount fee
        /// </summary>
        Fixed = 1,

        /// <summary>
        /// Percentage-based fee
        /// </summary>
        Percentage = 2
    }
}
