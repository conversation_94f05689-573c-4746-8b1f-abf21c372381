using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShoppingCart entity
    /// </summary>
    public class ShoppingCartConfiguration : IEntityTypeConfiguration<ShoppingCart>
    {
        public void Configure(EntityTypeBuilder<ShoppingCart> builder)
        {
            // Table configuration
            builder.ToTable("shopping_carts", "etyra_core");

            // Primary key
            builder.HasKey(x => x.Id);

            // Basic properties
            builder.Property(x => x.CustomerId)
                .IsRequired(false)
                .HasComment("Customer ID who owns the cart (null for guest carts)");

            builder.Property(x => x.SessionId)
                .HasMaxLength(255)
                .IsRequired(false)
                .HasComment("Session ID for guest carts");

            builder.Property(x => x.ExpiresAt)
                .IsRequired()
                .HasColumnType("timestamp with time zone")
                .HasComment("Cart expiration date");

            builder.Property(x => x.IsActive)
                .IsRequired()
                .HasDefaultValue(true)
                .HasComment("Whether this cart is active");

            // Currency Value Object
            builder.OwnsOne(x => x.Currency, currency =>
            {
                currency.Property(c => c.Code)
                    .HasColumnName("currency_code")
                    .HasMaxLength(3)
                    .IsRequired();

                currency.Property(c => c.Name)
                    .HasColumnName("currency_name")
                    .HasMaxLength(100)
                    .IsRequired();

                currency.Property(c => c.Symbol)
                    .HasColumnName("currency_symbol")
                    .HasMaxLength(10)
                    .IsRequired();

                currency.Property(c => c.DecimalPlaces)
                    .HasColumnName("currency_decimal_places")
                    .IsRequired();
            });

            // Subtotal Money Value Object
            builder.OwnsOne(x => x.Subtotal, subtotal =>
            {
                subtotal.Property(s => s.Amount)
                    .HasColumnName("subtotal_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                subtotal.Property(s => s.Currency)
                    .HasConversion(
                        c => c.Code,
                        s => Currency.FromCode(s))
                    .HasColumnName("subtotal_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Relationships
            builder.HasMany(x => x.CartItems)
                .WithOne(x => x.ShoppingCart)
                .HasForeignKey(x => x.ShoppingCartId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(x => x.CustomerId)
                .HasDatabaseName("ix_shopping_carts_customer_id");

            builder.HasIndex(x => x.SessionId)
                .HasDatabaseName("ix_shopping_carts_session_id");

            builder.HasIndex(x => x.ExpiresAt)
                .HasDatabaseName("ix_shopping_carts_expires_at");

            builder.HasIndex(x => x.IsActive)
                .HasDatabaseName("ix_shopping_carts_is_active");

            builder.HasIndex(x => new { x.CustomerId, x.IsActive })
                .HasDatabaseName("ix_shopping_carts_customer_active");

            builder.HasIndex(x => new { x.SessionId, x.IsActive })
                .HasDatabaseName("ix_shopping_carts_session_active");

            // Constraints
            builder.HasCheckConstraint("ck_shopping_carts_customer_or_session",
                "(\"CustomerId\" IS NOT NULL AND \"SessionId\" IS NULL) OR (\"CustomerId\" IS NULL AND \"SessionId\" IS NOT NULL)");

            builder.HasCheckConstraint("ck_shopping_carts_subtotal_positive",
                "subtotal_amount >= 0");

            builder.HasCheckConstraint("ck_shopping_carts_expires_future",
                "\"ExpiresAt\" > \"CreatedAt\"");

            // Audit fields (inherited from AuditableBaseEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasColumnType("timestamp with time zone")
                .HasDefaultValueSql("NOW()");

            builder.Property(x => x.UpdatedAt)
                .IsRequired()
                .HasColumnType("timestamp with time zone")
                .HasDefaultValueSql("NOW()");

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(255)
                .IsRequired(false);

            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(255)
                .IsRequired(false);

            // Soft delete (inherited from BaseEntity)
            builder.Property(x => x.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(x => x.DeletedAt)
                .IsRequired(false)
                .HasColumnType("timestamp with time zone");

            // Global query filter for soft delete
            builder.HasQueryFilter(x => !x.IsDeleted);

            // Comments
            builder.HasComment("Shopping cart entity for storing customer cart items");
        }
    }
}
