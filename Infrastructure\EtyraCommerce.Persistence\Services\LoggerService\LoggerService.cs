﻿using EtyraCommerce.Application.Services.LoggerService;
using Serilog;

namespace EtyraCommerce.Persistence.Services.LoggerService;

public class LoggerService : ILoggerService
{
    public void LogInformation(string message)
    {
        Log.Information(message);
    }

    public void LogWarning(string message)
    {
        Log.Warning(message);
    }

    public void LogError(string message, Exception exception = null)
    {
        Log.Error(message, exception);
    }



    public List<string> GetLogFiles(string logDirectory)
    {
        List<string> logFiles = new List<string>();

        // Log dizinindeki tüm dosyaları al
        string[] files = Directory.GetFiles(logDirectory, "*.txt");

        foreach (string file in files)
        {
            logFiles.Add(Path.GetFileName(file));
        }

        return logFiles;
    }

    public List<string> GetLogContent(string logFilePath)
    {
        // Log dosyasının içeriğini al
        List<string> logs = new List<string>();
        logs.AddRange(File.ReadAllLines(logFilePath));

        return logs;
    }




}