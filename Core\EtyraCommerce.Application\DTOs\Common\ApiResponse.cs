namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Standard API response wrapper with data
    /// </summary>
    /// <typeparam name="T">Type of data in the response</typeparam>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? TraceId { get; set; }
        public int StatusCode { get; set; }
        public PaginationMetadata? Pagination { get; set; }

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        public static ApiResponse<T> Ok(T data, string message = "")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                StatusCode = 200
            };
        }

        /// <summary>
        /// Creates a successful response with data and pagination
        /// </summary>
        public static ApiResponse<T> Ok(T data, PaginationMetadata pagination, string message = "")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                StatusCode = 200,
                Pagination = pagination
            };
        }

        /// <summary>
        /// Creates a created response (201)
        /// </summary>
        public static ApiResponse<T> Created(T data, string message = "Resource created successfully")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                StatusCode = 201
            };
        }

        /// <summary>
        /// Creates a no content response (204)
        /// </summary>
        public static ApiResponse<T> NoContent(string message = "")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                StatusCode = 204
            };
        }

        /// <summary>
        /// Creates a bad request response (400)
        /// </summary>
        public static ApiResponse<T> BadRequest(string message, List<string>? errors = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>(),
                StatusCode = 400
            };
        }

        /// <summary>
        /// Creates a bad request response with validation errors
        /// </summary>
        public static ApiResponse<T> BadRequest(Dictionary<string, List<string>> validationErrors)
        {
            var errors = validationErrors.SelectMany(kvp =>
                kvp.Value.Select(error => $"{kvp.Key}: {error}")).ToList();

            return new ApiResponse<T>
            {
                Success = false,
                Message = "Validation failed",
                Errors = errors,
                StatusCode = 400
            };
        }

        /// <summary>
        /// Creates an unauthorized response (401)
        /// </summary>
        public static ApiResponse<T> Unauthorized(string message = "Unauthorized access")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                StatusCode = 401
            };
        }

        /// <summary>
        /// Creates a forbidden response (403)
        /// </summary>
        public static ApiResponse<T> Forbidden(string message = "Access forbidden")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                StatusCode = 403
            };
        }

        /// <summary>
        /// Creates a not found response (404)
        /// </summary>
        public static ApiResponse<T> NotFound(string message = "Resource not found")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                StatusCode = 404
            };
        }

        /// <summary>
        /// Creates a conflict response (409)
        /// </summary>
        public static ApiResponse<T> Conflict(string message = "Resource conflict")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                StatusCode = 409
            };
        }

        /// <summary>
        /// Creates an internal server error response (500)
        /// </summary>
        public static ApiResponse<T> InternalServerError(string message = "Internal server error")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                StatusCode = 500
            };
        }

        /// <summary>
        /// Creates response from ServiceResult
        /// </summary>
        public static ApiResponse<T> FromServiceResult(ServiceResult<T> serviceResult)
        {
            if (serviceResult.IsSuccess)
            {
                return Ok(serviceResult.Data!, serviceResult.ErrorMessage);
            }

            var statusCode = serviceResult.HasValidationErrors ? 400 : 500;
            return new ApiResponse<T>
            {
                Success = false,
                Message = serviceResult.ErrorMessage,
                Errors = serviceResult.ValidationErrors,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Sets trace ID for request tracking
        /// </summary>
        public ApiResponse<T> WithTraceId(string traceId)
        {
            TraceId = traceId;
            return this;
        }

        /// <summary>
        /// Adds additional error
        /// </summary>
        public ApiResponse<T> AddError(string error)
        {
            Errors.Add(error);
            return this;
        }

        /// <summary>
        /// Adds multiple errors
        /// </summary>
        public ApiResponse<T> AddErrors(IEnumerable<string> errors)
        {
            Errors.AddRange(errors);
            return this;
        }

        /// <summary>
        /// Checks if response has errors
        /// </summary>
        public bool HasErrors => Errors.Any();

        /// <summary>
        /// Gets all error messages combined
        /// </summary>
        public string GetAllErrors() => string.Join("; ", Errors);
    }

    /// <summary>
    /// API response without data
    /// </summary>
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? TraceId { get; set; }
        public int StatusCode { get; set; }

        /// <summary>
        /// Creates a successful response
        /// </summary>
        public static ApiResponse Ok(string message = "Success")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                StatusCode = 200
            };
        }

        /// <summary>
        /// Creates a created response (201)
        /// </summary>
        public static ApiResponse Created(string message = "Resource created successfully")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                StatusCode = 201
            };
        }

        /// <summary>
        /// Creates a no content response (204)
        /// </summary>
        public static ApiResponse NoContent(string message = "")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                StatusCode = 204
            };
        }

        /// <summary>
        /// Creates a bad request response (400)
        /// </summary>
        public static ApiResponse BadRequest(string message, List<string>? errors = null)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>(),
                StatusCode = 400
            };
        }

        /// <summary>
        /// Creates response from ServiceResult
        /// </summary>
        public static ApiResponse FromServiceResult(ServiceResult serviceResult)
        {
            if (serviceResult.IsSuccess)
            {
                return Ok(serviceResult.ErrorMessage);
            }

            var statusCode = serviceResult.HasValidationErrors ? 400 : 500;
            return new ApiResponse
            {
                Success = false,
                Message = serviceResult.ErrorMessage,
                Errors = serviceResult.ValidationErrors,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Converts to generic ApiResponse<T>
        /// </summary>
        public ApiResponse<T> ToGeneric<T>(T? data = default)
        {
            return new ApiResponse<T>
            {
                Success = Success,
                Data = data,
                Message = Message,
                Errors = Errors,
                Timestamp = Timestamp,
                TraceId = TraceId,
                StatusCode = StatusCode
            };
        }
    }
}
