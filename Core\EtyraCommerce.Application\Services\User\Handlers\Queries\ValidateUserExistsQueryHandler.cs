using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for ValidateUserExistsQuery - Validates and delegates to UserProcessService
    /// </summary>
    public class ValidateUserExistsQueryHandler : IRequestHandler<ValidateUserExistsQuery, CustomResponseDto<bool>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<ValidateUserExistsQueryHandler> _logger;

        public ValidateUserExistsQueryHandler(
            IUserProcessService userProcessService,
            ILogger<ValidateUserExistsQueryHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(ValidateUserExistsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing validate user exists query for Email: {Email}, Username: {Username}",
                    request.Email, request.Username);

                // Validate that at least one parameter is provided
                if (string.IsNullOrWhiteSpace(request.Email) && string.IsNullOrWhiteSpace(request.Username))
                {
                    _logger.LogWarning("Validate user exists query failed: Either email or username is required");
                    return CustomResponseDto<bool>.BadRequest("Either email or username is required");
                }

                CustomResponseDto<bool> result;

                // Check email existence if provided
                if (!string.IsNullOrWhiteSpace(request.Email))
                {
                    result = await _userProcessService.ProcessEmailExistsAsync(request.Email, request.ExcludeUserId);

                    if (result.IsSuccess)
                    {
                        _logger.LogDebug("Email existence check completed for: {Email}, Exists: {Exists}",
                            request.Email, result.Data);
                    }

                    return result;
                }

                // Check username existence if provided
                if (!string.IsNullOrWhiteSpace(request.Username))
                {
                    result = await _userProcessService.ProcessUsernameExistsAsync(request.Username, request.ExcludeUserId);

                    if (result.IsSuccess)
                    {
                        _logger.LogDebug("Username existence check completed for: {Username}, Exists: {Exists}",
                            request.Username, result.Data);
                    }

                    return result;
                }

                // This should never be reached due to validation above
                return CustomResponseDto<bool>.BadRequest("Invalid validation request");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing validate user exists query for Email: {Email}, Username: {Username}",
                    request.Email, request.Username);
                return CustomResponseDto<bool>.InternalServerError("An error occurred during validation");
            }
        }
    }
}
