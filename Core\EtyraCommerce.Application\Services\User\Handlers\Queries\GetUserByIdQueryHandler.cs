using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using EtyraCommerce.Application.UnitOfWork;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserByIdQuery
    /// </summary>
    public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, CustomResponseDto<UserDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<GetUserByIdQueryHandler> _logger;

        public GetUserByIdQueryHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<GetUserByIdQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing GetUserByIdQuery for UserId: {UserId}", request.UserId);

                // Validation
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserByIdQuery received empty UserId");
                    return CustomResponseDto<UserDto>.BadRequest("User ID is required");
                }

                // Get user from repository
                var userRepository = _unitOfWork.ReadRepository<Domain.Entities.User.User>();
                var user = await userRepository.GetByIdAsync(request.UserId);

                if (user == null)
                {
                    _logger.LogWarning("User not found with ID: {UserId}", request.UserId);
                    return CustomResponseDto<UserDto>.NotFound("User not found");
                }

                // Check if user is active (not deleted)
                if (user.IsDeleted)
                {
                    _logger.LogWarning("Attempt to access deleted user: {UserId}", request.UserId);
                    return CustomResponseDto<UserDto>.NotFound("User not found");
                }

                // Map to DTO
                var userDto = _mapper.Map<UserDto>(user);

                _logger.LogInformation("Successfully retrieved user: {UserId}", request.UserId);
                return CustomResponseDto<UserDto>.Success(200, userDto, "User retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing GetUserByIdQuery for UserId: {UserId}", request.UserId);
                return CustomResponseDto<UserDto>.InternalServerError("An error occurred while retrieving user");
            }
        }
    }
}
