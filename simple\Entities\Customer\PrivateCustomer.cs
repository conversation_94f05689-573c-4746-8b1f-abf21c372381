﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Customer;

public class PrivateCustomer : BaseEntity
{
    public int CustomerType { get; set; } = 1;
    [MaxLength(100)] public string? Firstname { get; set; }
    [MaxLength(100)] public string? Lastname { get; set; }
    [MaxLength(20)] public string? ContactPersonTelephone { get; set; }
    [MaxLength(100)] public string? ContactEmail { get; set; }

    [MaxLength(20)] public string? CompanyTelephone { get; set; }
    [MaxLength(100)] public string? CompanyEmail { get; set; }
    [MaxLength(500)] public string? AccountCustomField { get; set; }
    [MaxLength(500)] public string? Address { get; set; }
    [MaxLength(50)] public string? City { get; set; }
    [MaxLength(50)] public string? Zone { get; set; }
    [MaxLength(50)] public string? Country { get; set; }
    [MaxLength(10)] public string? PostalCode { get; set; }
    [MaxLength(100)] public string? Company { get; set; }
    [MaxLength(20)] public string? VatNumber { get; set; }
    public bool Status { get; set; } = true;
    public int OrderCount { get; set; }
    public Todo? Todo { get; set; }

}

