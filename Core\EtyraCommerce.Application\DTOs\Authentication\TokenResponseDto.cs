using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Authentication
{
    /// <summary>
    /// Response DTO for JWT token operations
    /// </summary>
    public class TokenResponseDto
    {
        /// <summary>
        /// JWT access token
        /// </summary>
        [Required]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Refresh token for obtaining new access tokens
        /// </summary>
        [Required]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// Access token expiration date and time
        /// </summary>
        [Required]
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Refresh token expiration date and time
        /// </summary>
        [Required]
        public DateTime RefreshTokenExpiresAt { get; set; }

        /// <summary>
        /// Token type (usually "Bearer")
        /// </summary>
        [Required]
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Access token lifetime in seconds
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// User information associated with the token
        /// </summary>
        public TokenUserDto? User { get; set; }

        /// <summary>
        /// Scopes or permissions associated with the token
        /// </summary>
        public List<string>? Scopes { get; set; }

        /// <summary>
        /// Additional claims or metadata
        /// </summary>
        public Dictionary<string, object>? Claims { get; set; }

        public TokenResponseDto()
        {
            Scopes = new List<string>();
            Claims = new Dictionary<string, object>();
        }

        public TokenResponseDto(string accessToken, string refreshToken, DateTime expiresAt, DateTime refreshTokenExpiresAt)
        {
            AccessToken = accessToken;
            RefreshToken = refreshToken;
            ExpiresAt = expiresAt;
            RefreshTokenExpiresAt = refreshTokenExpiresAt;
            ExpiresIn = (int)(expiresAt - DateTime.UtcNow).TotalSeconds;
            Scopes = new List<string>();
            Claims = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Simplified user information for token response
    /// </summary>
    public class TokenUserDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address
        /// </summary>
        [Required]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's full name
        /// </summary>
        public string? FullName { get; set; }

        /// <summary>
        /// User roles
        /// </summary>
        public List<string>? Roles { get; set; }

        /// <summary>
        /// User permissions
        /// </summary>
        public List<string>? Permissions { get; set; }

        public TokenUserDto()
        {
            Roles = new List<string>();
            Permissions = new List<string>();
        }
    }
}
