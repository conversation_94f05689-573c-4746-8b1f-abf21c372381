using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Domain.Entities.Payment;

namespace EtyraCommerce.Application.Repositories.Payment;

/// <summary>
/// Write repository interface for PaymentMethod entity
/// </summary>
public interface IPaymentMethodWriteRepository : IWriteRepository<PaymentMethod>
{
    /// <summary>
    /// Update payment method status
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method or null if not found</returns>
    Task<PaymentMethod?> UpdateStatusAsync(Guid id, bool isActive);

    /// <summary>
    /// Update display order for multiple payment methods
    /// </summary>
    /// <param name="orderUpdates">Dictionary of payment method ID and new display order</param>
    /// <returns>Number of updated records</returns>
    Task<int> UpdateDisplayOrdersAsync(Dictionary<Guid, int> orderUpdates);
}
