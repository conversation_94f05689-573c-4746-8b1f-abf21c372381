using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;

namespace EtyraCommerce.Application.Services.UserAddress
{
    /// <summary>
    /// Process service interface for UserAddress operations
    /// Handles business logic and coordinates between handlers and repositories
    /// </summary>
    public interface IUserAddressProcessService
    {
        #region Command Operations

        /// <summary>
        /// Processes the creation of a new user address
        /// </summary>
        /// <param name="userId">ID of the user who owns the address</param>
        /// <param name="createDto">Address creation data</param>
        /// <returns>Created address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> ProcessCreateUserAddressAsync(Guid userId, CreateUserAddressDto createDto);

        /// <summary>
        /// Processes the update of an existing user address
        /// </summary>
        /// <param name="addressId">ID of the address to update</param>
        /// <param name="userId">ID of the user who owns the address (for security validation)</param>
        /// <param name="updateDto">Address update data</param>
        /// <returns>Updated address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> ProcessUpdateUserAddressAsync(Guid addressId, Guid userId, UpdateUserAddressDto updateDto);

        /// <summary>
        /// Processes the deletion of a user address
        /// </summary>
        /// <param name="addressId">ID of the address to delete</param>
        /// <param name="userId">ID of the user who owns the address (for security validation)</param>
        /// <param name="hardDelete">Whether to perform hard delete (true) or soft delete (false)</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessDeleteUserAddressAsync(Guid addressId, Guid userId, bool hardDelete = false);

        /// <summary>
        /// Processes setting an address as the default address for a user
        /// </summary>
        /// <param name="addressId">ID of the address to set as default</param>
        /// <param name="userId">ID of the user who owns the address (for security validation)</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessSetDefaultAddressAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Processes toggling the active status of an address
        /// </summary>
        /// <param name="addressId">ID of the address to toggle</param>
        /// <param name="userId">ID of the user who owns the address (for security validation)</param>
        /// <param name="isActive">New active status</param>
        /// <returns>No content response</returns>
        Task<CustomResponseDto<NoContentDto>> ProcessToggleAddressStatusAsync(Guid addressId, Guid userId, bool isActive);

        #endregion

        #region Query Operations

        /// <summary>
        /// Processes getting all addresses for a specific user
        /// </summary>
        /// <param name="userId">ID of the user whose addresses to retrieve</param>
        /// <param name="addressType">Filter by address type (optional)</param>
        /// <param name="isActive">Filter by active status (optional)</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of user address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetUserAddressesAsync(Guid userId, int? addressType = null, bool? isActive = null, bool includeInactive = false);

        /// <summary>
        /// Processes getting a specific user address by ID
        /// </summary>
        /// <param name="addressId">ID of the address to retrieve</param>
        /// <param name="userId">ID of the user who owns the address (for security validation)</param>
        /// <returns>User address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> ProcessGetUserAddressByIdAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Processes getting the default address for a user
        /// </summary>
        /// <param name="userId">ID of the user whose default address to retrieve</param>
        /// <param name="addressType">Filter by address type for default address (optional)</param>
        /// <returns>Default user address DTO</returns>
        Task<CustomResponseDto<UserAddressDto>> ProcessGetDefaultAddressAsync(Guid userId, int? addressType = null);

        /// <summary>
        /// Processes getting addresses suitable for billing
        /// </summary>
        /// <param name="userId">ID of the user whose billing addresses to retrieve</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of billing address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetBillingAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Processes getting addresses suitable for shipping
        /// </summary>
        /// <param name="userId">ID of the user whose shipping addresses to retrieve</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of shipping address DTOs</returns>
        Task<CustomResponseDto<List<UserAddressDto>>> ProcessGetShippingAddressesAsync(Guid userId, bool includeInactive = false);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Validates if a user can have a new address
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result</returns>
        Task<CustomResponseDto<bool>> ValidateUserCanHaveNewAddressAsync(Guid userId);

        /// <summary>
        /// Validates if an address belongs to a specific user
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result</returns>
        Task<CustomResponseDto<bool>> ValidateAddressBelongsToUserAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Validates if an address can be deleted
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>Validation result with reason if cannot be deleted</returns>
        Task<CustomResponseDto<bool>> ValidateAddressCanBeDeletedAsync(Guid addressId, Guid userId);

        #endregion

        #region Business Logic Operations

        /// <summary>
        /// Gets the count of addresses for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses in count</param>
        /// <returns>Address count</returns>
        Task<CustomResponseDto<int>> GetUserAddressCountAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Checks if a user has any addresses
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>True if user has addresses, false otherwise</returns>
        Task<CustomResponseDto<bool>> UserHasAddressesAsync(Guid userId);

        /// <summary>
        /// Gets address statistics for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Address statistics</returns>
        Task<CustomResponseDto<UserAddressStatsDto>> GetUserAddressStatsAsync(Guid userId);

        #endregion
    }

    /// <summary>
    /// DTO for user address statistics
    /// </summary>
    public class UserAddressStatsDto
    {
        /// <summary>
        /// Total number of addresses
        /// </summary>
        public int TotalAddresses { get; set; }

        /// <summary>
        /// Number of active addresses
        /// </summary>
        public int ActiveAddresses { get; set; }

        /// <summary>
        /// Number of inactive addresses
        /// </summary>
        public int InactiveAddresses { get; set; }

        /// <summary>
        /// Number of billing addresses
        /// </summary>
        public int BillingAddresses { get; set; }

        /// <summary>
        /// Number of shipping addresses
        /// </summary>
        public int ShippingAddresses { get; set; }

        /// <summary>
        /// Number of addresses that can be used for both billing and shipping
        /// </summary>
        public int BothTypeAddresses { get; set; }

        /// <summary>
        /// Whether user has a default address
        /// </summary>
        public bool HasDefaultAddress { get; set; }

        /// <summary>
        /// ID of the default address (if any)
        /// </summary>
        public Guid? DefaultAddressId { get; set; }
    }
}
