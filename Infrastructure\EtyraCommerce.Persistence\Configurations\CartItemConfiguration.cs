using EtyraCommerce.Domain.Entities.Cart;
using EtyraCommerce.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for CartItem entity
    /// </summary>
    public class CartItemConfiguration : IEntityTypeConfiguration<CartItem>
    {
        public void Configure(EntityTypeBuilder<CartItem> builder)
        {
            // Table configuration
            builder.ToTable("cart_items", "etyra_core");

            // Primary key
            builder.HasKey(x => x.Id);

            // Basic properties
            builder.Property(x => x.ProductId)
                .IsRequired()
                .HasComment("Product ID");

            builder.Property(x => x.ProductName)
                .IsRequired()
                .HasMaxLength(500)
                .HasComment("Product name at the time of adding to cart");

            builder.Property(x => x.ProductSku)
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("Product SKU at the time of adding to cart");

            builder.Property(x => x.Quantity)
                .IsRequired()
                .HasComment("Quantity of this item in cart");

            builder.Property(x => x.VariantInfo)
                .HasMaxLength(1000)
                .IsRequired(false)
                .HasComment("Product variant information (e.g., Size: L, Color: Red)");

            builder.Property(x => x.ProductImageUrl)
                .HasMaxLength(2000)
                .IsRequired(false)
                .HasComment("Product image URL for display");

            builder.Property(x => x.Notes)
                .HasMaxLength(1000)
                .IsRequired(false)
                .HasComment("Special notes for this item");

            builder.Property(x => x.ShoppingCartId)
                .IsRequired()
                .HasComment("Shopping cart ID this item belongs to");

            // UnitPrice Money Value Object
            builder.OwnsOne(x => x.UnitPrice, unitPrice =>
            {
                unitPrice.Property(u => u.Amount)
                    .HasColumnName("unit_price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                unitPrice.Property(u => u.Currency)
                    .HasConversion(
                        c => c.Code,
                        s => Currency.FromCode(s))
                    .HasColumnName("unit_price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // TotalPrice Money Value Object
            builder.OwnsOne(x => x.TotalPrice, totalPrice =>
            {
                totalPrice.Property(t => t.Amount)
                    .HasColumnName("total_price_amount")
                    .HasColumnType("decimal(18,4)")
                    .IsRequired();

                totalPrice.Property(t => t.Currency)
                    .HasConversion(
                        c => c.Code,
                        s => Currency.FromCode(s))
                    .HasColumnName("total_price_currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Relationships
            builder.HasOne(x => x.ShoppingCart)
                .WithMany(x => x.CartItems)
                .HasForeignKey(x => x.ShoppingCartId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(x => x.Product)
                .WithMany()
                .HasForeignKey(x => x.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(x => x.ProductId)
                .HasDatabaseName("ix_cart_items_product_id");

            builder.HasIndex(x => x.ShoppingCartId)
                .HasDatabaseName("ix_cart_items_shopping_cart_id");

            builder.HasIndex(x => x.ProductSku)
                .HasDatabaseName("ix_cart_items_product_sku");

            builder.HasIndex(x => new { x.ShoppingCartId, x.ProductId, x.VariantInfo })
                .HasDatabaseName("ix_cart_items_cart_product_variant")
                .IsUnique(); // Prevent duplicate product variants in same cart

            // Constraints
            builder.HasCheckConstraint("ck_cart_items_quantity_positive",
                "\"Quantity\" > 0");

            builder.HasCheckConstraint("ck_cart_items_unit_price_positive",
                "unit_price_amount > 0");

            builder.HasCheckConstraint("ck_cart_items_total_price_positive",
                "total_price_amount > 0");

            builder.HasCheckConstraint("ck_cart_items_currencies_match",
                "unit_price_currency = total_price_currency");

            // Soft delete (inherited from BaseEntity)
            builder.Property(x => x.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(x => x.DeletedAt)
                .IsRequired(false)
                .HasColumnType("timestamp with time zone");

            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasColumnType("timestamp with time zone")
                .HasDefaultValueSql("NOW()");

            builder.Property(x => x.UpdatedAt)
                .IsRequired()
                .HasColumnType("timestamp with time zone")
                .HasDefaultValueSql("NOW()");

            // RowVersion for optimistic concurrency
            builder.Property(x => x.RowVersion)
                .IsRowVersion();

            // Global query filter for soft delete
            builder.HasQueryFilter(x => !x.IsDeleted);

            // Comments
            builder.HasComment("Cart item entity representing an item in a shopping cart");
        }
    }
}
