using EtyraCommerce.Domain.Entities.Inventory;

namespace EtyraCommerce.Application.DTOs.Inventory
{
    /// <summary>
    /// Inventory transaction data transfer object
    /// </summary>
    public class InventoryTransactionDto
    {
        public Guid Id { get; set; }
        public Guid InventoryId { get; set; }
        public InventoryTransactionType Type { get; set; }
        public int Quantity { get; set; }
        public int QuantityBefore { get; set; }
        public int QuantityAfter { get; set; }
        public string? Reference { get; set; }
        public string? ReferenceType { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
        public Guid? UserId { get; set; }
        public DateTime TransactionDate { get; set; }
        public decimal? UnitCost { get; set; }
        public decimal? TotalCost { get; set; }
        public string? ExternalReference { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public string? ProductName { get; set; }
        public string? ProductModel { get; set; }
        public string? WarehouseName { get; set; }
        public string? UserName { get; set; }
    }

    /// <summary>
    /// Create inventory transaction DTO
    /// </summary>
    public class CreateInventoryTransactionDto
    {
        public Guid InventoryId { get; set; }
        public InventoryTransactionType Type { get; set; }
        public int Quantity { get; set; }
        public int QuantityBefore { get; set; }
        public int QuantityAfter { get; set; }
        public string? Reference { get; set; }
        public string? ReferenceType { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
        public Guid? UserId { get; set; }
        public decimal? UnitCost { get; set; }
        public string? ExternalReference { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
    }

    /// <summary>
    /// Inventory transaction filter DTO
    /// </summary>
    public class InventoryTransactionFilterDto
    {
        public Guid? InventoryId { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public InventoryTransactionType? Type { get; set; }
        public string? Reference { get; set; }
        public string? ReferenceType { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? TransactionDateFrom { get; set; }
        public DateTime? TransactionDateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "TransactionDate";
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// Stock movement report DTO
    /// </summary>
    public class StockMovementReportDto
    {
        public Guid ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductModel { get; set; } = string.Empty;
        public Guid WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public int OpeningStock { get; set; }
        public int StockIn { get; set; }
        public int StockOut { get; set; }
        public int Adjustments { get; set; }
        public int ClosingStock { get; set; }
        public decimal? TotalValue { get; set; }
        public List<InventoryTransactionDto> Transactions { get; set; } = new List<InventoryTransactionDto>();
    }

    /// <summary>
    /// Transaction summary DTO
    /// </summary>
    public class TransactionSummaryDto
    {
        public DateTime Date { get; set; }
        public int TotalTransactions { get; set; }
        public int StockInTransactions { get; set; }
        public int StockOutTransactions { get; set; }
        public int AdjustmentTransactions { get; set; }
        public decimal? TotalValue { get; set; }
    }
}
