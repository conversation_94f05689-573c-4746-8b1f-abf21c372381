using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product entity representing a catalog item
    /// </summary>
    public class Product : AuditableBaseEntity
    {
        #region Basic Information

        /// <summary>
        /// Product model/code
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// Product name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product rating (1-5 stars)
        /// </summary>
        public int? Stars { get; set; }

        /// <summary>
        /// AI Platform ID for integration
        /// </summary>
        public int? AiPlatformId { get; set; }

        #endregion

        #region Product Codes & Identifiers

        /// <summary>
        /// European Article Number (EAN-13)
        /// </summary>
        public string? EAN { get; set; }

        /// <summary>
        /// Manufacturer Part Number
        /// </summary>
        public string? MPN { get; set; }

        /// <summary>
        /// Product barcode
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// Product brand
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Universal Product Code
        /// </summary>
        public string? UPC { get; set; }

        /// <summary>
        /// Stock Keeping Unit (internal SKU)
        /// </summary>
        public string SKU { get; set; } = string.Empty;

        #endregion

        #region Value Objects

        /// <summary>
        /// Product dimensions and weight
        /// </summary>
        public ProductDimensions? Dimensions { get; set; }

        /// <summary>
        /// Base price of the product
        /// </summary>
        public Money BasePrice { get; set; } = null!;

        /// <summary>
        /// Cost price (for profit calculation)
        /// </summary>
        public Money? Cost { get; set; }

        /// <summary>
        /// Sale price (if on sale)
        /// </summary>
        public Money? SalePrice { get; set; }

        /// <summary>
        /// Default currency for this product
        /// </summary>
        public ValueObjects.Currency DefaultCurrency { get; set; } = null!;

        #endregion

        #region Images

        /// <summary>
        /// Main product image URL
        /// </summary>
        public string? MainImage { get; set; }

        /// <summary>
        /// Thumbnail image URL
        /// </summary>
        public string? ThumbnailImage { get; set; }

        #endregion

        #region Status & Type

        /// <summary>
        /// Product status
        /// </summary>
        public ProductStatus Status { get; set; } = ProductStatus.Draft;

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Type { get; set; } = ProductType.Simple;

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// Whether product is digital (no shipping required)
        /// </summary>
        public bool IsDigital { get; set; } = false;

        #endregion

        #region SEO & Marketing

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags (comma separated)
        /// </summary>
        public string? Tags { get; set; }

        #endregion

        #region Dates

        /// <summary>
        /// Sale start date
        /// </summary>
        public DateTime? SaleStartDate { get; set; }

        /// <summary>
        /// Sale end date
        /// </summary>
        public DateTime? SaleEndDate { get; set; }

        /// <summary>
        /// Product launch date
        /// </summary>
        public DateTime? LaunchDate { get; set; }

        /// <summary>
        /// Product discontinue date
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Product descriptions in different languages
        /// </summary>
        public ICollection<ProductDescription> Descriptions { get; set; } = new List<ProductDescription>();

        /// <summary>
        /// Product categories (many-to-many)
        /// </summary>
        public ICollection<ProductCategory> ProductCategories { get; set; } = new List<ProductCategory>();

        /// <summary>
        /// Product images
        /// </summary>
        public ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();

        /// <summary>
        /// Product discounts
        /// </summary>
        public ICollection<ProductDiscount> Discounts { get; set; } = new List<ProductDiscount>();

        /// <summary>
        /// Warehouse products (stock per warehouse)
        /// </summary>
        public ICollection<WarehouseProduct> WarehouseProducts { get; set; } = new List<WarehouseProduct>();

        /// <summary>
        /// Product variants (if variable product)
        /// </summary>
        public ICollection<ProductVariant> Variants { get; set; } = new List<ProductVariant>();

        /// <summary>
        /// Product attributes
        /// </summary>
        public ICollection<ProductAttribute> Attributes { get; set; } = new List<ProductAttribute>();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the effective price (sale price if on sale, otherwise base price)
        /// </summary>
        public Money EffectivePrice
        {
            get
            {
                if (IsOnSale && SalePrice != null)
                    return SalePrice;
                return BasePrice;
            }
        }

        /// <summary>
        /// Checks if product is currently on sale
        /// </summary>
        public bool IsOnSale
        {
            get
            {
                if (SalePrice == null) return false;
                var now = DateTime.UtcNow;
                return (SaleStartDate == null || SaleStartDate <= now) &&
                       (SaleEndDate == null || SaleEndDate >= now);
            }
        }



        /// <summary>
        /// Gets the discount percentage if on sale
        /// </summary>
        public decimal? DiscountPercentage
        {
            get
            {
                if (!IsOnSale || SalePrice == null) return null;
                if (BasePrice.Amount == 0) return null;

                var discount = BasePrice.Amount - SalePrice.Amount;
                return Math.Round((discount / BasePrice.Amount) * 100, 2);
            }
        }

        /// <summary>
        /// Gets the profit margin if cost is set
        /// </summary>
        public decimal? ProfitMargin
        {
            get
            {
                if (Cost == null || EffectivePrice.Amount == 0) return null;
                var profit = EffectivePrice.Amount - Cost.Amount;
                return Math.Round((profit / EffectivePrice.Amount) * 100, 2);
            }
        }

        /// <summary>
        /// Gets display name (Name or Model if Name is empty)
        /// </summary>
        public string DisplayName => !string.IsNullOrWhiteSpace(Name) ? Name : Model;

        #endregion

        #region Business Methods

        /// <summary>
        /// Sets the product on sale
        /// </summary>
        public void SetOnSale(Money salePrice, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (salePrice.Currency.Code != BasePrice.Currency.Code)
                throw new ArgumentException("Sale price currency must match base price currency");

            SalePrice = salePrice;
            SaleStartDate = startDate;
            SaleEndDate = endDate;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes the product from sale
        /// </summary>
        public void RemoveFromSale()
        {
            SalePrice = null;
            SaleStartDate = null;
            SaleEndDate = null;
            MarkAsUpdated();
        }



        /// <summary>
        /// Publishes the product
        /// </summary>
        public void Publish()
        {
            Status = ProductStatus.Published;
            if (LaunchDate == null)
                LaunchDate = DateTime.UtcNow;
            MarkAsUpdated();
        }

        /// <summary>
        /// Unpublishes the product
        /// </summary>
        public void Unpublish()
        {
            Status = ProductStatus.Draft;
            MarkAsUpdated();
        }

        /// <summary>
        /// Discontinues the product
        /// </summary>
        public void Discontinue()
        {
            Status = ProductStatus.Discontinued;
            DiscontinueDate = DateTime.UtcNow;
            MarkAsUpdated();
        }

        /// <summary>
        /// Generates SEO-friendly slug from name
        /// </summary>
        public void GenerateSlug()
        {
            if (string.IsNullOrWhiteSpace(Name)) return;

            Slug = Name.ToLowerInvariant()
                      .Replace(" ", "-")
                      .Replace("_", "-")
                      .Trim('-');

            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"Product [Id: {Id}, Model: {Model}, Name: {DisplayName}, Price: {EffectivePrice}]";
        }
    }

    /// <summary>
    /// Product status enumeration
    /// </summary>
    public enum ProductStatus
    {
        Draft = 0,
        Published = 1,
        Discontinued = 2,
        OutOfStock = 3
    }

    /// <summary>
    /// Product type enumeration
    /// </summary>
    public enum ProductType
    {
        Simple = 0,
        Variable = 1,
        Grouped = 2,
        Digital = 3
    }
}
