﻿using EtyraApp.Domain.Entities.Catalog;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.RelationsTable;

public class ProductEmagCharacteristic
{
    public int ProductId { get; set; }

    public int? ParentCategoryId { get; set; }

    [MaxLength(50)]
    public string CharacteristicsGlobalName { get; set; }

    [MaxLength(50)]
    public string CharacteristicsROName { get; set; }

    public int? ParentCharacteristicsId { get; set; }

    [MaxLength(250)]
    public string? EmagProductName { get; set; }
    public string? EmagProductDescription { get; set; }

    [MaxLength(500)]
    public string? Image { get; set; }

    public decimal EmagSpecialPrice { get; set; }

    public bool Required { get; set; } = false;

    public Product Product { get; set; }

}