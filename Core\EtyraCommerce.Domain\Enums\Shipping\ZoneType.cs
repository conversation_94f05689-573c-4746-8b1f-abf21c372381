namespace EtyraCommerce.Domain.Enums.Shipping
{
    /// <summary>
    /// Shipping zone types for European market
    /// </summary>
    public enum ZoneType
    {
        /// <summary>
        /// Domestic shipping within Romania
        /// </summary>
        Domestic = 1,

        /// <summary>
        /// EU Zone 1 - Close neighbors and major markets
        /// </summary>
        EuZone1 = 2,

        /// <summary>
        /// EU Zone 2 - Farther EU countries
        /// </summary>
        EuZone2 = 3,

        /// <summary>
        /// International - Non-EU countries
        /// </summary>
        International = 4
    }
}
