namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Many-to-many relationship between Product and Category
    /// </summary>
    public class ProductCategory : BaseEntity
    {
        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Category ID
        /// </summary>
        public Guid CategoryId { get; set; }

        /// <summary>
        /// Whether this is the primary category for the product
        /// </summary>
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// Sort order within the category
        /// </summary>
        public int SortOrder { get; set; } = 0;

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Related category
        /// </summary>
        public Category.Category Category { get; set; } = null!;

        #endregion

        #region Business Methods

        /// <summary>
        /// Sets this as the primary category
        /// </summary>
        public void SetAsPrimary()
        {
            IsPrimary = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes primary status
        /// </summary>
        public void RemovePrimary()
        {
            IsPrimary = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int order)
        {
            SortOrder = order;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"ProductCategory [ProductId: {ProductId}, CategoryId: {CategoryId}, IsPrimary: {IsPrimary}]";
        }
    }
}
