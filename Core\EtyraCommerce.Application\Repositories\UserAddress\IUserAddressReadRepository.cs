using EtyraCommerce.Domain.Entities.User;

namespace EtyraCommerce.Application.Repositories.UserAddress
{
    /// <summary>
    /// Read repository interface for UserAddress entity
    /// Provides read-only operations for user addresses
    /// </summary>
    public interface IUserAddressReadRepository : IReadRepository<Domain.Entities.User.UserAddress>
    {
        /// <summary>
        /// Gets all addresses for a specific user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of user addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Gets addresses for a specific user filtered by type
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="addressType">Type of address to filter by</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of user addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByTypeAsync(Guid userId, AddressType addressType, bool includeInactive = false);

        /// <summary>
        /// Gets the default address for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="addressType">Optional filter by address type</param>
        /// <returns>Default user address or null if not found</returns>
        Task<Domain.Entities.User.UserAddress?> GetDefaultAddressAsync(Guid userId, AddressType? addressType = null);

        /// <summary>
        /// Gets addresses suitable for billing for a specific user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of billing addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetBillingAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Gets addresses suitable for shipping for a specific user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of shipping addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetShippingAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Gets a specific address by ID and validates ownership
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user (for ownership validation)</param>
        /// <returns>User address or null if not found or not owned by user</returns>
        Task<Domain.Entities.User.UserAddress?> GetUserAddressByIdAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Checks if a user has any addresses
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>True if user has addresses, false otherwise</returns>
        Task<bool> UserHasAddressesAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Gets the count of addresses for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>Number of addresses</returns>
        Task<int> GetUserAddressCountAsync(Guid userId, bool includeInactive = false);

        /// <summary>
        /// Checks if an address belongs to a specific user
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>True if address belongs to user, false otherwise</returns>
        Task<bool> AddressBelongsToUserAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Gets addresses by multiple IDs for a specific user
        /// </summary>
        /// <param name="addressIds">List of address IDs</param>
        /// <param name="userId">ID of the user (for ownership validation)</param>
        /// <returns>List of user addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByIdsAsync(List<Guid> addressIds, Guid userId);

        /// <summary>
        /// Searches addresses by text (street, city, label, etc.)
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="searchText">Text to search for</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of matching addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> SearchUserAddressesAsync(Guid userId, string searchText, bool includeInactive = false);

        /// <summary>
        /// Gets addresses by country for a specific user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="country">Country to filter by</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of addresses in the specified country</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByCountryAsync(Guid userId, string country, bool includeInactive = false);

        /// <summary>
        /// Gets addresses by city for a specific user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="city">City to filter by</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of addresses in the specified city</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetUserAddressesByCityAsync(Guid userId, string city, bool includeInactive = false);

        /// <summary>
        /// Gets the most recently used addresses for a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="count">Number of recent addresses to return</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of recently used addresses</returns>
        Task<List<Domain.Entities.User.UserAddress>> GetRecentUserAddressesAsync(Guid userId, int count = 5, bool includeInactive = false);
    }
}
