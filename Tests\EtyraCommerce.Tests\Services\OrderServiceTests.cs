using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Persistence.Services.Order;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Services
{
    public class OrderServiceTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<ILogger<OrderService>> _mockLogger;
        private readonly OrderService _orderService;

        public OrderServiceTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockLogger = new Mock<ILogger<OrderService>>();
            _orderService = new OrderService(_mockMediator.Object, _mockLogger.Object);
        }

        #region GetOrderByIdAsync Tests

        [Fact]
        public async Task GetOrderByIdAsync_ValidId_ReturnsSuccessResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var expectedOrderDto = new OrderDto
            {
                Id = orderId,
                OrderNumber = "ORD-20241201-ABC123",
                CustomerId = Guid.NewGuid(),
                CustomerEmail = "<EMAIL>",
                Status = OrderStatus.Confirmed,
                Total = 100.00m,
                Currency = "USD"
            };

            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, expectedOrderDto, "Order retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrderByIdAsync(orderId, true, false, "en-US");

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be(orderId);

            _mockMediator.Verify(m => m.Send(It.Is<GetOrderByIdQuery>(q =>
                q.OrderId == orderId &&
                q.IncludeItems == true &&
                q.IncludeCustomer == false &&
                q.LanguageCode == "en-US"
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetOrderByIdAsync_NonExistentId_ReturnsNotFoundResponse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<OrderDto>.NotFound("Order not found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrderByIdAsync(orderId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockMediator.Verify(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region SearchOrdersAsync Tests

        [Fact]
        public async Task SearchOrdersAsync_ValidSearchDto_ReturnsPagedResults()
        {
            // Arrange
            var searchDto = new OrderSearchDto
            {
                SearchTerm = "ORD-123",
                PageNumber = 1,
                PageSize = 10,
                Status = OrderStatus.Confirmed,
                IncludeItems = true
            };

            var orderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                OrderNumber = "ORD-20241201-123456",
                Status = OrderStatus.Confirmed,
                Total = 150.00m
            };

            var pagedResult = PagedResult<OrderDto>.Create(
                new List<OrderDto> { orderDto },
                1, // totalCount
                1, // pageNumber
                10 // pageSize
            );

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.SearchOrdersAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().HaveCount(1);
            result.Data.TotalCount.Should().Be(1);

            _mockMediator.Verify(m => m.Send(It.Is<SearchOrdersQuery>(q =>
                q.SearchTerm == searchDto.SearchTerm &&
                q.Page == searchDto.PageNumber &&
                q.PageSize == searchDto.PageSize &&
                q.Status == searchDto.Status &&
                q.IncludeItems == searchDto.IncludeItems
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task SearchOrdersAsync_EmptyResults_ReturnsEmptyPagedResult()
        {
            // Arrange
            var searchDto = new OrderSearchDto
            {
                SearchTerm = "NonExistent",
                PageNumber = 1,
                PageSize = 10
            };

            var pagedResult = PagedResult<OrderDto>.Empty(1, 10);

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.SearchOrdersAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().BeEmpty();
            result.Data.TotalCount.Should().Be(0);

            _mockMediator.Verify(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetOrderStatisticsAsync Tests

        [Fact]
        public async Task GetOrderStatisticsAsync_ValidParameters_ReturnsStatistics()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var customerId = Guid.NewGuid();

            var expectedStatistics = new OrderStatisticsDto
            {
                TotalOrders = 100,
                TotalRevenue = 10000.00m,
                AverageOrderValue = 100.00m
            };

            var expectedResponse = CustomResponseDto<OrderStatisticsDto>.Success(200, expectedStatistics, "Statistics retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderStatisticsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrderStatisticsAsync(startDate, endDate, customerId, "USD", 10, 5);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.TotalOrders.Should().Be(100);
            result.Data.TotalRevenue.Should().Be(10000.00m);

            _mockMediator.Verify(m => m.Send(It.Is<GetOrderStatisticsQuery>(q =>
                q.StartDate == startDate &&
                q.EndDate == endDate &&
                q.CustomerId == customerId &&
                q.Currency == "USD" &&
                q.TopCustomersCount == 10 &&
                q.RecentOrdersCount == 5
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetOrdersByCustomerIdAsync Tests

        [Fact]
        public async Task GetOrdersByCustomerIdAsync_ValidCustomerId_ReturnsCustomerOrders()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var orderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                CustomerId = customerId,
                OrderNumber = "ORD-20241201-ABC123",
                Total = 100.00m
            };

            var pagedResult = PagedResult<OrderDto>.Create(
                new List<OrderDto> { orderDto },
                1, 1, 10
            );

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Customer orders retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetUserOrdersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrdersByCustomerIdAsync(customerId, 1, 10);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().HaveCount(1);
            result.Data.Items.First().CustomerId.Should().Be(customerId);

            _mockMediator.Verify(m => m.Send(It.Is<GetUserOrdersQuery>(q =>
                q.UserId == customerId &&
                q.Page == 1 &&
                q.PageSize == 10 &&
                q.IncludeItems == false
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetOrderByOrderNumberAsync Tests

        [Fact]
        public async Task GetOrderByOrderNumberAsync_ValidOrderNumber_ReturnsOrder()
        {
            // Arrange
            var orderNumber = "ORD-20241201-ABC123";
            var orderDto = new OrderDto
            {
                Id = Guid.NewGuid(),
                OrderNumber = orderNumber,
                Total = 100.00m
            };

            var pagedResult = PagedResult<OrderDto>.Create(
                new List<OrderDto> { orderDto },
                1, 1, 1
            );

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrderByOrderNumberAsync(orderNumber, true);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.OrderNumber.Should().Be(orderNumber);

            _mockMediator.Verify(m => m.Send(It.Is<SearchOrdersQuery>(q =>
                q.SearchTerm == orderNumber &&
                q.Page == 1 &&
                q.PageSize == 1 &&
                q.IncludeItems == true
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetOrderByOrderNumberAsync_NonExistentOrderNumber_ReturnsNotFound()
        {
            // Arrange
            var orderNumber = "ORD-NONEXISTENT";
            var pagedResult = PagedResult<OrderDto>.Empty(1, 1);

            var expectedResponse = CustomResponseDto<PagedResult<OrderDto>>.Success(200, pagedResult, "Orders retrieved successfully");

            _mockMediator.Setup(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.GetOrderByOrderNumberAsync(orderNumber);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(404);
            result.Message.Should().Be("Order not found");

            _mockMediator.Verify(m => m.Send(It.IsAny<SearchOrdersQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region OrderExistsAsync Tests

        [Fact]
        public async Task OrderExistsAsync_ExistingOrder_ReturnsTrue()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var orderDto = new OrderDto { Id = orderId };
            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, orderDto, "Order found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.OrderExistsAsync(orderId);

            // Assert
            result.Should().BeTrue();

            _mockMediator.Verify(m => m.Send(It.Is<GetOrderByIdQuery>(q =>
                q.OrderId == orderId &&
                q.IncludeItems == false &&
                q.IncludeCustomer == false
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task OrderExistsAsync_NonExistentOrder_ReturnsFalse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<OrderDto>.NotFound("Order not found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.OrderExistsAsync(orderId);

            // Assert
            result.Should().BeFalse();

            _mockMediator.Verify(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region OrderBelongsToCustomerAsync Tests

        [Fact]
        public async Task OrderBelongsToCustomerAsync_OrderBelongsToCustomer_ReturnsTrue()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var customerId = Guid.NewGuid();
            var orderDto = new OrderDto
            {
                Id = orderId,
                CustomerId = customerId
            };
            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, orderDto, "Order found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.OrderBelongsToCustomerAsync(orderId, customerId);

            // Assert
            result.Should().BeTrue();

            _mockMediator.Verify(m => m.Send(It.Is<GetOrderByIdQuery>(q =>
                q.OrderId == orderId &&
                q.IncludeItems == false &&
                q.IncludeCustomer == false
            ), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task OrderBelongsToCustomerAsync_OrderBelongsToDifferentCustomer_ReturnsFalse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var customerId = Guid.NewGuid();
            var differentCustomerId = Guid.NewGuid();
            var orderDto = new OrderDto
            {
                Id = orderId,
                CustomerId = differentCustomerId
            };
            var expectedResponse = CustomResponseDto<OrderDto>.Success(200, orderDto, "Order found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.OrderBelongsToCustomerAsync(orderId, customerId);

            // Assert
            result.Should().BeFalse();

            _mockMediator.Verify(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task OrderBelongsToCustomerAsync_OrderNotFound_ReturnsFalse()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var customerId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<OrderDto>.NotFound("Order not found");

            _mockMediator.Setup(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _orderService.OrderBelongsToCustomerAsync(orderId, customerId);

            // Assert
            result.Should().BeFalse();

            _mockMediator.Verify(m => m.Send(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion
    }
}
