namespace EtyraCommerce.Domain.Entities
{
    /// <summary>
    /// Base entity class with audit information
    /// Extends BaseEntity with user tracking capabilities
    /// </summary>
    public abstract class AuditableBaseEntity : BaseEntity, IAuditableEntity
    {
        /// <summary>
        /// ID of the user who created this entity
        /// </summary>
        public Guid? CreatedBy { get; set; }

        /// <summary>
        /// ID of the user who last updated this entity
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        /// <summary>
        /// ID of the user who deleted this entity (for soft deletes)
        /// </summary>
        public Guid? DeletedBy { get; set; }

        /// <summary>
        /// Sets the audit information for entity creation
        /// </summary>
        /// <param name="userId">ID of the user creating the entity</param>
        public virtual void SetCreatedBy(Guid userId)
        {
            CreatedBy = userId;
            UpdatedBy = userId; // Creator is also the first updater
        }

        /// <summary>
        /// Sets the audit information for entity update
        /// </summary>
        /// <param name="userId">ID of the user updating the entity</param>
        public virtual void SetUpdatedBy(Guid userId)
        {
            UpdatedBy = userId;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets the audit information for entity deletion
        /// </summary>
        /// <param name="userId">ID of the user deleting the entity</param>
        public virtual void SetDeletedBy(Guid userId)
        {
            DeletedBy = userId;
            MarkAsDeleted();
        }

        /// <summary>
        /// Override the base MarkAsDeleted to include user information
        /// </summary>
        public override void MarkAsDeleted()
        {
            base.MarkAsDeleted();
            // DeletedBy should be set separately using SetDeletedBy method
        }

        /// <summary>
        /// Override the base Restore method to clear deleted by information
        /// </summary>
        public override void Restore()
        {
            base.Restore();
            DeletedBy = null;
        }

        /// <summary>
        /// Gets the name of the user who created this entity
        /// This is a placeholder - actual implementation would require user service
        /// </summary>
        public string CreatedByName => CreatedBy?.ToString() ?? "System";

        /// <summary>
        /// Gets the name of the user who last updated this entity
        /// This is a placeholder - actual implementation would require user service
        /// </summary>
        public string UpdatedByName => UpdatedBy?.ToString() ?? "System";

        /// <summary>
        /// Gets the name of the user who deleted this entity
        /// This is a placeholder - actual implementation would require user service
        /// </summary>
        public string DeletedByName => DeletedBy?.ToString() ?? "System";

        /// <summary>
        /// String representation including audit information
        /// </summary>
        public override string ToString()
        {
            var baseString = base.ToString();
            return $"{baseString} [CreatedBy: {CreatedByName}, UpdatedBy: {UpdatedByName}]";
        }
    }
}
