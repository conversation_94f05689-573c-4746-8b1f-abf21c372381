using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Commands;
using EtyraCommerce.Application.Services.User.Queries;
using EtyraCommerce.Persistence.Services.User;
using FluentAssertions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Services
{
    public class UserServiceTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<ILogger<UserService>> _mockLogger;
        private readonly UserService _service;

        public UserServiceTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockLogger = new Mock<ILogger<UserService>>();
            _service = new UserService(_mockMediator.Object, _mockLogger.Object);
        }

        #region LoginAsync Tests

        [Fact]
        public async Task LoginAsync_ValidLoginDto_SendsLoginCommandAndReturnsResult()
        {
            // Arrange
            var loginDto = new UserLoginDto
            {
                EmailOrUsername = "<EMAIL>",
                Password = "ValidPassword123!",
                RememberMe = true
            };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                Username = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            var expectedResponse = CustomResponseDto<UserDto>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "Login successful"
            );

            _mockMediator
                .Setup(x => x.Send(It.Is<LoginUserCommand>(cmd =>
                    cmd.EmailOrUsername == loginDto.EmailOrUsername &&
                    cmd.Password == loginDto.Password &&
                    cmd.RememberMe == loginDto.RememberMe
                ), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _service.LoginAsync(loginDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(expectedUserDto.Id);
            result.Data.Email.Should().Be(expectedUserDto.Email);

            _mockMediator.Verify(
                x => x.Send(It.Is<LoginUserCommand>(cmd =>
                    cmd.EmailOrUsername == loginDto.EmailOrUsername &&
                    cmd.Password == loginDto.Password &&
                    cmd.RememberMe == loginDto.RememberMe
                ), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task LoginAsync_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var loginDto = new UserLoginDto
            {
                EmailOrUsername = "<EMAIL>",
                Password = "ValidPassword123!",
                RememberMe = false
            };

            _mockMediator
                .Setup(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Handler failed"));

            // Act
            var result = await _service.LoginAsync(loginDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during login");

            _mockMediator.Verify(
                x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        #endregion

        #region RegisterAsync Tests

        [Fact]
        public async Task RegisterAsync_ValidRegisterDto_SendsRegisterCommandAndReturnsResult()
        {
            // Arrange
            var registerDto = new CreateUserDto
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                PhoneNumber = "+1234567890",
                AcceptTerms = true,
                Culture = "en-US",
                TimeZone = "UTC"
            };

            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = registerDto.Email,
                Username = registerDto.Username,
                FirstName = registerDto.FirstName,
                LastName = registerDto.LastName
            };

            var expectedResponse = CustomResponseDto<UserDto>.Created(
                expectedUserDto,
                "User registered successfully"
            );

            _mockMediator
                .Setup(x => x.Send(It.Is<RegisterUserCommand>(cmd =>
                    cmd.FirstName == registerDto.FirstName &&
                    cmd.LastName == registerDto.LastName &&
                    cmd.Email == registerDto.Email &&
                    cmd.Username == registerDto.Username &&
                    cmd.Password == registerDto.Password &&
                    cmd.ConfirmPassword == registerDto.ConfirmPassword &&
                    cmd.PhoneNumber == registerDto.PhoneNumber &&
                    cmd.AcceptTerms == registerDto.AcceptTerms &&
                    cmd.Culture == registerDto.Culture &&
                    cmd.TimeZone == registerDto.TimeZone
                ), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _service.RegisterAsync(registerDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
            result.Data.Email.Should().Be(registerDto.Email);
            result.Data.Username.Should().Be(registerDto.Username);

            _mockMediator.Verify(
                x => x.Send(It.Is<RegisterUserCommand>(cmd =>
                    cmd.FirstName == registerDto.FirstName &&
                    cmd.LastName == registerDto.LastName &&
                    cmd.Email == registerDto.Email &&
                    cmd.Username == registerDto.Username &&
                    cmd.Password == registerDto.Password &&
                    cmd.ConfirmPassword == registerDto.ConfirmPassword &&
                    cmd.PhoneNumber == registerDto.PhoneNumber &&
                    cmd.AcceptTerms == registerDto.AcceptTerms &&
                    cmd.Culture == registerDto.Culture &&
                    cmd.TimeZone == registerDto.TimeZone
                ), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task RegisterAsync_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var registerDto = new CreateUserDto
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Username = "johndoe",
                Password = "SecurePassword123!",
                ConfirmPassword = "SecurePassword123!",
                AcceptTerms = true
            };

            _mockMediator
                .Setup(x => x.Send(It.IsAny<RegisterUserCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Handler failed"));

            // Act
            var result = await _service.RegisterAsync(registerDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during registration");

            _mockMediator.Verify(
                x => x.Send(It.IsAny<RegisterUserCommand>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        #endregion

        #region GetUserByEmailAsync Tests

        [Fact]
        public async Task GetUserByEmailAsync_ValidEmail_SendsQueryAndReturnsResult()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedUserDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = email,
                Username = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            var expectedResponse = CustomResponseDto<UserDto?>.Success(
                StatusCodes.Status200OK,
                expectedUserDto,
                "User found"
            );

            _mockMediator
                .Setup(x => x.Send(It.Is<GetUserByEmailQuery>(q => q.Email == email), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _service.GetUserByEmailAsync(email);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data.Email.Should().Be(email);

            _mockMediator.Verify(
                x => x.Send(It.Is<GetUserByEmailQuery>(q => q.Email == email), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task GetUserByEmailAsync_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var email = "<EMAIL>";

            _mockMediator
                .Setup(x => x.Send(It.IsAny<GetUserByEmailQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Handler failed"));

            // Act
            var result = await _service.GetUserByEmailAsync(email);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred while retrieving user");

            _mockMediator.Verify(
                x => x.Send(It.IsAny<GetUserByEmailQuery>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        #endregion

        #region EmailExistsAsync Tests

        [Fact]
        public async Task EmailExistsAsync_ValidEmail_SendsQueryAndReturnsResult()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedResponse = CustomResponseDto<bool>.Success(
                StatusCodes.Status200OK,
                true,
                "Email exists"
            );

            _mockMediator
                .Setup(x => x.Send(It.Is<ValidateUserExistsQuery>(q => q.Email == email), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _service.EmailExistsAsync(email);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeTrue();

            _mockMediator.Verify(
                x => x.Send(It.Is<ValidateUserExistsQuery>(q => q.Email == email), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task EmailExistsAsync_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var email = "<EMAIL>";

            _mockMediator
                .Setup(x => x.Send(It.IsAny<ValidateUserExistsQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Handler failed"));

            // Act
            var result = await _service.EmailExistsAsync(email);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during validation");

            _mockMediator.Verify(
                x => x.Send(It.IsAny<ValidateUserExistsQuery>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        #endregion

        #region ActivateUserAsync Tests

        [Fact]
        public async Task ActivateUserAsync_ValidUserId_SendsCommandAndReturnsResult()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var expectedResponse = CustomResponseDto<NoContentDto>.Success(
                StatusCodes.Status200OK,
                "User activated successfully"
            );

            _mockMediator
                .Setup(x => x.Send(It.Is<ActivateUserCommand>(cmd => cmd.UserId == userId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _service.ActivateUserAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Message.Should().Be("User activated successfully");

            _mockMediator.Verify(
                x => x.Send(It.Is<ActivateUserCommand>(cmd => cmd.UserId == userId), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task ActivateUserAsync_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();

            _mockMediator
                .Setup(x => x.Send(It.IsAny<ActivateUserCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Handler failed"));

            // Act
            var result = await _service.ActivateUserAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
            result.Message.Should().Be("An error occurred during user activation");

            _mockMediator.Verify(
                x => x.Send(It.IsAny<ActivateUserCommand>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        #endregion
    }
}
