# EtyraCommerce Docker Setup

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed
- Docker Compose v2.0+

### 1. Start Services
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### 2. Access Services

| Service | URL | Credentials |
|---------|-----|-------------|
| PostgreSQL | `localhost:5432` | User: `etyra`, Password: `******************` |
| Redis | `localhost:6379` | Password: `EtyraRedis2024!` |
| pgAdmin | `http://localhost:8080` | Email: `<EMAIL>`, Password: `EtyraAdmin2024!` |
| Redis Commander | `http://localhost:8081` | User: `admin`, Password: `EtyraRedis2024!` |

### 3. Connection Strings

**PostgreSQL:**
```
Host=localhost;Port=5432;Database=etyracommerce;Username=etyra;Password=******************
```

**Redis:**
```
localhost:6379,password=EtyraRedis2024!
```

## 🔧 Management Commands

### Database Operations
```bash
# Connect to PostgreSQL
docker exec -it etyra-postgres psql -U etyra -d etyracommerce

# Backup database
docker exec etyra-postgres pg_dump -U etyra etyracommerce > backup.sql

# Restore database
docker exec -i etyra-postgres psql -U etyra -d etyracommerce < backup.sql
```

### Redis Operations
```bash
# Connect to Redis CLI
docker exec -it etyra-redis redis-cli -a EtyraRedis2024!

# Monitor Redis
docker exec -it etyra-redis redis-cli -a EtyraRedis2024! monitor

# Clear all Redis data
docker exec -it etyra-redis redis-cli -a EtyraRedis2024! FLUSHALL
```

### Container Management
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (⚠️ Data will be lost)
docker-compose down -v

# Restart specific service
docker-compose restart etyra-postgres

# View service logs
docker-compose logs etyra-postgres
docker-compose logs etyra-redis
```

## 📊 Data Persistence

Data is persisted in Docker volumes:
- `postgres_data`: PostgreSQL data
- `redis_data`: Redis data
- `pgadmin_data`: pgAdmin settings

## 🔒 Security Notes

**Default passwords are for development only!**

For production:
1. Change all passwords in `docker-compose.yml`
2. Use environment variables
3. Enable SSL/TLS
4. Configure firewall rules

## 🐛 Troubleshooting

### Port Conflicts
If ports are already in use, modify `docker-compose.yml`:
```yaml
ports:
  - "5433:5432"  # PostgreSQL
  - "6380:6379"  # Redis
```

### Permission Issues
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./docker
```

### Health Check Failed
```bash
# Check container health
docker-compose ps
docker inspect etyra-postgres --format='{{.State.Health.Status}}'
```

## 📝 Environment Variables

Copy `.env.example` to `.env` and modify as needed:
```bash
cp .env.example .env
```

## 🔄 Updates

```bash
# Pull latest images
docker-compose pull

# Recreate containers with new images
docker-compose up -d --force-recreate
```
