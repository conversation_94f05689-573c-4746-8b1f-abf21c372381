using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserOrdersQuery
    /// </summary>
    public class GetUserOrdersQueryHandler : IRequestHandler<GetUserOrdersQuery, CustomResponseDto<PagedResult<OrderDto>>>
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<GetUserOrdersQueryHandler> _logger;

        public GetUserOrdersQueryHandler(
            IOrderService orderService,
            ILogger<GetUserOrdersQueryHandler> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<OrderDto>>> Handle(GetUserOrdersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get user orders query for user: {UserId}", request.UserId);

                // Validation
                if (request.UserId == Guid.Empty)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("User ID is required");

                if (request.Page < 1)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<OrderDto>>.BadRequest("Page size must be between 1 and 100");

                // Create search criteria
                var searchDto = new OrderSearchDto
                {
                    CustomerId = request.UserId,
                    Status = request.Status,
                    PaymentStatus = request.PaymentStatus,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    IncludeItems = request.IncludeItems,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    Page = request.Page,
                    PageSize = request.PageSize
                };

                // Delegate to service
                var result = await _orderService.SearchOrdersAsync(searchDto);

                _logger.LogInformation("Get user orders query processed successfully for user: {UserId}", request.UserId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get user orders query for user: {UserId}", request.UserId);
                return CustomResponseDto<PagedResult<OrderDto>>.InternalServerError("An error occurred while retrieving user orders");
            }
        }
    }
}
