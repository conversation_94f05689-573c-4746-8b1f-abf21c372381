﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "etyra_core");

            migrationBuilder.CreateTable(
                name: "categories",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    slug = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    parent_category_id = table.Column<Guid>(type: "uuid", nullable: true),
                    image_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    icon = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    sort_order = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    show_in_menu = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    meta_title = table.Column<string>(type: "character varying(120)", maxLength: 120, nullable: true),
                    meta_description = table.Column<string>(type: "character varying(350)", maxLength: 350, nullable: true),
                    meta_keywords = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    product_count = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_categories", x => x.id);
                    table.CheckConstraint("CK_Categories_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                    table.CheckConstraint("CK_Categories_NoSelfReference", "parent_category_id != id");
                    table.CheckConstraint("CK_Categories_ProductCount_NonNegative", "product_count >= 0");
                    table.CheckConstraint("CK_Categories_Slug_NotEmpty", "LENGTH(TRIM(slug)) > 0");
                    table.CheckConstraint("CK_Categories_SortOrder_NonNegative", "sort_order >= 0");
                    table.ForeignKey(
                        name: "FK_categories_categories_parent_category_id",
                        column: x => x.parent_category_id,
                        principalSchema: "etyra_core",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "example_entities",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    phone_number = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    optional_price_json = table.Column<string>(type: "jsonb", nullable: true),
                    preferred_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_example_entities", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "products",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    model = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    stars = table.Column<int>(type: "integer", nullable: true),
                    ai_platform_id = table.Column<int>(type: "integer", nullable: true),
                    ean = table.Column<string>(type: "character varying(13)", maxLength: 13, nullable: true),
                    mpn = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    barcode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    brand = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    upc = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    sku = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    dimensions = table.Column<string>(type: "jsonb", nullable: true),
                    base_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    base_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    cost_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    cost_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    sale_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    sale_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    default_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    main_image = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    thumbnail_image = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    total_stock_quantity = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    min_stock_alert = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    status = table.Column<int>(type: "integer", nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    manage_stock = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    is_featured = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    is_digital = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    slug = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    meta_title = table.Column<string>(type: "character varying(120)", maxLength: 120, nullable: true),
                    meta_description = table.Column<string>(type: "character varying(350)", maxLength: 350, nullable: true),
                    meta_keywords = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    tags = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    sale_start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    sale_end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    launch_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    discontinue_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_products", x => x.id);
                    table.CheckConstraint("CK_Products_MinStock_NonNegative", "min_stock_alert >= 0");
                    table.CheckConstraint("CK_Products_Model_NotEmpty", "LENGTH(TRIM(model)) > 0");
                    table.CheckConstraint("CK_Products_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                    table.CheckConstraint("CK_Products_SaleDates", "sale_start_date IS NULL OR sale_end_date IS NULL OR sale_start_date <= sale_end_date");
                    table.CheckConstraint("CK_Products_SKU_NotEmpty", "LENGTH(TRIM(sku)) > 0");
                    table.CheckConstraint("CK_Products_Stars_Range", "stars IS NULL OR (stars >= 1 AND stars <= 5)");
                    table.CheckConstraint("CK_Products_Stock_NonNegative", "total_stock_quantity >= 0");
                });

            migrationBuilder.CreateTable(
                name: "users",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    first_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    last_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    phone_number = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    username = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    password_hash = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    password_salt = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    is_enabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    is_email_confirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    is_phone_confirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    last_login_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    failed_login_attempts = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    locked_until = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    email_confirmation_token = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    password_reset_token = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    password_reset_token_expiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PasswordChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EmailConfirmationTokenExpiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PhoneConfirmationToken = table.Column<string>(type: "text", nullable: true),
                    PhoneConfirmationTokenExpiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    culture = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "en-US"),
                    time_zone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "UTC"),
                    profile_picture_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    refresh_token = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    refresh_token_expires_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    refresh_token_created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    refresh_token_created_by_ip = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    refresh_token_revoked_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    refresh_token_revoked_by_ip = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    refresh_token_revoked_reason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true),
                    deleted_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_users", x => x.id);
                    table.CheckConstraint("CK_Users_FailedLoginAttempts_NonNegative", "failed_login_attempts >= 0");
                    table.CheckConstraint("CK_Users_FirstName_NotEmpty", "LENGTH(TRIM(first_name)) > 0");
                    table.CheckConstraint("CK_Users_LastName_NotEmpty", "LENGTH(TRIM(last_name)) > 0");
                    table.CheckConstraint("CK_Users_Username_NotEmpty", "LENGTH(TRIM(username)) > 0");
                });

            migrationBuilder.CreateTable(
                name: "category_descriptions",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    meta_title = table.Column<string>(type: "character varying(120)", maxLength: 120, nullable: true),
                    meta_description = table.Column<string>(type: "character varying(350)", maxLength: 350, nullable: true),
                    meta_keywords = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    slug = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    language_code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "en-US"),
                    store_id = table.Column<Guid>(type: "uuid", nullable: true),
                    category_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_category_descriptions", x => x.id);
                    table.CheckConstraint("CK_CategoryDescriptions_LanguageCode_NotEmpty", "LENGTH(TRIM(language_code)) > 0");
                    table.CheckConstraint("CK_CategoryDescriptions_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                    table.ForeignKey(
                        name: "FK_category_descriptions_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "etyra_core",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CategoryCategory",
                schema: "etyra_core",
                columns: table => new
                {
                    AncestorsId = table.Column<Guid>(type: "uuid", nullable: false),
                    DescendantsId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CategoryCategory", x => new { x.AncestorsId, x.DescendantsId });
                    table.ForeignKey(
                        name: "FK_CategoryCategory_categories_AncestorsId",
                        column: x => x.AncestorsId,
                        principalSchema: "etyra_core",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CategoryCategory_categories_DescendantsId",
                        column: x => x.DescendantsId,
                        principalSchema: "etyra_core",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_categories",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    category_id = table.Column<Guid>(type: "uuid", nullable: false),
                    is_primary = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    sort_order = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_categories", x => x.id);
                    table.CheckConstraint("CK_ProductCategories_SortOrder_NonNegative", "sort_order >= 0");
                    table.ForeignKey(
                        name: "FK_product_categories_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "etyra_core",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_product_categories_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_descriptions",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    short_description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    meta_title = table.Column<string>(type: "character varying(120)", maxLength: 120, nullable: true),
                    meta_description = table.Column<string>(type: "character varying(350)", maxLength: 350, nullable: true),
                    meta_keywords = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    tags = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    slug = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    language_code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "en-US"),
                    store_id = table.Column<Guid>(type: "uuid", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_descriptions", x => x.id);
                    table.CheckConstraint("CK_ProductDescriptions_Description_NotEmpty", "LENGTH(TRIM(description)) > 0");
                    table.CheckConstraint("CK_ProductDescriptions_LanguageCode_NotEmpty", "LENGTH(TRIM(language_code)) > 0");
                    table.CheckConstraint("CK_ProductDescriptions_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                    table.ForeignKey(
                        name: "FK_product_descriptions_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_discounts",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    value = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    min_quantity = table.Column<int>(type: "integer", nullable: true),
                    max_quantity = table.Column<int>(type: "integer", nullable: true),
                    min_order_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    min_order_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    max_discount_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    max_discount_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    max_uses = table.Column<int>(type: "integer", nullable: true),
                    current_uses = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    can_combine = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_discounts", x => x.id);
                    table.CheckConstraint("CK_ProductDiscounts_CurrentUses_NonNegative", "current_uses >= 0");
                    table.CheckConstraint("CK_ProductDiscounts_Date_Range", "start_date IS NULL OR end_date IS NULL OR start_date <= end_date");
                    table.CheckConstraint("CK_ProductDiscounts_MaxQuantity_Positive", "max_quantity IS NULL OR max_quantity > 0");
                    table.CheckConstraint("CK_ProductDiscounts_MaxUses_Positive", "max_uses IS NULL OR max_uses > 0");
                    table.CheckConstraint("CK_ProductDiscounts_MinQuantity_Positive", "min_quantity IS NULL OR min_quantity > 0");
                    table.CheckConstraint("CK_ProductDiscounts_Name_NotEmpty", "LENGTH(TRIM(name)) > 0");
                    table.CheckConstraint("CK_ProductDiscounts_Percentage_Range", "type != 0 OR value <= 100");
                    table.CheckConstraint("CK_ProductDiscounts_Quantity_Range", "min_quantity IS NULL OR max_quantity IS NULL OR min_quantity <= max_quantity");
                    table.CheckConstraint("CK_ProductDiscounts_Uses_Range", "max_uses IS NULL OR current_uses <= max_uses");
                    table.CheckConstraint("CK_ProductDiscounts_Value_Positive", "value > 0");
                    table.ForeignKey(
                        name: "FK_product_discounts_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_images",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    image_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    thumbnail_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    alt_text = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    sort_order = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    is_main = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    file_size = table.Column<long>(type: "bigint", nullable: true),
                    width = table.Column<int>(type: "integer", nullable: true),
                    height = table.Column<int>(type: "integer", nullable: true),
                    mime_type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    original_file_name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_images", x => x.id);
                    table.CheckConstraint("CK_ProductImages_Dimensions_Positive", "width IS NULL OR width > 0");
                    table.CheckConstraint("CK_ProductImages_FileSize_Positive", "file_size IS NULL OR file_size > 0");
                    table.CheckConstraint("CK_ProductImages_Height_Positive", "height IS NULL OR height > 0");
                    table.CheckConstraint("CK_ProductImages_ImageUrl_NotEmpty", "LENGTH(TRIM(image_url)) > 0");
                    table.CheckConstraint("CK_ProductImages_SortOrder_NonNegative", "sort_order >= 0");
                    table.ForeignKey(
                        name: "FK_product_images_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_variants",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    cost_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    cost_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    stock_quantity = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    manage_stock = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    weight = table.Column<decimal>(type: "numeric(10,3)", nullable: true),
                    dimensions_length = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    dimensions_width = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    dimensions_height = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    dimensions_weight = table.Column<decimal>(type: "numeric(10,3)", nullable: true),
                    dimensions_unit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    dimensions_weight_unit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    image_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    sort_order = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    is_default = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    barcode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_variants", x => x.id);
                    table.CheckConstraint("ck_product_variants_cost_amount", "cost_amount IS NULL OR cost_amount >= 0");
                    table.CheckConstraint("ck_product_variants_price_amount", "price_amount IS NULL OR price_amount >= 0");
                    table.CheckConstraint("ck_product_variants_sort_order", "sort_order >= 0");
                    table.CheckConstraint("ck_product_variants_stock_quantity", "stock_quantity >= 0");
                    table.CheckConstraint("ck_product_variants_weight", "weight IS NULL OR weight >= 0");
                    table.ForeignKey(
                        name: "FK_product_variants_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductAttribute",
                schema: "etyra_core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Group = table.Column<string>(type: "text", nullable: true),
                    SortOrder = table.Column<int>(type: "integer", nullable: false),
                    IsVisible = table.Column<bool>(type: "boolean", nullable: false),
                    IsSearchable = table.Column<bool>(type: "boolean", nullable: false),
                    IsFilterable = table.Column<bool>(type: "boolean", nullable: false),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    Unit = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductAttribute", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductAttribute_products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "warehouse_products",
                schema: "etyra_core",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    cost_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    cost_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    stock_quantity = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    min_stock_alert = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    max_stock_capacity = table.Column<int>(type: "integer", nullable: true),
                    reserved_stock = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    status = table.Column<int>(type: "integer", nullable: false),
                    location_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    supplier_reference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    lead_time_days = table.Column<int>(type: "integer", nullable: true),
                    last_stock_update = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    last_stock_count = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_primary_warehouse = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    manage_stock = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    allow_backorders = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    warehouse_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    row_version = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_warehouse_products", x => x.id);
                    table.CheckConstraint("CK_WarehouseProducts_LeadTimeDays_NonNegative", "lead_time_days IS NULL OR lead_time_days >= 0");
                    table.CheckConstraint("CK_WarehouseProducts_MaxStockCapacity_Positive", "max_stock_capacity IS NULL OR max_stock_capacity > 0");
                    table.CheckConstraint("CK_WarehouseProducts_MinStockAlert_NonNegative", "min_stock_alert >= 0");
                    table.CheckConstraint("CK_WarehouseProducts_ReservedStock_NonNegative", "reserved_stock >= 0");
                    table.CheckConstraint("CK_WarehouseProducts_ReservedStock_Range", "reserved_stock <= stock_quantity");
                    table.CheckConstraint("CK_WarehouseProducts_StockCapacity_Range", "max_stock_capacity IS NULL OR stock_quantity <= max_stock_capacity");
                    table.CheckConstraint("CK_WarehouseProducts_StockQuantity_NonNegative", "stock_quantity >= 0");
                    table.ForeignKey(
                        name: "FK_warehouse_products_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "etyra_core",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductVariantAttribute",
                schema: "etyra_core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    SortOrder = table.Column<int>(type: "integer", nullable: false),
                    ProductVariantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductVariantAttribute", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductVariantAttribute_product_variants_ProductVariantId",
                        column: x => x.ProductVariantId,
                        principalSchema: "etyra_core",
                        principalTable: "product_variants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_categories_active_menu",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "is_active", "show_in_menu" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_created_at",
                schema: "etyra_core",
                table: "categories",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_categories_created_by",
                schema: "etyra_core",
                table: "categories",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_categories_created_by_created_at",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "created_by", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_deleted_by",
                schema: "etyra_core",
                table: "categories",
                column: "deleted_by");

            migrationBuilder.CreateIndex(
                name: "ix_categories_hierarchy",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "parent_category_id", "is_active", "sort_order" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_is_active",
                schema: "etyra_core",
                table: "categories",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "ix_categories_is_deleted",
                schema: "etyra_core",
                table: "categories",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_categories_is_deleted_created_at",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_name",
                schema: "etyra_core",
                table: "categories",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ix_categories_parent_active",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "parent_category_id", "is_active" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_parent_category_id",
                schema: "etyra_core",
                table: "categories",
                column: "parent_category_id");

            migrationBuilder.CreateIndex(
                name: "ix_categories_parent_sort",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "parent_category_id", "sort_order" });

            migrationBuilder.CreateIndex(
                name: "ix_categories_show_in_menu",
                schema: "etyra_core",
                table: "categories",
                column: "show_in_menu");

            migrationBuilder.CreateIndex(
                name: "ix_categories_slug_unique",
                schema: "etyra_core",
                table: "categories",
                column: "slug",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_categories_sort_order",
                schema: "etyra_core",
                table: "categories",
                column: "sort_order");

            migrationBuilder.CreateIndex(
                name: "ix_categories_updated_by",
                schema: "etyra_core",
                table: "categories",
                column: "updated_by");

            migrationBuilder.CreateIndex(
                name: "ix_categories_updated_by_updated_at",
                schema: "etyra_core",
                table: "categories",
                columns: new[] { "updated_by", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_category_id",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_category_language",
                schema: "etyra_core",
                table: "category_descriptions",
                columns: new[] { "category_id", "language_code" });

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_category_language_store_unique",
                schema: "etyra_core",
                table: "category_descriptions",
                columns: new[] { "category_id", "language_code", "store_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_created_at",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_is_deleted",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_is_deleted_created_at",
                schema: "etyra_core",
                table: "category_descriptions",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_language_code",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "language_code");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_language_store",
                schema: "etyra_core",
                table: "category_descriptions",
                columns: new[] { "language_code", "store_id" });

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_name",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_slug",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "slug");

            migrationBuilder.CreateIndex(
                name: "ix_category_descriptions_store_id",
                schema: "etyra_core",
                table: "category_descriptions",
                column: "store_id");

            migrationBuilder.CreateIndex(
                name: "IX_CategoryCategory_DescendantsId",
                schema: "etyra_core",
                table: "CategoryCategory",
                column: "DescendantsId");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_created_at",
                schema: "etyra_core",
                table: "example_entities",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_created_by",
                schema: "etyra_core",
                table: "example_entities",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_created_by_created_at",
                schema: "etyra_core",
                table: "example_entities",
                columns: new[] { "created_by", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_deleted_by",
                schema: "etyra_core",
                table: "example_entities",
                column: "deleted_by");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_email",
                schema: "etyra_core",
                table: "example_entities",
                column: "email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_is_deleted",
                schema: "etyra_core",
                table: "example_entities",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_is_deleted_created_at",
                schema: "etyra_core",
                table: "example_entities",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_name",
                schema: "etyra_core",
                table: "example_entities",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_name_currency",
                schema: "etyra_core",
                table: "example_entities",
                columns: new[] { "name", "preferred_currency" });

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_updated_by",
                schema: "etyra_core",
                table: "example_entities",
                column: "updated_by");

            migrationBuilder.CreateIndex(
                name: "ix_example_entities_updated_by_updated_at",
                schema: "etyra_core",
                table: "example_entities",
                columns: new[] { "updated_by", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_category_id",
                schema: "etyra_core",
                table: "product_categories",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_category_primary",
                schema: "etyra_core",
                table: "product_categories",
                columns: new[] { "category_id", "is_primary" });

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_category_sort",
                schema: "etyra_core",
                table: "product_categories",
                columns: new[] { "category_id", "sort_order" });

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_created_at",
                schema: "etyra_core",
                table: "product_categories",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_is_deleted",
                schema: "etyra_core",
                table: "product_categories",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_is_deleted_created_at",
                schema: "etyra_core",
                table: "product_categories",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_is_primary",
                schema: "etyra_core",
                table: "product_categories",
                column: "is_primary");

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_product_category_unique",
                schema: "etyra_core",
                table: "product_categories",
                columns: new[] { "product_id", "category_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_product_id",
                schema: "etyra_core",
                table: "product_categories",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_product_primary",
                schema: "etyra_core",
                table: "product_categories",
                columns: new[] { "product_id", "is_primary" });

            migrationBuilder.CreateIndex(
                name: "ix_product_categories_sort_order",
                schema: "etyra_core",
                table: "product_categories",
                column: "sort_order");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_created_at",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_is_deleted",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_is_deleted_created_at",
                schema: "etyra_core",
                table: "product_descriptions",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_language_code",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "language_code");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_language_store",
                schema: "etyra_core",
                table: "product_descriptions",
                columns: new[] { "language_code", "store_id" });

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_name",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_product_id",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_product_language_store_unique",
                schema: "etyra_core",
                table: "product_descriptions",
                columns: new[] { "product_id", "language_code", "store_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_slug",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "slug");

            migrationBuilder.CreateIndex(
                name: "ix_product_descriptions_store_id",
                schema: "etyra_core",
                table: "product_descriptions",
                column: "store_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_active_dates",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "is_active", "start_date", "end_date" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_created_at",
                schema: "etyra_core",
                table: "product_discounts",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_date_range_active",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "start_date", "end_date", "is_active" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_end_date",
                schema: "etyra_core",
                table: "product_discounts",
                column: "end_date");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_is_active",
                schema: "etyra_core",
                table: "product_discounts",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_is_deleted",
                schema: "etyra_core",
                table: "product_discounts",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_is_deleted_created_at",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_priority",
                schema: "etyra_core",
                table: "product_discounts",
                column: "priority");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_product_active",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "product_id", "is_active" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_product_active_priority",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "product_id", "is_active", "priority" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_product_id",
                schema: "etyra_core",
                table: "product_discounts",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_product_priority",
                schema: "etyra_core",
                table: "product_discounts",
                columns: new[] { "product_id", "priority" });

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_start_date",
                schema: "etyra_core",
                table: "product_discounts",
                column: "start_date");

            migrationBuilder.CreateIndex(
                name: "ix_product_discounts_type",
                schema: "etyra_core",
                table: "product_discounts",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_created_at",
                schema: "etyra_core",
                table: "product_images",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_is_deleted",
                schema: "etyra_core",
                table: "product_images",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_is_deleted_created_at",
                schema: "etyra_core",
                table: "product_images",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_product_images_is_main",
                schema: "etyra_core",
                table: "product_images",
                column: "is_main");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_product_id",
                schema: "etyra_core",
                table: "product_images",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_product_main_unique",
                schema: "etyra_core",
                table: "product_images",
                columns: new[] { "product_id", "is_main" },
                unique: true,
                filter: "is_main = true");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_product_sort",
                schema: "etyra_core",
                table: "product_images",
                columns: new[] { "product_id", "sort_order" });

            migrationBuilder.CreateIndex(
                name: "ix_product_images_product_type",
                schema: "etyra_core",
                table: "product_images",
                columns: new[] { "product_id", "type" });

            migrationBuilder.CreateIndex(
                name: "ix_product_images_product_type_sort",
                schema: "etyra_core",
                table: "product_images",
                columns: new[] { "product_id", "type", "sort_order" });

            migrationBuilder.CreateIndex(
                name: "ix_product_images_sort_order",
                schema: "etyra_core",
                table: "product_images",
                column: "sort_order");

            migrationBuilder.CreateIndex(
                name: "ix_product_images_type",
                schema: "etyra_core",
                table: "product_images",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_is_active",
                schema: "etyra_core",
                table: "product_variants",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_is_default",
                schema: "etyra_core",
                table: "product_variants",
                column: "is_default");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_active",
                schema: "etyra_core",
                table: "product_variants",
                columns: new[] { "product_id", "is_active" });

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_default",
                schema: "etyra_core",
                table: "product_variants",
                columns: new[] { "product_id", "is_default" });

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_id",
                schema: "etyra_core",
                table: "product_variants",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_sku_unique",
                schema: "etyra_core",
                table: "product_variants",
                column: "sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_sort_order",
                schema: "etyra_core",
                table: "product_variants",
                column: "sort_order");

            migrationBuilder.CreateIndex(
                name: "IX_ProductAttribute_ProductId",
                schema: "etyra_core",
                table: "ProductAttribute",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "ix_products_barcode",
                schema: "etyra_core",
                table: "products",
                column: "barcode");

            migrationBuilder.CreateIndex(
                name: "ix_products_brand",
                schema: "etyra_core",
                table: "products",
                column: "brand");

            migrationBuilder.CreateIndex(
                name: "ix_products_brand_status",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "brand", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_products_created_at",
                schema: "etyra_core",
                table: "products",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_products_created_by",
                schema: "etyra_core",
                table: "products",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_products_created_by_created_at",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "created_by", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_products_deleted_by",
                schema: "etyra_core",
                table: "products",
                column: "deleted_by");

            migrationBuilder.CreateIndex(
                name: "ix_products_ean",
                schema: "etyra_core",
                table: "products",
                column: "ean");

            migrationBuilder.CreateIndex(
                name: "ix_products_featured_status",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "is_featured", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_products_is_deleted",
                schema: "etyra_core",
                table: "products",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_products_is_deleted_created_at",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_products_is_featured",
                schema: "etyra_core",
                table: "products",
                column: "is_featured");

            migrationBuilder.CreateIndex(
                name: "ix_products_model",
                schema: "etyra_core",
                table: "products",
                column: "model");

            migrationBuilder.CreateIndex(
                name: "ix_products_name",
                schema: "etyra_core",
                table: "products",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ix_products_sale_end_date",
                schema: "etyra_core",
                table: "products",
                column: "sale_end_date");

            migrationBuilder.CreateIndex(
                name: "ix_products_sale_start_date",
                schema: "etyra_core",
                table: "products",
                column: "sale_start_date");

            migrationBuilder.CreateIndex(
                name: "ix_products_sku_unique",
                schema: "etyra_core",
                table: "products",
                column: "sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_products_slug_unique",
                schema: "etyra_core",
                table: "products",
                column: "slug",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_products_status",
                schema: "etyra_core",
                table: "products",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_products_status_type",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "status", "type" });

            migrationBuilder.CreateIndex(
                name: "ix_products_type",
                schema: "etyra_core",
                table: "products",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_products_updated_by",
                schema: "etyra_core",
                table: "products",
                column: "updated_by");

            migrationBuilder.CreateIndex(
                name: "ix_products_updated_by_updated_at",
                schema: "etyra_core",
                table: "products",
                columns: new[] { "updated_by", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductVariantAttribute_ProductVariantId",
                schema: "etyra_core",
                table: "ProductVariantAttribute",
                column: "ProductVariantId");

            migrationBuilder.CreateIndex(
                name: "ix_users_created_at",
                schema: "etyra_core",
                table: "users",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_users_created_by",
                schema: "etyra_core",
                table: "users",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_users_created_by_created_at",
                schema: "etyra_core",
                table: "users",
                columns: new[] { "created_by", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_users_deleted_by",
                schema: "etyra_core",
                table: "users",
                column: "deleted_by");

            migrationBuilder.CreateIndex(
                name: "ix_users_email_confirmation_token",
                schema: "etyra_core",
                table: "users",
                column: "email_confirmation_token");

            migrationBuilder.CreateIndex(
                name: "ix_users_email_unique",
                schema: "etyra_core",
                table: "users",
                column: "email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_users_enabled_email_confirmed",
                schema: "etyra_core",
                table: "users",
                columns: new[] { "is_enabled", "is_email_confirmed" });

            migrationBuilder.CreateIndex(
                name: "ix_users_full_name",
                schema: "etyra_core",
                table: "users",
                columns: new[] { "first_name", "last_name" });

            migrationBuilder.CreateIndex(
                name: "ix_users_is_deleted",
                schema: "etyra_core",
                table: "users",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_users_is_deleted_created_at",
                schema: "etyra_core",
                table: "users",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_users_is_email_confirmed",
                schema: "etyra_core",
                table: "users",
                column: "is_email_confirmed");

            migrationBuilder.CreateIndex(
                name: "ix_users_is_enabled",
                schema: "etyra_core",
                table: "users",
                column: "is_enabled");

            migrationBuilder.CreateIndex(
                name: "ix_users_last_login_at",
                schema: "etyra_core",
                table: "users",
                column: "last_login_at");

            migrationBuilder.CreateIndex(
                name: "ix_users_password_reset_token",
                schema: "etyra_core",
                table: "users",
                column: "password_reset_token");

            migrationBuilder.CreateIndex(
                name: "ix_users_refresh_token",
                schema: "etyra_core",
                table: "users",
                column: "refresh_token");

            migrationBuilder.CreateIndex(
                name: "ix_users_refresh_token_expires_at",
                schema: "etyra_core",
                table: "users",
                column: "refresh_token_expires_at");

            migrationBuilder.CreateIndex(
                name: "ix_users_updated_by",
                schema: "etyra_core",
                table: "users",
                column: "updated_by");

            migrationBuilder.CreateIndex(
                name: "ix_users_updated_by_updated_at",
                schema: "etyra_core",
                table: "users",
                columns: new[] { "updated_by", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_users_username_unique",
                schema: "etyra_core",
                table: "users",
                column: "username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_created_at",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_is_deleted",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_is_deleted_created_at",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "is_deleted", "created_at" });

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_is_primary",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "is_primary_warehouse");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_location_code",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "location_code");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_product_id",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_product_primary_unique",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "product_id", "is_primary_warehouse" },
                unique: true,
                filter: "is_primary_warehouse = true");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_product_status",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "product_id", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_product_stock_reserved",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "product_id", "stock_quantity", "reserved_stock" });

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_product_warehouse_unique",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "product_id", "warehouse_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_status",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_stock_alert",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "stock_quantity", "min_stock_alert" });

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_stock_quantity",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "stock_quantity");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_warehouse_id",
                schema: "etyra_core",
                table: "warehouse_products",
                column: "warehouse_id");

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_warehouse_status",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "warehouse_id", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_warehouse_products_warehouse_stock",
                schema: "etyra_core",
                table: "warehouse_products",
                columns: new[] { "warehouse_id", "stock_quantity" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "category_descriptions",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "CategoryCategory",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "example_entities",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "product_categories",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "product_descriptions",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "product_discounts",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "product_images",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "ProductAttribute",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "ProductVariantAttribute",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "users",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "warehouse_products",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "categories",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "product_variants",
                schema: "etyra_core");

            migrationBuilder.DropTable(
                name: "products",
                schema: "etyra_core");
        }
    }
}
