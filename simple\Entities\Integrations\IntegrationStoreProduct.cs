﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Company;

namespace EtyraApp.Domain.Entities.Integrations;

public class IntegrationStoreProduct
{
    public Product Product { get; set; }
    public int? ProductId { get; set; }
    public int? WarehouseId { get; set; }
    public int? StoreId { get; set; }

    public decimal Price { get; set; }

    public int StockQuantity { get; set; }

    public Store Store { get; set; }
    public int? RemoteProductId { get; set; }
}


