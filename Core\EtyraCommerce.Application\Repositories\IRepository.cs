﻿using EtyraCommerce.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Application.Repositories
{
    /// <summary>
    /// Base repository interface - marker interface for all repositories
    /// </summary>
    /// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
    public interface IRepository<T> where T : BaseEntity
    {
        DbSet<T> Table { get; }

        // Marker interface - no methods exposed
        // This prevents direct DbSet access and maintains abstraction
    }
}