﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddCompanyInfoToUserAddress : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                schema: "etyra_core",
                table: "user_addresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyTitle",
                schema: "etyra_core",
                table: "user_addresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCompanyAddress",
                schema: "etyra_core",
                table: "user_addresses",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "TaxNumber",
                schema: "etyra_core",
                table: "user_addresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxOffice",
                schema: "etyra_core",
                table: "user_addresses",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompanyName",
                schema: "etyra_core",
                table: "user_addresses");

            migrationBuilder.DropColumn(
                name: "CompanyTitle",
                schema: "etyra_core",
                table: "user_addresses");

            migrationBuilder.DropColumn(
                name: "IsCompanyAddress",
                schema: "etyra_core",
                table: "user_addresses");

            migrationBuilder.DropColumn(
                name: "TaxNumber",
                schema: "etyra_core",
                table: "user_addresses");

            migrationBuilder.DropColumn(
                name: "TaxOffice",
                schema: "etyra_core",
                table: "user_addresses");
        }
    }
}
