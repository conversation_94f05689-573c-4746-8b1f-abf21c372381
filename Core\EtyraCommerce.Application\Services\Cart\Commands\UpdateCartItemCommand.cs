using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Commands
{
    /// <summary>
    /// Command to update cart item quantity
    /// </summary>
    public class UpdateCartItemCommand : IRequest<CustomResponseDto<CartDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Product ID to update
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// New quantity (set to 0 to remove item)
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Updated variant information (optional)
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Updated notes (optional)
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Creates command from DTO
        /// </summary>
        public static UpdateCartItemCommand FromDto(UpdateCartItemDto dto, Guid? customerId = null, string? sessionId = null)
        {
            return new UpdateCartItemCommand
            {
                CustomerId = customerId,
                SessionId = sessionId,
                ProductId = dto.ProductId,
                Quantity = dto.Quantity,
                VariantInfo = dto.VariantInfo,
                Notes = dto.Notes
            };
        }
    }
}
