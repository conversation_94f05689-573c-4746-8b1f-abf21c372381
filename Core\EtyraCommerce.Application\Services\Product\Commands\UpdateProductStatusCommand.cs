using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Domain.Entities.Product;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Commands
{
    /// <summary>
    /// Command for updating product status
    /// </summary>
    public class UpdateProductStatusCommand : IRequest<CustomResponseDto<ProductDto>>
    {
        /// <summary>
        /// Product ID to update status for
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// New product status
        /// </summary>
        public ProductStatus Status { get; set; }

        /// <summary>
        /// Whether product is active
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Product discontinue date (if status is Discontinued)
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        /// <summary>
        /// Product available start date
        /// </summary>
        public DateTime? AvailableStartDate { get; set; }

        /// <summary>
        /// Product available end date
        /// </summary>
        public DateTime? AvailableEndDate { get; set; }

        /// <summary>
        /// Reason for status change
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Whether to send notification about status change
        /// </summary>
        public bool SendNotification { get; set; } = false;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateProductStatusCommand()
        {
        }

        /// <summary>
        /// Constructor with product ID and status
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="status">Product status</param>
        public UpdateProductStatusCommand(Guid productId, ProductStatus status)
        {
            ProductId = productId;
            Status = status;
        }

        /// <summary>
        /// Constructor with full parameters
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="status">Product status</param>
        /// <param name="isActive">Is active</param>
        /// <param name="reason">Reason for change</param>
        public UpdateProductStatusCommand(Guid productId, ProductStatus status, bool? isActive, string? reason = null)
        {
            ProductId = productId;
            Status = status;
            IsActive = isActive;
            Reason = reason;
        }
    }
}
