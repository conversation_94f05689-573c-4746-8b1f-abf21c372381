﻿using AutoMapper;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.Services;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;

namespace EtyraCommerce.Persistence.Services
{
    /// <summary>
    /// Generic service implementation
    /// </summary>
    /// <typeparam name="TEntity">Entity type that inherits from BaseEntity</typeparam>
    /// <typeparam name="TDto">DTO type for data transfer</typeparam>
    public class Service<TEntity, TDto> : IService<TEntity, TDto> where TEntity : BaseEntity where TDto : class
    {
        protected readonly IMapper _mapper;
        protected readonly IUnitOfWork _unitOfWork;
        protected readonly ILogger<Service<TEntity, TDto>> _logger;
        private readonly IReadRepository<TEntity> _readRepository;
        private readonly IWriteRepository<TEntity> _writeRepository;

        public Service(IUnitOfWork unitOfWork, IMapper mapper, ILogger<Service<TEntity, TDto>> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _readRepository = unitOfWork.ReadRepository<TEntity>();
            _writeRepository = unitOfWork.WriteRepository<TEntity>();
        }
        #region Read Operations

        /// <summary>
        /// Gets all entities
        /// </summary>
        public async Task<CustomResponseDto<IEnumerable<TDto>>> GetAllAsync(bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { tracking });

                var entities = await _readRepository.GetAllAsync(tracking);
                var dtos = _mapper.Map<IEnumerable<TDto>>(entities.ToList());

                _logger.LogInformation("GetAllAsync => Retrieved {EntityCount} entities", dtos.Count());
                LogMethodExit(dtos);

                return CustomResponseDto<IEnumerable<TDto>>.Success(StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<IEnumerable<TDto>>(ex);
            }
        }

        /// <summary>
        /// Gets entities matching the expression
        /// </summary>
        public async Task<CustomResponseDto<IEnumerable<TDto>>> GetWhereAsync(Expression<Func<TEntity, bool>> expression, bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: [expression, tracking]);

                var entities = await _readRepository.GetWhereAsync(expression, tracking);
                var dtos = _mapper.Map<IEnumerable<TDto>>(entities.ToList());

                _logger.LogInformation("GetWhereAsync => Retrieved {EntityCount} entities matching expression", dtos.Count());
                LogMethodExit(dtos);

                return CustomResponseDto<IEnumerable<TDto>>.Success(StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<IEnumerable<TDto>>(ex);
            }
        }

        /// <summary>
        /// Gets first entity matching the expression or null
        /// </summary>
        public async Task<CustomResponseDto<TDto?>> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> expression, bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: [expression, tracking]);

                var entity = await _readRepository.GetFirstOrDefaultAsync(expression, tracking);
                if (entity == null)
                {
                    _logger.LogWarning("GetFirstOrDefaultAsync => Entity not found matching expression");
                    return CustomResponseDto<TDto?>.NotFound("Entity not found");
                }

                var dto = _mapper.Map<TDto>(entity);
                LogMethodExit(dto);
                return CustomResponseDto<TDto?>.Success(StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<TDto?>(ex);
            }
        }

        /// <summary>
        /// Gets entity by ID or null if not found
        /// </summary>
        public async Task<CustomResponseDto<TDto?>> GetByIdAsync(Guid id, bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: new object[] { id, tracking });

                var entity = await _readRepository.GetByIdAsync(id, tracking);
                if (entity == null)
                {
                    _logger.LogWarning("GetByIdAsync => Entity not found with ID: {EntityId}", id);
                    return CustomResponseDto<TDto?>.NotFound("Entity not found");
                }

                var dto = _mapper.Map<TDto>(entity);
                LogMethodExit(dto);
                return CustomResponseDto<TDto?>.Success(StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<TDto?>(ex);
            }
        }

        /// <summary>
        /// Checks if any entity matches the expression
        /// </summary>
        public async Task<CustomResponseDto<bool>> AnyAsync(Expression<Func<TEntity, bool>> expression, bool tracking = false)
        {
            try
            {
                LogMethodEntry(parameters: [expression, tracking]);

                var exists = await _readRepository.AnyAsync(expression, tracking);

                _logger.LogDebug("AnyAsync => Entity exists: {Exists}", exists);
                LogMethodExit(exists);

                return CustomResponseDto<bool>.Success(StatusCodes.Status200OK, exists);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<bool>(ex);
            }
        }

        /// <summary>
        /// Gets count of entities matching the expression
        /// </summary>
        public async Task<CustomResponseDto<int>> CountAsync(Expression<Func<TEntity, bool>>? expression = null)
        {
            try
            {
                LogMethodEntry(parameters: [expression]);

                var count = await _readRepository.CountAsync(expression);

                _logger.LogInformation("CountAsync => Found {EntityCount} entities", count);
                LogMethodExit(count);

                return CustomResponseDto<int>.Success(StatusCodes.Status200OK, count);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<int>(ex);
            }
        }

        /// <summary>
        /// Gets paginated results
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<TDto>>> GetPagedAsync(int pageNumber, int pageSize, bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: [pageNumber, pageSize, tracking]);

                var pagedResult = await _readRepository.GetPagedAsync(pageNumber, pageSize, tracking);
                var dtoResult = pagedResult.Map(entity => _mapper.Map<TDto>(entity));

                _logger.LogInformation("GetPagedAsync => Page {PageNumber}/{TotalPages}, {ItemCount}/{TotalCount} items",
                    dtoResult.PageNumber, dtoResult.TotalPages, dtoResult.Items.Count, dtoResult.TotalCount);
                LogMethodExit(dtoResult);

                return CustomResponseDto<PagedResult<TDto>>.Success(StatusCodes.Status200OK, dtoResult);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<PagedResult<TDto>>(ex);
            }
        }

        /// <summary>
        /// Gets paginated results matching the expression
        /// </summary>
        public async Task<CustomResponseDto<PagedResult<TDto>>> GetPagedWhereAsync(Expression<Func<TEntity, bool>> expression, int pageNumber, int pageSize, bool tracking = true)
        {
            try
            {
                LogMethodEntry(parameters: [expression, pageNumber, pageSize, tracking]);

                var pagedResult = await _readRepository.GetPagedWhereAsync(expression, pageNumber, pageSize, tracking);
                var dtoResult = pagedResult.Map(entity => _mapper.Map<TDto>(entity));

                _logger.LogInformation("GetPagedWhereAsync => Page {PageNumber}/{TotalPages}, {ItemCount}/{TotalCount} items matching expression",
                    dtoResult.PageNumber, dtoResult.TotalPages, dtoResult.Items.Count, dtoResult.TotalCount);
                LogMethodExit(dtoResult);

                return CustomResponseDto<PagedResult<TDto>>.Success(StatusCodes.Status200OK, dtoResult);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<PagedResult<TDto>>(ex);
            }
        }

        /// <summary>
        /// Searches entities with pagination (basic implementation)
        /// Override in derived classes for specific search logic
        /// </summary>
        public virtual async Task<CustomResponseDto<PagedResult<TDto>>> SearchAsync(SearchRequest searchRequest)
        {
            try
            {
                // Basic implementation - override in derived services for specific search logic
                var pagedResult = await _readRepository.GetPagedAsync(searchRequest.PageNumber, searchRequest.PageSize);
                var dtoResult = pagedResult.Map(entity => _mapper.Map<TDto>(entity));
                return CustomResponseDto<PagedResult<TDto>>.Success(StatusCodes.Status200OK, dtoResult);
            }
            catch (Exception ex)
            {
                return CustomResponseDto<PagedResult<TDto>>.InternalServerError(ex.Message);
            }
        }

        #endregion

        #region Write Operations

        /// <summary>
        /// Adds a new entity
        /// </summary>
        public async Task<CustomResponseDto<TDto>> AddAsync(TDto dto)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                LogMethodEntry(parameters: new object[] { dto });

                var entity = _mapper.Map<TEntity>(dto);
                var createdEntity = await _writeRepository.AddAsync(entity);

                _logger.LogDebug("AddAsync => Entity mapped and added to repository");

                var savedCount = await _unitOfWork.CommitAsync();
                _logger.LogInformation("AddAsync => {SavedCount} entities saved to database", savedCount);

                var resultDto = _mapper.Map<TDto>(createdEntity);

                stopwatch.Stop();
                LogMethodExitWithTiming(stopwatch.Elapsed);

                return CustomResponseDto<TDto>.Created(resultDto, "Entity created successfully");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return CreateErrorResponse<TDto>(ex);
            }
        }

        /// <summary>
        /// Adds multiple entities
        /// </summary>
        public async Task<CustomResponseDto<IEnumerable<TDto>>> AddRangeAsync(IEnumerable<TDto> dtoList)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                LogMethodEntry(parameters: [dtoList]);

                var entities = _mapper.Map<IEnumerable<TEntity>>(dtoList);
                await _writeRepository.AddRangeAsync(entities);

                _logger.LogDebug("AddRangeAsync => {EntityCount} entities mapped and added to repository", entities.Count());

                var savedCount = await _unitOfWork.CommitAsync();
                _logger.LogInformation("AddRangeAsync => {SavedCount} entities saved to database", savedCount);

                var resultDtos = _mapper.Map<IEnumerable<TDto>>(entities);

                stopwatch.Stop();
                LogMethodExitWithTiming(stopwatch.Elapsed);

                return CustomResponseDto<IEnumerable<TDto>>.Created(resultDtos, "Entities created successfully");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return CreateErrorResponse<IEnumerable<TDto>>(ex);
            }
        }

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        public async Task<CustomResponseDto<TDto>> UpdateAsync(TDto dto)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                LogMethodEntry(parameters: [dto]);

                var entity = _mapper.Map<TEntity>(dto);
                var updatedEntity = await _writeRepository.UpdateAsync(entity);

                _logger.LogDebug("UpdateAsync => Entity mapped and updated in repository");

                var savedCount = await _unitOfWork.CommitAsync();
                _logger.LogInformation("UpdateAsync => {SavedCount} entities saved to database", savedCount);

                var resultDto = _mapper.Map<TDto>(updatedEntity);

                stopwatch.Stop();
                LogMethodExitWithTiming(stopwatch.Elapsed);

                return CustomResponseDto<TDto>.Success(StatusCodes.Status200OK, resultDto, "Entity updated successfully");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return CreateErrorResponse<TDto>(ex);
            }
        }

        /// <summary>
        /// Updates multiple entities
        /// </summary>
        public async Task<CustomResponseDto<IEnumerable<TDto>>> UpdateRangeAsync(IEnumerable<TDto> dtoList)
        {
            try
            {
                var entities = _mapper.Map<IEnumerable<TEntity>>(dtoList);
                var updatedEntities = new List<TEntity>();

                foreach (var entity in entities)
                {
                    var updated = await _writeRepository.UpdateAsync(entity);
                    updatedEntities.Add(updated);
                }

                await _unitOfWork.CommitAsync();

                var resultDtos = _mapper.Map<IEnumerable<TDto>>(updatedEntities);
                return CustomResponseDto<IEnumerable<TDto>>.Success(StatusCodes.Status200OK, resultDtos, "Entities updated successfully");
            }
            catch (Exception ex)
            {
                return CustomResponseDto<IEnumerable<TDto>>.InternalServerError(ex.Message);
            }
        }

        /// <summary>
        /// Removes entity by ID (hard delete)
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> RemoveByIdAsync(Guid id)
        {
            try
            {
                LogMethodEntry(parameters: [id]);

                var removed = await _writeRepository.RemoveByIdAsync(id);
                if (!removed)
                {
                    _logger.LogWarning("RemoveByIdAsync => Entity not found with ID: {EntityId}", id);
                    return CustomResponseDto<NoContentDto>.NotFound("Entity not found");
                }

                var savedCount = await _unitOfWork.CommitAsync();
                _logger.LogInformation("RemoveByIdAsync => Entity deleted, {SavedCount} changes saved", savedCount);
                LogMethodExit("Success");

                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status204NoContent, "Entity deleted successfully");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        /// <summary>
        /// Removes entity (hard delete)
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> RemoveAsync(TDto dto)
        {
            try
            {
                var entity = _mapper.Map<TEntity>(dto);
                await _writeRepository.RemoveAsync(entity);
                await _unitOfWork.CommitAsync();

                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status204NoContent, "Entity deleted successfully");
            }
            catch (Exception ex)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError(ex.Message);
            }
        }

        /// <summary>
        /// Removes multiple entities by IDs (hard delete)
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> RemoveRangeAsync(IEnumerable<Guid> ids)
        {
            try
            {
                var entities = new List<TEntity>();
                foreach (var id in ids)
                {
                    var entity = await _readRepository.GetByIdAsync(id);
                    if (entity != null)
                        entities.Add(entity);
                }

                if (entities.Any())
                {
                    await _writeRepository.RemoveRangeAsync(entities);
                    await _unitOfWork.CommitAsync();
                }

                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status204NoContent, "Entities deleted successfully");
            }
            catch (Exception ex)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError(ex.Message);
            }
        }

        /// <summary>
        /// Soft deletes entity by ID
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> SoftDeleteByIdAsync(Guid id)
        {
            try
            {
                LogMethodEntry(parameters: [id]);

                var deleted = await _writeRepository.SoftDeleteByIdAsync(id);
                if (!deleted)
                {
                    _logger.LogWarning("SoftDeleteByIdAsync => Entity not found with ID: {EntityId}", id);
                    return CustomResponseDto<NoContentDto>.NotFound("Entity not found");
                }

                var savedCount = await _unitOfWork.CommitAsync();
                _logger.LogInformation("SoftDeleteByIdAsync => Entity soft deleted, {SavedCount} changes saved", savedCount);
                LogMethodExit("Success");

                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status204NoContent, "Entity soft deleted successfully");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<NoContentDto>(ex);
            }
        }

        /// <summary>
        /// Soft deletes entity
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> SoftDeleteAsync(TDto dto)
        {
            try
            {
                var entity = _mapper.Map<TEntity>(dto);
                await _writeRepository.SoftDeleteAsync(entity);
                await _unitOfWork.CommitAsync();

                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status204NoContent, "Entity soft deleted successfully");
            }
            catch (Exception ex)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError(ex.Message);
            }
        }

        /// <summary>
        /// Restores soft deleted entity by ID
        /// </summary>
        public async Task<CustomResponseDto<NoContentDto>> RestoreByIdAsync(Guid id)
        {
            try
            {
                var restored = await _writeRepository.RestoreByIdAsync(id);
                if (!restored)
                    return CustomResponseDto<NoContentDto>.NotFound("Entity not found or not deleted");

                await _unitOfWork.CommitAsync();
                return CustomResponseDto<NoContentDto>.Success(StatusCodes.Status200OK, "Entity restored successfully");
            }
            catch (Exception ex)
            {
                return CustomResponseDto<NoContentDto>.InternalServerError(ex.Message);
            }
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates DTO before operations
        /// Override in derived classes for specific validation logic
        /// </summary>
        public virtual async Task<CustomResponseDto<ValidationResult>> ValidateAsync(TDto dto)
        {
            try
            {
                var validationResult = new ValidationResult { IsValid = true };

                // Basic validation - override in derived services for specific validation
                if (dto == null)
                {
                    validationResult.AddGeneralError("DTO cannot be null");
                    return CustomResponseDto<ValidationResult>.Success(StatusCodes.Status200OK, validationResult);
                }

                // Additional validation can be added in derived classes
                return CustomResponseDto<ValidationResult>.Success(StatusCodes.Status200OK, validationResult);
            }
            catch (Exception ex)
            {
                return CustomResponseDto<ValidationResult>.InternalServerError(ex.Message);
            }
        }

        /// <summary>
        /// Validates multiple DTOs
        /// Override in derived classes for specific validation logic
        /// </summary>
        public virtual async Task<CustomResponseDto<ValidationResult>> ValidateRangeAsync(IEnumerable<TDto> dtoList)
        {
            try
            {
                var validationResult = new ValidationResult { IsValid = true };

                if (dtoList == null || !dtoList.Any())
                {
                    validationResult.AddGeneralError("DTO list cannot be null or empty");
                    return CustomResponseDto<ValidationResult>.Success(StatusCodes.Status200OK, validationResult);
                }

                // Validate each DTO
                foreach (var dto in dtoList)
                {
                    var individualResult = await ValidateAsync(dto);
                    if (individualResult.Data != null && !individualResult.Data.IsValid)
                    {
                        validationResult.Merge(individualResult.Data);
                    }
                }

                return CustomResponseDto<ValidationResult>.Success(StatusCodes.Status200OK, validationResult);
            }
            catch (Exception ex)
            {
                return CustomResponseDto<ValidationResult>.InternalServerError(ex.Message);
            }
        }

        #endregion

        #region Logging Helpers

        /// <summary>
        /// Logs error with inner exception details (based on your original approach)
        /// </summary>
        protected void LogErrorWithInnerExceptions(Exception ex, [CallerMemberName] string methodName = "")
        {
            _logger.LogError(ex, "{MethodName} => Error: {ErrorMessage}", methodName, ex.Message);

            var innerExceptionDepth = 0;
            var currentException = ex;

            while (currentException.InnerException != null && innerExceptionDepth < 5)
            {
                innerExceptionDepth++;
                currentException = currentException.InnerException;
                _logger.LogError("{MethodName} - Inner Exception {Depth} => Error: {ErrorMessage}",
                    methodName, innerExceptionDepth, currentException.Message);
            }
        }

        /// <summary>
        /// Logs method entry with parameters
        /// </summary>
        protected void LogMethodEntry([CallerMemberName] string methodName = "", params object[] parameters)
        {
            _logger.LogDebug("{MethodName} => Started with parameters: {@Parameters}", methodName, parameters);
        }

        /// <summary>
        /// Logs method exit with result
        /// </summary>
        protected void LogMethodExit<T>(T result, [CallerMemberName] string methodName = "")
        {
            _logger.LogDebug("{MethodName} => Completed successfully", methodName);
        }

        /// <summary>
        /// Logs method exit with execution time
        /// </summary>
        protected void LogMethodExitWithTiming(TimeSpan executionTime, [CallerMemberName] string methodName = "")
        {
            _logger.LogInformation("{MethodName} => Completed in {ExecutionTime}ms", methodName, executionTime.TotalMilliseconds);
        }

        /// <summary>
        /// Creates error response with logging (your original pattern)
        /// </summary>
        protected CustomResponseDto<T> CreateErrorResponse<T>(Exception ex, [CallerMemberName] string methodName = "")
        {
            LogErrorWithInnerExceptions(ex, methodName);
            return CustomResponseDto<T>.InternalServerError(ex.Message);
        }

        /// <summary>
        /// Creates error response with custom status code
        /// </summary>
        protected CustomResponseDto<T> CreateErrorResponse<T>(Exception ex, int statusCode, [CallerMemberName] string methodName = "")
        {
            LogErrorWithInnerExceptions(ex, methodName);
            return CustomResponseDto<T>.Failure(statusCode, ex.Message);
        }

        #endregion
    }
}