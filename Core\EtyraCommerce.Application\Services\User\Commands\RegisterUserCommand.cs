using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Commands
{
    /// <summary>
    /// Command for user registration
    /// </summary>
    public class RegisterUserCommand : IRequest<CustomResponseDto<UserDto>>
    {
        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (optional)
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Unique username for login
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// User password
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Password confirmation
        /// </summary>
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// User's preferred language/culture
        /// </summary>
        public string? Culture { get; set; }

        /// <summary>
        /// User's timezone
        /// </summary>
        public string? TimeZone { get; set; }

        /// <summary>
        /// Terms and conditions acceptance
        /// </summary>
        public bool AcceptTerms { get; set; } = false;

        /// <summary>
        /// Newsletter subscription opt-in
        /// </summary>
        public bool SubscribeToNewsletter { get; set; } = false;

        /// <summary>
        /// IP address of the registration
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent string from the browser
        /// </summary>
        public string? UserAgent { get; set; }

        public RegisterUserCommand() { }

        public RegisterUserCommand(CreateUserDto createUserDto)
        {
            FirstName = createUserDto.FirstName;
            LastName = createUserDto.LastName;
            Email = createUserDto.Email;
            PhoneNumber = createUserDto.PhoneNumber;
            Username = createUserDto.Username;
            Password = createUserDto.Password;
            ConfirmPassword = createUserDto.ConfirmPassword;
            Culture = createUserDto.Culture;
            TimeZone = createUserDto.TimeZone;
            AcceptTerms = createUserDto.AcceptTerms;
            SubscribeToNewsletter = createUserDto.SubscribeToNewsletter;
        }
    }
}
