using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Controller for managing user addresses
    /// Provides endpoints for CRUD operations on user addresses
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserAddressController : ControllerBase
    {
        private readonly IUserAddressService _userAddressService;
        private readonly ILogger<UserAddressController> _logger;

        public UserAddressController(
            IUserAddressService userAddressService,
            ILogger<UserAddressController> logger)
        {
            _userAddressService = userAddressService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new address for the authenticated user
        /// </summary>
        /// <param name="createDto">Address creation data</param>
        /// <returns>Created address information</returns>
        [HttpPost]
        public async Task<IActionResult> CreateUserAddress([FromBody] CreateUserAddressDto createDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("CreateUserAddress called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Creating address for user {UserId}", userId);

                var result = await _userAddressService.CreateUserAddressAsync(userId, createDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Address created successfully for user {UserId}, AddressId: {AddressId}",
                        userId, result.Data?.Id);
                    return CreatedAtAction(nameof(GetUserAddressById),
                        new { addressId = result.Data?.Id }, result);
                }

                _logger.LogWarning("Failed to create address for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user address");
                return StatusCode(500, "An error occurred while creating the address");
            }
        }

        /// <summary>
        /// Updates an existing address for the authenticated user
        /// </summary>
        /// <param name="addressId">ID of the address to update</param>
        /// <param name="updateDto">Address update data</param>
        /// <returns>Updated address information</returns>
        [HttpPut("{addressId:guid}")]
        public async Task<IActionResult> UpdateUserAddress(Guid addressId, [FromBody] UpdateUserAddressDto updateDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("UpdateUserAddress called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Updating address {AddressId} for user {UserId}", addressId, userId);

                var result = await _userAddressService.UpdateUserAddressAsync(addressId, userId, updateDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Address {AddressId} updated successfully for user {UserId}", addressId, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to update address {AddressId} for user {UserId}: {Error}",
                    addressId, userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user address {AddressId}", addressId);
                return StatusCode(500, "An error occurred while updating the address");
            }
        }

        /// <summary>
        /// Deletes an address for the authenticated user
        /// </summary>
        /// <param name="addressId">ID of the address to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete (default: false)</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{addressId:guid}")]
        public async Task<IActionResult> DeleteUserAddress(Guid addressId, [FromQuery] bool hardDelete = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("DeleteUserAddress called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Deleting address {AddressId} for user {UserId}, HardDelete: {HardDelete}",
                    addressId, userId, hardDelete);

                var result = await _userAddressService.DeleteUserAddressAsync(addressId, userId, hardDelete);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Address {AddressId} deleted successfully for user {UserId}", addressId, userId);
                    return NoContent();
                }

                _logger.LogWarning("Failed to delete address {AddressId} for user {UserId}: {Error}",
                    addressId, userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user address {AddressId}", addressId);
                return StatusCode(500, "An error occurred while deleting the address");
            }
        }

        /// <summary>
        /// Gets all addresses for the authenticated user
        /// </summary>
        /// <param name="addressType">Filter by address type (1=Billing, 2=Shipping, 3=Both)</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of user addresses</returns>
        [HttpGet]
        public async Task<IActionResult> GetUserAddresses(
            [FromQuery] int? addressType = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddresses called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting addresses for user {UserId}, Type: {AddressType}, Active: {IsActive}",
                    userId, addressType, isActive);

                var result = await _userAddressService.GetUserAddressesAsync(userId, addressType, isActive, includeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved {Count} addresses for user {UserId}",
                        result.Data?.Count ?? 0, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get addresses for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user addresses");
                return StatusCode(500, "An error occurred while retrieving addresses");
            }
        }

        /// <summary>
        /// Gets a specific address by ID for the authenticated user
        /// </summary>
        /// <param name="addressId">ID of the address to retrieve</param>
        /// <returns>Address information</returns>
        [HttpGet("{addressId:guid}")]
        public async Task<IActionResult> GetUserAddressById(Guid addressId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddressById called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting address {AddressId} for user {UserId}", addressId, userId);

                var result = await _userAddressService.GetUserAddressByIdAsync(addressId, userId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved address {AddressId} for user {UserId}", addressId, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get address {AddressId} for user {UserId}: {Error}",
                    addressId, userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user address {AddressId}", addressId);
                return StatusCode(500, "An error occurred while retrieving the address");
            }
        }

        /// <summary>
        /// Gets the default address for the authenticated user
        /// </summary>
        /// <param name="addressType">Filter by address type (1=Billing, 2=Shipping, 3=Both)</param>
        /// <returns>Default address information</returns>
        [HttpGet("default")]
        public async Task<IActionResult> GetDefaultAddress([FromQuery] int? addressType = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetDefaultAddress called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting default address for user {UserId}, Type: {AddressType}", userId, addressType);

                var result = await _userAddressService.GetDefaultAddressAsync(userId, addressType);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved default address for user {UserId}", userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get default address for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting default address for user");
                return StatusCode(500, "An error occurred while retrieving the default address");
            }
        }

        /// <summary>
        /// Gets billing addresses for the authenticated user
        /// </summary>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of billing addresses</returns>
        [HttpGet("billing")]
        public async Task<IActionResult> GetBillingAddresses([FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetBillingAddresses called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting billing addresses for user {UserId}", userId);

                var result = await _userAddressService.GetBillingAddressesAsync(userId, includeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved {Count} billing addresses for user {UserId}",
                        result.Data?.Count ?? 0, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get billing addresses for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting billing addresses for user");
                return StatusCode(500, "An error occurred while retrieving billing addresses");
            }
        }

        /// <summary>
        /// Gets shipping addresses for the authenticated user
        /// </summary>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of shipping addresses</returns>
        [HttpGet("shipping")]
        public async Task<IActionResult> GetShippingAddresses([FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetShippingAddresses called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting shipping addresses for user {UserId}", userId);

                var result = await _userAddressService.GetShippingAddressesAsync(userId, includeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved {Count} shipping addresses for user {UserId}",
                        result.Data?.Count ?? 0, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get shipping addresses for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shipping addresses for user");
                return StatusCode(500, "An error occurred while retrieving shipping addresses");
            }
        }

        /// <summary>
        /// Sets an address as the default address for the authenticated user
        /// </summary>
        /// <param name="addressId">ID of the address to set as default</param>
        /// <returns>Operation result</returns>
        [HttpPatch("{addressId:guid}/set-default")]
        public async Task<IActionResult> SetDefaultAddress(Guid addressId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("SetDefaultAddress called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Setting address {AddressId} as default for user {UserId}", addressId, userId);

                var result = await _userAddressService.SetDefaultAddressAsync(addressId, userId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Address {AddressId} set as default for user {UserId}", addressId, userId);
                    return NoContent();
                }

                _logger.LogWarning("Failed to set address {AddressId} as default for user {UserId}: {Error}",
                    addressId, userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default address {AddressId}", addressId);
                return StatusCode(500, "An error occurred while setting the default address");
            }
        }

        /// <summary>
        /// Toggles the active status of an address for the authenticated user
        /// </summary>
        /// <param name="addressId">ID of the address to toggle</param>
        /// <param name="isActive">New active status</param>
        /// <returns>Operation result</returns>
        [HttpPatch("{addressId:guid}/toggle-status")]
        public async Task<IActionResult> ToggleAddressStatus(Guid addressId, [FromQuery] bool isActive)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("ToggleAddressStatus called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Toggling address {AddressId} status to {IsActive} for user {UserId}",
                    addressId, isActive, userId);

                var result = await _userAddressService.ToggleAddressStatusAsync(addressId, userId, isActive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Address {AddressId} status toggled to {IsActive} for user {UserId}",
                        addressId, isActive, userId);
                    return NoContent();
                }

                _logger.LogWarning("Failed to toggle address {AddressId} status for user {UserId}: {Error}",
                    addressId, userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling address status {AddressId}", addressId);
                return StatusCode(500, "An error occurred while toggling the address status");
            }
        }

        /// <summary>
        /// Gets address statistics for the authenticated user
        /// </summary>
        /// <returns>Address statistics</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetUserAddressStats()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddressStats called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting address statistics for user {UserId}", userId);

                var result = await _userAddressService.GetUserAddressStatsAsync(userId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved address statistics for user {UserId}", userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get address statistics for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting address statistics for user");
                return StatusCode(500, "An error occurred while retrieving address statistics");
            }
        }

        /// <summary>
        /// Searches user addresses by text
        /// </summary>
        /// <param name="searchText">Text to search for in addresses</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of matching addresses</returns>
        [HttpGet("search")]
        public async Task<IActionResult> SearchUserAddresses(
            [FromQuery] string searchText,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("SearchUserAddresses called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    return BadRequest("Search text is required");
                }

                _logger.LogInformation("Searching addresses for user {UserId} with text: {SearchText}", userId, searchText);

                var result = await _userAddressService.SearchUserAddressesAsync(userId, searchText, includeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Found {Count} addresses for user {UserId} with search text: {SearchText}",
                        result.Data?.Count ?? 0, userId, searchText);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to search addresses for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching addresses for user");
                return StatusCode(500, "An error occurred while searching addresses");
            }
        }

        /// <summary>
        /// Gets recent addresses for the authenticated user
        /// </summary>
        /// <param name="count">Number of recent addresses to retrieve (default: 5)</param>
        /// <param name="includeInactive">Include inactive addresses</param>
        /// <returns>List of recent addresses</returns>
        [HttpGet("recent")]
        public async Task<IActionResult> GetRecentUserAddresses(
            [FromQuery] int count = 5,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == Guid.Empty)
                {
                    _logger.LogWarning("GetRecentUserAddresses called without valid user ID");
                    return Unauthorized("User not authenticated");
                }

                if (count <= 0 || count > 20)
                {
                    return BadRequest("Count must be between 1 and 20");
                }

                _logger.LogInformation("Getting {Count} recent addresses for user {UserId}", count, userId);

                var result = await _userAddressService.GetRecentUserAddressesAsync(userId, count, includeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Retrieved {Count} recent addresses for user {UserId}",
                        result.Data?.Count ?? 0, userId);
                    return Ok(result);
                }

                _logger.LogWarning("Failed to get recent addresses for user {UserId}: {Error}", userId, result.Message);
                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent addresses for user");
                return StatusCode(500, "An error occurred while retrieving recent addresses");
            }
        }

        #region Helper Methods

        /// <summary>
        /// Gets the current user ID from the JWT token
        /// </summary>
        /// <returns>User ID or Guid.Empty if not found</returns>
        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            return Guid.Empty;
        }

        #endregion
    }
}
