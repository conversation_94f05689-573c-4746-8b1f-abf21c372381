using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product variant entity for variable products (e.g., different sizes, colors)
    /// </summary>
    public class ProductVariant : BaseEntity
    {
        /// <summary>
        /// Variant name (e.g., "Red - Large", "Blue - Medium")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Variant description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Variant SKU (unique identifier)
        /// </summary>
        public string SKU { get; set; } = string.Empty;

        /// <summary>
        /// Variant price (can override product base price)
        /// </summary>
        public Money? Price { get; set; }

        /// <summary>
        /// Variant cost
        /// </summary>
        public Money? Cost { get; set; }

        /// <summary>
        /// Stock quantity for this variant
        /// </summary>
        public int StockQuantity { get; set; } = 0;

        /// <summary>
        /// Whether to manage stock for this variant
        /// </summary>
        public bool ManageStock { get; set; } = true;

        /// <summary>
        /// Variant weight (can override product weight)
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// Variant dimensions (can override product dimensions)
        /// </summary>
        public ProductDimensions? Dimensions { get; set; }

        /// <summary>
        /// Variant image URL
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether variant is active
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether variant is default for the product
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Variant barcode (if different from product)
        /// </summary>
        public string? Barcode { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Variant attributes (e.g., Color=Red, Size=Large)
        /// </summary>
        public ICollection<ProductVariantAttribute> Attributes { get; set; } = new List<ProductVariantAttribute>();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the effective price (variant price or product base price)
        /// </summary>
        public Money GetEffectivePrice(Money productBasePrice)
        {
            return Price ?? productBasePrice;
        }

        /// <summary>
        /// Gets the effective cost (variant cost or product cost)
        /// </summary>
        public Money? GetEffectiveCost(Money? productCost)
        {
            return Cost ?? productCost;
        }

        /// <summary>
        /// Checks if variant is in stock
        /// </summary>
        public bool IsInStock => !ManageStock || StockQuantity > 0;

        /// <summary>
        /// Checks if variant is out of stock
        /// </summary>
        public bool IsOutOfStock => ManageStock && StockQuantity <= 0;

        /// <summary>
        /// Gets formatted attribute string (e.g., "Red, Large")
        /// </summary>
        public string AttributeString
        {
            get
            {
                return string.Join(", ", Attributes.Select(a => a.Value));
            }
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates stock quantity
        /// </summary>
        public void UpdateStock(int newQuantity)
        {
            if (newQuantity < 0)
                throw new ArgumentException("Stock quantity cannot be negative");

            StockQuantity = newQuantity;
            MarkAsUpdated();
        }

        /// <summary>
        /// Reduces stock quantity
        /// </summary>
        public void ReduceStock(int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            if (ManageStock && StockQuantity < quantity)
                throw new InvalidOperationException("Insufficient stock");

            if (ManageStock)
                StockQuantity -= quantity;

            MarkAsUpdated();
        }

        /// <summary>
        /// Increases stock quantity
        /// </summary>
        public void IncreaseStock(int quantity)
        {
            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive");

            StockQuantity += quantity;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets as default variant
        /// </summary>
        public void SetAsDefault()
        {
            IsDefault = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes default status
        /// </summary>
        public void RemoveDefault()
        {
            IsDefault = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Activates the variant
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the variant
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates pricing
        /// </summary>
        public void UpdatePricing(Money? newPrice = null, Money? newCost = null)
        {
            if (newPrice != null) Price = newPrice;
            if (newCost != null) Cost = newCost;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int order)
        {
            SortOrder = order;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"ProductVariant [Id: {Id}, ProductId: {ProductId}, Name: {Name}, SKU: {SKU}, Stock: {StockQuantity}]";
        }
    }

    /// <summary>
    /// Product variant attribute (e.g., Color=Red, Size=Large)
    /// </summary>
    public class ProductVariantAttribute : BaseEntity
    {
        /// <summary>
        /// Attribute name (e.g., "Color", "Size")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Attribute value (e.g., "Red", "Large")
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        #region Navigation Properties

        /// <summary>
        /// Related product variant
        /// </summary>
        public ProductVariant ProductVariant { get; set; } = null!;

        /// <summary>
        /// Product variant ID
        /// </summary>
        public Guid ProductVariantId { get; set; }

        #endregion

        public override string ToString()
        {
            return $"ProductVariantAttribute [Name: {Name}, Value: {Value}]";
        }
    }
}
