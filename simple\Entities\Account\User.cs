﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Account;

public class User : BaseEntity
{
    [MaxLength(30)]
    public string? Name { get; set; }

    [MaxLength(30)]
    public string? SurName { get; set; }

    [MaxLength(30)]
    public string? UserName { get; set; }

    [MaxLength(50)]
    public string? Email { get; set; }

    [MaxLength(50)]
    public string? Telephone { get; set; }

    [MaxLength(500)]
    public string? Password { get; set; }
    public bool EmailConfirmed { get; set; }

    public bool IsAdmin { get; set; }

    public string? Role { get; set; }
    public bool Status { get; set; }

 //   public ICollection<TodoUser>? AssignedToUser { get; set; }


}