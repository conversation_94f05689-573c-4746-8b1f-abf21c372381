{"openapi": "3.0.1", "info": {"title": "EtyraCommerce API", "description": "EtyraCommerce E-commerce Platform API", "version": "v1"}, "paths": {"/api/Cart/add-item": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddToCartDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddToCartDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/update-item": {"put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/remove-item": {"delete": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveFromCartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemoveFromCartDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RemoveFromCartDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/clear": {"delete": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Cart/merge": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MergeCartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MergeCartDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MergeCartDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeExpired", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/Cart/summary": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Cart/my-cart": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Category": {"post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Category"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "showInMenu", "in": "query", "schema": {"type": "boolean"}}, {"name": "parentCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "level", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "schema": {"$ref": "#/components/schemas/CategorySortField"}}, {"name": "sortDirection", "in": "query", "schema": {"$ref": "#/components/schemas/SortDirection"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/{categoryId}": {"put": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "forceDelete", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "deleteC<PERSON><PERSON>n", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeParent", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/search": {"post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategorySearchDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Category/by-parent": {"get": {"tags": ["Category"], "parameters": [{"name": "parentCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeDescriptions", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/root": {"get": {"tags": ["Category"], "parameters": [{"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/tree": {"get": {"tags": ["Category"], "parameters": [{"name": "rootCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "max<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "activeOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "menuOnly", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/menu": {"get": {"tags": ["Category"], "parameters": [{"name": "max<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 3}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/health": {"get": {"tags": ["EtyraCommerce.API"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/api/Inventory/{id}": {"get": {"tags": ["Inventory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/product/{productId}": {"get": {"tags": ["Inventory"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "warehouseId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "activeWarehousesOnly", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryDtoListCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoListCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoListCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/stock-status/{productId}": {"get": {"tags": ["Inventory"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "activeWarehousesOnly", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockStatusDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockStatusDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockStatusDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/low-stock": {"get": {"tags": ["Inventory"], "parameters": [{"name": "warehouseId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "activeWarehousesOnly", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "maxItems", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LowStockItemDtoListCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LowStockItemDtoListCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LowStockItemDtoListCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInventoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInventoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInventoryDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}, "put": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/reserve": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockReservationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockReservationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockReservationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/allocate": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockAllocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAllocationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockAllocationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/release": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReleaseReservationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReleaseReservationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReleaseReservationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/adjust": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Inventory/transfer": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockTransferDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/inventory-transactions": {"get": {"tags": ["InventoryTransaction"], "parameters": [{"name": "inventoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "warehouseId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/InventoryTransactionType"}}, {"name": "reference", "in": "query", "schema": {"type": "string"}}, {"name": "referenceType", "in": "query", "schema": {"type": "string"}}, {"name": "userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "transactionDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "transactionDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "TransactionDate"}}, {"name": "sortDirection", "in": "query", "schema": {"type": "string", "default": "DESC"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/inventory-transactions/product/{productId}": {"get": {"tags": ["InventoryTransaction"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "warehouseId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/InventoryTransactionType"}}, {"name": "transactionDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "transactionDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "TransactionDate"}}, {"name": "sortDirection", "in": "query", "schema": {"type": "string", "default": "DESC"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/inventory-transactions/warehouse/{warehouseId}": {"get": {"tags": ["InventoryTransaction"], "parameters": [{"name": "warehouseId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/InventoryTransactionType"}}, {"name": "transactionDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "transactionDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "TransactionDate"}}, {"name": "sortDirection", "in": "query", "schema": {"type": "string", "default": "DESC"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/inventory-transactions/reference/{reference}": {"get": {"tags": ["InventoryTransaction"], "parameters": [{"name": "reference", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "referenceType", "in": "query", "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "TransactionDate"}}, {"name": "sortDirection", "in": "query", "schema": {"type": "string", "default": "DESC"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResultCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Order": {"post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Order"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "customerId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/OrderStatus"}}, {"name": "paymentStatus", "in": "query", "schema": {"$ref": "#/components/schemas/PaymentStatus"}}, {"name": "shippingStatus", "in": "query", "schema": {"$ref": "#/components/schemas/ShippingStatus"}}, {"name": "minTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "currency", "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "paymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "shippingMethod", "in": "query", "schema": {"type": "string"}}, {"name": "includeItems", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeCustomer", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "sortBy", "in": "query", "schema": {"$ref": "#/components/schemas/OrderSortField"}}, {"name": "sortDirection", "in": "query", "schema": {"$ref": "#/components/schemas/SortDirection"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/from-cart": {"post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderFromCartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderFromCartDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderFromCartDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}": {"get": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "includeItems", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "includeCustomer", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "languageCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/by-number/{orderNumber}": {"get": {"tags": ["Order"], "parameters": [{"name": "orderNumber", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "includeItems", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/customer/{customerId}": {"get": {"tags": ["Order"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/my-orders": {"get": {"tags": ["Order"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/statistics": {"get": {"tags": ["Order"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "customerId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "currency", "in": "query", "schema": {"type": "string"}}, {"name": "topCustomersCount", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "recentOrdersCount", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/status": {"put": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/payment-status": {"put": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/confirm": {"post": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/cancel": {"post": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/ship": {"post": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShipOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShipOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/deliver": {"post": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliverOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliverOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeliverOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/exists": {"get": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/{orderId}/belongs-to/{customerId}": {"get": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "customerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentMethods": {"get": {"tags": ["PaymentMethods"], "parameters": [{"name": "onlyActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/PaymentMethodType"}}, {"name": "orderAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "orderCurrency", "in": "query", "schema": {"type": "string", "default": "TRY"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["PaymentMethods"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentMethodDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentMethodDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePaymentMethodDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentMethods/active": {"get": {"tags": ["PaymentMethods"], "parameters": [{"name": "orderAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "orderCurrency", "in": "query", "schema": {"type": "string", "default": "TRY"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentMethods/{id}": {"get": {"tags": ["PaymentMethods"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["PaymentMethods"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentMethodDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentMethodDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentMethodDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PaymentMethods"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentMethods/by-code/{code}": {"get": {"tags": ["PaymentMethods"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentMethods/{id}/status": {"patch": {"tags": ["PaymentMethods"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Product": {"get": {"tags": ["Product"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "isFeatured", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}}}}}, "post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}}, "/api/Product/{id}": {"get": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}, "put": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}, "delete": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/Product/search": {"post": {"tags": ["Product"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}}}}}}, "/api/Product/category/{categoryId}": {"get": {"tags": ["Product"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "includeChildCategories", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultCustomResponseDto"}}}}}}}, "/api/Product/{id}/stock": {"patch": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductStockCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductStockCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductStockCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}}, "/api/Product/{id}/price": {"patch": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductPriceCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductPriceCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductPriceCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}}, "/api/Product/{id}/status": {"patch": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductStatusCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductStatusCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductStatusCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoCustomResponseDto"}}}}}}}, "/api/User/login": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}}}}, "/api/User/register": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/refresh-token": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoCustomResponseDto"}}}}}}}, "/api/User/revoke-token": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokeTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/logout-all": {"post": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoContentDtoCustomResponseDto"}}}}}}}, "/api/User/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoCustomResponseDto"}}}}}}}, "/api/UserAddress": {"post": {"tags": ["User<PERSON>ddress"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserAddressDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserAddressDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserAddressDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/{addressId}": {"put": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "hardDelete", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/default": {"get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressType", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/billing": {"get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/shipping": {"get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/{addressId}/set-default": {"patch": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/{addressId}/toggle-status": {"patch": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "addressId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/stats": {"get": {"tags": ["User<PERSON>ddress"], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/search": {"get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "searchText", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAddress/recent": {"get": {"tags": ["User<PERSON>ddress"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/Warehouse": {"get": {"tags": ["Warehouse"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "Name"}}, {"name": "sortDirection", "in": "query", "schema": {"type": "string", "default": "ASC"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseDtoPagedResultCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoPagedResultCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoPagedResultCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}, "post": {"tags": ["Warehouse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWarehouseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWarehouseDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWarehouseDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}, "put": {"tags": ["Warehouse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWarehouseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWarehouseDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWarehouseDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseDtoCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/api/Warehouse/{id}": {"delete": {"tags": ["Warehouse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanCustomResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectCustomResponseDto"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"AddToCartDto": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "variantInfo": {"maxLength": 1000, "type": "string", "nullable": true}, "notes": {"maxLength": 1000, "type": "string", "nullable": true}, "sessionId": {"maxLength": 255, "type": "string", "nullable": true}}, "additionalProperties": false}, "Address": {"type": "object", "properties": {"street": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AttributeType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "BooleanCustomResponseDto": {"type": "object", "properties": {"data": {"type": "boolean"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "CancelOrderDto": {"type": "object", "properties": {"reason": {"maxLength": 500, "type": "string", "nullable": true}, "cancelledBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CategoryBreadcrumbDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "level": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryDescriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "languageCode": {"type": "string", "nullable": true}, "storeId": {"type": "string", "format": "uuid", "nullable": true}, "categoryId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CategoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "deletedBy": {"type": "string", "format": "uuid", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}, "deletedByName": {"type": "string", "nullable": true}, "createdByEmail": {"type": "string", "nullable": true}, "updatedByEmail": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "creatorDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "updaterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "deleterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "isSelfUpdated": {"type": "boolean", "readOnly": true}, "timeSinceDeletion": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isDeletedToday": {"type": "boolean", "readOnly": true}, "isRecentlyDeleted": {"type": "boolean", "readOnly": true}, "formattedDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "showInMenu": {"type": "boolean"}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "productCount": {"type": "integer", "format": "int32"}, "parentCategory": {"$ref": "#/components/schemas/CategoryDto"}, "childCategories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}, "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDescriptionDto"}, "nullable": true}, "level": {"type": "integer", "format": "int32"}, "isRoot": {"type": "boolean", "readOnly": true}, "hasChildren": {"type": "boolean", "readOnly": true}, "fullPath": {"type": "string", "nullable": true}, "breadcrumbs": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryBreadcrumbDto"}, "nullable": true}}, "additionalProperties": false}, "CategorySearchDto": {"type": "object", "properties": {"searchTerm": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "showInMenu": {"type": "boolean", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "level": {"maximum": 10, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "includeChildren": {"type": "boolean"}, "sortBy": {"$ref": "#/components/schemas/CategorySortField"}, "sortDirection": {"$ref": "#/components/schemas/SortDirection"}, "pageNumber": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategorySortField": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "ChangePasswordDto": {"required": ["confirmNewPassword", "currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string", "format": "password"}, "newPassword": {"maxLength": 100, "minLength": 8, "type": "string", "format": "password"}, "confirmNewPassword": {"minLength": 1, "type": "string", "format": "password"}}, "additionalProperties": false}, "CreateAddressDto": {"required": ["city", "country", "postalCode", "state", "street"], "type": "object", "properties": {"street": {"maxLength": 200, "minLength": 1, "type": "string"}, "addressLine2": {"maxLength": 200, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 1, "type": "string"}, "state": {"maxLength": 100, "minLength": 1, "type": "string"}, "postalCode": {"maxLength": 20, "minLength": 1, "type": "string"}, "country": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateCategoryDescriptionDto": {"required": ["languageCode", "name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 2000, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "languageCode": {"maxLength": 10, "minLength": 2, "type": "string"}, "storeId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "showInMenu": {"type": "boolean"}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/CreateCategoryDescriptionDto"}, "nullable": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CreateInventoryDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "warehouseId": {"type": "string", "format": "uuid"}, "availableQuantity": {"type": "integer", "format": "int32"}, "minStockLevel": {"type": "integer", "format": "int32"}, "maxStockLevel": {"type": "integer", "format": "int32"}, "reorderPoint": {"type": "integer", "format": "int32"}, "reorderQuantity": {"type": "integer", "format": "int32"}, "locationCode": {"type": "string", "nullable": true}, "supplierReference": {"type": "string", "nullable": true}, "leadTimeDays": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateOrderDto": {"required": ["billing<PERSON><PERSON>ress", "currency", "customerEmail", "customerFirstName", "customerId", "customerLastName", "orderItems", "shippingAddress"], "type": "object", "properties": {"customerId": {"type": "string", "format": "uuid"}, "customerEmail": {"maxLength": 254, "minLength": 1, "type": "string", "format": "email"}, "customerPhone": {"maxLength": 20, "type": "string", "format": "tel", "nullable": true}, "customerFirstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "customerLastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "billingAddress": {"$ref": "#/components/schemas/CreateAddressDto"}, "shippingAddress": {"$ref": "#/components/schemas/CreateAddressDto"}, "orderItems": {"minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemDto"}}, "currency": {"maxLength": 3, "minLength": 1, "type": "string"}, "notes": {"maxLength": 1000, "type": "string", "nullable": true}, "shippingMethod": {"maxLength": 100, "type": "string", "nullable": true}, "paymentMethod": {"maxLength": 100, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreateOrderFromCartDto": {"required": ["billing<PERSON><PERSON>ress", "customerEmail", "customerFirstName", "customerLastName", "paymentMethod", "shippingAddress"], "type": "object", "properties": {"sessionId": {"maxLength": 255, "type": "string", "nullable": true}, "customerFirstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "customerLastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "customerEmail": {"maxLength": 255, "minLength": 1, "type": "string", "format": "email"}, "customerPhone": {"maxLength": 20, "type": "string", "nullable": true}, "billingAddress": {"$ref": "#/components/schemas/CreateAddressDto"}, "shippingAddress": {"$ref": "#/components/schemas/CreateAddressDto"}, "paymentMethod": {"maxLength": 50, "minLength": 1, "type": "string"}, "shippingMethod": {"maxLength": 100, "type": "string", "nullable": true}, "notes": {"maxLength": 1000, "type": "string", "nullable": true}, "currency": {"maxLength": 3, "type": "string", "nullable": true}, "clearCartAfterOrder": {"type": "boolean"}}, "additionalProperties": false}, "CreateOrderItemDto": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "variantInfo": {"maxLength": 500, "type": "string", "nullable": true}, "specialInstructions": {"maxLength": 1000, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreatePaymentMethodDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/components/schemas/PaymentMethodType"}, "feeCalculationType": {"$ref": "#/components/schemas/FeeCalculationType"}, "feeValue": {"type": "number", "format": "double"}, "feeCurrencyCode": {"type": "string", "nullable": true}, "feeCurrencyName": {"type": "string", "nullable": true}, "feeCurrencySymbol": {"type": "string", "nullable": true}, "feeCurrencyDecimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "minimumOrderAmount": {"type": "number", "format": "double", "nullable": true}, "minimumOrderCurrency": {"type": "string", "nullable": true}, "maximumOrderAmount": {"type": "number", "format": "double", "nullable": true}, "maximumOrderCurrency": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductAttributeDto": {"required": ["name", "value"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "value": {"maxLength": 500, "minLength": 1, "type": "string"}, "type": {"$ref": "#/components/schemas/AttributeType"}, "group": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isVisible": {"type": "boolean"}, "isSearchable": {"type": "boolean"}, "isFilterable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "unit": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductCommand": {"type": "object", "properties": {"model": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "stars": {"type": "integer", "format": "int32", "nullable": true}, "aiPlatformId": {"type": "integer", "format": "int32", "nullable": true}, "ean": {"type": "string", "nullable": true}, "mpn": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "upc": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "basePrice": {"type": "number", "format": "double"}, "basePriceCurrency": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "costCurrency": {"type": "string", "nullable": true}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "salePriceCurrency": {"type": "string", "nullable": true}, "defaultCurrency": {"type": "string", "nullable": true}, "dimensions": {"$ref": "#/components/schemas/CreateProductDimensionsDto"}, "mainImage": {"type": "string", "nullable": true}, "thumbnailImage": {"type": "string", "nullable": true}, "totalStockQuantity": {"type": "integer", "format": "int32"}, "minStockAlert": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/ProductStatus"}, "type": {"$ref": "#/components/schemas/ProductType"}, "manageStock": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "isDigital": {"type": "boolean"}, "requiresShipping": {"type": "boolean"}, "isTaxable": {"type": "boolean"}, "slug": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "availableStartDate": {"type": "string", "format": "date-time", "nullable": true}, "availableEndDate": {"type": "string", "format": "date-time", "nullable": true}, "saleStartDate": {"type": "string", "format": "date-time", "nullable": true}, "saleEndDate": {"type": "string", "format": "date-time", "nullable": true}, "discontinueDate": {"type": "string", "format": "date-time", "nullable": true}, "primaryCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "categoryIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductDescriptionDto"}, "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductImageDto"}, "nullable": true}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductAttributeDto"}, "nullable": true}}, "additionalProperties": false}, "CreateProductDescriptionDto": {"required": ["description", "languageCode", "name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 10000, "minLength": 1, "type": "string"}, "shortDescription": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "tags": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "languageCode": {"maxLength": 10, "minLength": 2, "type": "string"}, "storeId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreateProductDimensionsDto": {"type": "object", "properties": {"length": {"minimum": 0, "type": "number", "format": "double"}, "width": {"minimum": 0, "type": "number", "format": "double"}, "height": {"minimum": 0, "type": "number", "format": "double"}, "weight": {"minimum": 0, "type": "number", "format": "double"}, "unit": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "weightUnit": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductImageDto": {"required": ["imageUrl"], "type": "object", "properties": {"imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri"}, "thumbnailUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri", "nullable": true}, "altText": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "title": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/ProductImageType"}, "fileSize": {"minimum": 1, "type": "integer", "format": "int64", "nullable": true}, "width": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32", "nullable": true}, "height": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32", "nullable": true}, "mimeType": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "originalFileName": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserAddressDto": {"required": ["city", "country", "firstName", "lastName", "postalCode", "state", "street", "type"], "type": "object", "properties": {"type": {"maximum": 3, "minimum": 1, "type": "integer", "format": "int32"}, "isDefault": {"type": "boolean"}, "firstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "label": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "deliveryInstructions": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "street": {"maxLength": 200, "minLength": 1, "type": "string"}, "addressLine2": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 1, "type": "string"}, "state": {"maxLength": 100, "minLength": 1, "type": "string"}, "postalCode": {"maxLength": 20, "minLength": 1, "type": "string"}, "country": {"maxLength": 100, "minLength": 1, "type": "string"}, "companyName": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "taxNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "taxOffice": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "companyTitle": {"maxLength": 300, "minLength": 0, "type": "string", "nullable": true}, "isCompanyAddress": {"type": "boolean"}}, "additionalProperties": false}, "CreateUserDto": {"required": ["confirmPassword", "email", "firstName", "lastName", "password", "username"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "email": {"maxLength": 254, "minLength": 0, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "username": {"maxLength": 50, "minLength": 3, "pattern": "^[a-zA-Z0-9_.-]+$", "type": "string"}, "password": {"maxLength": 100, "minLength": 8, "type": "string", "format": "password"}, "confirmPassword": {"minLength": 1, "type": "string", "format": "password"}, "culture": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "timeZone": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "skipEmailConfirmation": {"type": "boolean"}, "notes": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "acceptTerms": {"type": "boolean"}, "subscribeToNewsletter": {"type": "boolean"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CreateWarehouseDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/Address"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "managerName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/WarehouseType"}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeliverOrderDto": {"type": "object", "properties": {"deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "confirmationMethod": {"maxLength": 100, "type": "string", "nullable": true}, "receivedBy": {"maxLength": 100, "type": "string", "nullable": true}, "notes": {"maxLength": 500, "type": "string", "nullable": true}, "deliveryPhotoUrl": {"maxLength": 500, "type": "string", "nullable": true}, "signatureUrl": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "DiscountType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "FeeCalculationType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "InventoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "warehouseId": {"type": "string", "format": "uuid"}, "availableQuantity": {"type": "integer", "format": "int32"}, "reservedQuantity": {"type": "integer", "format": "int32"}, "allocatedQuantity": {"type": "integer", "format": "int32"}, "minStockLevel": {"type": "integer", "format": "int32"}, "maxStockLevel": {"type": "integer", "format": "int32"}, "reorderPoint": {"type": "integer", "format": "int32"}, "reorderQuantity": {"type": "integer", "format": "int32"}, "locationCode": {"type": "string", "nullable": true}, "supplierReference": {"type": "string", "nullable": true}, "leadTimeDays": {"type": "integer", "format": "int32", "nullable": true}, "lastStockUpdate": {"type": "string", "format": "date-time", "nullable": true}, "lastPhysicalCount": {"type": "string", "format": "date-time", "nullable": true}, "status": {"$ref": "#/components/schemas/InventoryStatus"}, "notes": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "totalQuantity": {"type": "integer", "format": "int32"}, "freeQuantity": {"type": "integer", "format": "int32"}, "isLowStock": {"type": "boolean"}, "isOutOfStock": {"type": "boolean"}, "isOverstocked": {"type": "boolean"}, "productName": {"type": "string", "nullable": true}, "productModel": {"type": "string", "nullable": true}, "warehouseName": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InventoryDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/InventoryDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "InventoryDtoListCustomResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryDto"}, "nullable": true}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "InventoryStatus": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "InventoryTransactionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "inventoryId": {"type": "string", "format": "uuid"}, "type": {"$ref": "#/components/schemas/InventoryTransactionType"}, "quantity": {"type": "integer", "format": "int32"}, "quantityBefore": {"type": "integer", "format": "int32"}, "quantityAfter": {"type": "integer", "format": "int32"}, "reference": {"type": "string", "nullable": true}, "referenceType": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "unitCost": {"type": "number", "format": "double", "nullable": true}, "totalCost": {"type": "number", "format": "double", "nullable": true}, "externalReference": {"type": "string", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "productName": {"type": "string", "nullable": true}, "productModel": {"type": "string", "nullable": true}, "warehouseName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InventoryTransactionDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryTransactionDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "itemCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "InventoryTransactionDtoPagedResultCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/InventoryTransactionDtoPagedResult"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "InventoryTransactionType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "type": "integer", "format": "int32"}, "LowStockItemDto": {"type": "object", "properties": {"inventoryId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "productModel": {"type": "string", "nullable": true}, "warehouseId": {"type": "string", "format": "uuid"}, "warehouseName": {"type": "string", "nullable": true}, "availableQuantity": {"type": "integer", "format": "int32"}, "reorderPoint": {"type": "integer", "format": "int32"}, "reorderQuantity": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/InventoryStatus"}, "leadTimeDays": {"type": "integer", "format": "int32", "nullable": true}, "supplierReference": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LowStockItemDtoListCustomResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LowStockItemDto"}, "nullable": true}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "MergeCartDto": {"required": ["customerId", "sessionId"], "type": "object", "properties": {"sessionId": {"maxLength": 255, "minLength": 1, "type": "string"}, "customerId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "NoContentDto": {"type": "object", "additionalProperties": false}, "NoContentDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/NoContentDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "ObjectCustomResponseDto": {"type": "object", "properties": {"data": {"nullable": true}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "OrderSortField": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "OrderStatus": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "PaymentMethodType": {"enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 99], "type": "integer", "format": "int32"}, "PaymentStatus": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "PriceAdjustmentType": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "ProductAttributeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/AttributeType"}, "group": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisible": {"type": "boolean"}, "isSearchable": {"type": "boolean"}, "isFilterable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "unit": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "formattedValue": {"type": "string", "nullable": true, "readOnly": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductDescriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "languageCode": {"type": "string", "nullable": true}, "storeId": {"type": "string", "format": "uuid", "nullable": true}, "productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "ProductDimensionsDto": {"type": "object", "properties": {"length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "unit": {"type": "string", "nullable": true}, "weightUnit": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volumetricWeight": {"type": "number", "format": "double"}, "formattedDimensions": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductDiscountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/DiscountType"}, "value": {"type": "number", "format": "double"}, "minQuantity": {"type": "integer", "format": "int32", "nullable": true}, "maxQuantity": {"type": "integer", "format": "int32", "nullable": true}, "minOrderAmount": {"type": "number", "format": "double", "nullable": true}, "minOrderCurrency": {"type": "string", "nullable": true}, "maxDiscountAmount": {"type": "number", "format": "double", "nullable": true}, "maxDiscountCurrency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "maxUses": {"type": "integer", "format": "int32", "nullable": true}, "currentUses": {"type": "integer", "format": "int32"}, "canCombine": {"type": "boolean"}, "productId": {"type": "string", "format": "uuid"}, "isValid": {"type": "boolean"}, "remainingUses": {"type": "integer", "format": "int32", "nullable": true}, "formattedValue": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "deletedBy": {"type": "string", "format": "uuid", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}, "deletedByName": {"type": "string", "nullable": true}, "createdByEmail": {"type": "string", "nullable": true}, "updatedByEmail": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "creatorDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "updaterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "deleterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "isSelfUpdated": {"type": "boolean", "readOnly": true}, "timeSinceDeletion": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isDeletedToday": {"type": "boolean", "readOnly": true}, "isRecentlyDeleted": {"type": "boolean", "readOnly": true}, "formattedDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "model": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "stars": {"type": "integer", "format": "int32", "nullable": true}, "aiPlatformId": {"type": "integer", "format": "int32", "nullable": true}, "ean": {"type": "string", "nullable": true}, "mpn": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "upc": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "basePrice": {"type": "number", "format": "double"}, "basePriceCurrency": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "costCurrency": {"type": "string", "nullable": true}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "salePriceCurrency": {"type": "string", "nullable": true}, "defaultCurrency": {"type": "string", "nullable": true}, "effectivePrice": {"type": "number", "format": "double"}, "effectivePriceCurrency": {"type": "string", "nullable": true}, "dimensions": {"$ref": "#/components/schemas/ProductDimensionsDto"}, "mainImage": {"type": "string", "nullable": true}, "thumbnailImage": {"type": "string", "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImageDto"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ProductStatus"}, "type": {"$ref": "#/components/schemas/ProductType"}, "isFeatured": {"type": "boolean"}, "isDigital": {"type": "boolean"}, "slug": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "saleStartDate": {"type": "string", "format": "date-time", "nullable": true}, "saleEndDate": {"type": "string", "format": "date-time", "nullable": true}, "launchDate": {"type": "string", "format": "date-time", "nullable": true}, "discontinueDate": {"type": "string", "format": "date-time", "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDescriptionDto"}, "nullable": true}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}, "nullable": true}, "primaryCategory": {"$ref": "#/components/schemas/CategoryDto"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDiscountDto"}, "nullable": true}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/ProductAttributeDto"}, "nullable": true}, "isOnSale": {"type": "boolean"}, "discountPercentage": {"type": "number", "format": "double", "nullable": true}, "profitMargin": {"type": "number", "format": "double", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "formattedPrice": {"type": "string", "nullable": true}, "formattedSalePrice": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "ProductDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "itemCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ProductDtoPagedResultCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductDtoPagedResult"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "ProductImageDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "imageUrl": {"type": "string", "nullable": true}, "thumbnailUrl": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/ProductImageType"}, "fileSize": {"type": "integer", "format": "int64", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "height": {"type": "integer", "format": "int32", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "originalFileName": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "displayUrl": {"type": "string", "nullable": true, "readOnly": true}, "formattedFileSize": {"type": "string", "nullable": true, "readOnly": true}, "dimensions": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductImageType": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "ProductStatus": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "ProductType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "RefreshTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReleaseReservationRequest": {"type": "object", "properties": {"reference": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "RemoveFromCartDto": {"required": ["productId"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "RevokeTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}, "reason": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipOrderDto": {"type": "object", "properties": {"trackingNumber": {"maxLength": 100, "type": "string", "nullable": true}, "expectedDeliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "shippingMethod": {"maxLength": 100, "type": "string", "nullable": true}, "carrier": {"maxLength": 100, "type": "string", "nullable": true}, "notes": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "ShippingStatus": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "SortDirection": {"enum": [0, 1], "type": "integer", "format": "int32"}, "StockAdjustmentDto": {"type": "object", "properties": {"inventoryId": {"type": "string", "format": "uuid"}, "newQuantity": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockAdjustmentType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StockAllocationDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "warehouseId": {"type": "string", "format": "uuid", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "reference": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockReservationDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "warehouseId": {"type": "string", "format": "uuid", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "reference": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockStatusDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "totalAvailable": {"type": "integer", "format": "int32"}, "totalReserved": {"type": "integer", "format": "int32"}, "totalAllocated": {"type": "integer", "format": "int32"}, "isInStock": {"type": "boolean"}, "isLowStock": {"type": "boolean"}, "warehouseStocks": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseStockDto"}, "nullable": true}}, "additionalProperties": false}, "StockStatusDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/StockStatusDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "StockTransferDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "fromWarehouseId": {"type": "string", "format": "uuid"}, "toWarehouseId": {"type": "string", "format": "uuid"}, "quantity": {"type": "integer", "format": "int32"}, "reference": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenResponseDto": {"required": ["accessToken", "expiresAt", "refreshToken", "refreshTokenExpiresAt", "tokenType"], "type": "object", "properties": {"accessToken": {"minLength": 1, "type": "string"}, "refreshToken": {"minLength": 1, "type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "refreshTokenExpiresAt": {"type": "string", "format": "date-time"}, "tokenType": {"minLength": 1, "type": "string"}, "expiresIn": {"type": "integer", "format": "int32"}, "user": {"$ref": "#/components/schemas/TokenUserDto"}, "scopes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "claims": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "TokenResponseDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TokenResponseDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "TokenUserDto": {"required": ["email", "id", "username"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string"}, "fullName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateCartItemDto": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "quantity": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "variantInfo": {"maxLength": 1000, "type": "string", "nullable": true}, "notes": {"maxLength": 1000, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "parentCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "format": "uri", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "showInMenu": {"type": "boolean"}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UpdateInventoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "availableQuantity": {"type": "integer", "format": "int32", "nullable": true}, "minStockLevel": {"type": "integer", "format": "int32", "nullable": true}, "maxStockLevel": {"type": "integer", "format": "int32", "nullable": true}, "reorderPoint": {"type": "integer", "format": "int32", "nullable": true}, "reorderQuantity": {"type": "integer", "format": "int32", "nullable": true}, "locationCode": {"type": "string", "nullable": true}, "supplierReference": {"type": "string", "nullable": true}, "leadTimeDays": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateOrderStatusDto": {"required": ["orderId", "status"], "type": "object", "properties": {"orderId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/OrderStatus"}, "reason": {"maxLength": 500, "type": "string", "nullable": true}, "trackingNumber": {"maxLength": 100, "type": "string", "nullable": true}, "expectedDeliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "actualDeliveryDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdatePaymentMethodDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/components/schemas/PaymentMethodType"}, "feeCalculationType": {"$ref": "#/components/schemas/FeeCalculationType"}, "feeValue": {"type": "number", "format": "double"}, "feeCurrencyCode": {"type": "string", "nullable": true}, "feeCurrencyName": {"type": "string", "nullable": true}, "feeCurrencySymbol": {"type": "string", "nullable": true}, "feeCurrencyDecimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "minimumOrderAmount": {"type": "number", "format": "double", "nullable": true}, "minimumOrderCurrency": {"type": "string", "nullable": true}, "maximumOrderAmount": {"type": "number", "format": "double", "nullable": true}, "maximumOrderCurrency": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdatePaymentStatusDto": {"required": ["orderId", "paymentStatus"], "type": "object", "properties": {"orderId": {"type": "string", "format": "uuid"}, "paymentStatus": {"$ref": "#/components/schemas/PaymentStatus"}, "paymentMethod": {"maxLength": 100, "type": "string", "nullable": true}, "paymentReference": {"maxLength": 200, "type": "string", "nullable": true}, "notes": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductAttributeDto": {"required": ["value"], "type": "object", "properties": {"value": {"maxLength": 500, "minLength": 1, "type": "string"}, "group": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isVisible": {"type": "boolean"}, "isSearchable": {"type": "boolean"}, "isFilterable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "unit": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "stars": {"type": "integer", "format": "int32", "nullable": true}, "aiPlatformId": {"type": "integer", "format": "int32", "nullable": true}, "ean": {"type": "string", "nullable": true}, "mpn": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "upc": {"type": "string", "nullable": true}, "basePrice": {"type": "number", "format": "double"}, "basePriceCurrency": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "costCurrency": {"type": "string", "nullable": true}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "salePriceCurrency": {"type": "string", "nullable": true}, "defaultCurrency": {"type": "string", "nullable": true}, "dimensions": {"$ref": "#/components/schemas/UpdateProductDimensionsDto"}, "mainImage": {"type": "string", "nullable": true}, "thumbnailImage": {"type": "string", "nullable": true}, "totalStockQuantity": {"type": "integer", "format": "int32"}, "minStockAlert": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/ProductStatus"}, "type": {"$ref": "#/components/schemas/ProductType"}, "manageStock": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "isDigital": {"type": "boolean"}, "requiresShipping": {"type": "boolean"}, "isTaxable": {"type": "boolean"}, "slug": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "metaKeywords": {"type": "string", "nullable": true}, "availableStartDate": {"type": "string", "format": "date-time", "nullable": true}, "availableEndDate": {"type": "string", "format": "date-time", "nullable": true}, "saleStartDate": {"type": "string", "format": "date-time", "nullable": true}, "saleEndDate": {"type": "string", "format": "date-time", "nullable": true}, "discontinueDate": {"type": "string", "format": "date-time", "nullable": true}, "primaryCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "categoryIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "descriptions": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductDescriptionDto"}, "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductImageDto"}, "nullable": true}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductAttributeDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateProductDescriptionDto": {"required": ["description", "name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 10000, "minLength": 1, "type": "string"}, "shortDescription": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 120, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 350, "minLength": 0, "type": "string", "nullable": true}, "metaKeywords": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "tags": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "slug": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductDimensionsDto": {"type": "object", "properties": {"length": {"minimum": 0, "type": "number", "format": "double"}, "width": {"minimum": 0, "type": "number", "format": "double"}, "height": {"minimum": 0, "type": "number", "format": "double"}, "weight": {"minimum": 0, "type": "number", "format": "double"}, "unit": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "weightUnit": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductImageDto": {"type": "object", "properties": {"altText": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "title": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/ProductImageType"}}, "additionalProperties": false}, "UpdateProductPriceCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "basePrice": {"type": "number", "format": "double", "nullable": true}, "basePriceCurrency": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "costCurrency": {"type": "string", "nullable": true}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "salePriceCurrency": {"type": "string", "nullable": true}, "saleStartDate": {"type": "string", "format": "date-time", "nullable": true}, "saleEndDate": {"type": "string", "format": "date-time", "nullable": true}, "reason": {"type": "string", "nullable": true}, "adjustmentType": {"$ref": "#/components/schemas/PriceAdjustmentType"}}, "additionalProperties": false}, "UpdateProductStatusCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ProductStatus"}, "isActive": {"type": "boolean", "nullable": true}, "isFeatured": {"type": "boolean", "nullable": true}, "discontinueDate": {"type": "string", "format": "date-time", "nullable": true}, "availableStartDate": {"type": "string", "format": "date-time", "nullable": true}, "availableEndDate": {"type": "string", "format": "date-time", "nullable": true}, "reason": {"type": "string", "nullable": true}, "sendNotification": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductStockCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "totalStockQuantity": {"type": "integer", "format": "int32"}, "minStockAlert": {"type": "integer", "format": "int32", "nullable": true}, "manageStock": {"type": "boolean", "nullable": true}, "reason": {"type": "string", "nullable": true}, "adjustmentType": {"$ref": "#/components/schemas/StockAdjustmentType"}}, "additionalProperties": false}, "UpdateUserAddressDto": {"required": ["city", "country", "firstName", "lastName", "postalCode", "state", "street", "type"], "type": "object", "properties": {"type": {"maximum": 3, "minimum": 1, "type": "integer", "format": "int32"}, "isDefault": {"type": "boolean"}, "firstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "label": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "deliveryInstructions": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "street": {"maxLength": 200, "minLength": 1, "type": "string"}, "addressLine2": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 1, "type": "string"}, "state": {"maxLength": 100, "minLength": 1, "type": "string"}, "postalCode": {"maxLength": 20, "minLength": 1, "type": "string"}, "country": {"maxLength": 100, "minLength": 1, "type": "string"}, "companyName": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "taxNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "taxOffice": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "companyTitle": {"maxLength": 300, "minLength": 0, "type": "string", "nullable": true}, "isCompanyAddress": {"type": "boolean"}}, "additionalProperties": false}, "UpdateWarehouseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/Address"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "managerName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/WarehouseType"}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "rowVersion": {"type": "string", "format": "byte", "nullable": true}, "age": {"type": "string", "format": "date-span", "readOnly": true}, "timeSinceUpdate": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isCreatedToday": {"type": "boolean", "readOnly": true}, "isUpdatedToday": {"type": "boolean", "readOnly": true}, "isRecentlyCreated": {"type": "boolean", "readOnly": true}, "isRecentlyUpdated": {"type": "boolean", "readOnly": true}, "formattedCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "formattedUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeCreatedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeUpdatedAt": {"type": "string", "nullable": true, "readOnly": true}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "deletedBy": {"type": "string", "format": "uuid", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}, "deletedByName": {"type": "string", "nullable": true}, "createdByEmail": {"type": "string", "nullable": true}, "updatedByEmail": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "creatorDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "updaterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "deleterDisplayName": {"type": "string", "nullable": true, "readOnly": true}, "isSelfUpdated": {"type": "boolean", "readOnly": true}, "timeSinceDeletion": {"type": "string", "format": "date-span", "nullable": true, "readOnly": true}, "isDeletedToday": {"type": "boolean", "readOnly": true}, "isRecentlyDeleted": {"type": "boolean", "readOnly": true}, "formattedDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "relativeDeletedAt": {"type": "string", "nullable": true, "readOnly": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "isEmailConfirmed": {"type": "boolean"}, "isPhoneConfirmed": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "failedLoginAttempts": {"type": "integer", "format": "int32"}, "lockedUntil": {"type": "string", "format": "date-time", "nullable": true}, "culture": {"type": "string", "nullable": true}, "timeZone": {"type": "string", "nullable": true}, "profilePictureUrl": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "isLocked": {"type": "boolean", "readOnly": true}, "canLogin": {"type": "boolean", "readOnly": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}, "maskedEmail": {"type": "string", "nullable": true, "readOnly": true}, "maskedPhoneNumber": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "UserLoginDto": {"required": ["emailOrUsername", "password"], "type": "object", "properties": {"emailOrUsername": {"maxLength": 254, "minLength": 0, "type": "string"}, "password": {"minLength": 1, "type": "string", "format": "password"}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "WarehouseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/Address"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "managerName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isMain": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/WarehouseType"}, "sortOrder": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "totalProducts": {"type": "integer", "format": "int32"}, "lowStockItems": {"type": "integer", "format": "int32"}, "outOfStockItems": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WarehouseDtoCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/WarehouseDto"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "WarehouseDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "itemCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "WarehouseDtoPagedResultCustomResponseDto": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/WarehouseDtoPagedResult"}, "errorList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "validationErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": false}, "WarehouseStockDto": {"type": "object", "properties": {"warehouseId": {"type": "string", "format": "uuid"}, "warehouseName": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "availableQuantity": {"type": "integer", "format": "int32"}, "reservedQuantity": {"type": "integer", "format": "int32"}, "allocatedQuantity": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/InventoryStatus"}, "locationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WarehouseType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}