using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Domain.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers;

/// <summary>
/// Payment methods management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PaymentMethodsController : ControllerBase
{
    private readonly IPaymentMethodService _paymentMethodService;
    private readonly ILogger<PaymentMethodsController> _logger;

    public PaymentMethodsController(
        IPaymentMethodService paymentMethodService,
        ILogger<PaymentMethodsController> logger)
    {
        _paymentMethodService = paymentMethodService;
        _logger = logger;
    }

    /// <summary>
    /// Get all payment methods
    /// </summary>
    /// <param name="onlyActive">Filter only active payment methods</param>
    /// <param name="type">Filter by payment method type</param>
    /// <param name="orderAmount">Order amount to check availability and calculate fees</param>
    /// <param name="orderCurrency">Order currency (default: TRY)</param>
    /// <returns>List of payment methods</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetAllPaymentMethods(
        [FromQuery] bool? onlyActive = null,
        [FromQuery] PaymentMethodType? type = null,
        [FromQuery] decimal? orderAmount = null,
        [FromQuery] string? orderCurrency = "TRY")
    {
        try
        {
            _logger.LogInformation("Getting all payment methods with filters - OnlyActive: {OnlyActive}, Type: {Type}, OrderAmount: {OrderAmount}, OrderCurrency: {OrderCurrency}",
                onlyActive, type, orderAmount, orderCurrency);

            var result = await _paymentMethodService.GetAllPaymentMethodsAsync(onlyActive, type);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all payment methods");
            return StatusCode(500, "An error occurred while retrieving payment methods");
        }
    }

    /// <summary>
    /// Get active payment methods for checkout with calculated fees
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency (default: TRY)</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    [HttpGet("active")]
    [AllowAnonymous]
    public async Task<IActionResult> GetActivePaymentMethods(
        [FromQuery] decimal orderAmount,
        [FromQuery] string orderCurrency = "TRY")
    {
        try
        {
            _logger.LogInformation("Getting active payment methods for order amount: {OrderAmount} {OrderCurrency}", 
                orderAmount, orderCurrency);

            var result = await _paymentMethodService.GetActivePaymentMethodsForOrderAsync(orderAmount, orderCurrency);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting active payment methods for order amount: {OrderAmount} {OrderCurrency}", 
                orderAmount, orderCurrency);
            return StatusCode(500, "An error occurred while retrieving active payment methods");
        }
    }

    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Payment method</returns>
    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetPaymentMethodById(Guid id)
    {
        try
        {
            _logger.LogInformation("Getting payment method by ID: {PaymentMethodId}", id);

            var result = await _paymentMethodService.GetPaymentMethodByIdAsync(id);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.StatusCode == 404)
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting payment method by ID: {PaymentMethodId}", id);
            return StatusCode(500, "An error occurred while retrieving the payment method");
        }
    }

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <returns>Payment method</returns>
    [HttpGet("by-code/{code}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetPaymentMethodByCode(string code)
    {
        try
        {
            _logger.LogInformation("Getting payment method by code: {Code}", code);

            var result = await _paymentMethodService.GetPaymentMethodByCodeAsync(code);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.StatusCode == 404)
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting payment method by code: {Code}", code);
            return StatusCode(500, "An error occurred while retrieving the payment method");
        }
    }

    /// <summary>
    /// Create a new payment method
    /// </summary>
    /// <param name="createDto">Create payment method DTO</param>
    /// <returns>Created payment method</returns>
    [HttpPost]
    public async Task<IActionResult> CreatePaymentMethod([FromBody] CreatePaymentMethodDto createDto)
    {
        try
        {
            _logger.LogInformation("Creating payment method: {Name}", createDto.Name);

            var result = await _paymentMethodService.CreatePaymentMethodAsync(createDto);

            if (result.IsSuccess)
            {
                return CreatedAtAction(
                    nameof(GetPaymentMethodById),
                    new { id = result.Data?.Id },
                    result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating payment method: {Name}", createDto.Name);
            return StatusCode(500, "An error occurred while creating the payment method");
        }
    }

    /// <summary>
    /// Update an existing payment method
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="updateDto">Update payment method DTO</param>
    /// <returns>Updated payment method</returns>
    [HttpPut("{id:guid}")]
    public async Task<IActionResult> UpdatePaymentMethod(Guid id, [FromBody] UpdatePaymentMethodDto updateDto)
    {
        try
        {
            if (id != updateDto.Id)
            {
                return BadRequest("Payment method ID in URL does not match the ID in the request body");
            }

            _logger.LogInformation("Updating payment method: {PaymentMethodId}", id);

            var result = await _paymentMethodService.UpdatePaymentMethodAsync(updateDto);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.StatusCode == 404)
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating payment method: {PaymentMethodId}", id);
            return StatusCode(500, "An error occurred while updating the payment method");
        }
    }

    /// <summary>
    /// Delete a payment method
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Success result</returns>
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeletePaymentMethod(Guid id)
    {
        try
        {
            _logger.LogInformation("Deleting payment method: {PaymentMethodId}", id);

            var result = await _paymentMethodService.DeletePaymentMethodAsync(id);

            if (result.IsSuccess)
            {
                return NoContent();
            }

            if (result.StatusCode == 404)
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting payment method: {PaymentMethodId}", id);
            return StatusCode(500, "An error occurred while deleting the payment method");
        }
    }

    /// <summary>
    /// Toggle payment method status (activate/deactivate)
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method</returns>
    [HttpPatch("{id:guid}/status")]
    public async Task<IActionResult> TogglePaymentMethodStatus(Guid id, [FromBody] bool isActive)
    {
        try
        {
            _logger.LogInformation("Toggling payment method status: {PaymentMethodId} to {IsActive}", id, isActive);

            var result = await _paymentMethodService.TogglePaymentMethodStatusAsync(id, isActive);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.StatusCode == 404)
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while toggling payment method status: {PaymentMethodId}", id);
            return StatusCode(500, "An error occurred while toggling the payment method status");
        }
    }
}
