using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Order.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Order.Handlers.Commands
{
    /// <summary>
    /// Handler for CancelOrderCommand
    /// </summary>
    public class CancelOrderCommandHandler : IRequestHandler<CancelOrderCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IOrderProcessService _orderProcessService;
        private readonly ILogger<CancelOrderCommandHandler> _logger;

        public CancelOrderCommandHandler(
            IOrderProcessService orderProcessService,
            ILogger<CancelOrderCommandHandler> logger)
        {
            _orderProcessService = orderProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(CancelOrderCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing cancel order command for order: {OrderId}", request.OrderId);

                // Validation
                if (request.OrderId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Order ID is required");

                // Delegate to process service
                var result = await _orderProcessService.ProcessCancelOrderAsync(
                    request.OrderId,
                    request.Reason,
                    request.CancelledBy);

                _logger.LogInformation("Cancel order command processed successfully for order: {OrderId}", request.OrderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing cancel order command for order: {OrderId}", request.OrderId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while cancelling the order");
            }
        }
    }
}
