using EtyraCommerce.Application.Features.Shipping.Countries.Commands;
using EtyraCommerce.Application.Features.Shipping.Countries.Queries;
using EtyraCommerce.Application.Interfaces.Repositories.Shipping;
using EtyraCommerce.Application.Interfaces.Services.Shipping;
using EtyraCommerce.Domain.Entities.Shipping;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Shipping
{
    /// <summary>
    /// Country process service implementation - contains business logic
    /// Called by CQRS handlers for actual business processing
    /// </summary>
    public class CountryProcessService : ICountryProcessService
    {
        private readonly ICountryRepository _countryRepository;
        private readonly ILogger<CountryProcessService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CountryProcessService(
            ICountryRepository countryRepository,
            ILogger<CountryProcessService> logger)
        {
            _countryRepository = countryRepository ?? throw new ArgumentNullException(nameof(countryRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processes country creation with business logic
        /// </summary>
        public async Task<CreateCountryResponse> ProcessCreateCountryAsync(CreateCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing country creation: {CountryName} ({CountryCode})", command.Name, command.Code);

                // Check if country already exists by code
                if (await _countryRepository.CountryExistsByCodeAsync(command.Code, cancellationToken))
                {
                    _logger.LogWarning("Country with code {CountryCode} already exists", command.Code);
                    throw new InvalidOperationException($"Country with code '{command.Code}' already exists");
                }

                // Check if country already exists by ISO code
                if (await _countryRepository.CountryExistsByIsoCodeAsync(command.IsoCode, cancellationToken))
                {
                    _logger.LogWarning("Country with ISO code {IsoCode} already exists", command.IsoCode);
                    throw new InvalidOperationException($"Country with ISO code '{command.IsoCode}' already exists");
                }

                // Create new country entity
                var country = new Country(
                    command.Name,
                    command.Code,
                    command.IsoCode,
                    command.CurrencyCode,
                    command.IsEuMember,
                    command.PhoneCode)
                {
                    IsShippingEnabled = command.IsShippingEnabled,
                    DisplayOrder = command.DisplayOrder
                };

                // Add to repository
                await _countryRepository.AddCountryAsync(country, cancellationToken);
                await _countryRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created country: {CountryId} - {CountryName}", country.Id, country.Name);

                // Return response
                return new CreateCountryResponse
                {
                    Id = country.Id,
                    Name = country.Name,
                    Code = country.Code,
                    IsoCode = country.IsoCode,
                    CurrencyCode = country.CurrencyCode,
                    PhoneCode = country.PhoneCode,
                    IsEuMember = country.IsEuMember,
                    IsShippingEnabled = country.IsShippingEnabled,
                    DisplayOrder = country.DisplayOrder,
                    CreatedAt = country.CreatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing country creation: {CountryName} ({CountryCode})", command.Name, command.Code);
                throw;
            }
        }

        /// <summary>
        /// Processes country update with business logic
        /// </summary>
        public async Task<UpdateCountryResponse> ProcessUpdateCountryAsync(UpdateCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing country update: {CountryId}", command.Id);

                // Get existing country
                var country = await _countryRepository.GetCountryByIdAsync(command.Id, cancellationToken);
                if (country == null)
                {
                    _logger.LogWarning("Country not found: {CountryId}", command.Id);
                    throw new InvalidOperationException($"Country with ID '{command.Id}' not found");
                }

                // Update country properties
                country.UpdateDetails(
                    command.Name,
                    command.CurrencyCode,
                    command.PhoneCode,
                    command.IsEuMember,
                    command.IsShippingEnabled,
                    command.DisplayOrder);

                // Save changes
                await _countryRepository.UpdateCountryAsync(country, cancellationToken);
                await _countryRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated country: {CountryId} - {CountryName}", country.Id, country.Name);

                // Return response
                return new UpdateCountryResponse
                {
                    Id = country.Id,
                    Name = country.Name,
                    Code = country.Code,
                    IsoCode = country.IsoCode,
                    CurrencyCode = country.CurrencyCode,
                    PhoneCode = country.PhoneCode,
                    IsEuMember = country.IsEuMember,
                    IsShippingEnabled = country.IsShippingEnabled,
                    DisplayOrder = country.DisplayOrder,
                    UpdatedAt = country.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing country update: {CountryId}", command.Id);
                throw;
            }
        }

        /// <summary>
        /// Processes country deletion with business logic
        /// </summary>
        public async Task<DeleteCountryResponse> ProcessDeleteCountryAsync(DeleteCountryCommand command, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing country deletion: {CountryId}", command.Id);

                // Get existing country
                var country = await _countryRepository.GetCountryByIdAsync(command.Id, cancellationToken);
                if (country == null)
                {
                    _logger.LogWarning("Country not found: {CountryId}", command.Id);
                    throw new InvalidOperationException($"Country with ID '{command.Id}' not found");
                }

                // Soft delete
                await _countryRepository.DeleteCountryAsync(country, cancellationToken);
                await _countryRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully deleted country: {CountryId} - {CountryName}", country.Id, country.Name);

                // Return response
                return new DeleteCountryResponse
                {
                    Id = country.Id,
                    Name = country.Name,
                    DeletedAt = country.DeletedAt ?? DateTime.UtcNow,
                    Message = $"Country '{country.Name}' has been successfully deleted"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing country deletion: {CountryId}", command.Id);
                throw;
            }
        }

        /// <summary>
        /// Processes get country by ID with business logic
        /// </summary>
        public async Task<GetCountryByIdResponse> ProcessGetCountryByIdAsync(GetCountryByIdQuery query, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing get country by ID: {CountryId}", query.Id);

                var country = await _countryRepository.GetCountryByIdAsync(query.Id, cancellationToken);
                if (country == null)
                {
                    _logger.LogWarning("Country not found: {CountryId}", query.Id);
                    throw new InvalidOperationException($"Country with ID '{query.Id}' not found");
                }

                _logger.LogInformation("Successfully retrieved country: {CountryId} - {CountryName}", country.Id, country.Name);

                return new GetCountryByIdResponse
                {
                    Id = country.Id,
                    Name = country.Name,
                    Code = country.Code,
                    IsoCode = country.IsoCode,
                    CurrencyCode = country.CurrencyCode,
                    PhoneCode = country.PhoneCode,
                    IsEuMember = country.IsEuMember,
                    IsShippingEnabled = country.IsShippingEnabled,
                    DisplayOrder = country.DisplayOrder,
                    CreatedAt = country.CreatedAt,
                    UpdatedAt = country.UpdatedAt,
                    RegionCount = country.Regions?.Count(r => !r.IsDeleted) ?? 0,
                    DisplayName = country.GetDisplayName()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get country by ID: {CountryId}", query.Id);
                throw;
            }
        }

        /// <summary>
        /// Processes get all countries with business logic
        /// </summary>
        public async Task<GetAllCountriesResponse> ProcessGetAllCountriesAsync(GetAllCountriesQuery query, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing get all countries with filters - EU: {IsEuMember}, Shipping: {IsShippingEnabled}, Search: {SearchTerm}",
                    query.IsEuMember, query.IsShippingEnabled, query.SearchTerm);

                var countriesQuery = _countryRepository.GetAllCountries()
                    .Where(c => !c.IsDeleted);

                // Apply filters
                if (query.IsEuMember.HasValue)
                {
                    countriesQuery = countriesQuery.Where(c => c.IsEuMember == query.IsEuMember.Value);
                }

                if (query.IsShippingEnabled.HasValue)
                {
                    countriesQuery = countriesQuery.Where(c => c.IsShippingEnabled == query.IsShippingEnabled.Value);
                }

                if (!string.IsNullOrWhiteSpace(query.SearchTerm))
                {
                    var searchTerm = query.SearchTerm.ToLower();
                    countriesQuery = countriesQuery.Where(c =>
                        c.Name.ToLower().Contains(searchTerm) ||
                        c.Code.ToLower().Contains(searchTerm) ||
                        c.IsoCode.ToLower().Contains(searchTerm));
                }

                // Apply ordering
                countriesQuery = query.OrderBy?.ToLower() switch
                {
                    "name" => query.OrderDirection?.ToLower() == "desc"
                        ? countriesQuery.OrderByDescending(c => c.Name)
                        : countriesQuery.OrderBy(c => c.Name),
                    "code" => query.OrderDirection?.ToLower() == "desc"
                        ? countriesQuery.OrderByDescending(c => c.Code)
                        : countriesQuery.OrderBy(c => c.Code),
                    _ => query.OrderDirection?.ToLower() == "desc"
                        ? countriesQuery.OrderByDescending(c => c.DisplayOrder).ThenByDescending(c => c.Name)
                        : countriesQuery.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name)
                };

                var countries = await countriesQuery.ToListAsync(cancellationToken);

                _logger.LogInformation("Successfully retrieved {CountryCount} countries", countries.Count);

                // Build response
                var response = new GetAllCountriesResponse
                {
                    TotalCount = countries.Count,
                    EuMemberCount = countries.Count(c => c.IsEuMember),
                    ShippingEnabledCount = countries.Count(c => c.IsShippingEnabled),
                    Countries = countries.Select(c => new CountryListItem
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Code = c.Code,
                        IsoCode = c.IsoCode,
                        CurrencyCode = c.CurrencyCode,
                        PhoneCode = c.PhoneCode,
                        IsEuMember = c.IsEuMember,
                        IsShippingEnabled = c.IsShippingEnabled,
                        DisplayOrder = c.DisplayOrder,
                        RegionCount = query.IncludeRegionCount ? c.Regions?.Count(r => !r.IsDeleted) : null,
                        DisplayName = c.GetDisplayName(),
                        StatusText = GetCountryStatusText(c)
                    }).ToList()
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get all countries");
                throw;
            }
        }

        /// <summary>
        /// Gets status text for a country
        /// </summary>
        private static string GetCountryStatusText(Country country)
        {
            var statuses = new List<string>();

            if (country.IsEuMember)
                statuses.Add("EU Member");

            if (country.IsShippingEnabled)
                statuses.Add("Shipping Enabled");
            else
                statuses.Add("Shipping Disabled");

            return string.Join(", ", statuses);
        }
    }
}
