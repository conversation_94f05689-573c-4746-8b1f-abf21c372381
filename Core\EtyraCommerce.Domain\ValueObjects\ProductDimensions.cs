namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Product dimensions value object (Length, Width, Height, Weight)
    /// </summary>
    public class ProductDimensions : IEquatable<ProductDimensions>
    {
        public decimal? Length { get; private set; }
        public decimal? Width { get; private set; }
        public decimal? Height { get; private set; }
        public decimal? Weight { get; private set; }
        public string Unit { get; private set; }
        public string WeightUnit { get; private set; }

        // Parameterless constructor for EF Core
        private ProductDimensions()
        {
            Unit = "cm";
            WeightUnit = "kg";
        }

        public ProductDimensions(decimal? length, decimal? width, decimal? height, decimal? weight,
            string unit = "cm", string weightUnit = "kg")
        {
            if (length < 0) throw new ArgumentException("Length cannot be negative", nameof(length));
            if (width < 0) throw new ArgumentException("Width cannot be negative", nameof(width));
            if (height < 0) throw new ArgumentException("Height cannot be negative", nameof(height));
            if (weight < 0) throw new ArgumentException("Weight cannot be negative", nameof(weight));
            if (string.IsNullOrWhiteSpace(unit)) throw new ArgumentException("Unit cannot be null or empty", nameof(unit));
            if (string.IsNullOrWhiteSpace(weightUnit)) throw new ArgumentException("Weight unit cannot be null or empty", nameof(weightUnit));

            Length = length.HasValue ? Math.Round(length.Value, 2) : null;
            Width = width.HasValue ? Math.Round(width.Value, 2) : null;
            Height = height.HasValue ? Math.Round(height.Value, 2) : null;
            Weight = weight.HasValue ? Math.Round(weight.Value, 3) : null;
            Unit = unit.ToLowerInvariant();
            WeightUnit = weightUnit.ToLowerInvariant();
        }

        /// <summary>
        /// Creates dimensions with metric units (cm, kg)
        /// </summary>
        public static ProductDimensions CreateMetric(decimal? length, decimal? width, decimal? height, decimal? weight)
        {
            return new ProductDimensions(length, width, height, weight, "cm", "kg");
        }

        /// <summary>
        /// Creates dimensions with imperial units (in, lb)
        /// </summary>
        public static ProductDimensions CreateImperial(decimal? length, decimal? width, decimal? height, decimal? weight)
        {
            return new ProductDimensions(length, width, height, weight, "in", "lb");
        }

        /// <summary>
        /// Calculates volume in cubic units
        /// </summary>
        public decimal? Volume => Length.HasValue && Width.HasValue && Height.HasValue
            ? Length * Width * Height
            : null;

        /// <summary>
        /// Calculates volumetric weight (for shipping)
        /// Standard: Volume (cm³) / 5000 = kg
        /// </summary>
        public decimal? VolumetricWeight
        {
            get
            {
                if (!Volume.HasValue) return null;
                var volumeInCm3 = Unit == "cm" ? Volume.Value : ConvertToCm3(Volume.Value);
                return Math.Round(volumeInCm3 / 5000, 2);
            }
        }

        /// <summary>
        /// Gets the chargeable weight (higher of actual weight or volumetric weight)
        /// </summary>
        public decimal? ChargeableWeight
        {
            get
            {
                if (!Weight.HasValue || !VolumetricWeight.HasValue) return Weight;
                var actualWeightInKg = WeightUnit == "kg" ? Weight.Value : ConvertToKg(Weight.Value);
                return Math.Max(actualWeightInKg, VolumetricWeight.Value);
            }
        }

        /// <summary>
        /// Converts volume to cm³
        /// </summary>
        private decimal ConvertToCm3(decimal volume)
        {
            return Unit switch
            {
                "in" => volume * 16.387m, // cubic inches to cm³
                "mm" => volume / 1000m,   // mm³ to cm³
                "m" => volume * 1000000m, // m³ to cm³
                _ => volume
            };
        }

        /// <summary>
        /// Converts weight to kg
        /// </summary>
        private decimal ConvertToKg(decimal weight)
        {
            return WeightUnit switch
            {
                "lb" => weight * 0.453592m,  // pounds to kg
                "g" => weight / 1000m,       // grams to kg
                "oz" => weight * 0.0283495m, // ounces to kg
                _ => weight
            };
        }

        /// <summary>
        /// Formats dimensions for display
        /// </summary>
        public string Format()
        {
            return $"{Length} × {Width} × {Height} {Unit}, {Weight} {WeightUnit}";
        }

        /// <summary>
        /// Formats dimensions with volume
        /// </summary>
        public string FormatWithVolume()
        {
            return $"{Format()} (Volume: {Volume:F2} {Unit}³)";
        }

        /// <summary>
        /// Checks if the product fits in a given container
        /// </summary>
        public bool FitsIn(ProductDimensions container)
        {
            if (container == null) return false;

            // Convert to same units for comparison
            var thisInCm = ConvertToCm();
            var containerInCm = container.ConvertToCm();

            // Check if dimensions fit (allowing rotation)
            var thisDims = new[] { thisInCm.Length, thisInCm.Width, thisInCm.Height }.OrderBy(x => x).ToArray();
            var containerDims = new[] { containerInCm.Length, containerInCm.Width, containerInCm.Height }.OrderBy(x => x).ToArray();

            return thisDims[0] <= containerDims[0] &&
                   thisDims[1] <= containerDims[1] &&
                   thisDims[2] <= containerDims[2];
        }

        /// <summary>
        /// Converts dimensions to centimeters
        /// </summary>
        private ProductDimensions ConvertToCm()
        {
            if (Unit == "cm") return this;

            var factor = Unit switch
            {
                "in" => 2.54m,
                "mm" => 0.1m,
                "m" => 100m,
                _ => 1m
            };

            return new ProductDimensions(
                Length * factor,
                Width * factor,
                Height * factor,
                Weight,
                "cm",
                WeightUnit);
        }

        #region Equality

        public bool Equals(ProductDimensions? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;

            return Length == other.Length &&
                   Width == other.Width &&
                   Height == other.Height &&
                   Weight == other.Weight &&
                   Unit == other.Unit &&
                   WeightUnit == other.WeightUnit;
        }

        public override bool Equals(object? obj)
        {
            return obj is ProductDimensions other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Length, Width, Height, Weight, Unit, WeightUnit);
        }

        public static bool operator ==(ProductDimensions? left, ProductDimensions? right)
        {
            if (left is null && right is null) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(ProductDimensions? left, ProductDimensions? right) => !(left == right);

        #endregion

        public override string ToString() => Format();
    }
}
