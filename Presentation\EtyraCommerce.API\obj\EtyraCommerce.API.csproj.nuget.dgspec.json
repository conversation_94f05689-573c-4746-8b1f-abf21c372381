{"format": 1, "restore": {"F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj": {}}, "projects": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj", "projectName": "EtyraCommerce.Application", "projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "projectName": "EtyraCommerce.Domain", "projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\EtyraCommerce.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\EtyraCommerce.Infrastructure.csproj", "projectName": "EtyraCommerce.Infrastructure", "projectPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\EtyraCommerce.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj", "projectName": "EtyraCommerce.Persistence", "projectPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj"}, "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\EtyraCommerce.Infrastructure.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Infrastructure\\EtyraCommerce.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Autofac": {"target": "Package", "version": "[8.3.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.12.1, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "projectName": "EtyraCommerce.API", "projectPath": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj"}, "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj"}, "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj": {"projectPath": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}