using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Inventory.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Commands
{
    /// <summary>
    /// Handler for releasing stock reservation
    /// </summary>
    public class ReleaseReservationCommandHandler : IRequestHandler<ReleaseReservationCommand, CustomResponseDto<bool>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<ReleaseReservationCommandHandler> _logger;

        public ReleaseReservationCommandHandler(IInventoryProcessService inventoryProcessService, ILogger<ReleaseReservationCommandHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<bool>> Handle(ReleaseReservationCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling release reservation command for Reference: {Reference}", request.Reference);

                // Validation
                if (string.IsNullOrEmpty(request.Reference))
                    return CustomResponseDto<bool>.BadRequest("Reference is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessReleaseReservationAsync(
                    request.Reference,
                    request.Reason,
                    request.UserId);

                _logger.LogInformation("Release reservation command handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling release reservation command for Reference: {Reference}", request.Reference);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while releasing reservation");
            }
        }
    }
}
