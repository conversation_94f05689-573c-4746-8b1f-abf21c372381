using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Commands
{
    /// <summary>
    /// Handler for AddToCartCommand
    /// </summary>
    public class AddToCartCommandHandler : IRequestHandler<AddToCartCommand, CustomResponseDto<CartDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<AddToCartCommandHandler> _logger;

        public AddToCartCommandHandler(
            ICartProcessService cartProcessService,
            ILogger<AddToCartCommandHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CartDto>> Handle(AddToCartCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing add to cart command for ProductId: {ProductId}, Quantity: {Quantity}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.ProductId, request.Quantity, request.CustomerId, request.SessionId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<CartDto>.BadRequest("Product ID is required");

                if (request.Quantity <= 0)
                    return CustomResponseDto<CartDto>.BadRequest("Quantity must be greater than zero");

                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartDto>.BadRequest("Either Customer ID or Session ID is required");

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartDto>.BadRequest("Cannot specify both Customer ID and Session ID");

                if (!string.IsNullOrEmpty(request.VariantInfo) && request.VariantInfo.Length > 1000)
                    return CustomResponseDto<CartDto>.BadRequest("Variant information cannot exceed 1000 characters");

                if (!string.IsNullOrEmpty(request.Notes) && request.Notes.Length > 1000)
                    return CustomResponseDto<CartDto>.BadRequest("Notes cannot exceed 1000 characters");

                // Create DTO
                var addToCartDto = new AddToCartDto
                {
                    ProductId = request.ProductId,
                    Quantity = request.Quantity,
                    VariantInfo = request.VariantInfo,
                    Notes = request.Notes,
                    SessionId = request.SessionId
                };

                // Delegate to process service
                var result = await _cartProcessService.ProcessAddToCartAsync(
                    addToCartDto,
                    request.CustomerId,
                    request.Currency);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Add to cart command processed successfully for ProductId: {ProductId}, CartId: {CartId}",
                        request.ProductId, result.Data?.Id);
                }
                else
                {
                    _logger.LogWarning("Add to cart command failed for ProductId: {ProductId}, Errors: {Errors}",
                        request.ProductId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing add to cart command for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.ProductId, request.CustomerId, request.SessionId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while adding item to cart");
            }
        }
    }
}
