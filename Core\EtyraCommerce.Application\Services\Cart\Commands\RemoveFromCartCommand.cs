using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Commands
{
    /// <summary>
    /// Command to remove item from shopping cart
    /// </summary>
    public class RemoveFromCartCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Product ID to remove
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Creates command from DTO
        /// </summary>
        public static RemoveFromCartCommand FromDto(RemoveFromCartDto dto, Guid? customerId = null, string? sessionId = null)
        {
            return new RemoveFromCartCommand
            {
                CustomerId = customerId,
                SessionId = sessionId,
                ProductId = dto.ProductId
            };
        }
    }
}
