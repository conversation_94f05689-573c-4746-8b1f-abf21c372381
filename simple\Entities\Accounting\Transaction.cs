using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

// Transaction.cs (Gelir/Gider işlemleri için ana entity)
public class Transaction : BaseEntity
{
    public int AccountId { get; set; }
    public DateTime TransactionDate { get; set; }
    public decimal Amount { get; set; }
    public int CurrencyId { get; set; }
    public Currency Currency { get; set; }
    public TransactionType Type { get; set; } // Gelir veya Gider
    public int CategoryId { get; set; }
    public TransactionCategory Category { get; set; }
    [MaxLength(500)]
    public string? Description { get; set; }
    public int? RelatedInvoiceId { get; set; }
    public Invoice RelatedInvoice { get; set; }
    public int? RelatedAccountId { get; set; }
    public Account RelatedAccount { get; set; }
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    [MaxLength(100)]
    public string CreatedBy { get; set; }

    [MaxLength(100)]
    public string ModifiedBy { get; set; }

    public string? ReferenceNumber { get; set; }

    public TransactionStatus Status { get; set; }

}


