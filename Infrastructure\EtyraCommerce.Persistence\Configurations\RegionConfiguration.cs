using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for Region entity
    /// </summary>
    public class RegionConfiguration : IEntityTypeConfiguration<Region>
    {
        public void Configure(EntityTypeBuilder<Region> builder)
        {
            // Table configuration
            builder.ToTable("Regions", "etyra_shipping");

            // Primary key
            builder.HasKey(r => r.Id);

            // Properties
            builder.Property(r => r.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(r => r.Code)
                .HasMaxLength(10);

            builder.Property(r => r.Type)
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(r => r.IsShippingEnabled)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(r => r.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(r => r.ShippingNotes)
                .HasMaxLength(500);

            // Foreign key relationships
            builder.HasOne(r => r.Country)
                .WithMany(c => c.Regions)
                .HasForeignKey(r => r.CountryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Navigation properties
            builder.HasMany(r => r.Cities)
                .WithOne(c => c.Region)
                .HasForeignKey(c => c.RegionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(r => r.CountryId)
                .HasDatabaseName("IX_Regions_CountryId");

            builder.HasIndex(r => new { r.Name, r.CountryId })
                .IsUnique()
                .HasDatabaseName("IX_Regions_Name_Country_Unique");

            builder.HasIndex(r => r.Code)
                .HasDatabaseName("IX_Regions_Code");

            builder.HasIndex(r => r.Type)
                .HasDatabaseName("IX_Regions_Type");

            builder.HasIndex(r => r.IsShippingEnabled)
                .HasDatabaseName("IX_Regions_IsShippingEnabled");

            builder.HasIndex(r => r.DisplayOrder)
                .HasDatabaseName("IX_Regions_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_Regions_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Administrative regions within countries (states, provinces, counties, etc.)"));
        }
    }
}
