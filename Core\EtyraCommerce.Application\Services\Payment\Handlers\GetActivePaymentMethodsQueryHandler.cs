using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.Services.Payment.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Payment.Handlers;

/// <summary>
/// Handler for getting active payment methods for checkout
/// </summary>
public class GetActivePaymentMethodsQueryHandler : IRequestHandler<GetActivePaymentMethodsQuery, CustomResponseDto<List<PaymentMethodDto>>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<GetActivePaymentMethodsQueryHandler> _logger;

    public GetActivePaymentMethodsQueryHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<GetActivePaymentMethodsQueryHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get active payment methods query
    /// </summary>
    /// <param name="request">Get active payment methods query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> Handle(GetActivePaymentMethodsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetActivePaymentMethodsQuery for order amount: {OrderAmount} {OrderCurrency}", 
                request.OrderAmount, request.OrderCurrency);

            var result = await _paymentMethodProcessService.GetAvailableForAmountAsync(request.OrderAmount, request.OrderCurrency, tracking: false);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} active payment methods for order amount: {OrderAmount} {OrderCurrency}", 
                    result.Data?.Count ?? 0, request.OrderAmount, request.OrderCurrency);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve active payment methods for order amount {OrderAmount} {OrderCurrency}: {Message}",
                    request.OrderAmount, request.OrderCurrency, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetActivePaymentMethodsQuery for order amount: {OrderAmount} {OrderCurrency}", 
                request.OrderAmount, request.OrderCurrency);
            return CustomResponseDto<List<PaymentMethodDto>>.InternalServerError("An error occurred while retrieving active payment methods");
        }
    }
}
