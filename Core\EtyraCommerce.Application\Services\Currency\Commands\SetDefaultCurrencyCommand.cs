using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Commands;

/// <summary>
/// Command to set a currency as the default currency
/// </summary>
public class SetDefaultCurrencyCommand : IRequest<CustomResponseDto<CurrencyDto>>
{
    /// <summary>
    /// Currency ID to set as default
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">Currency ID</param>
    public SetDefaultCurrencyCommand(Guid id)
    {
        Id = id;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public SetDefaultCurrencyCommand() { }
}
