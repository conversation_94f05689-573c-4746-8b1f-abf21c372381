using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Commands
{
    /// <summary>
    /// Handler for MergeCartCommand
    /// </summary>
    public class MergeCartCommandHandler : IRequestHandler<MergeCartCommand, CustomResponseDto<CartDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<MergeCartCommandHandler> _logger;

        public MergeCartCommandHandler(
            ICartProcessService cartProcessService,
            ILogger<MergeCartCommandHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CartDto>> Handle(MergeCartCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing merge cart command for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    request.SessionId, request.CustomerId);

                // Validation
                if (string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<CartDto>.BadRequest("Session ID is required");

                if (request.CustomerId == Guid.Empty)
                    return CustomResponseDto<CartDto>.BadRequest("Customer ID is required");

                if (request.SessionId.Length > 255)
                    return CustomResponseDto<CartDto>.BadRequest("Session ID cannot exceed 255 characters");

                // Create DTO
                var mergeCartDto = new MergeCartDto
                {
                    SessionId = request.SessionId,
                    CustomerId = request.CustomerId
                };

                // Delegate to process service
                var result = await _cartProcessService.ProcessMergeCartAsync(mergeCartDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Merge cart command processed successfully for SessionId: {SessionId}, CustomerId: {CustomerId}, CartId: {CartId}",
                        request.SessionId, request.CustomerId, result.Data?.Id);
                }
                else
                {
                    _logger.LogWarning("Merge cart command failed for SessionId: {SessionId}, CustomerId: {CustomerId}, Errors: {Errors}",
                        request.SessionId, request.CustomerId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing merge cart command for SessionId: {SessionId}, CustomerId: {CustomerId}",
                    request.SessionId, request.CustomerId);
                return CustomResponseDto<CartDto>.InternalServerError("An error occurred while merging cart");
            }
        }
    }
}
