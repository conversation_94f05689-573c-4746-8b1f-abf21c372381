// Invoice.cs (Fatura ana entity)
using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class Invoice : BaseEntity
{
    [MaxLength(50)]
    public string InvoiceNumber { get; set; }
    
    public DateTime InvoiceDate { get; set; }
    
    public DateTime DueDate { get; set; }
    
    public InvoiceType Type { get; set; } // Satış veya Alım
    
    public InvoiceStatus Status { get; set; }
    
    public int CurrencyId { get; set; }
    public Currency Currency { get; set; }
    
    public decimal SubTotal { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    public decimal PaidAmount { get; set; }
    
    public decimal RemainingAmount { get; set; }
    
    [MaxLength(1000)]
    public string Notes { get; set; }
    
    public int? CustomerId { get; set; }
    public Customer.Customer Customer { get; set; }
    
    public int? SupplierId { get; set; }
    public Supplier Supplier { get; set; }
    
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    
    public ICollection<InvoiceItem> Items { get; set; }
    
    public ICollection<InvoicePayment> Payments { get; set; }
    
    public ICollection<InvoiceTax> Taxes { get; set; }
    
    [MaxLength(100)]
    public string CreatedBy { get; set; }
    
    [MaxLength(100)]
    public string ModifiedBy { get; set; }
}

