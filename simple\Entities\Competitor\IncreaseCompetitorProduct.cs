﻿using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Competitor;

public class IncreaseCompetitorProduct : BaseEntity
{
    public CompetitorProduct CompetitorProduct { get; set; }
    public int CompetitorProductId { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public Competitor Competitor { get; set; }
    public int CompetitorId { get; set; }

    [MaxLength(250)]
    public string? ProductName { get; set; }
    [MaxLength(250)]
    public string? ProductLink { get; set; }

    [MaxLength(4)]
    public string? Currency { get; set; }
    [MaxLength(100)] public string MatchGroupId { get; set; } = "0";
    public int Status { get; set; }  //=> 1 özel takip , 2 => 2  bla bla 

}

