using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;

namespace EtyraCommerce.Application.Services.Cart
{
    /// <summary>
    /// Cart process service interface for business logic operations
    /// </summary>
    public interface ICartProcessService
    {
        #region Cart Management

        /// <summary>
        /// Processes adding an item to cart
        /// </summary>
        Task<CustomResponseDto<CartDto>> ProcessAddToCartAsync(AddToCartDto addToCartDto, Guid? customerId = null, string currency = "USD");

        /// <summary>
        /// Processes updating cart item quantity
        /// </summary>
        Task<CustomResponseDto<CartDto>> ProcessUpdateCartItemAsync(UpdateCartItemDto updateCartItemDto, Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Processes removing an item from cart
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessRemoveFromCartAsync(RemoveFromCartDto removeFromCartDto, Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Processes clearing all items from cart
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessClearCartAsync(Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Processes merging guest cart to user cart
        /// </summary>
        Task<CustomResponseDto<CartDto>> ProcessMergeCartAsync(MergeCartDto mergeCartDto);

        #endregion

        #region Cart Retrieval

        /// <summary>
        /// Gets cart with all items
        /// </summary>
        Task<CustomResponseDto<CartDto>> GetCartAsync(Guid? customerId = null, string? sessionId = null, bool includeInactive = false, bool includeExpired = false);

        /// <summary>
        /// Gets cart summary without detailed items
        /// </summary>
        Task<CustomResponseDto<CartSummaryDto>> GetCartSummaryAsync(Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Gets cart by ID
        /// </summary>
        Task<CustomResponseDto<CartDto>> GetCartByIdAsync(Guid cartId);

        #endregion

        #region Cart Utilities

        /// <summary>
        /// Validates cart ownership
        /// </summary>
        Task<bool> ValidateCartOwnershipAsync(Guid cartId, Guid? customerId = null, string? sessionId = null);

        /// <summary>
        /// Cleans up expired carts
        /// </summary>
        Task<CustomResponseDto<int>> CleanupExpiredCartsAsync();

        /// <summary>
        /// Gets or creates cart for user/session
        /// </summary>
        Task<CustomResponseDto<CartDto>> GetOrCreateCartAsync(Guid? customerId = null, string? sessionId = null, string currency = "USD");

        /// <summary>
        /// Validates product availability for cart operations
        /// </summary>
        Task<CustomResponseDto<bool>> ValidateProductAvailabilityAsync(Guid productId, int quantity);

        #endregion
    }
}
