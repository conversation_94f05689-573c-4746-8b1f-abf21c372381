# EtyraCommerce API Usage Examples

## ✅ Inventory Management Examples (COMPLETED 2025-07-02)

### Create Warehouse
```json
POST /api/warehouse
{
  "name": "Main Warehouse",
  "code": "MAIN",
  "description": "Primary distribution center",
  "address": {
    "street": "123 Industrial Ave",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  },
  "phone": "+1234567890",
  "email": "<EMAIL>",
  "managerName": "John Manager",
  "isActive": true,
  "isMain": true,
  "type": 0,
  "sortOrder": 1
}
```

### Create Inventory Record
```json
POST /api/inventory
{
  "productId": "product-guid-here",
  "warehouseId": "warehouse-guid-here",
  "availableQuantity": 100,
  "reorderPoint": 20,
  "maxStockLevel": 500,
  "locationCode": "A-01-001",
  "notes": "Initial stock entry"
}
```

### Reserve Stock (Customer Checkout)
```json
POST /api/inventory/reserve
{
  "productId": "product-guid-here",
  "warehouseId": "warehouse-guid-here",
  "quantity": 5,
  "reference": "ORDER-2025-001",
  "reason": "Customer order - payment pending"
}
```

### Allocate Stock (Order Confirmation)
```json
POST /api/inventory/allocate
{
  "productId": "product-guid-here",
  "warehouseId": "warehouse-guid-here",
  "quantity": 5,
  "reference": "ORDER-2025-001",
  "reason": "Payment confirmed - ready to ship"
}
```

### Release Stock (Order Cancellation)
```json
POST /api/inventory/release
{
  "reference": "ORDER-2025-001",
  "reason": "Customer cancelled order"
}
```

### Adjust Stock (Physical Count)
```json
POST /api/inventory/adjust
{
  "inventoryId": "inventory-guid-here",
  "newQuantity": 95,
  "reason": "Physical count correction",
  "notes": "Found 5 damaged units"
}
```

### Transfer Stock Between Warehouses
```json
POST /api/inventory/transfer
{
  "productId": "product-guid-here",
  "fromWarehouseId": "source-warehouse-guid",
  "toWarehouseId": "target-warehouse-guid",
  "quantity": 20,
  "reference": "TRANSFER-001",
  "reason": "Regional stock distribution"
}
```

### Get Low Stock Items
```
GET /api/inventory/low-stock
```

### Get Stock Status for Product
```
GET /api/inventory/stock-status/{productId}
```

### List All Inventory
```
GET /api/inventory?pageNumber=1&pageSize=10
```

## E-Commerce Stock Flow Example

### Complete Order Process
```
1. Customer Checkout:
   POST /api/inventory/reserve
   {
     "productId": "samsung-s24-guid",
     "quantity": 2,
     "reference": "ORDER-2025-123"
   }

2. Payment Confirmed:
   POST /api/inventory/allocate
   {
     "productId": "samsung-s24-guid", 
     "quantity": 2,
     "reference": "ORDER-2025-123"
   }

3. If Order Cancelled:
   POST /api/inventory/release
   {
     "reference": "ORDER-2025-123"
   }
```

## Product Management Examples

### Create Product
```json
POST /api/product
{
  "model": "IPHONE-15-PRO",
  "name": "iPhone 15 Pro",
  "sku": "IPHONE-15-PRO-128",
  "slug": "iphone-15-pro-128gb",
  "basePrice": 999.99,
  "basePriceCurrency": "USD",
  "brand": "Apple",
  "status": 1,
  "type": 1,
  "ean": "1234567890123",
  "primaryCategoryId": "category-guid-here",
  "descriptions": [
    {
      "name": "iPhone 15 Pro English",
      "languageCode": "en-US",
      "title": "iPhone 15 Pro",
      "description": "Latest iPhone with advanced features",
      "shortDescription": "iPhone 15 Pro 128GB"
    }
  ],
  "categoryIds": ["category-guid-here"]
}
```

## Category Management Examples

### Create Category
```json
POST /api/category
{
  "name": "Electronics",
  "description": "Electronic devices and accessories",
  "slug": "electronics",
  "isActive": true,
  "sortOrder": 1,
  "metaTitle": "Electronics Category",
  "metaDescription": "Browse our electronics collection"
}
```

## Stock Management Best Practices

### Reorder Point Logic
- **Reorder Point**: When to order new stock (e.g., 20 units)
- **Low Stock Alert**: Available ≤ Reorder Point
- **Max Stock Level**: Maximum warehouse capacity
- **Location Code**: Warehouse organization (e.g., "A-01-001")

### Reference System
- Use consistent reference format: "ORDER-YYYY-NNN"
- Same reference for Reserve → Allocate → Release
- Enables complete order tracking and audit trail

### Stock Status Meanings
- **Available**: Ready to sell
- **Reserved**: Locked for pending orders
- **Allocated**: Confirmed for shipping
- **Total**: Available + Reserved + Allocated

## Common Response Format
All API responses follow this format:

### Success Response
```json
{
  "data": { /* response data */ },
  "isSuccess": true,
  "statusCode": 200,
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T12:00:00Z",
  "errorList": [],
  "validationErrors": {}
}
```

### Error Response
```json
{
  "data": null,
  "isSuccess": false,
  "statusCode": 400,
  "message": "Validation failed",
  "timestamp": "2024-01-01T12:00:00Z",
  "errorList": ["Field is required"],
  "validationErrors": {
    "Email": ["Email is required"]
  }
}
```
