using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart;
using EtyraCommerce.Application.Services.Cart.Commands;
using EtyraCommerce.Application.Services.Cart.Handlers.Commands;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace EtyraCommerce.Tests.Handlers.Commands
{
    public class AddToCartCommandHandlerTests
    {
        private readonly Mock<ICartProcessService> _mockCartProcessService;
        private readonly Mock<ILogger<AddToCartCommandHandler>> _mockLogger;
        private readonly AddToCartCommandHandler _handler;

        public AddToCartCommandHandlerTests()
        {
            _mockCartProcessService = new Mock<ICartProcessService>();
            _mockLogger = new Mock<ILogger<AddToCartCommandHandler>>();
            _handler = new AddToCartCommandHandler(_mockCartProcessService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessResponse()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();

            var command = new AddToCartCommand
            {
                CustomerId = customerId,
                ProductId = productId,
                Quantity = 2,
                Currency = "USD"
            };

            var expectedCartDto = new CartDto
            {
                Id = Guid.NewGuid(),
                CustomerId = customerId,
                TotalItems = 2,
                UniqueItems = 1
            };

            var expectedResponse = CustomResponseDto<CartDto>.Success(200, expectedCartDto, "Item added to cart successfully");

            _mockCartProcessService
                .Setup(x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), customerId, "USD"))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(expectedCartDto.Id);
            result.Data.CustomerId.Should().Be(customerId);

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), customerId, "USD"),
                Times.Once);
        }

        [Fact]
        public async Task Handle_EmptyProductId_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.Empty, // Invalid
                Quantity = 1,
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Product ID is required");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_ZeroQuantity_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Quantity = 0, // Invalid
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Quantity must be greater than zero");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_NoCustomerIdOrSessionId_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = null,
                SessionId = null, // Both null - invalid
                ProductId = Guid.NewGuid(),
                Quantity = 1,
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Either Customer ID or Session ID is required");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_BothCustomerIdAndSessionId_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                SessionId = "session123", // Both provided - invalid
                ProductId = Guid.NewGuid(),
                Quantity = 1,
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Cannot specify both Customer ID and Session ID");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_LongVariantInfo_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Quantity = 1,
                VariantInfo = new string('x', 1001), // Too long
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Variant information cannot exceed 1000 characters");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_LongNotes_ReturnsBadRequest()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Quantity = 1,
                Notes = new string('x', 1001), // Too long
                Currency = "USD"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(400);
            result.Message.Should().Contain("Notes cannot exceed 1000 characters");

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()),
                Times.Never);
        }

        [Fact]
        public async Task Handle_ProcessServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new AddToCartCommand
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Quantity = 1,
                Currency = "USD"
            };

            _mockCartProcessService
                .Setup(x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), It.IsAny<Guid?>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.StatusCode.Should().Be(500);
            result.Message.Should().Contain("An error occurred while adding item to cart");
        }

        [Fact]
        public async Task Handle_GuestCart_ValidSessionId_ReturnsSuccess()
        {
            // Arrange
            var sessionId = "guest-session-123";
            var productId = Guid.NewGuid();

            var command = new AddToCartCommand
            {
                CustomerId = null,
                SessionId = sessionId,
                ProductId = productId,
                Quantity = 1,
                Currency = "USD"
            };

            var expectedCartDto = new CartDto
            {
                Id = Guid.NewGuid(),
                SessionId = sessionId,
                TotalItems = 1,
                UniqueItems = 1
            };

            var expectedResponse = CustomResponseDto<CartDto>.Success(200, expectedCartDto, "Item added to cart successfully");

            _mockCartProcessService
                .Setup(x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), null, "USD"))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.SessionId.Should().Be(sessionId);

            _mockCartProcessService.Verify(
                x => x.ProcessAddToCartAsync(It.IsAny<AddToCartDto>(), null, "USD"),
                Times.Once);
        }
    }
}
