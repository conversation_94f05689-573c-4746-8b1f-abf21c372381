using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Commands
{
    /// <summary>
    /// Handler for UpdateCategoryCommand
    /// </summary>
    public class UpdateCategoryCommandHandler : IRequestHandler<UpdateCategoryCommand, CustomResponseDto<CategoryDto>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<UpdateCategoryCommandHandler> _logger;

        public UpdateCategoryCommandHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<UpdateCategoryCommandHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CategoryDto>> Handle(UpdateCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing update category command for category ID: {CategoryId}", request.CategoryId);

                // Validation
                if (request.CategoryId == Guid.Empty)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category ID is required");

                if (string.IsNullOrWhiteSpace(request.Name))
                    return CustomResponseDto<CategoryDto>.BadRequest("Category name is required");

                if (request.Name.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category name cannot exceed 200 characters");

                if (!string.IsNullOrEmpty(request.Description) && request.Description.Length > 1000)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category description cannot exceed 1000 characters");

                if (!string.IsNullOrEmpty(request.Slug) && request.Slug.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category slug cannot exceed 200 characters");

                if (!string.IsNullOrEmpty(request.ImageUrl) && request.ImageUrl.Length > 500)
                    return CustomResponseDto<CategoryDto>.BadRequest("Image URL cannot exceed 500 characters");

                if (!string.IsNullOrEmpty(request.Icon) && request.Icon.Length > 100)
                    return CustomResponseDto<CategoryDto>.BadRequest("Icon cannot exceed 100 characters");

                if (request.SortOrder < 0)
                    return CustomResponseDto<CategoryDto>.BadRequest("Sort order must be non-negative");

                if (!string.IsNullOrEmpty(request.MetaTitle) && request.MetaTitle.Length > 120)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta title cannot exceed 120 characters");

                if (!string.IsNullOrEmpty(request.MetaDescription) && request.MetaDescription.Length > 350)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta description cannot exceed 350 characters");

                if (!string.IsNullOrEmpty(request.MetaKeywords) && request.MetaKeywords.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta keywords cannot exceed 200 characters");

                // Prevent self-referencing (category cannot be its own parent)
                if (request.ParentCategoryId == request.CategoryId)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category cannot be its own parent");

                // Create DTO for business logic
                var updateCategoryDto = new UpdateCategoryDto
                {
                    Name = request.Name,
                    Description = request.Description,
                    Slug = request.Slug,
                    ParentCategoryId = request.ParentCategoryId,
                    ImageUrl = request.ImageUrl,
                    Icon = request.Icon,
                    SortOrder = request.SortOrder,
                    IsActive = request.IsActive,
                    ShowInMenu = request.ShowInMenu,
                    MetaTitle = request.MetaTitle,
                    MetaDescription = request.MetaDescription,
                    MetaKeywords = request.MetaKeywords
                };

                // Delegate to process service
                var result = await _categoryProcessService.ProcessUpdateCategoryAsync(request.CategoryId, updateCategoryDto);

                _logger.LogInformation("Update category command processed successfully for category ID: {CategoryId}", request.CategoryId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing update category command for category ID: {CategoryId}", request.CategoryId);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while updating the category");
            }
        }
    }
}
