using EtyraCommerce.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// Product discount DTO
    /// </summary>
    public class ProductDiscountDto : BaseDto
    {
        /// <summary>
        /// Discount name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Discount description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Discount type
        /// </summary>
        public DiscountType Type { get; set; }

        /// <summary>
        /// Discount value (percentage or fixed amount)
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// Minimum quantity required for discount
        /// </summary>
        public int? MinQuantity { get; set; }

        /// <summary>
        /// Maximum quantity for discount (null = unlimited)
        /// </summary>
        public int? MaxQuantity { get; set; }

        /// <summary>
        /// Minimum order amount for discount
        /// </summary>
        public decimal? MinOrderAmount { get; set; }

        /// <summary>
        /// Minimum order currency
        /// </summary>
        public string? MinOrderCurrency { get; set; }

        /// <summary>
        /// Maximum discount amount (for percentage discounts)
        /// </summary>
        public decimal? MaxDiscountAmount { get; set; }

        /// <summary>
        /// Maximum discount currency
        /// </summary>
        public string? MaxDiscountCurrency { get; set; }

        /// <summary>
        /// Discount start date
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Discount end date
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Whether the discount is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Priority order (higher number = higher priority)
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Maximum number of uses (null = unlimited)
        /// </summary>
        public int? MaxUses { get; set; }

        /// <summary>
        /// Current number of uses
        /// </summary>
        public int CurrentUses { get; set; }

        /// <summary>
        /// Whether discount can be combined with other discounts
        /// </summary>
        public bool CanCombine { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Checks if the discount is currently valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Gets remaining uses
        /// </summary>
        public int? RemainingUses { get; set; }

        /// <summary>
        /// Formatted discount value
        /// </summary>
        public string FormattedValue
        {
            get
            {
                return Type switch
                {
                    DiscountType.Percentage => $"{Value}%",
                    DiscountType.FixedAmount => $"{Value:C}",
                    DiscountType.FixedAmountPerItem => $"{Value:C} per item",
                    _ => Value.ToString()
                };
            }
        }
    }

    /// <summary>
    /// DTO for creating product discount
    /// </summary>
    public class CreateProductDiscountDto
    {
        /// <summary>
        /// Discount name/title
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Discount description
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Discount type
        /// </summary>
        public DiscountType Type { get; set; } = DiscountType.Percentage;

        /// <summary>
        /// Discount value (percentage or fixed amount)
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Value must be greater than 0")]
        public decimal Value { get; set; }

        /// <summary>
        /// Minimum quantity required for discount
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Min quantity must be positive")]
        public int? MinQuantity { get; set; }

        /// <summary>
        /// Maximum quantity for discount (null = unlimited)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Max quantity must be positive")]
        public int? MaxQuantity { get; set; }

        /// <summary>
        /// Minimum order amount for discount
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Min order amount must be greater than 0")]
        public decimal? MinOrderAmount { get; set; }

        /// <summary>
        /// Minimum order currency
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? MinOrderCurrency { get; set; }

        /// <summary>
        /// Maximum discount amount (for percentage discounts)
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Max discount amount must be greater than 0")]
        public decimal? MaxDiscountAmount { get; set; }

        /// <summary>
        /// Maximum discount currency
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be exactly 3 characters")]
        public string? MaxDiscountCurrency { get; set; }

        /// <summary>
        /// Discount start date
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Discount end date
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Whether the discount is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Priority order (higher number = higher priority)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Maximum number of uses (null = unlimited)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Max uses must be positive")]
        public int? MaxUses { get; set; }

        /// <summary>
        /// Whether discount can be combined with other discounts
        /// </summary>
        public bool CanCombine { get; set; } = false;

        #region Validation

        /// <summary>
        /// Custom validation
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (StartDate.HasValue && EndDate.HasValue && StartDate > EndDate)
            {
                yield return new ValidationResult(
                    "Start date cannot be after end date",
                    new[] { nameof(StartDate), nameof(EndDate) });
            }

            if (MinQuantity.HasValue && MaxQuantity.HasValue && MinQuantity > MaxQuantity)
            {
                yield return new ValidationResult(
                    "Min quantity cannot be greater than max quantity",
                    new[] { nameof(MinQuantity), nameof(MaxQuantity) });
            }

            if (Type == DiscountType.Percentage && Value > 100)
            {
                yield return new ValidationResult(
                    "Percentage discount cannot exceed 100%",
                    new[] { nameof(Value) });
            }

            if (MinOrderAmount.HasValue && string.IsNullOrEmpty(MinOrderCurrency))
            {
                yield return new ValidationResult(
                    "Min order currency is required when min order amount is specified",
                    new[] { nameof(MinOrderCurrency) });
            }

            if (MaxDiscountAmount.HasValue && string.IsNullOrEmpty(MaxDiscountCurrency))
            {
                yield return new ValidationResult(
                    "Max discount currency is required when max discount amount is specified",
                    new[] { nameof(MaxDiscountCurrency) });
            }
        }

        #endregion
    }

    /// <summary>
    /// DTO for updating product discount
    /// </summary>
    public class UpdateProductDiscountDto
    {
        /// <summary>
        /// Discount name/title
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Discount description
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Discount value (percentage or fixed amount)
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Value must be greater than 0")]
        public decimal Value { get; set; }

        /// <summary>
        /// Minimum quantity required for discount
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Min quantity must be positive")]
        public int? MinQuantity { get; set; }

        /// <summary>
        /// Maximum quantity for discount (null = unlimited)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Max quantity must be positive")]
        public int? MaxQuantity { get; set; }

        /// <summary>
        /// Discount start date
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Discount end date
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Whether the discount is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Priority order (higher number = higher priority)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Maximum number of uses (null = unlimited)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Max uses must be positive")]
        public int? MaxUses { get; set; }

        /// <summary>
        /// Whether discount can be combined with other discounts
        /// </summary>
        public bool CanCombine { get; set; } = false;
    }

    /// <summary>
    /// Discount type enumeration
    /// </summary>
    public enum DiscountType
    {
        Percentage = 0,
        FixedAmount = 1,
        FixedAmountPerItem = 2
    }
}
