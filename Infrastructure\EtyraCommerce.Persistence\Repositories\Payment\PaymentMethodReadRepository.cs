using EtyraCommerce.Application.Repositories.Payment;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Payment;

/// <summary>
/// Read repository implementation for PaymentMethod entity
/// </summary>
public class PaymentMethodReadRepository : ReadRepository<PaymentMethod>, IPaymentMethodReadRepository
{
    public PaymentMethodReadRepository(EtyraCommerceDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    public async Task<PaymentMethod?> GetByCodeAsync(string code, bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .FirstOrDefaultAsync(pm => pm.Code == code);
    }

    /// <summary>
    /// Get active payment methods ordered by display order
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active payment methods</returns>
    public async Task<List<PaymentMethod>> GetActivePaymentMethodsAsync(bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Where(pm => pm.IsActive)
            .OrderBy(pm => pm.DisplayOrder)
            .ThenBy(pm => pm.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Get payment methods by type
    /// </summary>
    /// <param name="type">Payment method type</param>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    public async Task<List<PaymentMethod>> GetByTypeAsync(PaymentMethodType type, bool onlyActive = true, bool tracking = false)
    {
        var query = Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Where(pm => pm.Type == type);

        if (onlyActive)
        {
            query = query.Where(pm => pm.IsActive);
        }

        return await query
            .OrderBy(pm => pm.DisplayOrder)
            .ThenBy(pm => pm.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Check if payment method code exists
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    public async Task<bool> CodeExistsAsync(string code, Guid? excludeId = null)
    {
        var query = Table.Where(pm => pm.Code == code);

        if (excludeId.HasValue)
        {
            query = query.Where(pm => pm.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// Get payment methods available for order amount
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of available payment methods</returns>
    public async Task<List<PaymentMethod>> GetAvailableForAmountAsync(decimal orderAmount, string orderCurrency, bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Where(pm => pm.IsActive &&
                        (pm.MinimumOrderAmount == null || 
                         (pm.MinimumOrderAmount.Amount <= orderAmount && 
                          pm.MinimumOrderAmount.Currency.Code == orderCurrency)) &&
                        (pm.MaximumOrderAmount == null || 
                         (pm.MaximumOrderAmount.Amount >= orderAmount && 
                          pm.MaximumOrderAmount.Currency.Code == orderCurrency)))
            .OrderBy(pm => pm.DisplayOrder)
            .ThenBy(pm => pm.Name)
            .ToListAsync();
    }
}
