﻿using EtyraCommerce.Application.Repositories;
using EtyraCommerce.Application.UnitOfWork;
using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace EtyraCommerce.Persistence.UnitOfWork
{
    /// <summary>
    /// Unit of Work implementation for managing transactions and repository access
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly EtyraCommerceDbContext _context;
        private readonly Dictionary<Type, object> _readRepositories;
        private readonly Dictionary<Type, object> _writeRepositories;
        private IDbContextTransaction? _transaction;

        public UnitOfWork(EtyraCommerceDbContext context)
        {
            _context = context;
            _readRepositories = new Dictionary<Type, object>();
            _writeRepositories = new Dictionary<Type, object>();
        }

        #region Repository Access
        /// <summary>
        /// Gets read repository for entity type T
        /// </summary>
        public IReadRepository<T> ReadRepository<T>() where T : BaseEntity
        {
            var type = typeof(T);

            if (_readRepositories.ContainsKey(type))
            {
                return (IReadRepository<T>)_readRepositories[type];
            }

            var repository = new ReadRepository<T>(_context);
            _readRepositories.Add(type, repository);

            return repository;
        }

        /// <summary>
        /// Gets write repository for entity type T
        /// </summary>
        public IWriteRepository<T> WriteRepository<T>() where T : BaseEntity
        {
            var type = typeof(T);

            if (_writeRepositories.ContainsKey(type))
            {
                return (IWriteRepository<T>)_writeRepositories[type];
            }

            var repository = new WriteRepository<T>(_context);
            _writeRepositories.Add(type, repository);

            return repository;
        }

        #endregion

        #region Save Operations

        /// <summary>
        /// Saves all changes asynchronously
        /// </summary>
        public async Task<int> CommitAsync()
        {
            try
            {
                return await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log the exception here if you have a logging service
                throw new InvalidOperationException("An error occurred while saving changes to the database.", ex);
            }
        }

        /// <summary>
        /// Saves all changes synchronously
        /// </summary>
        public int Commit()
        {
            try
            {
                return _context.SaveChanges();
            }
            catch (Exception ex)
            {
                // Log the exception here if you have a logging service
                throw new InvalidOperationException("An error occurred while saving changes to the database.", ex);
            }
        }

        #endregion

        #region Transaction Management

        /// <summary>
        /// Begins a new transaction
        /// </summary>
        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("A transaction is already in progress.");
            }

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        /// <summary>
        /// Commits the current transaction
        /// </summary>
        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress.");
            }

            try
            {
                await _context.SaveChangesAsync();
                await _transaction.CommitAsync();
            }
            catch
            {
                await _transaction.RollbackAsync();
                throw;
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        /// <summary>
        /// Rolls back the current transaction
        /// </summary>
        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress.");
            }

            try
            {
                await _transaction.RollbackAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        /// <summary>
        /// Checks if there's an active transaction
        /// </summary>
        public bool HasActiveTransaction => _transaction != null;

        #endregion

        #region Utility Methods

        /// <summary>
        /// Discards all changes without saving
        /// </summary>
        public void RejectChanges()
        {
            foreach (var entry in _context.ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    case EntityState.Deleted:
                        entry.State = EntityState.Unchanged;
                        break;
                    case EntityState.Added:
                        entry.State = EntityState.Detached;
                        break;
                }
            }
        }

        /// <summary>
        /// Gets entities including soft deleted ones
        /// </summary>
        public IQueryable<T> GetWithDeleted<T>() where T : BaseEntity
        {
            return _context.GetWithDeleted<T>();
        }

        /// <summary>
        /// Gets only soft deleted entities
        /// </summary>
        public IQueryable<T> GetOnlyDeleted<T>() where T : BaseEntity
        {
            return _context.GetOnlyDeleted<T>();
        }

        /// <summary>
        /// Permanently deletes an entity (bypasses soft delete)
        /// </summary>
        public void HardDelete<T>(T entity) where T : BaseEntity
        {
            _context.HardDelete(entity);
        }

        /// <summary>
        /// Restores a soft deleted entity
        /// </summary>
        public void Restore<T>(T entity) where T : BaseEntity
        {
            _context.Restore(entity);
        }

        /// <summary>
        /// Gets the number of pending changes
        /// </summary>
        public int GetPendingChangesCount()
        {
            return _context.ChangeTracker.Entries()
                .Count(e => e.State == EntityState.Added ||
                           e.State == EntityState.Modified ||
                           e.State == EntityState.Deleted);
        }

        /// <summary>
        /// Checks if there are any pending changes
        /// </summary>
        public bool HasPendingChanges()
        {
            return GetPendingChangesCount() > 0;
        }

        #endregion
    }
}