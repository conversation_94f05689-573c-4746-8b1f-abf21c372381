using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.Services.Payment.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Payment.Handlers;

/// <summary>
/// Handler for deleting payment methods
/// </summary>
public class DeletePaymentMethodCommandHandler : IRequestHandler<DeletePaymentMethodCommand, CustomResponseDto<NoContentDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<DeletePaymentMethodCommandHandler> _logger;

    public DeletePaymentMethodCommandHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<DeletePaymentMethodCommandHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle delete payment method command
    /// </summary>
    /// <param name="request">Delete payment method command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> Handle(DeletePaymentMethodCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing DeletePaymentMethodCommand for payment method ID: {PaymentMethodId}", request.Id);

            var result = await _paymentMethodProcessService.ProcessDeleteAsync(request.Id);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment method deleted successfully with ID: {PaymentMethodId}", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to delete payment method {PaymentMethodId}: {Message}", request.Id, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing DeletePaymentMethodCommand for payment method ID: {PaymentMethodId}", request.Id);
            return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the payment method");
        }
    }
}

/// <summary>
/// Handler for toggling payment method status
/// </summary>
public class TogglePaymentMethodStatusCommandHandler : IRequestHandler<TogglePaymentMethodStatusCommand, CustomResponseDto<PaymentMethodDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<TogglePaymentMethodStatusCommandHandler> _logger;

    public TogglePaymentMethodStatusCommandHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<TogglePaymentMethodStatusCommandHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle toggle payment method status command
    /// </summary>
    /// <param name="request">Toggle payment method status command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> Handle(TogglePaymentMethodStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing TogglePaymentMethodStatusCommand for payment method ID: {PaymentMethodId} to status: {IsActive}", 
                request.Id, request.IsActive);

            var result = await _paymentMethodProcessService.ProcessToggleStatusAsync(request.Id, request.IsActive);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment method status toggled successfully for ID: {PaymentMethodId} to status: {IsActive}", 
                    request.Id, request.IsActive);
            }
            else
            {
                _logger.LogWarning("Failed to toggle payment method status {PaymentMethodId}: {Message}", request.Id, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing TogglePaymentMethodStatusCommand for payment method ID: {PaymentMethodId}", request.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while toggling the payment method status");
        }
    }
}
