using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Queries
{
    /// <summary>
    /// Query for getting categories by parent category ID
    /// </summary>
    public class GetCategoriesByParentQuery : IRequest<CustomResponseDto<List<CategoryDto>>>
    {
        /// <summary>
        /// Parent category ID (null for root categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Whether to include only active categories
        /// </summary>
        public bool ActiveOnly { get; set; } = true;

        /// <summary>
        /// Whether to include child categories recursively
        /// </summary>
        public bool IncludeChildren { get; set; } = false;

        /// <summary>
        /// Whether to include descriptions in all languages
        /// </summary>
        public bool IncludeDescriptions { get; set; } = true;

        /// <summary>
        /// Specific language code to filter descriptions
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Sort field
        /// </summary>
        public CategorySortField SortBy { get; set; } = CategorySortField.SortOrder;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

        /// <summary>
        /// Creates query for root categories
        /// </summary>
        public static GetCategoriesByParentQuery ForRootCategories(bool activeOnly = true, bool includeChildren = false, bool includeDescriptions = true, string? languageCode = null)
        {
            return new GetCategoriesByParentQuery
            {
                ParentCategoryId = null,
                ActiveOnly = activeOnly,
                IncludeChildren = includeChildren,
                IncludeDescriptions = includeDescriptions,
                LanguageCode = languageCode
            };
        }

        /// <summary>
        /// Creates query for specific parent category
        /// </summary>
        public static GetCategoriesByParentQuery ForParent(Guid parentCategoryId, bool activeOnly = true, bool includeChildren = false, bool includeDescriptions = true, string? languageCode = null)
        {
            return new GetCategoriesByParentQuery
            {
                ParentCategoryId = parentCategoryId,
                ActiveOnly = activeOnly,
                IncludeChildren = includeChildren,
                IncludeDescriptions = includeDescriptions,
                LanguageCode = languageCode
            };
        }
    }
}
