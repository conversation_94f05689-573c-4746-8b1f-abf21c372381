{"Version": 1, "WorkspaceRootPath": "F:\\Software Development\\EtyraCommerce\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\shipping\\countryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\shipping\\countryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\payment\\commands\\createpaymentmethodcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\payment\\commands\\createpaymentmethodcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\contexts\\etyracommercedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\contexts\\etyracommercedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\currency\\currencyprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\currency\\currencyprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\common\\pagedresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\common\\pagedresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\order\\orderprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\order\\orderprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\cart\\cartprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\cart\\cartprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\mapping\\currencyprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\mapping\\currencyprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\payment\\paymentmethodprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\payment\\paymentmethodprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\currency\\queries\\exchangeratequeries.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\currency\\queries\\exchangeratequeries.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\currency\\iexchangerateprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\currency\\iexchangerateprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\currency\\commands\\exchangeratecommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\currency\\commands\\exchangeratecommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\seeds\\paymentmethodseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\seeds\\paymentmethodseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\mapping\\paymentmethodprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\mapping\\paymentmethodprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\useraddress\\useraddressprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\useraddress\\useraddressprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\repositories\\useraddress\\useraddresswriterepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\repositories\\useraddress\\useraddresswriterepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\user\\userprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\user\\userprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\repositories\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\repositories\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\repositories\\useraddress\\iuseraddresswriterepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\repositories\\useraddress\\iuseraddresswriterepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\repositories\\useraddress\\iuseraddressreadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\repositories\\useraddress\\iuseraddressreadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\user\\useraddress.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\user\\useraddress.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\configurations\\useraddressconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\configurations\\useraddressconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\queries\\searchordersquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\queries\\searchordersquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\product\\productprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\product\\productprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\warehousecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\warehousecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\inventorytransactioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\inventorytransactioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\inventorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\inventorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\inventory\\warehouse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\inventory\\warehouse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\inventory\\inventory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\inventory\\inventory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\inventory\\inventorytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\inventory\\inventorytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\product\\warehouseproduct.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\product\\warehouseproduct.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\product\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\product\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\inventory\\inventoryprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\inventory\\inventoryprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\inventory\\warehousedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\inventory\\warehousedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\category\\categoryprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\category\\categoryprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\queries\\searchordersqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\queries\\searchordersqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\product\\productdiscountdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\product\\productdiscountdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\order\\orderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\order\\orderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\queries\\getorderstatisticsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\queries\\getorderstatisticsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\iorderprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\iorderprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\order\\ordersearchdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\order\\ordersearchdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\queries\\getorderbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\queries\\getorderbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\migrations\\20250701235128_mig001.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\migrations\\20250701235128_mig001.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\category\\queries\\getcategoriesbyparentquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\category\\queries\\getcategoriesbyparentquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\categorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\categorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\queries\\getorderbyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\queries\\getorderbyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\commands\\updateorderstatuscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\commands\\updateorderstatuscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\commands\\updateorderstatuscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\commands\\updateorderstatuscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\queries\\getuserordersqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\queries\\getuserordersqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\commands\\updatepaymentstatuscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\commands\\updatepaymentstatuscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\commands\\cancelordercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\commands\\cancelordercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\commands\\createordercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\commands\\createordercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\commands\\updatepaymentstatuscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\commands\\updatepaymentstatuscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\iorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\iorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\queries\\getuserordersquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\queries\\getuserordersquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\handlers\\queries\\getorderstatisticsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\handlers\\queries\\getorderstatisticsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\commands\\createordercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\commands\\createordercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\order\\commands\\cancelordercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\order\\commands\\cancelordercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\configurations\\valueobjects\\valueobjectconversions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\configurations\\valueobjects\\valueobjectconversions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\product\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\product\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\user\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\user\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\category\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\category\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\handlers\\commands\\createproductcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\handlers\\commands\\createproductcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\category\\handlers\\commands\\createcategorycommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\category\\handlers\\commands\\createcategorycommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\commands\\registerusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\commands\\registerusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\commands\\registerusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\commands\\registerusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\category\\commands\\createcategorycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\category\\commands\\createcategorycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\productcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\productcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\queries\\searchproductsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\queries\\searchproductsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\commands\\createproductcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\commands\\createproductcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\queries\\getproductsbycategoryquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\queries\\getproductsbycategoryquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\commands\\updateproductcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\commands\\updateproductcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\product\\queries\\getallproductsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\product\\queries\\getallproductsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\etyracommerce.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\etyracommerce.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\configurations\\productconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\configurations\\productconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\product\\createproductdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\product\\createproductdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\product\\updateproductdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\product\\updateproductdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\base\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\base\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\category\\category.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\category\\category.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\servicemodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\servicemodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\configurations\\productvariantconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\configurations\\productvariantconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\customresponse\\customresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\customresponse\\customresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\authentication\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\authentication\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|f:\\software development\\etyracommerce\\presentation\\etyracommerce.api\\etyracommerce.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{78728E7E-4C54-4B3D-9FFD-6A2BDFBFDB02}|Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj|solutionrelative:presentation\\etyracommerce.api\\etyracommerce.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\iuserprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\iuserprocessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserbyemailqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserbyemailqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\queries\\getuserqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\services\\service.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\services\\service.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\commands\\loginusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\commands\\loginusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\commands\\activateusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\commands\\activateusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\handlers\\commands\\changepasswordcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\handlers\\commands\\changepasswordcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\services\\user\\commands\\activateusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\services\\user\\commands\\activateusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\repositories\\ireadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\repositories\\ireadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\repositories\\readrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\repositories\\readrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\entities\\user\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\entities\\user\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\repositories\\user\\userreadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\repositories\\user\\userreadrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\user\\userstatisticsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\user\\userstatisticsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\product\\productattributedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\product\\productattributedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.application\\dtos\\category\\categorydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711EA98F-A1D9-4509-B261-1C3BD2DBDF5D}|Core\\EtyraCommerce.Application\\EtyraCommerce.Application.csproj|solutionrelative:core\\etyracommerce.application\\dtos\\category\\categorydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|f:\\software development\\etyracommerce\\infrastructure\\etyracommerce.persistence\\mapping\\mapprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50C1A7F2-E634-424B-B39D-39D4409F9172}|Infrastructure\\EtyraCommerce.Persistence\\EtyraCommerce.Persistence.csproj|solutionrelative:infrastructure\\etyracommerce.persistence\\mapping\\mapprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|f:\\software development\\etyracommerce\\core\\etyracommerce.domain\\etyracommerce.domain.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{557F91E7-9500-4B86-9749-E5E8C0C45B3D}|Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj|solutionrelative:core\\etyracommerce.domain\\etyracommerce.domain.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "CreatePaymentMethodCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Payment\\Commands\\CreatePaymentMethodCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Payment\\Commands\\CreatePaymentMethodCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Payment\\Commands\\CreatePaymentMethodCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Payment\\Commands\\CreatePaymentMethodCommand.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T00:25:29.704Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CountryService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Shipping\\CountryService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Shipping\\CountryService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Shipping\\CountryService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Shipping\\CountryService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T00:25:11.618Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CurrencyProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Currency\\CurrencyProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Currency\\CurrencyProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Currency\\CurrencyProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Currency\\CurrencyProcessService.cs", "ViewState": "AgIAAPMAAAAAAAAAAAAQwAgBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T22:10:28.684Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CurrencyProfile.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Mapping\\CurrencyProfile.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Mapping\\CurrencyProfile.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Mapping\\CurrencyProfile.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Mapping\\CurrencyProfile.cs", "ViewState": "AgIAADMAAAAAAAAAAAAlwEMAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T21:54:54.559Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ExchangeRateQueries.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\Queries\\ExchangeRateQueries.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Currency\\Queries\\ExchangeRateQueries.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\Queries\\ExchangeRateQueries.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Currency\\Queries\\ExchangeRateQueries.cs", "ViewState": "AgIAAJcAAAAAAAAAAAApwKgAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T21:46:36.53Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "IExchangeRateProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\IExchangeRateProcessService.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Currency\\IExchangeRateProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\IExchangeRateProcessService.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Currency\\IExchangeRateProcessService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T21:46:33.669Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ExchangeRateCommands.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\Commands\\ExchangeRateCommands.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Currency\\Commands\\ExchangeRateCommands.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Currency\\Commands\\ExchangeRateCommands.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Currency\\Commands\\ExchangeRateCommands.cs", "ViewState": "AgIAALEAAAAAAAAAAAApwMIAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T21:46:26.221Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "PaymentMethodProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Payment\\PaymentMethodProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Payment\\PaymentMethodProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Payment\\PaymentMethodProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Payment\\PaymentMethodProcessService.cs", "ViewState": "AgIAAK8BAAAAAAAAAAApwMABAACOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T15:10:18.409Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "PaymentMethodSeeder.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Seeds\\PaymentMethodSeeder.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Seeds\\PaymentMethodSeeder.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Seeds\\PaymentMethodSeeder.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Seeds\\PaymentMethodSeeder.cs", "ViewState": "AgIAADsAAAAAAAAAAAApwEwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T15:06:14.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PaymentMethodProfile.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Mapping\\PaymentMethodProfile.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Mapping\\PaymentMethodProfile.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Mapping\\PaymentMethodProfile.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Mapping\\PaymentMethodProfile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T14:59:40.745Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CartProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Cart\\CartProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Cart\\CartProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Cart\\CartProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Cart\\CartProcessService.cs", "ViewState": "AgIAAC0AAAAAAAAAAAApwD4AAABpAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T20:21:01.184Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "UserAddressWriteRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\UserAddress\\UserAddressWriteRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\UserAddress\\UserAddressWriteRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\UserAddress\\UserAddressWriteRepository.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\UserAddress\\UserAddressWriteRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T17:05:46.927Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "UserAddressProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\UserAddress\\UserAddressProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\UserAddress\\UserAddressProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\UserAddress\\UserAddressProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\UserAddress\\UserAddressProcessService.cs", "ViewState": "AgIAAGQBAAAAAAAAAAAlwGwBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T17:03:22.666Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "IRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\IRepository.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Repositories\\IRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\IRepository.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Repositories\\IRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T16:57:10.956Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "IUserAddressWriteRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressWriteRepository.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressWriteRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressWriteRepository.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressWriteRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T16:43:53.884Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "IUserAddressReadRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressReadRepository.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressReadRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressReadRepository.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Repositories\\UserAddress\\IUserAddressReadRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T16:43:21.506Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "UserAddressConfiguration.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserAddressConfiguration.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserAddressConfiguration.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserAddressConfiguration.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserAddressConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T16:25:00.241Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "UserAddress.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\User\\UserAddress.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\User\\UserAddress.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\User\\UserAddress.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\User\\UserAddress.cs", "ViewState": "AgIAAFoAAAAAAAAAAIA5wNMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T16:24:54.304Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "WarehouseController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\WarehouseController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\WarehouseController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\WarehouseController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\WarehouseController.cs", "ViewState": "AgIAACMAAAAAAAAAAAAMwCwAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:17:35.685Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "InventoryController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\InventoryController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\InventoryController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\InventoryController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\InventoryController.cs", "ViewState": "AgIAAHEBAAAAAAAAAAD4v3wBAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:53:25.243Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "InventoryTransactionController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\InventoryTransactionController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\InventoryTransactionController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\InventoryTransactionController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\InventoryTransactionController.cs", "ViewState": "AgIAAKIAAAAAAAAAAAAowLYAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:48:46.089Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "Inventory.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Inventory.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Inventory.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Inventory.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Inventory.cs", "ViewState": "AgIAAE0AAAAAAAAAAIAzwG0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:31:09.923Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "Warehouse.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Warehouse.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Warehouse.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Warehouse.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\Warehouse.cs", "ViewState": "AgIAACcAAAAAAAAAAAAtwDEAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:31:16.455Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "InventoryTransaction.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\InventoryTransaction.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\InventoryTransaction.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Inventory\\InventoryTransaction.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Inventory\\InventoryTransaction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:31:12.866Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "WarehouseProduct.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Product\\WarehouseProduct.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Product\\WarehouseProduct.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Product\\WarehouseProduct.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Product\\WarehouseProduct.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:31:04.931Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "WarehouseDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Inventory\\WarehouseDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Inventory\\WarehouseDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Inventory\\WarehouseDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Inventory\\WarehouseDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T08:25:43.472Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "InventoryProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Inventory\\InventoryProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Inventory\\InventoryProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Inventory\\InventoryProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Inventory\\InventoryProcessService.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAowLYAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T08:15:34.538Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrderProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderProcessService.cs", "ViewState": "AgIAAJECAAAAAAAAAAApwKICAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:41:32.294Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "IOrderProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\IOrderProcessService.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\IOrderProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\IOrderProcessService.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\IOrderProcessService.cs", "ViewState": "AgIAABcAAAAAAAAAAIA9wCQAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:41:06.112Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "OrderService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Order\\OrderService.cs", "ViewState": "AgIAAAQAAAAAAAAAAAD4vw8AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:25:43.927Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "20250701235128_mig001.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Migrations\\20250701235128_mig001.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Migrations\\20250701235128_mig001.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Migrations\\20250701235128_mig001.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Migrations\\20250701235128_mig001.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T23:51:29.033Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "GetCategoriesByParentQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Queries\\GetCategoriesByParentQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Category\\Queries\\GetCategoriesByParentQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Queries\\GetCategoriesByParentQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Category\\Queries\\GetCategoriesByParentQuery.cs", "ViewState": "AgIAACEAAAAAAAAAAAAMwC0AAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T23:23:09.917Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "OrderSearchDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Order\\OrderSearchDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Order\\OrderSearchDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Order\\OrderSearchDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Order\\OrderSearchDto.cs", "ViewState": "AgIAAIYAAAAAAAAAAAAMwJYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T23:18:35.384Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "GetOrderStatisticsQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderStatisticsQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderStatisticsQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderStatisticsQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderStatisticsQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:04:39.817Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "SearchOrdersQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\SearchOrdersQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\SearchOrdersQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\SearchOrdersQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\SearchOrdersQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:04:32.426Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "IOrderService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\IOrderService.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\IOrderService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\IOrderService.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\IOrderService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:04:30.786Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "GetOrderByIdQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderByIdQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderByIdQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderByIdQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderByIdQueryHandler.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAlwCAAAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:01:22.571Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "SearchOrdersQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\SearchOrdersQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\SearchOrdersQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\SearchOrdersQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\SearchOrdersQueryHandler.cs", "ViewState": "AgIAADIAAAAAAAAAAAAowDgAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:53.368Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "GetOrderByIdQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderByIdQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderByIdQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderByIdQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetOrderByIdQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA0AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:48.001Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "GetUserOrdersQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetUserOrdersQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetUserOrdersQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetUserOrdersQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Queries\\GetUserOrdersQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:43.173Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "GetUserOrdersQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetUserOrdersQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetUserOrdersQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetUserOrdersQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetUserOrdersQueryHandler.cs", "ViewState": "AgIAAA4AAAAAAAAAAIAwwBkAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:34.334Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "GetOrderStatisticsQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderStatisticsQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderStatisticsQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderStatisticsQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Queries\\GetOrderStatisticsQueryHandler.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAMwA0AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:20.485Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "UpdatePaymentStatusCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdatePaymentStatusCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdatePaymentStatusCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdatePaymentStatusCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdatePaymentStatusCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAsAAABzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:15.513Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "CancelOrderCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CancelOrderCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CancelOrderCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CancelOrderCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CancelOrderCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:00:02.521Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "CreateOrderCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CreateOrderCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CreateOrderCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CreateOrderCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CreateOrderCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:57.534Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "UpdateOrderStatusCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdateOrderStatusCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdateOrderStatusCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdateOrderStatusCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\UpdateOrderStatusCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAABvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:47.983Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "CreateOrderCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CreateOrderCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CreateOrderCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CreateOrderCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Handlers\\Commands\\CreateOrderCommandHandler.cs", "ViewState": "AgIAAA0AAAAAAAAAAIAwwBgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:41.38Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "UpdatePaymentStatusCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdatePaymentStatusCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdatePaymentStatusCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdatePaymentStatusCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdatePaymentStatusCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:33.727Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "UpdateOrderStatusCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdateOrderStatusCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdateOrderStatusCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdateOrderStatusCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\UpdateOrderStatusCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:28.248Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "CancelOrderCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CancelOrderCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CancelOrderCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CancelOrderCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Order\\Commands\\CancelOrderCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T21:59:23.334Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ProductProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductProcessService.cs", "ViewState": "AgIAACMAAAAAAAAAAAAlwDMAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T16:17:00.58Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "PagedResult.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Common\\PagedResult.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Common\\PagedResult.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Common\\PagedResult.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Common\\PagedResult.cs", "ViewState": "AgIAACQAAAAAAAAAAAAhwDMAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T15:27:00.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 65, "Title": "CreateCategoryCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Handlers\\Commands\\CreateCategoryCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Category\\Handlers\\Commands\\CreateCategoryCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Handlers\\Commands\\CreateCategoryCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Category\\Handlers\\Commands\\CreateCategoryCommandHandler.cs", "ViewState": "AgIAAFQAAACAZmZmZiYswAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T15:18:07.931Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "CreateCategoryCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Commands\\CreateCategoryCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Category\\Commands\\CreateCategoryCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Category\\Commands\\CreateCategoryCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Category\\Commands\\CreateCategoryCommand.cs", "ViewState": "AgIAAEcAAACAmZmZmdkqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T15:15:56.711Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "CategoryService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T15:15:16.805Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "ProductService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Product\\ProductService.cs", "ViewState": "AgIAABUAAAAAAAAAAAAjwBEAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T15:15:01.657Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "CategoryController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\CategoryController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\CategoryController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\CategoryController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\CategoryController.cs", "ViewState": "AgIAAHkAAAAAAAAAAAD4v4QAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:54:22.069Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "ProductController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\ProductController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\ProductController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\ProductController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\ProductController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGABAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:12:49.359Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "SearchProductsQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\SearchProductsQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\SearchProductsQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\SearchProductsQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\SearchProductsQuery.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAUwGUAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:00:09.423Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "CreateProductCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\CreateProductCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\CreateProductCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\CreateProductCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\CreateProductCommand.cs", "ViewState": "AgIAAIkAAAAAAAAAAAAUwJQAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:00:08.381Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "GetProductsByCategoryQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetProductsByCategoryQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetProductsByCategoryQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetProductsByCategoryQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetProductsByCategoryQuery.cs", "ViewState": "AgIAABMAAAAAAAAAAAAUwCoAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:00:06.922Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "UpdateProductCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\UpdateProductCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\UpdateProductCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\UpdateProductCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Commands\\UpdateProductCommand.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAUwIcAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:00:03.595Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "GetAllProductsQuery.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetAllProductsQuery.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetAllProductsQuery.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetAllProductsQuery.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Queries\\GetAllProductsQuery.cs", "ViewState": "AgIAABoAAAAAAAAAAAAUwCUAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T14:00:00.183Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "CreateProductCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Handlers\\Commands\\CreateProductCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\Product\\Handlers\\Commands\\CreateProductCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\Product\\Handlers\\Commands\\CreateProductCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\Product\\Handlers\\Commands\\CreateProductCommandHandler.cs", "ViewState": "AgIAAHgAAAAANDMzMzP5vzoAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:45:32.149Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "ProductConfiguration.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductConfiguration.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductConfiguration.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductConfiguration.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductConfiguration.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAjwFYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:27:28.864Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "UpdateProductDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\UpdateProductDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Product\\UpdateProductDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\UpdateProductDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Product\\UpdateProductDto.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAjwIwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:19:49.436Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "BaseEntity.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Base\\BaseEntity.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Base\\BaseEntity.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Base\\BaseEntity.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Base\\BaseEntity.cs", "ViewState": "AgIAACMAAAAAAAAAAAAUwCwAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T10:59:33.327Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "CategoryProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Category\\CategoryProcessService.cs", "ViewState": "AgIAAIEBAAAAAAAAAAAMwI8BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T00:45:14.279Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "ProductVariantConfiguration.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductVariantConfiguration.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductVariantConfiguration.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductVariantConfiguration.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ProductVariantConfiguration.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAjwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T23:35:10.354Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "appsettings.json", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\appsettings.json", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\appsettings.json", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\appsettings.json", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-30T22:58:30.615Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 85, "Title": "CustomResponseDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\CustomResponse\\CustomResponseDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\CustomResponse\\CustomResponseDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\CustomResponse\\CustomResponseDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\CustomResponse\\CustomResponseDto.cs", "ViewState": "AgIAAL0AAAAAAAAAAAAiwM4AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T22:54:46.944Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "UserController.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Controllers\\UserController.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Controllers\\UserController.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Controllers\\UserController.cs", "ViewState": "AgIAAGsAAAAAAAAAAAAiwIAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T22:53:23.214Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "JwtService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Authentication\\JwtService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Authentication\\JwtService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Authentication\\JwtService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Authentication\\JwtService.cs", "ViewState": "AgIAAOgAAAAAAAAAAAAcwPgAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T22:52:15.782Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "GetUserByIdQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByIdQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByIdQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByIdQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByIdQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T22:52:02.019Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Program.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Program.cs", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\Program.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\Program.cs", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T22:40:07.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 89, "Title": "EtyraCommerce.API", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-30T22:39:45.159Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "EtyraCommerce.API.csproj", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "RelativeDocumentMoniker": "Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "RelativeToolTip": "Presentation\\EtyraCommerce.API\\EtyraCommerce.API.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-30T22:38:48.524Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "UserService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserService.cs", "ViewState": "AgIAABEAAADAzMzMzOw3wDcAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T07:49:00.519Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "UserProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserProcessService.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserProcessService.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\User\\UserProcessService.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAtwGEAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T06:32:03.856Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "IUserProcessService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\IUserProcessService.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\IUserProcessService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\IUserProcessService.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\IUserProcessService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAJwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T06:31:03.165Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "GetUserQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserQueryHandler.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAuwEEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:27:38.959Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "ActivateUserCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ActivateUserCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ActivateUserCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ActivateUserCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ActivateUserCommandHandler.cs", "ViewState": "AgIAABUAAAAAAAAAAAAQwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:22:55.348Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "GetUserByEmailQueryHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByEmailQueryHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByEmailQueryHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByEmailQueryHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Queries\\GetUserByEmailQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:23:01.779Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "RegisterUserCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\RegisterUserCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\RegisterUserCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\RegisterUserCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\RegisterUserCommandHandler.cs", "ViewState": "AgIAACIAAACgmZmZmdktwFEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:21:40.895Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "ChangePasswordCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ChangePasswordCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ChangePasswordCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ChangePasswordCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\ChangePasswordCommandHandler.cs", "ViewState": "AgIAABYAAAAAAAAAAAAQwDcAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:21:35.598Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "IUserService.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\IUserService.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\IUserService.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\IUserService.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\IUserService.cs", "ViewState": "AgIAAAwAAACAZmZmZiYgwBsAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:18:32.476Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "LoginUserCommandHandler.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\LoginUserCommandHandler.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\LoginUserCommandHandler.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\LoginUserCommandHandler.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Handlers\\Commands\\LoginUserCommandHandler.cs", "ViewState": "AgIAACgAAAAAAAAAAAAtwEUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:17:38.56Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "ReadRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\ReadRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\ReadRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\ReadRepository.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\ReadRepository.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAtwBoAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:06:35.696Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "IReadRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\IReadRepository.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Repositories\\IReadRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Repositories\\IReadRepository.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Repositories\\IReadRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:06:30.209Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "UserReadRepository.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\User\\UserReadRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\User\\UserReadRepository.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Repositories\\User\\UserReadRepository.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Repositories\\User\\UserReadRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:30:42.937Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "Service.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Service.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Service.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Services\\Service.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Services\\Service.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAtwBMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:27:51.278Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "UserStatisticsDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\User\\UserStatisticsDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\User\\UserStatisticsDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\User\\UserStatisticsDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\User\\UserStatisticsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:26:23.073Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "RegisterUserCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Commands\\RegisterUserCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Commands\\RegisterUserCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Commands\\RegisterUserCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Commands\\RegisterUserCommand.cs", "ViewState": "AgIAAEcAAACAmZmZmdkhwF4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:20:25.996Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "ActivateUserCommand.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Commands\\ActivateUserCommand.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\Services\\User\\Commands\\ActivateUserCommand.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\Services\\User\\Commands\\ActivateUserCommand.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\Services\\User\\Commands\\ActivateUserCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:19:44.764Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ValueObjectConversions.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ValueObjects\\ValueObjectConversions.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ValueObjects\\ValueObjectConversions.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ValueObjects\\ValueObjectConversions.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\ValueObjects\\ValueObjectConversions.cs", "ViewState": "AgIAADEBAAAAAAAAAAAMwEABAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:08:16.077Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "UserConfiguration.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserConfiguration.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserConfiguration.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserConfiguration.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Configurations\\UserConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:07:59.767Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "ProductDiscountDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductDiscountDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductDiscountDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductDiscountDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductDiscountDto.cs", "ViewState": "AgIAAEAAAAAAAAAAAIA9wEoAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:06:24.519Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "ProductAttributeDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductAttributeDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductAttributeDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductAttributeDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Product\\ProductAttributeDto.cs", "ViewState": "AgIAAKYAAAAAAAAAAAAuwKYAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:05:58.905Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "CategoryDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Category\\CategoryDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Category\\CategoryDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Category\\CategoryDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Category\\CategoryDto.cs", "ViewState": "AgIAABwBAAAAAAAAAAAQwDcBAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:05:34.522Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "CreateProductDto.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\CreateProductDto.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Application\\DTOs\\Product\\CreateProductDto.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Application\\DTOs\\Product\\CreateProductDto.cs", "RelativeToolTip": "Core\\EtyraCommerce.Application\\DTOs\\Product\\CreateProductDto.cs", "ViewState": "AgIAAJoAAAAAAAAAAAAMwJoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T22:53:21.211Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "Product.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Product\\Product.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Product\\Product.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Product\\Product.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Product\\Product.cs", "ViewState": "AgIAAFwAAAAAAAAAAAD4vwkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:02:43.602Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "EtyraCommerceDbContext.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Contexts\\EtyraCommerceDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Contexts\\EtyraCommerceDbContext.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Contexts\\EtyraCommerceDbContext.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Contexts\\EtyraCommerceDbContext.cs", "ViewState": "AgIAAKcAAAAAAAAAAAAlwMEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T23:02:27.569Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 106, "Title": "EtyraCommerce.Domain.csproj", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\EtyraCommerce.Domain.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-29T23:02:09.548Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "Category.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Category\\Category.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\Category\\Category.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\Category\\Category.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\Category\\Category.cs", "ViewState": "AgIAAIgAAAAAAAAAAAASwJUAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T22:44:10.143Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "User.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\User\\User.cs", "RelativeDocumentMoniker": "Core\\EtyraCommerce.Domain\\Entities\\User\\User.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Core\\EtyraCommerce.Domain\\Entities\\User\\User.cs", "RelativeToolTip": "Core\\EtyraCommerce.Domain\\Entities\\User\\User.cs", "ViewState": "AgIAAMkAAAAAAAAAAAAAwNIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T22:25:42.709Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "MapProfile.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Mapping\\MapProfile.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\Mapping\\MapProfile.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\Mapping\\MapProfile.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\Mapping\\MapProfile.cs", "ViewState": "AgIAAJUAAAAAAAAAAAD4v6oBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T22:18:40.329Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "ServiceModule.cs", "DocumentMoniker": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\ServiceModule.cs", "RelativeDocumentMoniker": "Infrastructure\\EtyraCommerce.Persistence\\ServiceModule.cs", "ToolTip": "F:\\Software Development\\EtyraCommerce\\Infrastructure\\EtyraCommerce.Persistence\\ServiceModule.cs", "RelativeToolTip": "Infrastructure\\EtyraCommerce.Persistence\\ServiceModule.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAQwF4AAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T22:16:22.006Z"}]}]}]}