﻿using EtyraApp.Domain.Entities.Company;
using EtyraApp.Domain.Entities.Settings;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Catalog;

public class CategoryDescription
{
    [MaxLength(50)]
    public string Name { get; set; }

    [MaxLength(50)]
    public string MetaTitle { get; set; }

    [MaxLength(50)]
    public string? Seo { get; set; }

    public bool GoogleMerchantStatus { get; set; } = false;


    //Relationships

    // PRODUCT - LANGUAGE - STORE
    public int CategoryId { get; set; }
    public Category Category { get; set; }
    public Language Language { get; set; }
    public int LanguageId { get; set; }
    public Store Store { get; set; }
    public int StoreId { get; set; }

}