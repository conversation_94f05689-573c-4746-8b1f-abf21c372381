using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Currency;
using MediatR;

namespace EtyraCommerce.Application.Services.Currency.Queries;

/// <summary>
/// Query to get the default currency
/// </summary>
public class GetDefaultCurrencyQuery : IRequest<CustomResponseDto<CurrencyDto>>
{
    /// <summary>
    /// Whether to track changes for entities
    /// </summary>
    public bool Tracking { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    public GetDefaultCurrencyQuery(bool tracking = false)
    {
        Tracking = tracking;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetDefaultCurrencyQuery() { }
}
