using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Region entity for European shipping system
    /// Represents regions/states/counties within countries (e.g., Romanian Counties, German States, French Regions)
    /// </summary>
    public class Region : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Region name (e.g., "Bucuresti", "Bayern", "Île-de-France")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Region code (e.g., "B", "BY", "IDF")
        /// </summary>
        [Required]
        [MaxLength(10)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Type of region (County, State, Region, Province, etc.)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display order for region selection lists
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Indicates if shipping is available to this region
        /// </summary>
        public bool IsShippingEnabled { get; set; } = true;

        /// <summary>
        /// Additional shipping notes for this region
        /// </summary>
        [MaxLength(500)]
        public string? ShippingNotes { get; set; }

        #endregion

        #region Foreign Keys

        /// <summary>
        /// Reference to the parent country
        /// </summary>
        public Guid CountryId { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Parent country
        /// </summary>
        public virtual Country Country { get; set; } = null!;

        /// <summary>
        /// Cities within this region
        /// </summary>
        public virtual ICollection<City> Cities { get; set; } = new List<City>();

        /// <summary>
        /// Shipping zone locations that reference this region
        /// </summary>
        public virtual ICollection<ShippingZoneLocation> ShippingZoneLocations { get; set; } = new List<ShippingZoneLocation>();

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected Region() { }

        /// <summary>
        /// Constructor for creating a new region
        /// </summary>
        public Region(Guid countryId, string name, string code, string type)
        {
            CountryId = countryId;
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Code = code?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(code));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            IsShippingEnabled = true;
            DisplayOrder = 0;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Enables shipping to this region
        /// </summary>
        public void EnableShipping(string? notes = null)
        {
            IsShippingEnabled = true;
            if (!string.IsNullOrWhiteSpace(notes))
                ShippingNotes = notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Disables shipping to this region
        /// </summary>
        public void DisableShipping(string? reason = null)
        {
            IsShippingEnabled = false;
            if (!string.IsNullOrWhiteSpace(reason))
                ShippingNotes = reason.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates region information
        /// </summary>
        public void UpdateInfo(string name, string code, string type, string? shippingNotes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            Code = code?.Trim().ToUpperInvariant() ?? throw new ArgumentNullException(nameof(code));
            Type = type?.Trim() ?? throw new ArgumentNullException(nameof(type));
            ShippingNotes = string.IsNullOrWhiteSpace(shippingNotes) ? null : shippingNotes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for region lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets full region display name with code and type
        /// </summary>
        public string GetDisplayName() => $"{Name} ({Code}) - {Type}";

        /// <summary>
        /// Gets short display name
        /// </summary>
        public string GetShortDisplayName() => $"{Name} ({Code})";

        /// <summary>
        /// Checks if region supports shipping
        /// </summary>
        public bool CanShipTo() => IsShippingEnabled && IsActive;

        /// <summary>
        /// Gets region type display text
        /// </summary>
        public string GetTypeDisplayText()
        {
            return Type.ToLowerInvariant() switch
            {
                "county" => "County",
                "state" => "State",
                "region" => "Region",
                "province" => "Province",
                "department" => "Department",
                "canton" => "Canton",
                _ => Type
            };
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates region data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(Code) &&
                   !string.IsNullOrWhiteSpace(Type) &&
                   CountryId != Guid.Empty;
        }

        #endregion
    }
}
