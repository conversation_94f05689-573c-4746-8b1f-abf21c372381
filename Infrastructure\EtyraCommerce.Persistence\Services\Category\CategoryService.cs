using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category;
using EtyraCommerce.Application.Services.Category.Commands;
using EtyraCommerce.Application.Services.Category.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Category
{
    /// <summary>
    /// Category service implementation for MediatR command/query dispatch
    /// </summary>
    public class CategoryService : ICategoryService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CategoryService> _logger;

        public CategoryService(IMediator mediator, ILogger<CategoryService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        #region Category Management

        public async Task<CustomResponseDto<CategoryDto>> CreateCategoryAsync(CreateCategoryDto createCategoryDto)
        {
            _logger.LogInformation("CategoryService: Creating category with name: {CategoryName}", createCategoryDto.Name);

            var command = CreateCategoryCommand.FromDto(createCategoryDto);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CategoryService: Category creation completed for name: {CategoryName}", createCategoryDto.Name);
            return result;
        }

        public async Task<CustomResponseDto<CategoryDto>> UpdateCategoryAsync(Guid categoryId, UpdateCategoryDto updateCategoryDto)
        {
            _logger.LogInformation("CategoryService: Updating category with ID: {CategoryId}", categoryId);

            var command = UpdateCategoryCommand.FromDto(categoryId, updateCategoryDto);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CategoryService: Category update completed for ID: {CategoryId}", categoryId);
            return result;
        }

        public async Task<CustomResponseDto<NoContentDto>> DeleteCategoryAsync(Guid categoryId, bool forceDelete = false, bool deleteChildren = false)
        {
            _logger.LogInformation("CategoryService: Deleting category with ID: {CategoryId}, ForceDelete: {ForceDelete}, DeleteChildren: {DeleteChildren}",
                categoryId, forceDelete, deleteChildren);

            var command = DeleteCategoryCommand.Create(categoryId, forceDelete, deleteChildren);
            var result = await _mediator.Send(command);

            _logger.LogInformation("CategoryService: Category deletion completed for ID: {CategoryId}", categoryId);
            return result;
        }

        #endregion

        #region Category Queries

        public async Task<CustomResponseDto<CategoryDto>> GetCategoryByIdAsync(Guid categoryId, bool includeChildren = false, bool includeParent = false, bool includeDescriptions = true, string? languageCode = null)
        {
            _logger.LogInformation("CategoryService: Getting category by ID: {CategoryId}", categoryId);

            var query = GetCategoryByIdQuery.Create(categoryId, includeChildren, includeParent, includeDescriptions, languageCode);
            var result = await _mediator.Send(query);

            _logger.LogInformation("CategoryService: Category retrieval completed for ID: {CategoryId}", categoryId);
            return result;
        }

        public async Task<CustomResponseDto<PagedResult<CategoryDto>>> GetAllCategoriesAsync(CategorySearchDto searchDto)
        {
            _logger.LogInformation("CategoryService: Getting all categories with search term: {SearchTerm}", searchDto.SearchTerm ?? "None");

            var query = GetAllCategoriesQuery.FromDto(searchDto);
            var result = await _mediator.Send(query);

            _logger.LogInformation("CategoryService: All categories retrieval completed");
            return result;
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> GetCategoriesByParentAsync(Guid? parentCategoryId, bool activeOnly = true, bool includeChildren = false, bool includeDescriptions = true, string? languageCode = null)
        {
            _logger.LogInformation("CategoryService: Getting categories by parent ID: {ParentCategoryId}", parentCategoryId?.ToString() ?? "Root");

            var query = parentCategoryId.HasValue
                ? GetCategoriesByParentQuery.ForParent(parentCategoryId.Value, activeOnly, includeChildren, includeDescriptions, languageCode)
                : GetCategoriesByParentQuery.ForRootCategories(activeOnly, includeChildren, includeDescriptions, languageCode);

            var result = await _mediator.Send(query);

            _logger.LogInformation("CategoryService: Categories by parent retrieval completed for parent ID: {ParentCategoryId}", parentCategoryId?.ToString() ?? "Root");
            return result;
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> GetCategoryTreeAsync(Guid? rootCategoryId = null, int? maxDepth = null, bool activeOnly = true, bool menuOnly = false, string? languageCode = null)
        {
            _logger.LogInformation("CategoryService: Getting category tree for root ID: {RootCategoryId}", rootCategoryId?.ToString() ?? "Full Tree");

            var query = rootCategoryId.HasValue
                ? GetCategoryTreeQuery.SubTree(rootCategoryId.Value, activeOnly, menuOnly, maxDepth, languageCode)
                : GetCategoryTreeQuery.FullTree(activeOnly, menuOnly, maxDepth, languageCode);

            var result = await _mediator.Send(query);

            _logger.LogInformation("CategoryService: Category tree retrieval completed for root ID: {RootCategoryId}", rootCategoryId?.ToString() ?? "Full Tree");
            return result;
        }

        #endregion

        #region Utility Methods

        public async Task<CustomResponseDto<List<CategoryDto>>> GetRootCategoriesAsync(bool activeOnly = true, bool includeChildren = false, string? languageCode = null)
        {
            _logger.LogInformation("CategoryService: Getting root categories");

            return await GetCategoriesByParentAsync(null, activeOnly, includeChildren, true, languageCode);
        }

        public async Task<CustomResponseDto<List<CategoryDto>>> GetMenuCategoriesAsync(int? maxDepth = 3, string? languageCode = null)
        {
            _logger.LogInformation("CategoryService: Getting menu categories with max depth: {MaxDepth}", maxDepth);

            var query = GetCategoryTreeQuery.MenuTree(maxDepth, languageCode);
            var result = await _mediator.Send(query);

            _logger.LogInformation("CategoryService: Menu categories retrieval completed");
            return result;
        }

        #endregion
    }
}
