using EtyraCommerce.Application.Repositories.Currency;
using EtyraCommerce.Domain.Entities.Currency;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Currency;

/// <summary>
/// Read repository implementation for ExchangeRate entity
/// </summary>
public class ExchangeRateReadRepository : ReadRepository<ExchangeRate>, IExchangeRateReadRepository
{
    public ExchangeRateReadRepository(EtyraCommerceDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get exchange rate between two currencies
    /// </summary>
    /// <param name="fromCurrencyId">From currency ID</param>
    /// <param name="toCurrencyId">To currency ID</param>
    /// <param name="effectiveDate">Effective date (defaults to current date)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Exchange rate or null</returns>
    public async Task<ExchangeRate?> GetExchangeRateAsync(Guid fromCurrencyId, Guid toCurrencyId, DateTime? effectiveDate = null, bool tracking = false)
    {
        var targetDate = effectiveDate ?? DateTime.UtcNow;

        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.FromCurrencyId == fromCurrencyId && 
                        er.ToCurrencyId == toCurrencyId &&
                        er.IsActive &&
                        er.EffectiveDate <= targetDate &&
                        (er.ExpiryDate == null || er.ExpiryDate > targetDate))
            .OrderByDescending(er => er.EffectiveDate)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Get current exchange rates for a currency
    /// </summary>
    /// <param name="currencyId">Currency ID</param>
    /// <param name="asFromCurrency">Whether to get rates where this currency is the from currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of exchange rates</returns>
    public async Task<List<ExchangeRate>> GetCurrentRatesForCurrencyAsync(Guid currencyId, bool asFromCurrency = true, bool tracking = false)
    {
        var currentDate = DateTime.UtcNow;

        var query = Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.IsActive &&
                        er.EffectiveDate <= currentDate &&
                        (er.ExpiryDate == null || er.ExpiryDate > currentDate));

        if (asFromCurrency)
        {
            query = query.Where(er => er.FromCurrencyId == currencyId);
        }
        else
        {
            query = query.Where(er => er.ToCurrencyId == currencyId);
        }

        return await query
            .OrderBy(er => asFromCurrency ? er.ToCurrency.Code : er.FromCurrency.Code)
            .ToListAsync();
    }

    /// <summary>
    /// Get exchange rates by source
    /// </summary>
    /// <param name="source">Exchange rate source</param>
    /// <param name="onlyActive">Whether to include only active rates</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of exchange rates</returns>
    public async Task<List<ExchangeRate>> GetBySourceAsync(ExchangeRateSource source, bool onlyActive = true, bool tracking = false)
    {
        var query = Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.Source == source);

        if (onlyActive)
        {
            var currentDate = DateTime.UtcNow;
            query = query.Where(er => er.IsActive &&
                                     er.EffectiveDate <= currentDate &&
                                     (er.ExpiryDate == null || er.ExpiryDate > currentDate));
        }

        return await query
            .OrderBy(er => er.FromCurrency.Code)
            .ThenBy(er => er.ToCurrency.Code)
            .ThenByDescending(er => er.EffectiveDate)
            .ToListAsync();
    }

    /// <summary>
    /// Get expired exchange rates
    /// </summary>
    /// <param name="asOfDate">Date to check expiry against (defaults to current date)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of expired exchange rates</returns>
    public async Task<List<ExchangeRate>> GetExpiredRatesAsync(DateTime? asOfDate = null, bool tracking = false)
    {
        var checkDate = asOfDate ?? DateTime.UtcNow;

        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.IsActive && 
                        er.ExpiryDate != null && 
                        er.ExpiryDate <= checkDate)
            .OrderBy(er => er.ExpiryDate)
            .ToListAsync();
    }

    /// <summary>
    /// Get exchange rates that need updating
    /// </summary>
    /// <param name="olderThanHours">Get rates older than specified hours</param>
    /// <param name="sources">Specific sources to check</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of exchange rates that need updating</returns>
    public async Task<List<ExchangeRate>> GetRatesNeedingUpdateAsync(int olderThanHours = 24, ExchangeRateSource[]? sources = null, bool tracking = false)
    {
        var cutoffDate = DateTime.UtcNow.AddHours(-olderThanHours);

        var query = Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.IsActive && er.UpdatedAt < cutoffDate);

        if (sources != null && sources.Length > 0)
        {
            query = query.Where(er => sources.Contains(er.Source));
        }

        return await query
            .OrderBy(er => er.UpdatedAt)
            .ToListAsync();
    }

    /// <summary>
    /// Check if exchange rate exists between currencies
    /// </summary>
    /// <param name="fromCurrencyId">From currency ID</param>
    /// <param name="toCurrencyId">To currency ID</param>
    /// <param name="effectiveDate">Effective date</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if rate exists</returns>
    public async Task<bool> ExchangeRateExistsAsync(Guid fromCurrencyId, Guid toCurrencyId, DateTime effectiveDate, Guid? excludeId = null)
    {
        var query = Table.Where(er => er.FromCurrencyId == fromCurrencyId && 
                                     er.ToCurrencyId == toCurrencyId &&
                                     er.EffectiveDate.Date == effectiveDate.Date);

        if (excludeId.HasValue)
        {
            query = query.Where(er => er.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// Get exchange rate by ID with currency navigation properties
    /// </summary>
    /// <param name="id">Exchange rate ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Exchange rate with currency details or null if not found</returns>
    public async Task<ExchangeRate?> GetByIdWithCurrenciesAsync(Guid id, bool tracking = false)
    {
        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .FirstOrDefaultAsync(er => er.Id == id);
    }

    /// <summary>
    /// Get all exchange rates with currency navigation properties
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <param name="activeOnly">Whether to include only active rates</param>
    /// <param name="validOnly">Whether to include only currently valid rates</param>
    /// <returns>List of exchange rates with currency details</returns>
    public async Task<List<ExchangeRate>> GetAllWithCurrenciesAsync(bool tracking = false, bool activeOnly = false, bool validOnly = false)
    {
        IQueryable<ExchangeRate> query = Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency);

        if (activeOnly)
        {
            query = query.Where(er => er.IsActive);
        }

        if (validOnly)
        {
            var currentDate = DateTime.UtcNow;
            query = query.Where(er => er.IsActive &&
                                     er.EffectiveDate <= currentDate &&
                                     (er.ExpiryDate == null || er.ExpiryDate > currentDate));
        }

        return await query
            .OrderBy(er => er.FromCurrency.Code)
            .ThenBy(er => er.ToCurrency.Code)
            .ThenByDescending(er => er.EffectiveDate)
            .ToListAsync();
    }

    /// <summary>
    /// Get current exchange rate for currency pair
    /// </summary>
    /// <param name="fromCurrencyId">Base currency ID</param>
    /// <param name="toCurrencyId">Target currency ID</param>
    /// <param name="asOfDate">Date for which to get the rate (optional)</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Current exchange rate or null if not found</returns>
    public async Task<ExchangeRate?> GetCurrentRateAsync(Guid fromCurrencyId, Guid toCurrencyId, DateTime? asOfDate = null, bool tracking = false)
    {
        var checkDate = asOfDate ?? DateTime.UtcNow;

        return await Table
            .AsTracking(tracking ? QueryTrackingBehavior.TrackAll : QueryTrackingBehavior.NoTracking)
            .Include(er => er.FromCurrency)
            .Include(er => er.ToCurrency)
            .Where(er => er.FromCurrencyId == fromCurrencyId &&
                        er.ToCurrencyId == toCurrencyId &&
                        er.IsActive &&
                        er.EffectiveDate <= checkDate &&
                        (er.ExpiryDate == null || er.ExpiryDate > checkDate))
            .OrderByDescending(er => er.EffectiveDate)
            .FirstOrDefaultAsync();
    }
}
