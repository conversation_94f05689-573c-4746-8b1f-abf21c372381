using AutoMapper;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.Services.Payment.Commands;
using EtyraCommerce.Application.Services.Payment.Queries;
using EtyraCommerce.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.Payment;

/// <summary>
/// Payment method service implementation for CQRS operations
/// </summary>
public class PaymentMethodService : IPaymentMethodService
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<PaymentMethodService> _logger;

    public PaymentMethodService(
        IMediator mediator,
        IMapper mapper,
        ILogger<PaymentMethodService> logger)
    {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    #region Command Operations

    /// <summary>
    /// Create a new payment method
    /// </summary>
    /// <param name="command">Create payment method command</param>
    /// <returns>Created payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> CreatePaymentMethodAsync(CreatePaymentMethodCommand command)
    {
        try
        {
            _logger.LogInformation("Executing CreatePaymentMethodCommand for payment method: {Name}", command.Name);
            return await _mediator.Send(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing CreatePaymentMethodCommand for payment method: {Name}", command.Name);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while creating the payment method");
        }
    }

    /// <summary>
    /// Update an existing payment method
    /// </summary>
    /// <param name="command">Update payment method command</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> UpdatePaymentMethodAsync(UpdatePaymentMethodCommand command)
    {
        try
        {
            _logger.LogInformation("Executing UpdatePaymentMethodCommand for payment method ID: {PaymentMethodId}", command.Id);
            return await _mediator.Send(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing UpdatePaymentMethodCommand for payment method ID: {PaymentMethodId}", command.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while updating the payment method");
        }
    }

    /// <summary>
    /// Delete a payment method
    /// </summary>
    /// <param name="command">Delete payment method command</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> DeletePaymentMethodAsync(DeletePaymentMethodCommand command)
    {
        try
        {
            _logger.LogInformation("Executing DeletePaymentMethodCommand for payment method ID: {PaymentMethodId}", command.Id);
            return await _mediator.Send(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing DeletePaymentMethodCommand for payment method ID: {PaymentMethodId}", command.Id);
            return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the payment method");
        }
    }

    /// <summary>
    /// Toggle payment method status
    /// </summary>
    /// <param name="command">Toggle payment method status command</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> TogglePaymentMethodStatusAsync(TogglePaymentMethodStatusCommand command)
    {
        try
        {
            _logger.LogInformation("Executing TogglePaymentMethodStatusCommand for payment method ID: {PaymentMethodId} to status: {IsActive}", 
                command.Id, command.IsActive);
            return await _mediator.Send(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing TogglePaymentMethodStatusCommand for payment method ID: {PaymentMethodId}", command.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while toggling the payment method status");
        }
    }

    #endregion

    #region Query Operations

    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="query">Get payment method by ID query</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByIdAsync(GetPaymentMethodByIdQuery query)
    {
        try
        {
            _logger.LogInformation("Executing GetPaymentMethodByIdQuery for payment method ID: {PaymentMethodId}", query.Id);
            return await _mediator.Send(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing GetPaymentMethodByIdQuery for payment method ID: {PaymentMethodId}", query.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while retrieving the payment method");
        }
    }

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="query">Get payment method by code query</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByCodeAsync(GetPaymentMethodByCodeQuery query)
    {
        try
        {
            _logger.LogInformation("Executing GetPaymentMethodByCodeQuery for payment method code: {Code}", query.Code);
            return await _mediator.Send(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing GetPaymentMethodByCodeQuery for payment method code: {Code}", query.Code);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while retrieving the payment method");
        }
    }

    /// <summary>
    /// Get all payment methods with optional filters
    /// </summary>
    /// <param name="query">Get all payment methods query</param>
    /// <returns>List of payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllPaymentMethodsAsync(GetAllPaymentMethodsQuery query)
    {
        try
        {
            _logger.LogInformation("Executing GetAllPaymentMethodsQuery with filters - OnlyActive: {OnlyActive}, Type: {Type}", 
                query.OnlyActive, query.Type);
            return await _mediator.Send(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing GetAllPaymentMethodsQuery");
            return CustomResponseDto<List<PaymentMethodDto>>.InternalServerError("An error occurred while retrieving payment methods");
        }
    }

    /// <summary>
    /// Get active payment methods for checkout
    /// </summary>
    /// <param name="query">Get active payment methods query</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsAsync(GetActivePaymentMethodsQuery query)
    {
        try
        {
            _logger.LogInformation("Executing GetActivePaymentMethodsQuery for order amount: {OrderAmount} {OrderCurrency}", 
                query.OrderAmount, query.OrderCurrency);
            return await _mediator.Send(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while executing GetActivePaymentMethodsQuery for order amount: {OrderAmount} {OrderCurrency}", 
                query.OrderAmount, query.OrderCurrency);
            return CustomResponseDto<List<PaymentMethodDto>>.InternalServerError("An error occurred while retrieving active payment methods");
        }
    }

    #endregion

    #region Convenience Methods

    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByIdAsync(Guid id, bool tracking = false)
    {
        var query = new GetPaymentMethodByIdQuery(id, tracking);
        return await GetPaymentMethodByIdAsync(query);
    }

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> GetPaymentMethodByCodeAsync(string code, bool tracking = false)
    {
        var query = new GetPaymentMethodByCodeQuery(code, tracking);
        return await GetPaymentMethodByCodeAsync(query);
    }

    /// <summary>
    /// Get all payment methods
    /// </summary>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="type">Filter by payment method type</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllPaymentMethodsAsync(bool? onlyActive = null, PaymentMethodType? type = null, bool tracking = false)
    {
        var query = new GetAllPaymentMethodsQuery(onlyActive, type, tracking: tracking);
        return await GetAllPaymentMethodsAsync(query);
    }

    /// <summary>
    /// Get active payment methods for order amount
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <returns>List of active payment methods with calculated fees</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsForOrderAsync(decimal orderAmount, string orderCurrency = "TRY")
    {
        var query = new GetActivePaymentMethodsQuery(orderAmount, orderCurrency);
        return await GetActivePaymentMethodsAsync(query);
    }

    /// <summary>
    /// Create payment method from DTO
    /// </summary>
    /// <param name="createDto">Create payment method DTO</param>
    /// <returns>Created payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> CreatePaymentMethodAsync(CreatePaymentMethodDto createDto)
    {
        var command = _mapper.Map<CreatePaymentMethodCommand>(createDto);
        return await CreatePaymentMethodAsync(command);
    }

    /// <summary>
    /// Update payment method from DTO
    /// </summary>
    /// <param name="updateDto">Update payment method DTO</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> UpdatePaymentMethodAsync(UpdatePaymentMethodDto updateDto)
    {
        var command = _mapper.Map<UpdatePaymentMethodCommand>(updateDto);
        return await UpdatePaymentMethodAsync(command);
    }

    /// <summary>
    /// Delete payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Success result</returns>
    public async Task<CustomResponseDto<NoContentDto>> DeletePaymentMethodAsync(Guid id)
    {
        var command = new DeletePaymentMethodCommand(id);
        return await DeletePaymentMethodAsync(command);
    }

    /// <summary>
    /// Toggle payment method status
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> TogglePaymentMethodStatusAsync(Guid id, bool isActive)
    {
        var command = new TogglePaymentMethodStatusCommand(id, isActive);
        return await TogglePaymentMethodStatusAsync(command);
    }

    #endregion
}
