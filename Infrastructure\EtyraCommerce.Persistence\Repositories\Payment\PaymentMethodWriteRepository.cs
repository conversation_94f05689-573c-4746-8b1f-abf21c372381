using EtyraCommerce.Application.Repositories.Payment;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Payment;

/// <summary>
/// Write repository implementation for PaymentMethod entity
/// </summary>
public class PaymentMethodWriteRepository : WriteRepository<PaymentMethod>, IPaymentMethodWriteRepository
{
    public PaymentMethodWriteRepository(EtyraCommerceDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Update payment method status
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method or null if not found</returns>
    public async Task<PaymentMethod?> UpdateStatusAsync(Guid id, bool isActive)
    {
        var paymentMethod = await Table.FirstOrDefaultAsync(pm => pm.Id == id);
        
        if (paymentMethod == null)
            return null;

        paymentMethod.IsActive = isActive;
        paymentMethod.UpdatedAt = DateTime.UtcNow;

        return paymentMethod;
    }

    /// <summary>
    /// Update display order for multiple payment methods
    /// </summary>
    /// <param name="orderUpdates">Dictionary of payment method ID and new display order</param>
    /// <returns>Number of updated records</returns>
    public async Task<int> UpdateDisplayOrdersAsync(Dictionary<Guid, int> orderUpdates)
    {
        if (!orderUpdates.Any())
            return 0;

        var paymentMethodIds = orderUpdates.Keys.ToList();
        var paymentMethods = await Table
            .Where(pm => paymentMethodIds.Contains(pm.Id))
            .ToListAsync();

        var updatedCount = 0;
        foreach (var paymentMethod in paymentMethods)
        {
            if (orderUpdates.TryGetValue(paymentMethod.Id, out var newOrder))
            {
                paymentMethod.DisplayOrder = newOrder;
                paymentMethod.UpdatedAt = DateTime.UtcNow;
                updatedCount++;
            }
        }

        return updatedCount;
    }
}
