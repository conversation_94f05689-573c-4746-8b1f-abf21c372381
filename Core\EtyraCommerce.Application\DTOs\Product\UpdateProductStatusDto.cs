using EtyraCommerce.Domain.Entities.Product;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Product
{
    /// <summary>
    /// DTO for updating product status
    /// </summary>
    public class UpdateProductStatusDto
    {
        /// <summary>
        /// New product status
        /// </summary>
        [Required(ErrorMessage = "Status is required")]
        public ProductStatus Status { get; set; }

        /// <summary>
        /// Whether product is active
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Whether product is featured
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Product discontinue date (if status is Discontinued)
        /// </summary>
        public DateTime? DiscontinueDate { get; set; }

        /// <summary>
        /// Product available start date
        /// </summary>
        public DateTime? AvailableStartDate { get; set; }

        /// <summary>
        /// Product available end date
        /// </summary>
        public DateTime? AvailableEndDate { get; set; }

        /// <summary>
        /// Reason for status change
        /// </summary>
        [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
        public string? Reason { get; set; }

        /// <summary>
        /// Whether to send notification about status change
        /// </summary>
        public bool SendNotification { get; set; } = false;

        #region Validation

        /// <summary>
        /// Custom validation for status change
        /// </summary>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (Status == ProductStatus.Discontinued && !DiscontinueDate.HasValue)
            {
                yield return new ValidationResult(
                    "Discontinue date is required when status is Discontinued",
                    new[] { nameof(DiscontinueDate) });
            }

            if (AvailableStartDate.HasValue && AvailableEndDate.HasValue && AvailableStartDate > AvailableEndDate)
            {
                yield return new ValidationResult(
                    "Available start date cannot be after available end date",
                    new[] { nameof(AvailableStartDate), nameof(AvailableEndDate) });
            }

            if (Status == ProductStatus.Published && IsActive == false)
            {
                yield return new ValidationResult(
                    "Product cannot be published while inactive",
                    new[] { nameof(Status), nameof(IsActive) });
            }
        }

        #endregion
    }
}
