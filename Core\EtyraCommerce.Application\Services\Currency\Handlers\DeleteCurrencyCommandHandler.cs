using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Currency;
using EtyraCommerce.Application.Services.Currency.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Currency.Handlers;

/// <summary>
/// Handler for deleting currencies
/// </summary>
public class DeleteCurrencyCommandHandler : IRequestHandler<DeleteCurrencyCommand, CustomResponseDto<NoContentDto>>
{
    private readonly ICurrencyProcessService _currencyProcessService;
    private readonly ILogger<DeleteCurrencyCommandHandler> _logger;

    public DeleteCurrencyCommandHandler(
        ICurrencyProcessService currencyProcessService,
        ILogger<DeleteCurrencyCommandHandler> logger)
    {
        _currencyProcessService = currencyProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle delete currency command
    /// </summary>
    /// <param name="request">Delete currency command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content response</returns>
    public async Task<CustomResponseDto<NoContentDto>> Handle(DeleteCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing DeleteCurrencyCommand for currency ID: {CurrencyId}", request.Id);

            var result = await _currencyProcessService.ProcessDeleteAsync(request.Id);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Currency {CurrencyId} deleted successfully", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to delete currency {CurrencyId}. Errors: {Errors}", 
                    request.Id, string.Join(", ", result.ErrorList));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing DeleteCurrencyCommand for currency ID: {CurrencyId}", request.Id);
            return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the currency");
        }
    }
}
