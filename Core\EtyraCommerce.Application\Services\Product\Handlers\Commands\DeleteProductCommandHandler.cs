using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Product.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Commands
{
    /// <summary>
    /// Handler for deleting a product
    /// </summary>
    public class DeleteProductCommandHandler : IRequestHandler<DeleteProductCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<DeleteProductCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public DeleteProductCommandHandler(
            IProductProcessService productProcessService,
            ILogger<DeleteProductCommandHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the delete product command
        /// </summary>
        /// <param name="request">Delete product command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>No content response</returns>
        public async Task<CustomResponseDto<NoContentDto>> Handle(DeleteProductCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing delete product command for ProductId: {ProductId}, HardDelete: {HardDelete}, ForceDelete: {ForceDelete}",
                    request.ProductId, request.HardDelete, request.ForceDelete);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Product ID is required");

                // Delegate to business logic service
                var result = await _productProcessService.ProcessDeleteProductAsync(
                    request.ProductId,
                    request.HardDelete,
                    request.ForceDelete,
                    request.DeletionReason);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Delete product command processed successfully for ProductId: {ProductId}",
                        request.ProductId);
                }
                else
                {
                    _logger.LogWarning("Delete product command failed for ProductId: {ProductId}, Errors: {Errors}",
                        request.ProductId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing delete product command for ProductId: {ProductId}",
                    request.ProductId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the product");
            }
        }
    }
}
