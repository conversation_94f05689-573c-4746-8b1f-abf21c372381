namespace EtyraCommerce.Domain.Enums.Shipping
{
    /// <summary>
    /// Types of shipping methods
    /// </summary>
    public enum ShippingMethodType
    {
        /// <summary>
        /// Standard delivery (3-7 business days)
        /// </summary>
        Standard = 1,

        /// <summary>
        /// Express delivery (1-2 business days)
        /// </summary>
        Express = 2,

        /// <summary>
        /// Overnight delivery (next business day)
        /// </summary>
        Overnight = 3,

        /// <summary>
        /// Economy delivery (7-14 business days)
        /// </summary>
        Economy = 4
    }
}
