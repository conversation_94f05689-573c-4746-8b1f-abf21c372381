using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services.Payment;
using EtyraCommerce.Application.Services.Payment.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Payment.Handlers;

/// <summary>
/// Handler for getting payment method by ID
/// </summary>
public class GetPaymentMethodByIdQueryHandler : IRequestHandler<GetPaymentMethodByIdQuery, CustomResponseDto<PaymentMethodDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<GetPaymentMethodByIdQueryHandler> _logger;

    public GetPaymentMethodByIdQueryHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<GetPaymentMethodByIdQueryHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get payment method by ID query
    /// </summary>
    /// <param name="request">Get payment method by ID query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> Handle(GetPaymentMethodByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetPaymentMethodByIdQuery for payment method ID: {PaymentMethodId}", request.Id);

            var result = await _paymentMethodProcessService.GetByIdAsync(request.Id, request.Tracking);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment method retrieved successfully with ID: {PaymentMethodId}", request.Id);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve payment method {PaymentMethodId}: {Message}", request.Id, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetPaymentMethodByIdQuery for payment method ID: {PaymentMethodId}", request.Id);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while retrieving the payment method");
        }
    }
}

/// <summary>
/// Handler for getting payment method by code
/// </summary>
public class GetPaymentMethodByCodeQueryHandler : IRequestHandler<GetPaymentMethodByCodeQuery, CustomResponseDto<PaymentMethodDto>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<GetPaymentMethodByCodeQueryHandler> _logger;

    public GetPaymentMethodByCodeQueryHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<GetPaymentMethodByCodeQueryHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get payment method by code query
    /// </summary>
    /// <param name="request">Get payment method by code query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Payment method</returns>
    public async Task<CustomResponseDto<PaymentMethodDto>> Handle(GetPaymentMethodByCodeQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetPaymentMethodByCodeQuery for payment method code: {Code}", request.Code);

            var result = await _paymentMethodProcessService.GetByCodeAsync(request.Code, request.Tracking);

            if (result.IsSuccess && result.Data != null)
            {
                _logger.LogInformation("Payment method retrieved successfully with code: {Code}", request.Code);
                return CustomResponseDto<PaymentMethodDto>.Success(result.Data);
            }
            else if (result.IsSuccess && result.Data == null)
            {
                _logger.LogWarning("Payment method not found with code: {Code}", request.Code);
                return CustomResponseDto<PaymentMethodDto>.NotFound("Payment method not found");
            }
            else
            {
                _logger.LogWarning("Failed to retrieve payment method {Code}: {Message}", request.Code, result.Message);
                return CustomResponseDto<PaymentMethodDto>.BadRequest(result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetPaymentMethodByCodeQuery for payment method code: {Code}", request.Code);
            return CustomResponseDto<PaymentMethodDto>.InternalServerError("An error occurred while retrieving the payment method");
        }
    }
}

/// <summary>
/// Handler for getting all payment methods
/// </summary>
public class GetAllPaymentMethodsQueryHandler : IRequestHandler<GetAllPaymentMethodsQuery, CustomResponseDto<List<PaymentMethodDto>>>
{
    private readonly IPaymentMethodProcessService _paymentMethodProcessService;
    private readonly ILogger<GetAllPaymentMethodsQueryHandler> _logger;

    public GetAllPaymentMethodsQueryHandler(
        IPaymentMethodProcessService paymentMethodProcessService,
        ILogger<GetAllPaymentMethodsQueryHandler> logger)
    {
        _paymentMethodProcessService = paymentMethodProcessService;
        _logger = logger;
    }

    /// <summary>
    /// Handle get all payment methods query
    /// </summary>
    /// <param name="request">Get all payment methods query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of payment methods</returns>
    public async Task<CustomResponseDto<List<PaymentMethodDto>>> Handle(GetAllPaymentMethodsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetAllPaymentMethodsQuery with filters - OnlyActive: {OnlyActive}, Type: {Type}, OrderAmount: {OrderAmount}, OrderCurrency: {OrderCurrency}", 
                request.OnlyActive, request.Type, request.OrderAmount, request.OrderCurrency);

            CustomResponseDto<List<PaymentMethodDto>> result;

            // If order amount and currency are provided, get available payment methods with calculated fees
            if (request.OrderAmount.HasValue && !string.IsNullOrWhiteSpace(request.OrderCurrency))
            {
                result = await _paymentMethodProcessService.GetAvailableForAmountAsync(request.OrderAmount.Value, request.OrderCurrency, request.Tracking);
            }
            // If type filter is provided
            else if (request.Type.HasValue)
            {
                result = await _paymentMethodProcessService.GetByTypeAsync(request.Type.Value, request.OnlyActive ?? true, request.Tracking);
            }
            // If only active filter is provided
            else if (request.OnlyActive == true)
            {
                result = await _paymentMethodProcessService.GetActivePaymentMethodsAsync(request.Tracking);
            }
            // Get all payment methods
            else
            {
                result = await _paymentMethodProcessService.GetAllAsync(request.Tracking);
            }

            if (result.IsSuccess)
            {
                _logger.LogInformation("Retrieved {Count} payment methods successfully", result.Data?.Count ?? 0);
            }
            else
            {
                _logger.LogWarning("Failed to retrieve payment methods: {Message}", result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetAllPaymentMethodsQuery");
            return CustomResponseDto<List<PaymentMethodDto>>.InternalServerError("An error occurred while retrieving payment methods");
        }
    }
}
