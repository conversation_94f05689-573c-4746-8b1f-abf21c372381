using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Cart.Queries
{
    /// <summary>
    /// Query to get shopping cart with all items
    /// </summary>
    public class GetCartQuery : IRequest<CustomResponseDto<CartDto>>
    {
        /// <summary>
        /// Customer ID (null for guest carts)
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// Session ID for guest carts
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Whether to include inactive carts
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Whether to include expired carts
        /// </summary>
        public bool IncludeExpired { get; set; } = false;

        /// <summary>
        /// Creates query for user cart
        /// </summary>
        public static GetCartQuery ForUser(Guid customerId, bool includeInactive = false, bool includeExpired = false)
        {
            return new GetCartQuery
            {
                CustomerId = customerId,
                IncludeInactive = includeInactive,
                IncludeExpired = includeExpired
            };
        }

        /// <summary>
        /// Creates query for guest cart
        /// </summary>
        public static GetCartQuery ForGuest(string sessionId, bool includeInactive = false, bool includeExpired = false)
        {
            return new GetCartQuery
            {
                SessionId = sessionId,
                IncludeInactive = includeInactive,
                IncludeExpired = includeExpired
            };
        }
    }
}
