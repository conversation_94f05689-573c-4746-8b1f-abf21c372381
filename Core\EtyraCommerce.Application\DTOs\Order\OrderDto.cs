using EtyraCommerce.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Order data transfer object
    /// </summary>
    public class OrderDto
    {
        /// <summary>
        /// Order ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Unique order number
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// Customer ID
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Customer email
        /// </summary>
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number
        /// </summary>
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Customer first name
        /// </summary>
        public string CustomerFirstName { get; set; } = string.Empty;

        /// <summary>
        /// Customer last name
        /// </summary>
        public string CustomerLastName { get; set; } = string.Empty;

        /// <summary>
        /// Customer full name (computed)
        /// </summary>
        public string CustomerFullName => $"{CustomerFirstName} {CustomerLastName}".Trim();

        /// <summary>
        /// Order status
        /// </summary>
        public OrderStatus Status { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        public PaymentStatus PaymentStatus { get; set; }

        /// <summary>
        /// Shipping status
        /// </summary>
        public ShippingStatus ShippingStatus { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        public AddressDto BillingAddress { get; set; } = null!;

        /// <summary>
        /// Shipping address
        /// </summary>
        public AddressDto ShippingAddress { get; set; } = null!;

        /// <summary>
        /// Subtotal amount
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// Tax amount
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Shipping cost
        /// </summary>
        public decimal ShippingCost { get; set; }

        /// <summary>
        /// Discount amount
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Total amount
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Customer notes
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Internal notes
        /// </summary>
        public string? InternalNotes { get; set; }

        /// <summary>
        /// Shipping method
        /// </summary>
        public string? ShippingMethod { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Tracking number
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// Expected delivery date
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// Actual delivery date
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }

        /// <summary>
        /// Order items
        /// </summary>
        public List<OrderItemDto> OrderItems { get; set; } = new();

        /// <summary>
        /// Order creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Order last update date
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Created by user ID
        /// </summary>
        public Guid? CreatedBy { get; set; }

        /// <summary>
        /// Updated by user ID
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        /// <summary>
        /// Number of items in the order
        /// </summary>
        public int ItemCount => OrderItems?.Count ?? 0;

        /// <summary>
        /// Total quantity of all items
        /// </summary>
        public int TotalQuantity => OrderItems?.Sum(x => x.Quantity) ?? 0;

        /// <summary>
        /// Indicates if order can be modified
        /// </summary>
        public bool CanBeModified => Status == OrderStatus.Draft;

        /// <summary>
        /// Indicates if order can be cancelled
        /// </summary>
        public bool CanBeCancelled => Status != OrderStatus.Delivered &&
                                     Status != OrderStatus.Shipped &&
                                     Status != OrderStatus.Cancelled &&
                                     Status != OrderStatus.Refunded;

        /// <summary>
        /// Order status display text
        /// </summary>
        public string StatusText => Status.ToString();

        /// <summary>
        /// Payment status display text
        /// </summary>
        public string PaymentStatusText => PaymentStatus.ToString();

        /// <summary>
        /// Shipping status display text
        /// </summary>
        public string ShippingStatusText => ShippingStatus.ToString();
    }

    /// <summary>
    /// Address data transfer object
    /// </summary>
    public class AddressDto
    {
        /// <summary>
        /// Street address
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Street { get; set; } = string.Empty;

        /// <summary>
        /// Address line 2 (optional)
        /// </summary>
        [MaxLength(200)]
        public string? AddressLine2 { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State/Province
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal/ZIP code
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Country
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// Full address as single line
        /// </summary>
        public string FullAddress
        {
            get
            {
                var parts = new List<string> { Street };

                if (!string.IsNullOrEmpty(AddressLine2))
                    parts.Add(AddressLine2);

                parts.Add($"{City}, {State} {PostalCode}, {Country}");

                return string.Join(", ", parts);
            }
        }
    }
}
