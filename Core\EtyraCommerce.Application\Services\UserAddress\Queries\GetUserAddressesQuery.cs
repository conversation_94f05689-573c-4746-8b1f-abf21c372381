using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.Services.UserAddress.Queries
{
    /// <summary>
    /// Query for getting all addresses for a specific user
    /// </summary>
    public class GetUserAddressesQuery : IRequest<CustomResponseDto<List<UserAddressDto>>>
    {
        /// <summary>
        /// ID of the user whose addresses to retrieve
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Filter by address type (optional)
        /// 1=Billing, 2=Shipping, 3=Both
        /// </summary>
        [Range(1, 3, ErrorMessage = "Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)")]
        public int? AddressType { get; set; }

        /// <summary>
        /// Filter by active status (optional)
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Include inactive addresses (default: false)
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetUserAddressesQuery() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public GetUserAddressesQuery(Guid userId, int? addressType = null, bool? isActive = null, bool includeInactive = false)
        {
            UserId = userId;
            AddressType = addressType;
            IsActive = isActive;
            IncludeInactive = includeInactive;
        }
    }

    /// <summary>
    /// Query for getting a specific user address by ID
    /// </summary>
    public class GetUserAddressByIdQuery : IRequest<CustomResponseDto<UserAddressDto>>
    {
        /// <summary>
        /// ID of the address to retrieve
        /// </summary>
        [Required(ErrorMessage = "Address ID is required")]
        public Guid AddressId { get; set; }

        /// <summary>
        /// ID of the user who owns this address (for security validation)
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetUserAddressByIdQuery() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public GetUserAddressByIdQuery(Guid addressId, Guid userId)
        {
            AddressId = addressId;
            UserId = userId;
        }
    }

    /// <summary>
    /// Query for getting the default address for a user
    /// </summary>
    public class GetDefaultAddressQuery : IRequest<CustomResponseDto<UserAddressDto>>
    {
        /// <summary>
        /// ID of the user whose default address to retrieve
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Filter by address type for default address (optional)
        /// 1=Billing, 2=Shipping, 3=Both
        /// </summary>
        [Range(1, 3, ErrorMessage = "Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)")]
        public int? AddressType { get; set; }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetDefaultAddressQuery() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public GetDefaultAddressQuery(Guid userId, int? addressType = null)
        {
            UserId = userId;
            AddressType = addressType;
        }
    }

    /// <summary>
    /// Query for getting addresses suitable for billing
    /// </summary>
    public class GetBillingAddressesQuery : IRequest<CustomResponseDto<List<UserAddressDto>>>
    {
        /// <summary>
        /// ID of the user whose billing addresses to retrieve
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Include inactive addresses (default: false)
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetBillingAddressesQuery() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public GetBillingAddressesQuery(Guid userId, bool includeInactive = false)
        {
            UserId = userId;
            IncludeInactive = includeInactive;
        }
    }

    /// <summary>
    /// Query for getting addresses suitable for shipping
    /// </summary>
    public class GetShippingAddressesQuery : IRequest<CustomResponseDto<List<UserAddressDto>>>
    {
        /// <summary>
        /// ID of the user whose shipping addresses to retrieve
        /// </summary>
        [Required(ErrorMessage = "User ID is required")]
        public Guid UserId { get; set; }

        /// <summary>
        /// Include inactive addresses (default: false)
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetShippingAddressesQuery() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public GetShippingAddressesQuery(Guid userId, bool includeInactive = false)
        {
            UserId = userId;
            IncludeInactive = includeInactive;
        }
    }
}
