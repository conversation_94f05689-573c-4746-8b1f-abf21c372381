using System.ComponentModel.DataAnnotations;
using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Domain.Enums.Shipping;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Shipping
{
    /// <summary>
    /// Shipping rate entity for European shipping system
    /// Represents shipping costs for different zones, methods, and weight ranges
    /// </summary>
    public class ShippingRate : BaseEntity
    {
        #region Foreign Keys

        /// <summary>
        /// Reference to the shipping zone
        /// </summary>
        public Guid ShippingZoneId { get; set; }

        /// <summary>
        /// Reference to the shipping method
        /// </summary>
        public Guid ShippingMethodId { get; set; }

        #endregion

        #region Properties

        /// <summary>
        /// Rate name for identification
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Rate calculation type
        /// </summary>
        public RateCalculationType CalculationType { get; set; } = RateCalculationType.WeightBased;

        /// <summary>
        /// Minimum weight for this rate (in kg)
        /// </summary>
        public decimal MinWeight { get; set; } = 0.0m;

        /// <summary>
        /// Maximum weight for this rate (in kg)
        /// </summary>
        public decimal MaxWeight { get; set; } = 1.0m;

        /// <summary>
        /// Base shipping cost (Value Object)
        /// </summary>
        public Money BaseCost { get; set; } = null!;

        /// <summary>
        /// Additional cost per kg above minimum weight
        /// </summary>
        public Money? AdditionalCostPerKg { get; set; }

        /// <summary>
        /// Free shipping threshold - orders above this amount get free shipping
        /// </summary>
        public Money? FreeShippingThreshold { get; set; }

        /// <summary>
        /// Minimum order amount required for this shipping rate
        /// </summary>
        public Money? MinOrderAmount { get; set; }

        /// <summary>
        /// Maximum order amount for this shipping rate
        /// </summary>
        public Money? MaxOrderAmount { get; set; }

        /// <summary>
        /// Indicates if this rate is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for rate selection
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Rate effective from date
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// Rate effective until date
        /// </summary>
        public DateTime? EffectiveUntil { get; set; }

        /// <summary>
        /// Additional notes for this rate
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Shipping zone
        /// </summary>
        public virtual ShippingZone ShippingZone { get; set; } = null!;

        /// <summary>
        /// Shipping method
        /// </summary>
        public virtual ShippingMethod ShippingMethod { get; set; } = null!;

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected ShippingRate() { }

        /// <summary>
        /// Constructor for creating a new shipping rate
        /// </summary>
        public ShippingRate(Guid shippingZoneId, Guid shippingMethodId, string name, 
            decimal minWeight, decimal maxWeight, Money baseCost, 
            RateCalculationType calculationType = RateCalculationType.WeightBased)
        {
            ShippingZoneId = shippingZoneId;
            ShippingMethodId = shippingMethodId;
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            MinWeight = minWeight >= 0 ? minWeight : throw new ArgumentException("Minimum weight cannot be negative");
            MaxWeight = maxWeight > minWeight ? maxWeight : throw new ArgumentException("Maximum weight must be greater than minimum weight");
            BaseCost = baseCost ?? throw new ArgumentNullException(nameof(baseCost));
            CalculationType = calculationType;
            IsActive = true;
            DisplayOrder = 0;
            EffectiveFrom = DateTime.UtcNow;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Activates the shipping rate
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the shipping rate
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates rate information
        /// </summary>
        public void UpdateInfo(string name, Money baseCost, string? notes = null)
        {
            Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
            BaseCost = baseCost ?? throw new ArgumentNullException(nameof(baseCost));
            Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim();
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets weight range for this rate
        /// </summary>
        public void SetWeightRange(decimal minWeight, decimal maxWeight)
        {
            if (minWeight < 0)
                throw new ArgumentException("Minimum weight cannot be negative");
            
            if (maxWeight <= minWeight)
                throw new ArgumentException("Maximum weight must be greater than minimum weight");

            MinWeight = minWeight;
            MaxWeight = maxWeight;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets additional cost per kg
        /// </summary>
        public void SetAdditionalCostPerKg(Money? additionalCost)
        {
            AdditionalCostPerKg = additionalCost;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets free shipping threshold
        /// </summary>
        public void SetFreeShippingThreshold(Money? threshold)
        {
            FreeShippingThreshold = threshold;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets order amount limits
        /// </summary>
        public void SetOrderAmountLimits(Money? minAmount, Money? maxAmount)
        {
            if (minAmount != null && maxAmount != null && minAmount.Amount > maxAmount.Amount)
                throw new ArgumentException("Minimum order amount cannot be greater than maximum");

            MinOrderAmount = minAmount;
            MaxOrderAmount = maxAmount;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets effective date range
        /// </summary>
        public void SetEffectivePeriod(DateTime? from, DateTime? until)
        {
            if (from.HasValue && until.HasValue && from > until)
                throw new ArgumentException("Effective from date cannot be after effective until date");

            EffectiveFrom = from;
            EffectiveUntil = until;
            MarkAsUpdated();
        }

        /// <summary>
        /// Sets display order for rate lists
        /// </summary>
        public void SetDisplayOrder(int order)
        {
            DisplayOrder = order;
            MarkAsUpdated();
        }

        #endregion

        #region Calculation Methods

        /// <summary>
        /// Calculates shipping cost for given weight and order amount
        /// </summary>
        public Money CalculateShippingCost(decimal weight, Money orderAmount)
        {
            if (!CanApplyToOrder(weight, orderAmount))
                throw new InvalidOperationException("This rate cannot be applied to the given order");

            // Check for free shipping
            if (FreeShippingThreshold != null && orderAmount.Amount >= FreeShippingThreshold.Amount)
                return new Money(0, BaseCost.Currency);

            var cost = BaseCost;

            // Add additional cost based on calculation type
            if (CalculationType == RateCalculationType.WeightBased && weight > MinWeight && AdditionalCostPerKg != null)
            {
                var additionalWeight = weight - MinWeight;
                var additionalCost = new Money(AdditionalCostPerKg.Amount * additionalWeight, BaseCost.Currency);
                cost = new Money(cost.Amount + additionalCost.Amount, cost.Currency);
            }

            return cost;
        }

        /// <summary>
        /// Checks if this rate can be applied to the given order
        /// </summary>
        public bool CanApplyToOrder(decimal weight, Money orderAmount)
        {
            if (!IsEffective())
                return false;

            if (weight < MinWeight || weight > MaxWeight)
                return false;

            if (MinOrderAmount != null && orderAmount.Amount < MinOrderAmount.Amount)
                return false;

            if (MaxOrderAmount != null && orderAmount.Amount > MaxOrderAmount.Amount)
                return false;

            return true;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets rate display name with weight range
        /// </summary>
        public string GetDisplayName()
        {
            var weightRange = MinWeight == MaxWeight ? 
                $"{MinWeight}kg" : 
                $"{MinWeight}-{MaxWeight}kg";
            
            return $"{Name} ({weightRange})";
        }

        /// <summary>
        /// Gets weight range display text
        /// </summary>
        public string GetWeightRangeText()
        {
            if (MinWeight == 0 && MaxWeight == decimal.MaxValue)
                return "Any weight";
            
            if (MinWeight == MaxWeight)
                return $"{MinWeight} kg";
            
            if (MaxWeight == decimal.MaxValue)
                return $"{MinWeight}+ kg";
            
            return $"{MinWeight} - {MaxWeight} kg";
        }

        /// <summary>
        /// Checks if rate is currently effective
        /// </summary>
        public bool IsEffective(DateTime? checkDate = null)
        {
            if (!IsActive || IsDeleted)
                return false;

            var date = checkDate ?? DateTime.UtcNow;

            if (EffectiveFrom.HasValue && date < EffectiveFrom.Value)
                return false;

            if (EffectiveUntil.HasValue && date > EffectiveUntil.Value)
                return false;

            return true;
        }

        /// <summary>
        /// Checks if rate can be used for shipping
        /// </summary>
        public bool CanShip() => IsActive && !IsDeleted && IsEffective();

        /// <summary>
        /// Gets calculation type display text
        /// </summary>
        public string GetCalculationTypeText()
        {
            return CalculationType switch
            {
                RateCalculationType.Flat => "Flat Rate",
                RateCalculationType.WeightBased => "Weight Based",
                RateCalculationType.OrderAmountBased => "Order Amount Based",
                _ => CalculationType.ToString()
            };
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates shipping rate data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   ShippingZoneId != Guid.Empty &&
                   ShippingMethodId != Guid.Empty &&
                   MinWeight >= 0 &&
                   MaxWeight > MinWeight &&
                   BaseCost != null &&
                   BaseCost.Amount >= 0 &&
                   (!EffectiveFrom.HasValue || !EffectiveUntil.HasValue || EffectiveFrom <= EffectiveUntil);
        }

        #endregion
    }

}
