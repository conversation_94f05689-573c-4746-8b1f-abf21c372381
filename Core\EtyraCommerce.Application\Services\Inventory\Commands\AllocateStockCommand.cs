using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to allocate reserved stock for confirmed order
    /// </summary>
    public class AllocateStockCommand : IRequest<CustomResponseDto<bool>>
    {
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public int Quantity { get; set; }
        public string Reference { get; set; } = string.Empty;
        public string? Reason { get; set; }
        public Guid? UserId { get; set; }
    }
}
