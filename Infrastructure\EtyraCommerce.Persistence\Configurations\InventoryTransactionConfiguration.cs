using EtyraCommerce.Domain.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for InventoryTransaction entity
    /// </summary>
    public class InventoryTransactionConfiguration : IEntityTypeConfiguration<InventoryTransaction>
    {
        public void Configure(EntityTypeBuilder<InventoryTransaction> builder)
        {
            // Table configuration
            builder.ToTable("InventoryTransactions", "etyra_inventory");

            // Primary key
            builder.HasKey(t => t.Id);

            // Properties
            builder.Property(t => t.InventoryId)
                .IsRequired();

            builder.Property(t => t.Type)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(t => t.Quantity)
                .IsRequired();

            builder.Property(t => t.QuantityBefore)
                .IsRequired();

            builder.Property(t => t.QuantityAfter)
                .IsRequired();

            builder.Property(t => t.Reference)
                .HasMaxLength(100);

            builder.Property(t => t.ReferenceType)
                .HasMaxLength(50);

            builder.Property(t => t.Reason)
                .HasMaxLength(500);

            builder.Property(t => t.Notes)
                .HasMaxLength(1000);

            builder.Property(t => t.UserId);

            builder.Property(t => t.TransactionDate)
                .IsRequired()
                .HasDefaultValueSql("NOW()");

            builder.Property(t => t.UnitCost)
                .HasColumnType("decimal(18,4)");

            builder.Property(t => t.TotalCost)
                .HasColumnType("decimal(18,4)");

            builder.Property(t => t.ExternalReference)
                .HasMaxLength(100);

            builder.Property(t => t.BatchNumber)
                .HasMaxLength(50);

            builder.Property(t => t.ExpiryDate);

            // Indexes
            builder.HasIndex(t => t.InventoryId)
                .HasDatabaseName("IX_InventoryTransactions_InventoryId");

            builder.HasIndex(t => t.Type)
                .HasDatabaseName("IX_InventoryTransactions_Type");

            builder.HasIndex(t => t.Reference)
                .HasDatabaseName("IX_InventoryTransactions_Reference");

            builder.HasIndex(t => t.ReferenceType)
                .HasDatabaseName("IX_InventoryTransactions_ReferenceType");

            builder.HasIndex(t => t.UserId)
                .HasDatabaseName("IX_InventoryTransactions_UserId");

            builder.HasIndex(t => t.TransactionDate)
                .HasDatabaseName("IX_InventoryTransactions_TransactionDate");

            builder.HasIndex(t => new { t.Reference, t.Type })
                .HasDatabaseName("IX_InventoryTransactions_Reference_Type");

            // Relationships
            builder.HasOne(t => t.Inventory)
                .WithMany(i => i.Transactions)
                .HasForeignKey(t => t.InventoryId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(t => t.User)
                .WithMany()
                .HasForeignKey(t => t.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            // Audit fields (inherited from AuditableBaseEntity)
            builder.Property(t => t.CreatedAt)
                .IsRequired();

            builder.Property(t => t.UpdatedAt);

            builder.Property(t => t.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(t => t.DeletedAt);

            builder.Property(t => t.RowVersion)
                .IsRowVersion();

            // Global query filter for soft delete
            builder.HasQueryFilter(t => !t.IsDeleted);
        }
    }
}
