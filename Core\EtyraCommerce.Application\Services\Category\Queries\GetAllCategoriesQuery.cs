using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Queries
{
    /// <summary>
    /// Query for getting all categories with filtering and pagination
    /// </summary>
    public class GetAllCategoriesQuery : IRequest<CustomResponseDto<PagedResult<CategoryDto>>>
    {
        /// <summary>
        /// Search term (searches in Name, Description)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by show in menu status
        /// </summary>
        public bool? ShowInMenu { get; set; }

        /// <summary>
        /// Filter by parent category ID
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Filter by level (0 = root categories only)
        /// </summary>
        public int? Level { get; set; }

        /// <summary>
        /// Include child categories in results
        /// </summary>
        public bool IncludeChildren { get; set; } = false;

        /// <summary>
        /// Include descriptions in all languages
        /// </summary>
        public bool IncludeDescriptions { get; set; } = true;

        /// <summary>
        /// Specific language code to filter descriptions
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Sort field
        /// </summary>
        public CategorySortField SortBy { get; set; } = CategorySortField.SortOrder;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// Creates query from CategorySearchDto
        /// </summary>
        public static GetAllCategoriesQuery FromDto(CategorySearchDto dto)
        {
            return new GetAllCategoriesQuery
            {
                SearchTerm = dto.SearchTerm,
                IsActive = dto.IsActive,
                ShowInMenu = dto.ShowInMenu,
                ParentCategoryId = dto.ParentCategoryId,
                Level = dto.Level,
                IncludeChildren = dto.IncludeChildren,
                SortBy = dto.SortBy,
                SortDirection = dto.SortDirection,
                PageNumber = dto.PageNumber,
                PageSize = dto.PageSize
            };
        }
    }
}
