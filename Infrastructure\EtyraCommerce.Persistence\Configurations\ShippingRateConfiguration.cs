using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Domain.ValueObjects;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingRate entity
    /// </summary>
    public class ShippingRateConfiguration : IEntityTypeConfiguration<ShippingRate>
    {
        public void Configure(EntityTypeBuilder<ShippingRate> builder)
        {
            // Table configuration
            builder.ToTable("ShippingRates", "etyra_shipping");

            // Primary key
            builder.HasKey(sr => sr.Id);

            // Properties
            builder.Property(sr => sr.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sr => sr.MinWeight)
                .HasPrecision(10, 3);

            builder.Property(sr => sr.Max<PERSON>eight)
                .HasPrecision(10, 3);

            builder.Property(sr => sr.EffectiveFrom);

            builder.Property(sr => sr.EffectiveUntil);

            builder.Property(sr => sr.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(sr => sr.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(sr => sr.Notes)
                .HasMaxLength(500);

            // Money value objects configuration
            builder.OwnsOne(sr => sr.BaseCost, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("BaseCostAmount")
                    .HasPrecision(18, 2)
                    .IsRequired();

                money.Property(m => m.Currency)
                    .HasColumnName("BaseCostCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3)
                    .IsRequired();
            });

            builder.OwnsOne(sr => sr.AdditionalCostPerKg, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("AdditionalCostPerKgAmount")
                    .HasPrecision(18, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("AdditionalCostPerKgCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            builder.OwnsOne(sr => sr.FreeShippingThreshold, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("FreeShippingThresholdAmount")
                    .HasPrecision(18, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("FreeShippingThresholdCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            builder.OwnsOne(sr => sr.MinOrderAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("MinOrderAmountAmount")
                    .HasPrecision(18, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("MinOrderAmountCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            builder.OwnsOne(sr => sr.MaxOrderAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("MaxOrderAmountAmount")
                    .HasPrecision(18, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("MaxOrderAmountCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });



            // Enum conversions
            builder.Property(sr => sr.CalculationType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Foreign key relationships
            builder.HasOne(sr => sr.ShippingMethod)
                .WithMany(sm => sm.ShippingRates)
                .HasForeignKey(sr => sr.ShippingMethodId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(sr => sr.ShippingMethodId)
                .HasDatabaseName("IX_ShippingRates_ShippingMethodId");

            builder.HasIndex(sr => sr.IsActive)
                .HasDatabaseName("IX_ShippingRates_IsActive");

            builder.HasIndex(sr => new { sr.EffectiveFrom, sr.EffectiveUntil })
                .HasDatabaseName("IX_ShippingRates_EffectivePeriod");

            builder.HasIndex(sr => sr.DisplayOrder)
                .HasDatabaseName("IX_ShippingRates_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_WeightRange",
                "\"MinWeight\" <= \"MaxWeight\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_EffectivePeriod",
                "\"EffectiveUntil\" IS NULL OR \"EffectiveFrom\" <= \"EffectiveUntil\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_BaseCostAmount",
                "\"BaseCostAmount\" >= 0"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_AdditionalCostPerKgAmount",
                "\"AdditionalCostPerKgAmount\" IS NULL OR \"AdditionalCostPerKgAmount\" >= 0"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_DisplayOrder",
                "\"DisplayOrder\" >= 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Shipping rate configurations for different shipping methods and conditions"));
        }
    }
}
