using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EtyraCommerce.Domain.Entities.Shipping;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ShippingRate entity
    /// </summary>
    public class ShippingRateConfiguration : IEntityTypeConfiguration<ShippingRate>
    {
        public void Configure(EntityTypeBuilder<ShippingRate> builder)
        {
            // Table configuration
            builder.ToTable("ShippingRates", "etyra_shipping");

            // Primary key
            builder.HasKey(sr => sr.Id);

            // Properties
            builder.Property(sr => sr.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(sr => sr.Description)
                .HasMaxLength(1000);

            builder.Property(sr => sr.MinWeight)
                .HasPrecision(10, 3);

            builder.Property(sr => sr.<PERSON>ei<PERSON>)
                .HasPrecision(10, 3);

            builder.Property(sr => sr.MinOrderAmount)
                .HasPrecision(18, 2);

            builder.Property(sr => sr.MaxOrderAmount)
                .HasPrecision(18, 2);

            builder.Property(sr => sr.FreeShippingThreshold)
                .HasPrecision(18, 2);

            builder.Property(sr => sr.EffectiveFrom)
                .IsRequired();

            builder.Property(sr => sr.EffectiveTo);

            builder.Property(sr => sr.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(sr => sr.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0);

            // Money value objects configuration
            builder.OwnsOne(sr => sr.BaseCost, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("BaseCostAmount")
                    .HasPrecision(18, 2)
                    .IsRequired();

                money.Property(m => m.Currency)
                    .HasColumnName("BaseCostCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => new Currency(code, code, code, 2))
                    .HasMaxLength(3)
                    .IsRequired();
            });

            builder.OwnsOne(sr => sr.AdditionalCostPerKg, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("AdditionalCostPerKgAmount")
                    .HasPrecision(18, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("AdditionalCostPerKgCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => new Currency(code, code, code, 2))
                    .HasMaxLength(3);
            });

            builder.OwnsOne(sr => sr.AdditionalCostPercentage, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("AdditionalCostPercentageAmount")
                    .HasPrecision(5, 2);

                money.Property(m => m.Currency)
                    .HasColumnName("AdditionalCostPercentageCurrency")
                    .HasConversion(
                        c => c.Code,
                        code => new Currency(code, code, code, 2))
                    .HasMaxLength(3);
            });

            // Enum conversions
            builder.Property(sr => sr.CalculationType)
                .HasConversion<string>()
                .HasMaxLength(50)
                .IsRequired();

            // Foreign key relationships
            builder.HasOne(sr => sr.ShippingMethod)
                .WithMany(sm => sm.ShippingRates)
                .HasForeignKey(sr => sr.ShippingMethodId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(sr => sr.ShippingMethodId)
                .HasDatabaseName("IX_ShippingRates_ShippingMethodId");

            builder.HasIndex(sr => sr.IsActive)
                .HasDatabaseName("IX_ShippingRates_IsActive");

            builder.HasIndex(sr => new { sr.EffectiveFrom, sr.EffectiveTo })
                .HasDatabaseName("IX_ShippingRates_EffectivePeriod");

            builder.HasIndex(sr => sr.DisplayOrder)
                .HasDatabaseName("IX_ShippingRates_DisplayOrder");

            // Check constraints
            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_WeightRange",
                "\"MinWeight\" IS NULL OR \"MaxWeight\" IS NULL OR \"MinWeight\" <= \"MaxWeight\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_OrderAmountRange",
                "\"MinOrderAmount\" IS NULL OR \"MaxOrderAmount\" IS NULL OR \"MinOrderAmount\" <= \"MaxOrderAmount\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_EffectivePeriod",
                "\"EffectiveTo\" IS NULL OR \"EffectiveFrom\" <= \"EffectiveTo\""));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_BaseCostAmount",
                "\"BaseCostAmount\" >= 0"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_AdditionalCostPerKgAmount",
                "\"AdditionalCostPerKgAmount\" IS NULL OR \"AdditionalCostPerKgAmount\" >= 0"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_AdditionalCostPercentageAmount",
                "\"AdditionalCostPercentageAmount\" IS NULL OR (\"AdditionalCostPercentageAmount\" >= 0 AND \"AdditionalCostPercentageAmount\" <= 100)"));

            builder.ToTable(t => t.HasCheckConstraint(
                "CK_ShippingRates_FreeShippingThreshold",
                "\"FreeShippingThreshold\" IS NULL OR \"FreeShippingThreshold\" > 0"));

            // Table comment
            builder.ToTable(t => t.HasComment("Shipping rate configurations for different shipping methods and conditions"));
        }
    }
}
