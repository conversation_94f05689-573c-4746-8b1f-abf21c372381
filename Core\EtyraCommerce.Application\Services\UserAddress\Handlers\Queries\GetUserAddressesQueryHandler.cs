using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Queries
{
    /// <summary>
    /// Handler for getting all addresses for a specific user
    /// </summary>
    public class GetUserAddressesQueryHandler : IRequestHandler<GetUserAddressesQuery, CustomResponseDto<List<UserAddressDto>>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<GetUserAddressesQueryHandler> _logger;

        public GetUserAddressesQueryHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<GetUserAddressesQueryHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> Handle(GetUserAddressesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling GetUserAddressesQuery for UserId: {UserId}, AddressType: {AddressType}, IsActive: {IsActive}",
                    request.UserId, request.AddressType, request.IsActive);

                // Validate request
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetUserAddressesQuery received with empty UserId");
                    return CustomResponseDto<List<UserAddressDto>>.BadRequest("User ID is required");
                }

                if (request.AddressType.HasValue && (request.AddressType < 1 || request.AddressType > 3))
                {
                    _logger.LogWarning("GetUserAddressesQuery received with invalid AddressType: {AddressType} for UserId: {UserId}",
                        request.AddressType, request.UserId);
                    return CustomResponseDto<List<UserAddressDto>>.BadRequest("Address type must be 1 (Billing), 2 (Shipping), or 3 (Both)");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessGetUserAddressesAsync(
                    request.UserId,
                    request.AddressType,
                    request.IsActive,
                    request.IncludeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("GetUserAddressesQuery handled successfully for UserId: {UserId}, Count: {Count}",
                        request.UserId, result.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("GetUserAddressesQuery failed for UserId: {UserId}, Error: {Error}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling GetUserAddressesQuery for UserId: {UserId}", request.UserId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving addresses");
            }
        }
    }
}
