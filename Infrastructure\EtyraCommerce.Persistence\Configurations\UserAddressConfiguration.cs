using EtyraCommerce.Domain.Entities.User;
using EtyraCommerce.Persistence.Configurations.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for UserAddress entity
    /// </summary>
    public class UserAddressConfiguration : BaseEntityConfiguration<UserAddress>
    {
        public override void Configure(EntityTypeBuilder<UserAddress> builder)
        {
            // Apply base configuration (BaseEntity)
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("user_addresses", "etyra_core");

            #region Basic Properties

            // UserId (Foreign Key)
            builder.Property(x => x.UserId)
                .HasColumnName("user_id")
                .IsRequired();

            // Address Type
            builder.Property(x => x.Type)
                .HasColumnName("address_type")
                .HasConversion<int>()
                .IsRequired();

            // IsDefault
            builder.Property(x => x.IsDefault)
                .HasColumnName("is_default")
                .HasDefaultValue(false)
                .IsRequired();

            // FirstName
            builder.Property(x => x.FirstName)
                .HasColumnName("first_name")
                .HasMaxLength(100)
                .IsRequired();

            // LastName
            builder.Property(x => x.LastName)
                .HasColumnName("last_name")
                .HasMaxLength(100)
                .IsRequired();

            // Label
            builder.Property(x => x.Label)
                .HasColumnName("label")
                .HasMaxLength(50)
                .IsRequired(false);

            // DeliveryInstructions
            builder.Property(x => x.DeliveryInstructions)
                .HasColumnName("delivery_instructions")
                .HasMaxLength(500)
                .IsRequired(false);

            // IsActive
            builder.Property(x => x.IsActive)
                .HasColumnName("is_active")
                .HasDefaultValue(true)
                .IsRequired();

            #endregion

            #region Value Objects

            // Address Value Object (Required) - Separate Columns
            ValueObjectConversions.ConfigureAddressSeparateRequired<UserAddress>(
                builder,
                x => x.Address,
                "address");

            // PhoneNumber Value Object (Optional)
            ValueObjectConversions.ConfigureNullablePhoneNumber<UserAddress>(
                builder.Property(x => x.PhoneNumber),
                "phone_number");

            #endregion

            #region Relationships

            // User relationship (Many-to-One)
            builder.HasOne(x => x.User)
                .WithMany(u => u.Addresses)
                .HasForeignKey(x => x.UserId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("fk_user_addresses_user_id");

            #endregion

            #region Indexes

            // Index on UserId for efficient user address queries
            builder.HasIndex(x => x.UserId)
                .HasDatabaseName("ix_user_addresses_user_id");

            // Index on UserId + IsDefault for finding default addresses
            builder.HasIndex(x => new { x.UserId, x.IsDefault })
                .HasDatabaseName("ix_user_addresses_user_id_is_default");

            // Index on UserId + Type for finding addresses by type
            builder.HasIndex(x => new { x.UserId, x.Type })
                .HasDatabaseName("ix_user_addresses_user_id_type");

            // Index on UserId + IsActive for finding active addresses
            builder.HasIndex(x => new { x.UserId, x.IsActive })
                .HasDatabaseName("ix_user_addresses_user_id_is_active");

            // Composite index for common queries (UserId + IsActive + Type)
            builder.HasIndex(x => new { x.UserId, x.IsActive, x.Type })
                .HasDatabaseName("ix_user_addresses_user_id_active_type");

            #endregion

            #region Check Constraints

            // Ensure AddressType is valid
            builder.HasCheckConstraint("ck_user_addresses_address_type",
                "address_type IN (1, 2, 3)");

            // Ensure FirstName is not empty
            builder.HasCheckConstraint("ck_user_addresses_first_name_not_empty",
                "LENGTH(TRIM(first_name)) > 0");

            // Ensure LastName is not empty
            builder.HasCheckConstraint("ck_user_addresses_last_name_not_empty",
                "LENGTH(TRIM(last_name)) > 0");

            // Ensure Label is not empty if provided
            builder.HasCheckConstraint("ck_user_addresses_label_not_empty",
                "label IS NULL OR LENGTH(TRIM(label)) > 0");

            #endregion

            #region Comments (PostgreSQL)

            builder.HasComment("User addresses for billing and shipping");

            // Column comments
            builder.Property(x => x.UserId)
                .HasComment("Reference to the user who owns this address");

            builder.Property(x => x.Type)
                .HasComment("Address type: 1=Billing, 2=Shipping, 3=Both");

            builder.Property(x => x.IsDefault)
                .HasComment("Indicates if this is the default address for the user");

            builder.Property(x => x.FirstName)
                .HasComment("First name of the person at this address");

            builder.Property(x => x.LastName)
                .HasComment("Last name of the person at this address");

            builder.Property(x => x.Label)
                .HasComment("User-friendly label for this address (e.g., Home, Office)");

            builder.Property(x => x.DeliveryInstructions)
                .HasComment("Additional delivery instructions for this address");

            builder.Property(x => x.IsActive)
                .HasComment("Indicates if this address is active and can be used");

            #endregion
        }

        protected override string GetTableName() => "user_addresses";
    }
}
