using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for getting a user by email address
    /// </summary>
    public class GetUserByEmailQuery : IRequest<CustomResponseDto<UserDto?>>
    {
        /// <summary>
        /// Email address to search for
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Whether to include deleted users in the search
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Whether to track the entity for changes
        /// </summary>
        public bool Tracking { get; set; } = false;

        public GetUserByEmailQuery() { }

        public GetUserByEmailQuery(string email, bool includeDeleted = false, bool tracking = false)
        {
            Email = email;
            IncludeDeleted = includeDeleted;
            Tracking = tracking;
        }
    }
}
