using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Accounting;

public class Company : BaseEntity
{
    [MaxLength(100)]
    public string Name { get; set; }
    
    [MaxLength(50)]
    public string RegistrationNumber { get; set; }
    
    [MaxLength(50)]
    public string TaxNumber { get; set; }
    
    [MaxLength(200)]
    public string Address { get; set; }
    
    [MaxLength(50)]
    public string City { get; set; }
    
    [MaxLength(50)]
    public string Country { get; set; }
    
    [MaxLength(20)]
    public string Phone { get; set; }
    
    [MaxLength(100)]
    public string Email { get; set; }
    
    [MaxLength(100)]
    public string Website { get; set; }
    
    [MaxLength(100)]
    public string BankName { get; set; }
    
    [MaxLength(50)]
    public string BankAccount { get; set; }
    
    [MaxLength(20)]
    public string BankSwift { get; set; }
    
    [MaxLength(200)]
    public string LogoUrl { get; set; }
    
    public int DefaultCurrencyId { get; set; }
    public Currency DefaultCurrency { get; set; }
    
    // İlişkiler
    public ICollection<Account> Accounts { get; set; }
    public ICollection<Tax> Taxes { get; set; }
    public ICollection<TransactionCategory> TransactionCategories { get; set; }
    public ICollection<Transaction> Transactions { get; set; }
    public ICollection<Invoice> Invoices { get; set; }
    public ICollection<Supplier> Suppliers { get; set; }
} 