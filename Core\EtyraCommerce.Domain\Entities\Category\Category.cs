namespace EtyraCommerce.Domain.Entities.Category
{
    /// <summary>
    /// Product category entity
    /// </summary>
    public class Category : AuditableBaseEntity
    {
        /// <summary>
        /// Category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// URL slug for SEO
        /// </summary>
        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// Parent category ID (for hierarchical categories)
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// Category image URL
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Category icon (CSS class or icon name)
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether category is active/visible
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether to show category in menu
        /// </summary>
        public bool ShowInMenu { get; set; } = true;

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Number of products in this category (computed)
        /// </summary>
        public int ProductCount { get; set; } = 0;

        #region Navigation Properties

        /// <summary>
        /// Parent category
        /// </summary>
        public Category? ParentCategory { get; set; }

        /// <summary>
        /// Child categories
        /// </summary>
        public ICollection<Category> ChildCategories { get; set; } = new List<Category>();

        /// <summary>
        /// Product categories (many-to-many)
        /// </summary>
        public ICollection<Product.ProductCategory> ProductCategories { get; set; } = new List<Product.ProductCategory>();

        /// <summary>
        /// Category descriptions in different languages
        /// </summary>
        public ICollection<CategoryDescription> Descriptions { get; set; } = new List<CategoryDescription>();

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        public Category()
        {
        }

        /// <summary>
        /// Creates a new category
        /// </summary>
        public Category(string name, string? description, string slug, Guid? parentCategoryId,
            string? imageUrl, string? icon, int sortOrder, bool isActive, bool showInMenu,
            string? metaTitle, string? metaDescription, string? metaKeywords)
        {
            Name = name;
            Description = description;
            Slug = slug;
            ParentCategoryId = parentCategoryId;
            ImageUrl = imageUrl;
            Icon = icon;
            SortOrder = sortOrder;
            IsActive = isActive;
            ShowInMenu = showInMenu;
            MetaTitle = metaTitle;
            MetaDescription = metaDescription;
            MetaKeywords = metaKeywords;
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Updates basic category information
        /// </summary>
        public void UpdateBasicInfo(string name, string? description, string slug, string? imageUrl, string? icon)
        {
            Name = name;
            Description = description;
            Slug = slug;
            ImageUrl = imageUrl;
            Icon = icon;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates category hierarchy
        /// </summary>
        public void UpdateHierarchy(Guid? parentCategoryId)
        {
            ParentCategoryId = parentCategoryId;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates display settings
        /// </summary>
        public void UpdateDisplaySettings(int sortOrder, bool isActive, bool showInMenu)
        {
            SortOrder = sortOrder;
            IsActive = isActive;
            ShowInMenu = showInMenu;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates SEO settings
        /// </summary>
        public void UpdateSeoSettings(string? metaTitle, string? metaDescription, string? metaKeywords)
        {
            MetaTitle = metaTitle;
            MetaDescription = metaDescription;
            MetaKeywords = metaKeywords;
            MarkAsUpdated();
        }

        /// <summary>
        /// Soft deletes the category
        /// </summary>
        public void Delete()
        {
            IsDeleted = true;
            MarkAsUpdated();
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the category level (0 = root, 1 = first level, etc.)
        /// </summary>
        public int Level
        {
            get
            {
                var level = 0;
                var parent = ParentCategory;
                while (parent != null)
                {
                    level++;
                    parent = parent.ParentCategory;
                }
                return level;
            }
        }

        /// <summary>
        /// Checks if this is a root category
        /// </summary>
        public bool IsRoot => ParentCategoryId == null;

        /// <summary>
        /// Checks if this category has children
        /// </summary>
        public bool HasChildren => ChildCategories.Any();

        /// <summary>
        /// Gets the full category path (e.g., "Electronics > Computers > Laptops")
        /// </summary>
        public string FullPath
        {
            get
            {
                var path = new List<string>();
                var current = this;

                while (current != null)
                {
                    path.Insert(0, current.Name);
                    current = current.ParentCategory;
                }

                return string.Join(" > ", path);
            }
        }

        /// <summary>
        /// Gets all ancestor categories
        /// </summary>
        public IEnumerable<Category> Ancestors
        {
            get
            {
                var ancestors = new List<Category>();
                var parent = ParentCategory;

                while (parent != null)
                {
                    ancestors.Insert(0, parent);
                    parent = parent.ParentCategory;
                }

                return ancestors;
            }
        }

        /// <summary>
        /// Gets all descendant categories (recursive)
        /// </summary>
        public IEnumerable<Category> Descendants
        {
            get
            {
                var descendants = new List<Category>();

                foreach (var child in ChildCategories)
                {
                    descendants.Add(child);
                    descendants.AddRange(child.Descendants);
                }

                return descendants;
            }
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Generates SEO-friendly slug from name
        /// </summary>
        public void GenerateSlug()
        {
            if (string.IsNullOrWhiteSpace(Name)) return;

            Slug = Name.ToLowerInvariant()
                      .Replace(" ", "-")
                      .Replace("_", "-")
                      .Trim('-');

            MarkAsUpdated();
        }

        /// <summary>
        /// Activates the category
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            MarkAsUpdated();
        }

        /// <summary>
        /// Deactivates the category
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int order)
        {
            SortOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Moves category to a new parent
        /// </summary>
        public void MoveTo(Guid? newParentId)
        {
            // Prevent circular references
            if (newParentId.HasValue && IsAncestorOf(newParentId.Value))
                throw new InvalidOperationException("Cannot move category to its own descendant");

            ParentCategoryId = newParentId;
            MarkAsUpdated();
        }

        /// <summary>
        /// Checks if this category is an ancestor of the given category
        /// </summary>
        public bool IsAncestorOf(Guid categoryId)
        {
            return Descendants.Any(d => d.Id == categoryId);
        }

        /// <summary>
        /// Checks if this category is a descendant of the given category
        /// </summary>
        public bool IsDescendantOf(Guid categoryId)
        {
            return Ancestors.Any(a => a.Id == categoryId);
        }

        /// <summary>
        /// Updates product count
        /// </summary>
        public void UpdateProductCount(int count)
        {
            ProductCount = Math.Max(0, count);
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates SEO meta information
        /// </summary>
        public void UpdateSeoMeta(string? metaTitle = null, string? metaDescription = null, string? metaKeywords = null)
        {
            if (metaTitle != null) MetaTitle = metaTitle;
            if (metaDescription != null) MetaDescription = metaDescription;
            if (metaKeywords != null) MetaKeywords = metaKeywords;

            MarkAsUpdated();
        }

        /// <summary>
        /// Updates category image
        /// </summary>
        public void UpdateImage(string? imageUrl, string? icon = null)
        {
            ImageUrl = imageUrl;
            if (icon != null) Icon = icon;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"Category [Id: {Id}, Name: {Name}, Level: {Level}, ProductCount: {ProductCount}]";
        }
    }
}
