using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting all warehouses
    /// </summary>
    public class GetAllWarehousesQueryHandler : IRequestHandler<GetAllWarehousesQuery, CustomResponseDto<PagedResult<WarehouseDto>>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetAllWarehousesQueryHandler> _logger;

        public GetAllWarehousesQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetAllWarehousesQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<PagedResult<WarehouseDto>>> Handle(GetAllWarehousesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get all warehouses query with filter");

                // Validation
                if (request.PageNumber < 1)
                    return CustomResponseDto<PagedResult<WarehouseDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<WarehouseDto>>.BadRequest("Page size must be between 1 and 100");

                // Create filter DTO
                var filterDto = new WarehouseFilterDto
                {
                    SearchTerm = request.SearchTerm,
                    IsActive = request.IsActive,
                    IsMain = request.IsMain,
                    Type = request.Type,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection
                };

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetAllWarehousesAsync(filterDto);

                _logger.LogInformation("Get all warehouses query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get all warehouses query");
                return CustomResponseDto<PagedResult<WarehouseDto>>.InternalServerError("An error occurred while retrieving warehouses");
            }
        }
    }
}
