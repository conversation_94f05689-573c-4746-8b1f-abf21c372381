using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Commands
{
    /// <summary>
    /// Handler for creating a new user address
    /// </summary>
    public class CreateUserAddressCommandHandler : IRequestHandler<CreateUserAddressCommand, CustomResponseDto<UserAddressDto>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<CreateUserAddressCommandHandler> _logger;

        public CreateUserAddressCommandHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<CreateUserAddressCommandHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserAddressDto>> Handle(CreateUserAddressCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling CreateUserAddressCommand for UserId: {UserId}", request.UserId);

                // Validate request
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("CreateUserAddressCommand received with empty UserId");
                    return CustomResponseDto<UserAddressDto>.BadRequest("User ID is required");
                }

                // Create DTO from command properties
                var createDto = new CreateUserAddressDto
                {
                    Type = request.Type,
                    IsDefault = request.IsDefault,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    PhoneNumber = request.PhoneNumber,
                    Label = request.Label,
                    DeliveryInstructions = request.DeliveryInstructions,
                    Street = request.Street,
                    AddressLine2 = request.AddressLine2,
                    City = request.City,
                    State = request.State,
                    PostalCode = request.PostalCode,
                    Country = request.Country,
                    CompanyName = request.CompanyName,
                    TaxNumber = request.TaxNumber,
                    TaxOffice = request.TaxOffice,
                    CompanyTitle = request.CompanyTitle,
                    IsCompanyAddress = request.IsCompanyAddress
                };

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessCreateUserAddressAsync(request.UserId, createDto);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("CreateUserAddressCommand handled successfully for UserId: {UserId}, AddressId: {AddressId}",
                        request.UserId, result.Data?.Id);
                }
                else
                {
                    _logger.LogWarning("CreateUserAddressCommand failed for UserId: {UserId}, Error: {Error}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling CreateUserAddressCommand for UserId: {UserId}", request.UserId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while creating the address");
            }
        }
    }
}
