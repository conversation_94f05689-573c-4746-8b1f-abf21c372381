namespace EtyraCommerce.Application.DTOs.Common
{
    /// <summary>
    /// Validation result with detailed error information
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Indicates if validation passed
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Dictionary of field-specific errors
        /// </summary>
        public Dictionary<string, List<string>> Errors { get; set; } = new();

        /// <summary>
        /// General validation messages
        /// </summary>
        public List<string> GeneralErrors { get; set; } = new();

        /// <summary>
        /// Warning messages (non-blocking)
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Information messages
        /// </summary>
        public List<string> InfoMessages { get; set; } = new();

        /// <summary>
        /// Creates a successful validation result
        /// </summary>
        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// Creates a failed validation result with a general error
        /// </summary>
        public static ValidationResult Failure(string error)
        {
            return new ValidationResult
            {
                IsValid = false,
                GeneralErrors = new List<string> { error }
            };
        }

        /// <summary>
        /// Creates a failed validation result with multiple general errors
        /// </summary>
        public static ValidationResult Failure(List<string> errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                GeneralErrors = errors
            };
        }

        /// <summary>
        /// Creates a failed validation result with field-specific errors
        /// </summary>
        public static ValidationResult Failure(Dictionary<string, List<string>> fieldErrors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = fieldErrors
            };
        }

        /// <summary>
        /// Creates a failed validation result with both general and field-specific errors
        /// </summary>
        public static ValidationResult Failure(List<string> generalErrors, Dictionary<string, List<string>> fieldErrors)
        {
            return new ValidationResult
            {
                IsValid = false,
                GeneralErrors = generalErrors,
                Errors = fieldErrors
            };
        }

        /// <summary>
        /// Adds a field-specific error
        /// </summary>
        public ValidationResult AddError(string field, string error)
        {
            if (!Errors.ContainsKey(field))
                Errors[field] = new List<string>();

            Errors[field].Add(error);
            IsValid = false;
            return this;
        }

        /// <summary>
        /// Adds multiple errors for a field
        /// </summary>
        public ValidationResult AddErrors(string field, List<string> errors)
        {
            if (!Errors.ContainsKey(field))
                Errors[field] = new List<string>();

            Errors[field].AddRange(errors);
            IsValid = false;
            return this;
        }

        /// <summary>
        /// Adds a general error
        /// </summary>
        public ValidationResult AddGeneralError(string error)
        {
            GeneralErrors.Add(error);
            IsValid = false;
            return this;
        }

        /// <summary>
        /// Adds multiple general errors
        /// </summary>
        public ValidationResult AddGeneralErrors(List<string> errors)
        {
            GeneralErrors.AddRange(errors);
            IsValid = false;
            return this;
        }

        /// <summary>
        /// Adds a warning message
        /// </summary>
        public ValidationResult AddWarning(string warning)
        {
            Warnings.Add(warning);
            return this;
        }

        /// <summary>
        /// Adds multiple warning messages
        /// </summary>
        public ValidationResult AddWarnings(List<string> warnings)
        {
            Warnings.AddRange(warnings);
            return this;
        }

        /// <summary>
        /// Adds an info message
        /// </summary>
        public ValidationResult AddInfo(string info)
        {
            InfoMessages.Add(info);
            return this;
        }

        /// <summary>
        /// Adds multiple info messages
        /// </summary>
        public ValidationResult AddInfos(List<string> infos)
        {
            InfoMessages.AddRange(infos);
            return this;
        }

        /// <summary>
        /// Merges another validation result into this one
        /// </summary>
        public ValidationResult Merge(ValidationResult other)
        {
            if (other == null) return this;

            // Merge field errors
            foreach (var kvp in other.Errors)
            {
                if (!Errors.ContainsKey(kvp.Key))
                    Errors[kvp.Key] = new List<string>();

                Errors[kvp.Key].AddRange(kvp.Value);
            }

            // Merge general errors
            GeneralErrors.AddRange(other.GeneralErrors);

            // Merge warnings and info messages
            Warnings.AddRange(other.Warnings);
            InfoMessages.AddRange(other.InfoMessages);

            // Update validity
            IsValid = IsValid && other.IsValid;

            return this;
        }

        /// <summary>
        /// Checks if there are any errors for a specific field
        /// </summary>
        public bool HasErrorsForField(string field)
        {
            return Errors.ContainsKey(field) && Errors[field].Any();
        }

        /// <summary>
        /// Gets errors for a specific field
        /// </summary>
        public List<string> GetErrorsForField(string field)
        {
            return Errors.TryGetValue(field, out var errors) ? errors : new List<string>();
        }

        /// <summary>
        /// Gets the first error for a specific field
        /// </summary>
        public string? GetFirstErrorForField(string field)
        {
            var errors = GetErrorsForField(field);
            return errors.FirstOrDefault();
        }

        /// <summary>
        /// Checks if there are any errors at all
        /// </summary>
        public bool HasErrors => !IsValid || Errors.Any() || GeneralErrors.Any();

        /// <summary>
        /// Checks if there are any warnings
        /// </summary>
        public bool HasWarnings => Warnings.Any();

        /// <summary>
        /// Checks if there are any info messages
        /// </summary>
        public bool HasInfoMessages => InfoMessages.Any();

        /// <summary>
        /// Gets total error count
        /// </summary>
        public int ErrorCount => GeneralErrors.Count + Errors.Values.Sum(list => list.Count);

        /// <summary>
        /// Gets all errors as a flat list
        /// </summary>
        public List<string> GetAllErrors()
        {
            var allErrors = new List<string>();
            allErrors.AddRange(GeneralErrors);

            foreach (var kvp in Errors)
            {
                allErrors.AddRange(kvp.Value.Select(error => $"{kvp.Key}: {error}"));
            }

            return allErrors;
        }

        /// <summary>
        /// Gets all errors as a single string
        /// </summary>
        public string GetAllErrorsAsString(string separator = "; ")
        {
            return string.Join(separator, GetAllErrors());
        }

        /// <summary>
        /// Gets validation summary
        /// </summary>
        public ValidationSummary GetSummary()
        {
            return new ValidationSummary
            {
                IsValid = IsValid,
                ErrorCount = ErrorCount,
                WarningCount = Warnings.Count,
                InfoCount = InfoMessages.Count,
                FieldErrorCount = Errors.Count,
                GeneralErrorCount = GeneralErrors.Count
            };
        }

        /// <summary>
        /// Converts to ServiceResult
        /// </summary>
        public ServiceResult ToServiceResult()
        {
            if (IsValid)
                return ServiceResult.Success();

            return ServiceResult.ValidationFailure(GetAllErrors());
        }

        /// <summary>
        /// Converts to ServiceResult<T>
        /// </summary>
        public ServiceResult<T> ToServiceResult<T>(T? data = default)
        {
            if (IsValid)
                return ServiceResult<T>.Success(data!);

            return ServiceResult<T>.ValidationFailure(GetAllErrors());
        }

        /// <summary>
        /// Clears all errors and sets IsValid to true
        /// </summary>
        public ValidationResult Clear()
        {
            IsValid = true;
            Errors.Clear();
            GeneralErrors.Clear();
            Warnings.Clear();
            InfoMessages.Clear();
            return this;
        }

        /// <summary>
        /// Creates a copy of the validation result
        /// </summary>
        public ValidationResult Clone()
        {
            return new ValidationResult
            {
                IsValid = IsValid,
                Errors = new Dictionary<string, List<string>>(Errors.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new List<string>(kvp.Value))),
                GeneralErrors = new List<string>(GeneralErrors),
                Warnings = new List<string>(Warnings),
                InfoMessages = new List<string>(InfoMessages)
            };
        }

        public override string ToString()
        {
            if (IsValid)
                return "Validation passed";

            var summary = GetSummary();
            return $"Validation failed: {summary.ErrorCount} error(s), {summary.WarningCount} warning(s)";
        }
    }

    /// <summary>
    /// Validation summary information
    /// </summary>
    public class ValidationSummary
    {
        public bool IsValid { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int InfoCount { get; set; }
        public int FieldErrorCount { get; set; }
        public int GeneralErrorCount { get; set; }

        public override string ToString()
        {
            return $"Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}, Info: {InfoCount}";
        }
    }
}
