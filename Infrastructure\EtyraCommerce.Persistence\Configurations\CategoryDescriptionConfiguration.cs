using EtyraCommerce.Domain.Entities.Category;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EtyraCommerce.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for CategoryDescription entity
    /// </summary>
    public class CategoryDescriptionConfiguration : BaseEntityConfiguration<CategoryDescription>
    {
        public override void Configure(EntityTypeBuilder<CategoryDescription> builder)
        {
            // Apply base configuration
            base.Configure(builder);

            // Table Configuration
            builder.ToTable("category_descriptions", "etyra_core");

            #region Properties

            // Name
            builder.Property(x => x.Name)
                .HasColumnName("name")
                .HasMaxLength(200)
                .IsRequired();

            // Description
            builder.Property(x => x.Description)
                .HasColumnName("description")
                .HasColumnType("text")
                .IsRequired(false);

            // Meta Title
            builder.Property(x => x.MetaTitle)
                .HasColumnName("meta_title")
                .HasMaxLength(120)
                .IsRequired(false);

            // Meta Description
            builder.Property(x => x.MetaDescription)
                .HasColumnName("meta_description")
                .HasMaxLength(350)
                .IsRequired(false);

            // Meta Keywords
            builder.Property(x => x.MetaKeywords)
                .HasColumnName("meta_keywords")
                .HasMaxLength(200)
                .IsRequired(false);

            // Slug
            builder.Property(x => x.Slug)
                .HasColumnName("slug")
                .HasMaxLength(200)
                .IsRequired(false);

            // Language Code
            builder.Property(x => x.LanguageCode)
                .HasColumnName("language_code")
                .HasMaxLength(10)
                .HasDefaultValue("en-US")
                .IsRequired();

            // Store ID
            builder.Property(x => x.StoreId)
                .HasColumnName("store_id")
                .IsRequired(false);

            // Category ID
            builder.Property(x => x.CategoryId)
                .HasColumnName("category_id")
                .IsRequired();

            #endregion

            #region Navigation Properties

            // Category (Many-to-One)
            builder.HasOne(x => x.Category)
                .WithMany(x => x.Descriptions)
                .HasForeignKey(x => x.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            #endregion

            #region Indexes

            // Composite Primary Key Alternative (Category + Language + Store)
            builder.HasIndex(x => new { x.CategoryId, x.LanguageCode, x.StoreId })
                .IsUnique()
                .HasDatabaseName("ix_category_descriptions_category_language_store_unique");

            // Performance Indexes
            builder.HasIndex(x => x.CategoryId)
                .HasDatabaseName("ix_category_descriptions_category_id");

            builder.HasIndex(x => x.LanguageCode)
                .HasDatabaseName("ix_category_descriptions_language_code");

            builder.HasIndex(x => x.StoreId)
                .HasDatabaseName("ix_category_descriptions_store_id");

            builder.HasIndex(x => x.Slug)
                .HasDatabaseName("ix_category_descriptions_slug");

            // Search Indexes
            builder.HasIndex(x => x.Name)
                .HasDatabaseName("ix_category_descriptions_name");

            // Composite Indexes
            builder.HasIndex(x => new { x.LanguageCode, x.StoreId })
                .HasDatabaseName("ix_category_descriptions_language_store");

            builder.HasIndex(x => new { x.CategoryId, x.LanguageCode })
                .HasDatabaseName("ix_category_descriptions_category_language");

            #endregion

            #region Check Constraints

            // Business Rules
            builder.HasCheckConstraint("CK_CategoryDescriptions_Name_NotEmpty",
                "LENGTH(TRIM(name)) > 0");

            builder.HasCheckConstraint("CK_CategoryDescriptions_LanguageCode_NotEmpty",
                "LENGTH(TRIM(language_code)) > 0");

            #endregion
        }

        protected override string GetTableName() => "category_descriptions";
    }
}
