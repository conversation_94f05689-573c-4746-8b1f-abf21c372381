﻿using EtyraApp.Domain.Entities.Catalog;
using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Settings;

public class Language : BaseEntity
{
    [MaxLength(30)]
    public string Name { get; set; }

    [MaxLength(3)]
    public string Code { get; set; }

    public bool TranslateStatus { get; set; } = false;
    // public bool Status { get; set; } = true;

    public ICollection<ProductDescription> ProductDescriptions { get; set; }

    public ICollection<StoreLanguage> Languages { get; set; }
    public ICollection<BazaarLanguage> BLanguages { get; set; }

}