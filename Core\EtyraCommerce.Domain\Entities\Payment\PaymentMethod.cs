using EtyraCommerce.Domain.Entities;
using EtyraCommerce.Domain.Enums;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Domain.Entities.Payment
{
    /// <summary>
    /// Payment method entity representing different payment options
    /// </summary>
    public class PaymentMethod : BaseEntity
    {
        #region Properties

        /// <summary>
        /// Payment method name (e.g., "Cash on Delivery", "Bank Transfer")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Payment method code for system identification
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Description of the payment method
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether this payment method is currently active
        /// </summary>
        public new bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting payment methods
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Payment method type
        /// </summary>
        public PaymentMethodType Type { get; set; }

        /// <summary>
        /// Fee calculation type (Fixed amount or percentage)
        /// </summary>
        public FeeCalculationType FeeCalculationType { get; set; } = FeeCalculationType.None;

        /// <summary>
        /// Fee value (amount for fixed, percentage for percentage)
        /// </summary>
        public decimal FeeValue { get; set; } = 0;

        /// <summary>
        /// Currency for fixed fee amounts
        /// </summary>
        public ValueObjects.Currency? FeeCurrency { get; set; }

        /// <summary>
        /// Minimum order amount to use this payment method
        /// </summary>
        public Money? MinimumOrderAmount { get; set; }

        /// <summary>
        /// Maximum order amount to use this payment method
        /// </summary>
        public Money? MaximumOrderAmount { get; set; }

        /// <summary>
        /// Instructions for customers using this payment method
        /// </summary>
        public string? Instructions { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Parameterless constructor for EF Core
        /// </summary>
        private PaymentMethod()
        {
        }

        /// <summary>
        /// Creates a new payment method
        /// </summary>
        public PaymentMethod(string name, string code, PaymentMethodType type, string? description = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Code = code ?? throw new ArgumentNullException(nameof(code));
            Type = type;
            Description = description;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Sets fee configuration for this payment method
        /// </summary>
        public void SetFee(FeeCalculationType calculationType, decimal feeValue, ValueObjects.Currency? currency = null)
        {
            if (calculationType == FeeCalculationType.Fixed && currency == null)
                throw new ArgumentException("Currency is required for fixed fee calculation");

            if (calculationType == FeeCalculationType.Percentage && (feeValue < -100 || feeValue > 100))
                throw new ArgumentException("Percentage fee must be between -100 and 100");

            FeeCalculationType = calculationType;
            FeeValue = feeValue;
            FeeCurrency = currency;
        }

        /// <summary>
        /// Sets order amount limits for this payment method
        /// </summary>
        public void SetOrderLimits(Money? minimumAmount = null, Money? maximumAmount = null)
        {
            if (minimumAmount != null && maximumAmount != null)
            {
                if (!minimumAmount.Currency.Equals(maximumAmount.Currency))
                    throw new ArgumentException("Minimum and maximum amounts must have the same currency");

                if (minimumAmount.Amount > maximumAmount.Amount)
                    throw new ArgumentException("Minimum amount cannot be greater than maximum amount");
            }

            MinimumOrderAmount = minimumAmount;
            MaximumOrderAmount = maximumAmount;
        }

        /// <summary>
        /// Calculates fee for given order amount
        /// </summary>
        public Money CalculateFee(Money orderAmount)
        {
            if (FeeCalculationType == FeeCalculationType.None)
                return Money.Zero(orderAmount.Currency);

            if (FeeCalculationType == FeeCalculationType.Fixed)
            {
                if (FeeCurrency == null)
                    throw new InvalidOperationException("Fee currency is not set for fixed fee calculation");

                // Convert fee currency to order currency if different
                if (!FeeCurrency.Equals(orderAmount.Currency))
                {
                    // For now, assume same currency. In real implementation, you'd use exchange rates
                    throw new NotSupportedException("Cross-currency fee calculation not implemented yet");
                }

                return new Money(Math.Abs(FeeValue), orderAmount.Currency);
            }

            if (FeeCalculationType == FeeCalculationType.Percentage)
            {
                var feeAmount = orderAmount.Amount * (FeeValue / 100);
                return new Money(Math.Abs(feeAmount), orderAmount.Currency);
            }

            return Money.Zero(orderAmount.Currency);
        }

        /// <summary>
        /// Checks if this payment method is available for the given order amount
        /// </summary>
        public bool IsAvailableForAmount(Money orderAmount)
        {
            if (!IsActive)
                return false;

            if (MinimumOrderAmount != null)
            {
                if (!MinimumOrderAmount.Currency.Equals(orderAmount.Currency))
                    return false; // Different currency, not available

                if (orderAmount.Amount < MinimumOrderAmount.Amount)
                    return false;
            }

            if (MaximumOrderAmount != null)
            {
                if (!MaximumOrderAmount.Currency.Equals(orderAmount.Currency))
                    return false; // Different currency, not available

                if (orderAmount.Amount > MaximumOrderAmount.Amount)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Gets fee description for display
        /// </summary>
        public string GetFeeDescription()
        {
            return FeeCalculationType switch
            {
                FeeCalculationType.None => "No additional fee",
                FeeCalculationType.Fixed => $"Fixed fee: {FeeValue} {FeeCurrency?.Code}",
                FeeCalculationType.Percentage => FeeValue >= 0 
                    ? $"Additional fee: {FeeValue}%" 
                    : $"Discount: {Math.Abs(FeeValue)}%",
                _ => "Unknown fee calculation"
            };
        }

        #endregion
    }
}
