using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Payment;
using EtyraCommerce.Application.Services;
using EtyraCommerce.Domain.Entities.Payment;
using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.Services.Payment;

/// <summary>
/// Payment method process service interface for business logic operations
/// </summary>
public interface IPaymentMethodProcessService : IService<PaymentMethod, PaymentMethodDto>
{
    /// <summary>
    /// Process create payment method request
    /// </summary>
    /// <param name="createDto">Create payment method DTO</param>
    /// <returns>Created payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> ProcessCreateAsync(CreatePaymentMethodDto createDto);

    /// <summary>
    /// Process update payment method request
    /// </summary>
    /// <param name="updateDto">Update payment method DTO</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> ProcessUpdateAsync(UpdatePaymentMethodDto updateDto);

    /// <summary>
    /// Process delete payment method request
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <returns>Success result</returns>
    Task<CustomResponseDto<NoContentDto>> ProcessDeleteAsync(Guid id);

    /// <summary>
    /// Process toggle payment method status request
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated payment method</returns>
    Task<CustomResponseDto<PaymentMethodDto>> ProcessToggleStatusAsync(Guid id, bool isActive);

    /// <summary>
    /// Get payment method by ID
    /// </summary>
    /// <param name="id">Payment method ID</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    Task<CustomResponseDto<PaymentMethodDto?>> GetByIdAsync(Guid id, bool tracking = false);

    /// <summary>
    /// Get payment method by code
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>Payment method or null</returns>
    Task<CustomResponseDto<PaymentMethodDto?>> GetByCodeAsync(string code, bool tracking = false);

    /// <summary>
    /// Get all payment methods
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of all payment methods</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetAllAsync(bool tracking = false);

    /// <summary>
    /// Get active payment methods
    /// </summary>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of active payment methods</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetActivePaymentMethodsAsync(bool tracking = false);

    /// <summary>
    /// Get payment methods by type
    /// </summary>
    /// <param name="type">Payment method type</param>
    /// <param name="onlyActive">Whether to include only active methods</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of payment methods</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetByTypeAsync(PaymentMethodType type, bool onlyActive = true, bool tracking = false);

    /// <summary>
    /// Get payment methods available for order amount with calculated fees
    /// </summary>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <param name="tracking">Whether to track changes</param>
    /// <returns>List of available payment methods with calculated fees</returns>
    Task<CustomResponseDto<List<PaymentMethodDto>>> GetAvailableForAmountAsync(decimal orderAmount, string orderCurrency, bool tracking = false);

    /// <summary>
    /// Check if payment method code exists
    /// </summary>
    /// <param name="code">Payment method code</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>True if code exists</returns>
    Task<CustomResponseDto<bool>> CodeExistsAsync(string code, Guid? excludeId = null);

    /// <summary>
    /// Calculate fee for payment method and order amount
    /// </summary>
    /// <param name="paymentMethodId">Payment method ID</param>
    /// <param name="orderAmount">Order amount</param>
    /// <param name="orderCurrency">Order currency</param>
    /// <returns>Calculated fee amount</returns>
    Task<CustomResponseDto<decimal>> CalculateFeeAsync(Guid paymentMethodId, decimal orderAmount, string orderCurrency);
}
