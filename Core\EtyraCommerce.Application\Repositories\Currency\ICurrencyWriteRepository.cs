using EtyraCommerce.Application.Repositories;

namespace EtyraCommerce.Application.Repositories.Currency;

/// <summary>
/// Write repository interface for Currency entity
/// </summary>
public interface ICurrencyWriteRepository : IWriteRepository<Domain.Entities.Currency.Currency>
{
    /// <summary>
    /// Set currency as default (and unset others)
    /// </summary>
    /// <param name="currencyId">Currency ID to set as default</param>
    /// <returns>Updated currency or null if not found</returns>
    Task<Domain.Entities.Currency.Currency?> SetAsDefaultAsync(Guid currencyId);

    /// <summary>
    /// Update currency status
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated currency or null if not found</returns>
    Task<Domain.Entities.Currency.Currency?> UpdateStatusAsync(Guid id, bool isActive);

    /// <summary>
    /// Update display order for multiple currencies
    /// </summary>
    /// <param name="orderUpdates">Dictionary of currency ID and new display order</param>
    /// <returns>Number of updated records</returns>
    Task<int> UpdateDisplayOrdersAsync(Dictionary<Guid, int> orderUpdates);

    /// <summary>
    /// Bulk update currencies
    /// </summary>
    /// <param name="currencies">Currencies to update</param>
    /// <returns>Number of updated records</returns>
    Task<int> BulkUpdateAsync(IEnumerable<Domain.Entities.Currency.Currency> currencies);
}
