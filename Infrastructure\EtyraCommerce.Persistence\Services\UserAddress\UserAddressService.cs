using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress;
using EtyraCommerce.Application.Services.UserAddress.Commands;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Persistence.Services.UserAddress
{
    /// <summary>
    /// Service implementation for UserAddress operations
    /// Delegates to CQRS handlers via MediatR
    /// </summary>
    public class UserAddressService : IUserAddressService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<UserAddressService> _logger;

        public UserAddressService(IMediator mediator, ILogger<UserAddressService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        #region Command Operations

        public async Task<CustomResponseDto<UserAddressDto>> CreateUserAddressAsync(Guid userId, CreateUserAddressDto createDto)
        {
            try
            {
                _logger.LogInformation("Creating user address for UserId: {UserId}", userId);

                var command = new CreateUserAddressCommand(userId, createDto);
                var result = await _mediator.Send(command);

                _logger.LogInformation("User address creation completed for UserId: {UserId}, Success: {Success}",
                    userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateUserAddressAsync for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while creating the address");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> UpdateUserAddressAsync(Guid addressId, Guid userId, UpdateUserAddressDto updateDto)
        {
            try
            {
                _logger.LogInformation("Updating user address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                var command = new UpdateUserAddressCommand(addressId, userId, updateDto);
                var result = await _mediator.Send(command);

                _logger.LogInformation("User address update completed for AddressId: {AddressId}, UserId: {UserId}, Success: {Success}",
                    addressId, userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateUserAddressAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while updating the address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> DeleteUserAddressAsync(Guid addressId, Guid userId, bool hardDelete = false)
        {
            try
            {
                _logger.LogInformation("Deleting user address for AddressId: {AddressId}, UserId: {UserId}, HardDelete: {HardDelete}",
                    addressId, userId, hardDelete);

                var command = new DeleteUserAddressCommand(addressId, userId, hardDelete);
                var result = await _mediator.Send(command);

                _logger.LogInformation("User address deletion completed for AddressId: {AddressId}, UserId: {UserId}, Success: {Success}",
                    addressId, userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DeleteUserAddressAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while deleting the address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> SetDefaultAddressAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Setting default address for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                var command = new SetDefaultAddressCommand(addressId, userId);
                var result = await _mediator.Send(command);

                _logger.LogInformation("Set default address completed for AddressId: {AddressId}, UserId: {UserId}, Success: {Success}",
                    addressId, userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SetDefaultAddressAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while setting the default address");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> ToggleAddressStatusAsync(Guid addressId, Guid userId, bool isActive)
        {
            try
            {
                _logger.LogInformation("Toggling address status for AddressId: {AddressId}, UserId: {UserId}, IsActive: {IsActive}",
                    addressId, userId, isActive);

                var command = new ToggleAddressStatusCommand(addressId, userId, isActive);
                var result = await _mediator.Send(command);

                _logger.LogInformation("Toggle address status completed for AddressId: {AddressId}, UserId: {UserId}, Success: {Success}",
                    addressId, userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ToggleAddressStatusAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while toggling the address status");
            }
        }

        #endregion

        #region Query Operations

        public async Task<CustomResponseDto<List<UserAddressDto>>> GetUserAddressesAsync(Guid userId, int? addressType = null, bool? isActive = null, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting user addresses for UserId: {UserId}, AddressType: {AddressType}, IsActive: {IsActive}",
                    userId, addressType, isActive);

                var query = new GetUserAddressesQuery(userId, addressType, isActive, includeInactive);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Get user addresses completed for UserId: {UserId}, Count: {Count}, Success: {Success}",
                    userId, result.Data?.Count ?? 0, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetUserAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving addresses");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> GetUserAddressByIdAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Getting user address by ID for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                var query = new GetUserAddressByIdQuery(addressId, userId);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Get user address by ID completed for AddressId: {AddressId}, UserId: {UserId}, Success: {Success}",
                    addressId, userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetUserAddressByIdAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the address");
            }
        }

        public async Task<CustomResponseDto<UserAddressDto>> GetDefaultAddressAsync(Guid userId, int? addressType = null)
        {
            try
            {
                _logger.LogInformation("Getting default address for UserId: {UserId}, AddressType: {AddressType}", userId, addressType);

                var query = new GetDefaultAddressQuery(userId, addressType);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Get default address completed for UserId: {UserId}, Success: {Success}", userId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDefaultAddressAsync for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressDto>.InternalServerError("An error occurred while retrieving the default address");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> GetBillingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting billing addresses for UserId: {UserId}, IncludeInactive: {IncludeInactive}", userId, includeInactive);

                var query = new GetBillingAddressesQuery(userId, includeInactive);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Get billing addresses completed for UserId: {UserId}, Count: {Count}, Success: {Success}",
                    userId, result.Data?.Count ?? 0, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetBillingAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving billing addresses");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> GetShippingAddressesAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting shipping addresses for UserId: {UserId}, IncludeInactive: {IncludeInactive}", userId, includeInactive);

                var query = new GetShippingAddressesQuery(userId, includeInactive);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Get shipping addresses completed for UserId: {UserId}, Count: {Count}, Success: {Success}",
                    userId, result.Data?.Count ?? 0, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetShippingAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving shipping addresses");
            }
        }

        #endregion

        #region Validation Operations

        public async Task<CustomResponseDto<bool>> ValidateUserCanHaveNewAddressAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Validating if user can have new address for UserId: {UserId}", userId);

                // This would typically be handled by the process service
                // For now, we'll implement a simple validation
                var addressCountResult = await GetUserAddressCountAsync(userId);
                if (!addressCountResult.IsSuccess)
                    return CustomResponseDto<bool>.InternalServerError("Error validating address limit");

                const int maxAddressesPerUser = 10;
                var canHaveNewAddress = addressCountResult.Data < maxAddressesPerUser;

                if (!canHaveNewAddress)
                {
                    return CustomResponseDto<bool>.BadRequest($"Maximum {maxAddressesPerUser} addresses allowed per user");
                }

                return CustomResponseDto<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateUserCanHaveNewAddressAsync for UserId: {UserId}", userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address limit");
            }
        }

        public async Task<CustomResponseDto<bool>> ValidateAddressBelongsToUserAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Validating address ownership for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                var addressResult = await GetUserAddressByIdAsync(addressId, userId);
                var belongsToUser = addressResult.IsSuccess && addressResult.Data != null;

                return CustomResponseDto<bool>.Success(belongsToUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateAddressBelongsToUserAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address ownership");
            }
        }

        public async Task<CustomResponseDto<bool>> ValidateAddressCanBeDeletedAsync(Guid addressId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Validating if address can be deleted for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);

                // Check if this is the only address
                var addressCountResult = await GetUserAddressCountAsync(userId);
                if (!addressCountResult.IsSuccess)
                    return CustomResponseDto<bool>.InternalServerError("Error validating address deletion");

                if (addressCountResult.Data <= 1)
                {
                    return CustomResponseDto<bool>.BadRequest("Cannot delete the only address. User must have at least one address.");
                }

                return CustomResponseDto<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateAddressCanBeDeletedAsync for AddressId: {AddressId}, UserId: {UserId}", addressId, userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while validating address deletion");
            }
        }

        #endregion

        #region Business Logic Operations

        public async Task<CustomResponseDto<int>> GetUserAddressCountAsync(Guid userId, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting user address count for UserId: {UserId}, IncludeInactive: {IncludeInactive}", userId, includeInactive);

                var addressesResult = await GetUserAddressesAsync(userId, includeInactive: includeInactive);
                if (!addressesResult.IsSuccess)
                    return CustomResponseDto<int>.InternalServerError("Error retrieving address count");

                var count = addressesResult.Data?.Count ?? 0;
                return CustomResponseDto<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetUserAddressCountAsync for UserId: {UserId}", userId);
                return CustomResponseDto<int>.InternalServerError("An error occurred while getting address count");
            }
        }

        public async Task<CustomResponseDto<bool>> UserHasAddressesAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Checking if user has addresses for UserId: {UserId}", userId);

                var countResult = await GetUserAddressCountAsync(userId);
                if (!countResult.IsSuccess)
                    return CustomResponseDto<bool>.InternalServerError("Error checking user addresses");

                var hasAddresses = countResult.Data > 0;
                return CustomResponseDto<bool>.Success(hasAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UserHasAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<bool>.InternalServerError("An error occurred while checking user addresses");
            }
        }

        public async Task<CustomResponseDto<UserAddressStatsDto>> GetUserAddressStatsAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Getting user address stats for UserId: {UserId}", userId);

                var allAddressesResult = await GetUserAddressesAsync(userId, includeInactive: true);
                if (!allAddressesResult.IsSuccess)
                    return CustomResponseDto<UserAddressStatsDto>.InternalServerError("Error retrieving address statistics");

                var addresses = allAddressesResult.Data ?? new List<UserAddressDto>();

                var stats = new UserAddressStatsDto
                {
                    TotalAddresses = addresses.Count,
                    ActiveAddresses = addresses.Count(a => a.IsActive),
                    InactiveAddresses = addresses.Count(a => !a.IsActive),
                    BillingAddresses = addresses.Count(a => a.CanBeUsedForBilling),
                    ShippingAddresses = addresses.Count(a => a.CanBeUsedForShipping),
                    BothTypeAddresses = addresses.Count(a => a.Type == 3), // Both type
                    HasDefaultAddress = addresses.Any(a => a.IsDefault),
                    DefaultAddressId = addresses.FirstOrDefault(a => a.IsDefault)?.Id
                };

                return CustomResponseDto<UserAddressStatsDto>.Success(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetUserAddressStatsAsync for UserId: {UserId}", userId);
                return CustomResponseDto<UserAddressStatsDto>.InternalServerError("An error occurred while getting address statistics");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> SearchUserAddressesAsync(Guid userId, string searchText, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Searching user addresses for UserId: {UserId}, SearchText: {SearchText}", userId, searchText);

                if (string.IsNullOrWhiteSpace(searchText))
                    return await GetUserAddressesAsync(userId, includeInactive: includeInactive);

                var allAddressesResult = await GetUserAddressesAsync(userId, includeInactive: includeInactive);
                if (!allAddressesResult.IsSuccess)
                    return allAddressesResult;

                var addresses = allAddressesResult.Data ?? new List<UserAddressDto>();
                var searchTerm = searchText.Trim().ToLower();

                var filteredAddresses = addresses.Where(a =>
                    a.Street.ToLower().Contains(searchTerm) ||
                    a.City.ToLower().Contains(searchTerm) ||
                    a.State.ToLower().Contains(searchTerm) ||
                    a.Country.ToLower().Contains(searchTerm) ||
                    (!string.IsNullOrEmpty(a.AddressLine2) && a.AddressLine2.ToLower().Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(a.Label) && a.Label.ToLower().Contains(searchTerm)) ||
                    a.FirstName.ToLower().Contains(searchTerm) ||
                    a.LastName.ToLower().Contains(searchTerm)
                ).ToList();

                return CustomResponseDto<List<UserAddressDto>>.Success(filteredAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SearchUserAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while searching addresses");
            }
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> GetRecentUserAddressesAsync(Guid userId, int count = 5, bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting recent user addresses for UserId: {UserId}, Count: {Count}", userId, count);

                var allAddressesResult = await GetUserAddressesAsync(userId, includeInactive: includeInactive);
                if (!allAddressesResult.IsSuccess)
                    return allAddressesResult;

                var addresses = allAddressesResult.Data ?? new List<UserAddressDto>();
                var recentAddresses = addresses
                    .OrderByDescending(a => a.UpdatedAt ?? a.CreatedAt)
                    .Take(count)
                    .ToList();

                return CustomResponseDto<List<UserAddressDto>>.Success(recentAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetRecentUserAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while getting recent addresses");
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<CustomResponseDto<List<UserAddressDto>>> BulkCreateUserAddressesAsync(Guid userId, List<CreateUserAddressDto> createDtos)
        {
            try
            {
                _logger.LogInformation("Bulk creating user addresses for UserId: {UserId}, Count: {Count}", userId, createDtos.Count);

                var results = new List<UserAddressDto>();
                var errors = new List<string>();

                foreach (var createDto in createDtos)
                {
                    var result = await CreateUserAddressAsync(userId, createDto);
                    if (result.IsSuccess && result.Data != null)
                    {
                        results.Add(result.Data);
                    }
                    else
                    {
                        errors.Add(result.Message ?? "Unknown error");
                    }
                }

                if (errors.Any())
                {
                    var errorMessage = $"Some addresses could not be created: {string.Join(", ", errors)}";
                    _logger.LogWarning("Bulk create partially failed for UserId: {UserId}, Errors: {Errors}", userId, errorMessage);

                    if (results.Any())
                        return CustomResponseDto<List<UserAddressDto>>.Success(results, errorMessage);
                    else
                        return CustomResponseDto<List<UserAddressDto>>.BadRequest(errorMessage);
                }

                return CustomResponseDto<List<UserAddressDto>>.Success(results, "All addresses created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in BulkCreateUserAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while bulk creating addresses");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> BulkActivateAddressesAsync(List<Guid> addressIds, Guid userId)
        {
            try
            {
                _logger.LogInformation("Bulk activating addresses for UserId: {UserId}, Count: {Count}", userId, addressIds.Count);

                var errors = new List<string>();

                foreach (var addressId in addressIds)
                {
                    var result = await ToggleAddressStatusAsync(addressId, userId, true);
                    if (!result.IsSuccess)
                    {
                        errors.Add($"AddressId {addressId}: {result.Message}");
                    }
                }

                if (errors.Any())
                {
                    var errorMessage = $"Some addresses could not be activated: {string.Join(", ", errors)}";
                    _logger.LogWarning("Bulk activate partially failed for UserId: {UserId}, Errors: {Errors}", userId, errorMessage);
                    return CustomResponseDto<NoContentDto>.BadRequest(errorMessage);
                }

                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "All addresses activated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in BulkActivateAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while bulk activating addresses");
            }
        }

        public async Task<CustomResponseDto<NoContentDto>> BulkDeactivateAddressesAsync(List<Guid> addressIds, Guid userId)
        {
            try
            {
                _logger.LogInformation("Bulk deactivating addresses for UserId: {UserId}, Count: {Count}", userId, addressIds.Count);

                var errors = new List<string>();

                foreach (var addressId in addressIds)
                {
                    var result = await ToggleAddressStatusAsync(addressId, userId, false);
                    if (!result.IsSuccess)
                    {
                        errors.Add($"AddressId {addressId}: {result.Message}");
                    }
                }

                if (errors.Any())
                {
                    var errorMessage = $"Some addresses could not be deactivated: {string.Join(", ", errors)}";
                    _logger.LogWarning("Bulk deactivate partially failed for UserId: {UserId}, Errors: {Errors}", userId, errorMessage);
                    return CustomResponseDto<NoContentDto>.BadRequest(errorMessage);
                }

                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "All addresses deactivated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in BulkDeactivateAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while bulk deactivating addresses");
            }
        }

        #endregion

        #region Maintenance Operations

        public async Task<CustomResponseDto<NoContentDto>> EnsureAddressConsistencyAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Ensuring address consistency for UserId: {UserId}", userId);

                var addressesResult = await GetUserAddressesAsync(userId, includeInactive: true);
                if (!addressesResult.IsSuccess)
                    return CustomResponseDto<NoContentDto>.InternalServerError("Error retrieving addresses for consistency check");

                var addresses = addressesResult.Data ?? new List<UserAddressDto>();
                var defaultAddresses = addresses.Where(a => a.IsDefault).ToList();

                if (defaultAddresses.Count > 1)
                {
                    _logger.LogWarning("Multiple default addresses found for UserId: {UserId}, Count: {Count}", userId, defaultAddresses.Count);
                }

                if (addresses.Any() && !addresses.Any(a => a.IsActive))
                {
                    _logger.LogWarning("No active addresses found for UserId: {UserId}", userId);
                }

                return CustomResponseDto<NoContentDto>.Success(new NoContentDto(), "Address consistency check completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in EnsureAddressConsistencyAsync for UserId: {UserId}", userId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while ensuring address consistency");
            }
        }

        public async Task<CustomResponseDto<int>> ArchiveOldAddressesAsync(Guid userId, int olderThanDays)
        {
            try
            {
                _logger.LogInformation("Archiving old addresses for UserId: {UserId}, OlderThanDays: {OlderThanDays}", userId, olderThanDays);

                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                var addressesResult = await GetUserAddressesAsync(userId, includeInactive: true);

                if (!addressesResult.IsSuccess)
                    return CustomResponseDto<int>.InternalServerError("Error retrieving addresses for archiving");

                var addresses = addressesResult.Data ?? new List<UserAddressDto>();
                var oldAddresses = addresses.Where(a =>
                    !a.IsDefault &&
                    a.CreatedAt < cutoffDate &&
                    (a.UpdatedAt == null || a.UpdatedAt < cutoffDate)
                ).ToList();

                var archivedCount = 0;
                foreach (var address in oldAddresses)
                {
                    var result = await DeleteUserAddressAsync(address.Id, userId, false);
                    if (result.IsSuccess)
                        archivedCount++;
                }

                _logger.LogInformation("Archived {Count} old addresses for UserId: {UserId}", archivedCount, userId);
                return CustomResponseDto<int>.Success(archivedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ArchiveOldAddressesAsync for UserId: {UserId}", userId);
                return CustomResponseDto<int>.InternalServerError("An error occurred while archiving old addresses");
            }
        }

        #endregion
    }
}
