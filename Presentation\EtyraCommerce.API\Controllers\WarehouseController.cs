using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EtyraCommerce.API.Controllers
{
    /// <summary>
    /// Warehouse management controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WarehouseController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<WarehouseController> _logger;

        public WarehouseController(IInventoryService inventoryService, ILogger<WarehouseController> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
        }

        /// <summary>
        /// Get all warehouses with pagination and filtering
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(CustomResponseDto<PagedResult<WarehouseDto>>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<PagedResult<WarehouseDto>>>> GetAllWarehouses(
            [FromQuery] string? searchTerm = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "Name",
            [FromQuery] string sortDirection = "ASC")
        {
            try
            {
                _logger.LogInformation("Getting all warehouses with pagination");

                var filterDto = new WarehouseFilterDto
                {
                    SearchTerm = searchTerm,
                    IsActive = isActive,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _inventoryService.GetAllWarehousesAsync(filterDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all warehouses");
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while retrieving warehouses"));
            }
        }

        /// <summary>
        /// Create new warehouse
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(CustomResponseDto<WarehouseDto>), 201)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        public async Task<ActionResult<CustomResponseDto<WarehouseDto>>> CreateWarehouse([FromBody] CreateWarehouseDto createDto)
        {
            try
            {
                _logger.LogInformation("Creating warehouse with code: {WarehouseCode}", createDto.Code);

                var result = await _inventoryService.CreateWarehouseAsync(createDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return CreatedAtAction(nameof(GetAllWarehouses), new { }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating warehouse with code: {WarehouseCode}", createDto.Code);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while creating warehouse"));
            }
        }

        /// <summary>
        /// Update warehouse
        /// </summary>
        [HttpPut]
        [ProducesResponseType(typeof(CustomResponseDto<WarehouseDto>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 404)]
        public async Task<ActionResult<CustomResponseDto<WarehouseDto>>> UpdateWarehouse([FromBody] UpdateWarehouseDto updateDto)
        {
            try
            {
                _logger.LogInformation("Updating warehouse: {WarehouseId}", updateDto.Id);

                var result = await _inventoryService.UpdateWarehouseAsync(updateDto);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        404 => NotFound(result),
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse: {WarehouseId}", updateDto.Id);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while updating warehouse"));
            }
        }

        /// <summary>
        /// Delete warehouse
        /// </summary>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(typeof(CustomResponseDto<bool>), 200)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 400)]
        [ProducesResponseType(typeof(CustomResponseDto<object>), 404)]
        public async Task<ActionResult<CustomResponseDto<bool>>> DeleteWarehouse(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting warehouse: {WarehouseId}", id);

                var result = await _inventoryService.DeleteWarehouseAsync(id);

                if (!result.IsSuccess)
                {
                    return result.StatusCode switch
                    {
                        404 => NotFound(result),
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting warehouse: {WarehouseId}", id);
                return StatusCode(500, CustomResponseDto<object>.InternalServerError("An error occurred while deleting warehouse"));
            }
        }
    }
}
