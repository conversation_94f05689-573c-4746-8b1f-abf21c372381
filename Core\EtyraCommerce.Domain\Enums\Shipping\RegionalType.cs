namespace EtyraCommerce.Domain.Enums.Shipping
{
    /// <summary>
    /// Types of regional administrative divisions in Europe
    /// </summary>
    public enum RegionalType
    {
        /// <summary>
        /// County (Romania: Județ)
        /// </summary>
        County = 1,

        /// <summary>
        /// State (Germany: Bundesland)
        /// </summary>
        State = 2,

        /// <summary>
        /// Region (France: Région)
        /// </summary>
        Region = 3,

        /// <summary>
        /// Province (Netherlands: Provincie)
        /// </summary>
        Province = 4,

        /// <summary>
        /// Department (France: Département)
        /// </summary>
        Department = 5,

        /// <summary>
        /// Canton (Switzerland: Kanton)
        /// </summary>
        Canton = 6
    }
}
