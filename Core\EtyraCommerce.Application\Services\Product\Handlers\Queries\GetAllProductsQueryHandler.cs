using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Product;
using EtyraCommerce.Application.Services.Product.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Product.Handlers.Queries
{
    /// <summary>
    /// Handler for getting all products with pagination and filtering
    /// </summary>
    public class GetAllProductsQueryHandler : IRequestHandler<GetAllProductsQuery, CustomResponseDto<PagedResult<ProductDto>>>
    {
        private readonly IProductProcessService _productProcessService;
        private readonly ILogger<GetAllProductsQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="productProcessService">Product process service</param>
        /// <param name="logger">Logger</param>
        public GetAllProductsQueryHandler(
            IProductProcessService productProcessService,
            ILogger<GetAllProductsQueryHandler> logger)
        {
            _productProcessService = productProcessService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the get all products query
        /// </summary>
        /// <param name="request">Get all products query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paged product DTO response</returns>
        public async Task<CustomResponseDto<PagedResult<ProductDto>>> Handle(GetAllProductsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get all products query - Page: {Page}, PageSize: {PageSize}, SearchTerm: {SearchTerm}",
                    request.Page, request.PageSize, request.SearchTerm);

                // Validation
                if (request.Page < 1)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page number must be greater than 0");

                if (request.PageSize < 1 || request.PageSize > 100)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Page size must be between 1 and 100");

                // Validate price range
                if (request.MinPrice.HasValue && request.MaxPrice.HasValue && request.MinPrice > request.MaxPrice)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum price cannot be greater than maximum price");

                // Validate stock range
                if (request.MinStock.HasValue && request.MaxStock.HasValue && request.MinStock > request.MaxStock)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Minimum stock cannot be greater than maximum stock");

                // Validate date ranges
                if (request.CreatedFrom.HasValue && request.CreatedTo.HasValue && request.CreatedFrom > request.CreatedTo)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Created from date cannot be after created to date");

                if (request.UpdatedFrom.HasValue && request.UpdatedTo.HasValue && request.UpdatedFrom > request.UpdatedTo)
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Updated from date cannot be after updated to date");

                // Validate sort direction
                if (!string.IsNullOrEmpty(request.SortDirection) &&
                    !request.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
                    !request.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
                    return CustomResponseDto<PagedResult<ProductDto>>.BadRequest("Sort direction must be 'asc' or 'desc'");

                // Create filter DTO for business logic
                var filterDto = new ProductFilterDto
                {
                    Page = request.Page,
                    PageSize = request.PageSize,
                    Status = request.Status,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    IsDigital = request.IsDigital,
                    Brand = request.Brand,
                    CategoryId = request.CategoryId,
                    MinPrice = request.MinPrice,
                    MaxPrice = request.MaxPrice,
                    MinStock = request.MinStock,
                    MaxStock = request.MaxStock,
                    CreatedFrom = request.CreatedFrom,
                    CreatedTo = request.CreatedTo,
                    UpdatedFrom = request.UpdatedFrom,
                    UpdatedTo = request.UpdatedTo,
                    SearchTerm = request.SearchTerm,
                    SKU = request.SKU,
                    Model = request.Model,
                    EAN = request.EAN,
                    Barcode = request.Barcode,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    IncludeDescriptions = request.IncludeDescriptions,
                    IncludeImages = request.IncludeImages,
                    IncludeCategories = request.IncludeCategories,
                    IncludeDiscounts = request.IncludeDiscounts,
                    IncludeAttributes = request.IncludeAttributes,
                    IncludeVariants = request.IncludeVariants,
                    IncludeInventory = request.IncludeInventory,
                    IncludeDeleted = request.IncludeDeleted,
                    LanguageCode = request.LanguageCode,
                    StoreId = request.StoreId
                };

                // Delegate to business logic service
                var result = await _productProcessService.ProcessGetAllProductsAsync(filterDto);

                _logger.LogInformation("Get all products query processed successfully - Total: {Total}, Page: {Page}",
                    result.Data?.TotalCount, request.Page);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get all products query - Page: {Page}, PageSize: {PageSize}",
                    request.Page, request.PageSize);
                return CustomResponseDto<PagedResult<ProductDto>>.InternalServerError("An error occurred while retrieving products");
            }
        }
    }
}
