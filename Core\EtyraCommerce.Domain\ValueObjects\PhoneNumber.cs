using System.Text.RegularExpressions;

namespace EtyraCommerce.Domain.ValueObjects
{
    /// <summary>
    /// Phone number value object with international format support
    /// </summary>
    public class PhoneNumber : IEquatable<PhoneNumber>
    {
        private static readonly Regex PhoneRegex = new(
            @"^\+?[1-9]\d{1,14}$",
            RegexOptions.Compiled);

        private static readonly Regex DigitsOnlyRegex = new(
            @"[^\d+]",
            RegexOptions.Compiled);

        public string Value { get; }
        public string CountryCode { get; }
        public string Number { get; }

        public PhoneNumber(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Phone number cannot be null or empty", nameof(value));

            var cleanedValue = CleanPhoneNumber(value);

            if (!IsValidPhoneNumber(cleanedValue))
                throw new ArgumentException($"Invalid phone number format: {value}", nameof(value));

            Value = cleanedValue;
            (CountryCode, Number) = ParsePhoneNumber(cleanedValue);
        }

        /// <summary>
        /// Creates phone number from string with validation
        /// </summary>
        public static PhoneNumber Create(string value) => new(value);

        /// <summary>
        /// Tries to create phone number, returns null if invalid
        /// </summary>
        public static PhoneNumber? TryCreate(string value)
        {
            try
            {
                return new PhoneNumber(value);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Validates phone number format
        /// </summary>
        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var cleaned = CleanPhoneNumber(phoneNumber);

            // Must start with + and have 7-15 digits total
            if (!PhoneRegex.IsMatch(cleaned))
                return false;

            // Remove + for length check
            var digitsOnly = cleaned.TrimStart('+');
            return digitsOnly.Length >= 7 && digitsOnly.Length <= 15;
        }

        /// <summary>
        /// Cleans phone number by removing non-digit characters except +
        /// </summary>
        private static string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // Remove all non-digit characters except +
            var cleaned = DigitsOnlyRegex.Replace(phoneNumber.Trim(), "");

            // Ensure it starts with +
            if (!cleaned.StartsWith('+'))
            {
                // Add + if it starts with 00
                if (cleaned.StartsWith("00"))
                    cleaned = "+" + cleaned[2..];
                else if (cleaned.Length > 0 && char.IsDigit(cleaned[0]))
                    cleaned = "+" + cleaned;
            }

            return cleaned;
        }

        /// <summary>
        /// Parses phone number into country code and number
        /// </summary>
        private static (string CountryCode, string Number) ParsePhoneNumber(string phoneNumber)
        {
            if (!phoneNumber.StartsWith('+'))
                return (string.Empty, phoneNumber);

            var digitsOnly = phoneNumber[1..]; // Remove +

            // Common country codes (1-4 digits)
            var countryCodeLength = GetCountryCodeLength(digitsOnly);

            if (countryCodeLength > 0 && countryCodeLength < digitsOnly.Length)
            {
                var countryCode = "+" + digitsOnly[..countryCodeLength];
                var number = digitsOnly[countryCodeLength..];
                return (countryCode, number);
            }

            return (string.Empty, digitsOnly);
        }

        /// <summary>
        /// Determines country code length based on common patterns
        /// </summary>
        private static int GetCountryCodeLength(string digitsOnly)
        {
            if (digitsOnly.Length < 2)
                return 0;

            // 1-digit country codes
            if (digitsOnly.StartsWith('1'))
                return 1;

            // 2-digit country codes
            if (digitsOnly.Length >= 2)
            {
                var twoDigit = digitsOnly[..2];
                if (IsCommonTwoDigitCountryCode(twoDigit))
                    return 2;
            }

            // 3-digit country codes
            if (digitsOnly.Length >= 3)
            {
                var threeDigit = digitsOnly[..3];
                if (IsCommonThreeDigitCountryCode(threeDigit))
                    return 3;
            }

            // Default to 2 digits for unknown codes
            return 2;
        }

        private static bool IsCommonTwoDigitCountryCode(string code)
        {
            var commonCodes = new[]
            {
                "20", "27", "30", "31", "32", "33", "34", "36", "39", "40",
                "41", "43", "44", "45", "46", "47", "48", "49", "51", "52",
                "53", "54", "55", "56", "57", "58", "60", "61", "62", "63",
                "64", "65", "66", "81", "82", "84", "86", "90", "91", "92",
                "93", "94", "95", "98"
            };

            return commonCodes.Contains(code);
        }

        private static bool IsCommonThreeDigitCountryCode(string code)
        {
            var commonCodes = new[]
            {
                "212", "213", "216", "218", "220", "221", "222", "223", "224",
                "225", "226", "227", "228", "229", "230", "231", "232", "233",
                "234", "235", "236", "237", "238", "239", "240", "241", "242",
                "243", "244", "245", "246", "247", "248", "249", "250", "251",
                "252", "253", "254", "255", "256", "257", "258", "260", "261",
                "262", "263", "264", "265", "266", "267", "268", "269", "290",
                "291", "297", "298", "299", "350", "351", "352", "353", "354",
                "355", "356", "357", "358", "359", "370", "371", "372", "373",
                "374", "375", "376", "377", "378", "380", "381", "382", "383",
                "385", "386", "387", "389", "420", "421", "423", "500", "501",
                "502", "503", "504", "505", "506", "507", "508", "509", "590",
                "591", "592", "593", "594", "595", "596", "597", "598", "599",
                "670", "672", "673", "674", "675", "676", "677", "678", "679",
                "680", "681", "682", "683", "684", "685", "686", "687", "688",
                "689", "690", "691", "692", "850", "852", "853", "855", "856",
                "880", "886", "960", "961", "962", "963", "964", "965", "966",
                "967", "968", "970", "971", "972", "973", "974", "975", "976",
                "977", "992", "993", "994", "995", "996", "998"
            };

            return commonCodes.Contains(code);
        }

        /// <summary>
        /// Formats phone number for display
        /// </summary>
        public string Format()
        {
            if (string.IsNullOrEmpty(CountryCode))
                return Value;

            return CountryCode switch
            {
                "+1" => FormatNorthAmerican(),
                "+90" => FormatTurkish(),
                "+44" => FormatUK(),
                "+49" => FormatGerman(),
                "+33" => FormatFrench(),
                _ => Value
            };
        }

        private string FormatNorthAmerican()
        {
            if (Number.Length == 10)
                return $"+1 ({Number[..3]}) {Number[3..6]}-{Number[6..]}";
            return Value;
        }

        private string FormatTurkish()
        {
            if (Number.Length == 10)
                return $"+90 ({Number[..3]}) {Number[3..6]} {Number[6..8]} {Number[8..]}";
            return Value;
        }

        private string FormatUK()
        {
            if (Number.Length == 10)
                return $"+44 {Number[..4]} {Number[4..7]} {Number[7..]}";
            return Value;
        }

        private string FormatGerman()
        {
            if (Number.Length >= 10)
                return $"+49 {Number[..3]} {Number[3..6]} {Number[6..]}";
            return Value;
        }

        private string FormatFrench()
        {
            if (Number.Length == 9)
                return $"+33 {Number[0]} {Number[1..3]} {Number[3..5]} {Number[5..7]} {Number[7..]}";
            return Value;
        }

        /// <summary>
        /// Gets masked phone number for display
        /// </summary>
        public string GetMasked()
        {
            if (Value.Length <= 6)
                return Value;

            var visibleStart = Math.Min(4, Value.Length / 3);
            var visibleEnd = Math.Min(2, Value.Length / 4);
            var maskedLength = Value.Length - visibleStart - visibleEnd;

            return Value[..visibleStart] + new string('*', maskedLength) + Value[^visibleEnd..];
        }

        /// <summary>
        /// Implicit conversion from string
        /// </summary>
        public static implicit operator string(PhoneNumber phoneNumber) => phoneNumber.Value;

        /// <summary>
        /// Explicit conversion from string
        /// </summary>
        public static explicit operator PhoneNumber(string value) => new(value);

        #region Equality

        public bool Equals(PhoneNumber? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Value == other.Value;
        }

        public override bool Equals(object? obj)
        {
            return obj is PhoneNumber other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Value.GetHashCode();
        }

        public static bool operator ==(PhoneNumber? left, PhoneNumber? right)
        {
            if (left is null && right is null) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(PhoneNumber? left, PhoneNumber? right) => !(left == right);

        #endregion

        public override string ToString() => Format();
    }
}
