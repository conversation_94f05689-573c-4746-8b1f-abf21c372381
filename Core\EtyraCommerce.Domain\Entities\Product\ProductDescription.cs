namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product description in different languages and stores
    /// </summary>
    public class ProductDescription : BaseEntity
    {
        /// <summary>
        /// Product name in specific language
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Product description in specific language
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Short description/summary
        /// </summary>
        public string? ShortDescription { get; set; }

        /// <summary>
        /// Meta title for SEO
        /// </summary>
        public string? MetaTitle { get; set; }

        /// <summary>
        /// Meta description for SEO
        /// </summary>
        public string? MetaDescription { get; set; }

        /// <summary>
        /// Meta keywords for SEO
        /// </summary>
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Product tags in specific language
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// SEO-friendly URL slug
        /// </summary>
        public string? Slug { get; set; }

        /// <summary>
        /// Language code (e.g., "en-US", "tr-TR")
        /// </summary>
        public string LanguageCode { get; set; } = "en-US";

        /// <summary>
        /// Store ID (for multi-store support)
        /// </summary>
        public Guid? StoreId { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        #endregion

        #region Business Methods

        /// <summary>
        /// Generates SEO-friendly slug from name
        /// </summary>
        public void GenerateSlug()
        {
            if (string.IsNullOrWhiteSpace(Name)) return;

            Slug = Name.ToLowerInvariant()
                      .Replace(" ", "-")
                      .Replace("_", "-")
                      .Trim('-');

            MarkAsUpdated();
        }

        /// <summary>
        /// Updates SEO meta information
        /// </summary>
        public void UpdateSeoMeta(string? metaTitle = null, string? metaDescription = null, string? metaKeywords = null)
        {
            if (metaTitle != null) MetaTitle = metaTitle;
            if (metaDescription != null) MetaDescription = metaDescription;
            if (metaKeywords != null) MetaKeywords = metaKeywords;

            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"ProductDescription [ProductId: {ProductId}, Language: {LanguageCode}, Name: {Name}]";
        }
    }
}
