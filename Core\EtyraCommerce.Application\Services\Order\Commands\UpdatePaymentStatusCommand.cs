using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Domain.Enums;
using MediatR;

namespace EtyraCommerce.Application.Services.Order.Commands
{
    /// <summary>
    /// Command to update payment status
    /// </summary>
    public class UpdatePaymentStatusCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Order ID
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// New payment status
        /// </summary>
        public PaymentStatus PaymentStatus { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Payment reference/transaction ID
        /// </summary>
        public string? PaymentReference { get; set; }

        /// <summary>
        /// Notes about payment status change
        /// </summary>
        public string? Notes { get; set; }
    }
}
