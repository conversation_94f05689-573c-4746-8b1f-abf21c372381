using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using MediatR;

namespace EtyraCommerce.Application.Services.User.Queries
{
    /// <summary>
    /// Query for getting a user by username
    /// </summary>
    public class GetUserByUsernameQuery : IRequest<CustomResponseDto<UserDto?>>
    {
        /// <summary>
        /// Username to search for
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Whether to include deleted users in the search
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// Whether to track the entity for changes
        /// </summary>
        public bool Tracking { get; set; } = false;

        public GetUserByUsernameQuery() { }

        public GetUserByUsernameQuery(string username, bool includeDeleted = false, bool tracking = false)
        {
            Username = username;
            IncludeDeleted = includeDeleted;
            Tracking = tracking;
        }
    }
}
