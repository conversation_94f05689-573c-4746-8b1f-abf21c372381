using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Domain.Entities.Inventory;
using EtyraCommerce.Domain.ValueObjects;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to create a new warehouse
    /// </summary>
    public class CreateWarehouseCommand : IRequest<CustomResponseDto<WarehouseDto>>
    {
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Address? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsMain { get; set; } = false;
        public WarehouseType Type { get; set; } = WarehouseType.Physical;
        public int SortOrder { get; set; } = 0;
    }
}
