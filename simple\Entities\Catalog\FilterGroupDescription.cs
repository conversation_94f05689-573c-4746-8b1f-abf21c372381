﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Settings;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Catalog;

public class FilterGroupDescription : BaseEntity
{
    public int FilterGroupId { get; set; }
    public FilterGroup FilterGroup { get; set; }

    [MaxLength(50)]
    public string Name { get; set; }
    public Language Language { get; set; }
    public int LanguageId { get; set; }
}