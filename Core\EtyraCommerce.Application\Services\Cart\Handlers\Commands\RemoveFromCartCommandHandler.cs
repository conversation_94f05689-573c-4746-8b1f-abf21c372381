using EtyraCommerce.Application.DTOs.Cart;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Cart.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Cart.Handlers.Commands
{
    /// <summary>
    /// Handler for RemoveFromCartCommand
    /// </summary>
    public class RemoveFromCartCommandHandler : IRequestHandler<RemoveFromCartCommand, CustomResponseDto<NoContentDto>>
    {
        private readonly ICartProcessService _cartProcessService;
        private readonly ILogger<RemoveFromCartCommandHandler> _logger;

        public RemoveFromCartCommandHandler(
            ICartProcessService cartProcessService,
            ILogger<RemoveFromCartCommandHandler> logger)
        {
            _cartProcessService = cartProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<NoContentDto>> Handle(RemoveFromCartCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing remove from cart command for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.ProductId, request.CustomerId, request.SessionId);

                // Validation
                if (request.ProductId == Guid.Empty)
                    return CustomResponseDto<NoContentDto>.BadRequest("Product ID is required");

                if (!request.CustomerId.HasValue && string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<NoContentDto>.BadRequest("Either Customer ID or Session ID is required");

                if (request.CustomerId.HasValue && !string.IsNullOrEmpty(request.SessionId))
                    return CustomResponseDto<NoContentDto>.BadRequest("Cannot specify both Customer ID and Session ID");

                // Create DTO
                var removeFromCartDto = new RemoveFromCartDto
                {
                    ProductId = request.ProductId
                };

                // Delegate to process service
                var result = await _cartProcessService.ProcessRemoveFromCartAsync(
                    removeFromCartDto,
                    request.CustomerId,
                    request.SessionId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Remove from cart command processed successfully for ProductId: {ProductId}",
                        request.ProductId);
                }
                else
                {
                    _logger.LogWarning("Remove from cart command failed for ProductId: {ProductId}, Errors: {Errors}",
                        request.ProductId, string.Join(", ", result.ErrorList));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing remove from cart command for ProductId: {ProductId}, CustomerId: {CustomerId}, SessionId: {SessionId}",
                    request.ProductId, request.CustomerId, request.SessionId);
                return CustomResponseDto<NoContentDto>.InternalServerError("An error occurred while removing item from cart");
            }
        }
    }
}
