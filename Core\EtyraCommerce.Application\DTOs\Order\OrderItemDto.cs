using System.ComponentModel.DataAnnotations;

namespace EtyraCommerce.Application.DTOs.Order
{
    /// <summary>
    /// Order item data transfer object
    /// </summary>
    public class OrderItemDto
    {
        /// <summary>
        /// Order item ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Product name (snapshot)
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// Product SKU (snapshot)
        /// </summary>
        public string ProductSku { get; set; } = string.Empty;

        /// <summary>
        /// Unit price
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Quantity
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Total price for this line item
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// Discount amount (optional)
        /// </summary>
        public decimal? DiscountAmount { get; set; }

        /// <summary>
        /// Tax amount (optional)
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// Product variant information
        /// </summary>
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special instructions
        /// </summary>
        public string? SpecialInstructions { get; set; }

        /// <summary>
        /// Order ID this item belongs to
        /// </summary>
        public Guid OrderId { get; set; }

        /// <summary>
        /// Final price after discount
        /// </summary>
        public decimal FinalPrice => TotalPrice - (DiscountAmount ?? 0);

        /// <summary>
        /// Total price including tax
        /// </summary>
        public decimal TotalPriceWithTax => FinalPrice + (TaxAmount ?? 0);

        /// <summary>
        /// Display name including variant info
        /// </summary>
        public string DisplayName => string.IsNullOrEmpty(VariantInfo)
            ? ProductName
            : $"{ProductName} ({VariantInfo})";

        /// <summary>
        /// Discount percentage (if discount applied)
        /// </summary>
        public decimal? DiscountPercentage => DiscountAmount.HasValue && TotalPrice > 0
            ? (DiscountAmount.Value / TotalPrice) * 100
            : null;

        /// <summary>
        /// Indicates if item has discount
        /// </summary>
        public bool HasDiscount => DiscountAmount.HasValue && DiscountAmount.Value > 0;

        /// <summary>
        /// Indicates if item has tax
        /// </summary>
        public bool HasTax => TaxAmount.HasValue && TaxAmount.Value > 0;
    }

    /// <summary>
    /// Create order item DTO
    /// </summary>
    public class CreateOrderItemDto
    {
        /// <summary>
        /// Product ID
        /// </summary>
        [Required]
        public Guid ProductId { get; set; }

        /// <summary>
        /// Quantity to order
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        public int Quantity { get; set; }

        /// <summary>
        /// Product variant information (optional)
        /// </summary>
        [MaxLength(500)]
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special instructions for this item (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? SpecialInstructions { get; set; }
    }

    /// <summary>
    /// Update order item DTO
    /// </summary>
    public class UpdateOrderItemDto
    {
        /// <summary>
        /// Order item ID
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// New quantity
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        public int Quantity { get; set; }

        /// <summary>
        /// Product variant information (optional)
        /// </summary>
        [MaxLength(500)]
        public string? VariantInfo { get; set; }

        /// <summary>
        /// Special instructions for this item (optional)
        /// </summary>
        [MaxLength(1000)]
        public string? SpecialInstructions { get; set; }
    }
}
