using EtyraCommerce.Domain.Entities.Inventory;
using EtyraCommerce.Domain.ValueObjects;

namespace EtyraCommerce.Application.DTOs.Inventory
{
    /// <summary>
    /// Warehouse data transfer object
    /// </summary>
    public class WarehouseDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Address? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; }
        public bool IsMain { get; set; }
        public WarehouseType Type { get; set; }
        public int SortOrder { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Statistics
        public int TotalProducts { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
    }

    /// <summary>
    /// Create warehouse DTO
    /// </summary>
    public class CreateWarehouseDto
    {
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Address? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsMain { get; set; } = false;
        public WarehouseType Type { get; set; } = WarehouseType.Physical;
        public int SortOrder { get; set; } = 0;
    }

    /// <summary>
    /// Update warehouse DTO
    /// </summary>
    public class UpdateWarehouseDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Address? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; }
        public bool IsMain { get; set; }
        public WarehouseType Type { get; set; }
        public int SortOrder { get; set; }
    }

    /// <summary>
    /// Warehouse summary DTO
    /// </summary>
    public class WarehouseSummaryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsMain { get; set; }
        public WarehouseType Type { get; set; }
        public int TotalProducts { get; set; }
        public int TotalStock { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal StockValue { get; set; }
    }

    /// <summary>
    /// Warehouse filter DTO
    /// </summary>
    public class WarehouseFilterDto
    {
        public string? SearchTerm { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsMain { get; set; }
        public WarehouseType? Type { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "Name";
        public string? SortDirection { get; set; } = "asc";
    }
}
