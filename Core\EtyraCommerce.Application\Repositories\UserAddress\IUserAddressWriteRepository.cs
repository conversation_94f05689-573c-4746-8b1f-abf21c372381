namespace EtyraCommerce.Application.Repositories.UserAddress
{
    /// <summary>
    /// Write repository interface for UserAddress entity
    /// Provides write operations for user addresses
    /// </summary>
    public interface IUserAddressWriteRepository : IWriteRepository<Domain.Entities.User.UserAddress>
    {
        /// <summary>
        /// Sets an address as the default address for a user
        /// Automatically removes default status from other addresses of the same user
        /// </summary>
        /// <param name="addressId">ID of the address to set as default</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SetAsDefaultAddressAsync(Guid addressId, Guid userId);

        /// <summary>
        /// Removes default status from all addresses of a user
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task RemoveAllDefaultStatusAsync(Guid userId);

        /// <summary>
        /// Activates an address
        /// </summary>
        /// <param name="addressId">ID of the address to activate</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ActivateAddressAsync(Guid addressId);

        /// <summary>
        /// Deactivates an address
        /// </summary>
        /// <param name="addressId">ID of the address to deactivate</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeactivateAddressAsync(Guid addressId);

        /// <summary>
        /// Bulk activates multiple addresses
        /// </summary>
        /// <param name="addressIds">List of address IDs to activate</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task BulkActivateAddressesAsync(List<Guid> addressIds);

        /// <summary>
        /// Bulk deactivates multiple addresses
        /// </summary>
        /// <param name="addressIds">List of address IDs to deactivate</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task BulkDeactivateAddressesAsync(List<Guid> addressIds);

        /// <summary>
        /// Soft deletes all addresses for a user
        /// Used when a user account is deleted
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SoftDeleteUserAddressesAsync(Guid userId);

        /// <summary>
        /// Hard deletes all addresses for a user
        /// Used for GDPR compliance or complete data removal
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task HardDeleteUserAddressesAsync(Guid userId);

        /// <summary>
        /// Updates the last used timestamp for an address
        /// Used for tracking recently used addresses
        /// </summary>
        /// <param name="addressId">ID of the address</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task UpdateLastUsedAsync(Guid addressId);

        /// <summary>
        /// Bulk updates multiple addresses
        /// </summary>
        /// <param name="addresses">List of addresses to update</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task BulkUpdateAsync(List<Domain.Entities.User.UserAddress> addresses);

        /// <summary>
        /// Creates multiple addresses in a single transaction
        /// </summary>
        /// <param name="addresses">List of addresses to create</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task BulkAddAsync(List<Domain.Entities.User.UserAddress> addresses);

        /// <summary>
        /// Validates and ensures only one default address per user
        /// Utility method to fix data inconsistencies
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task EnsureSingleDefaultAddressAsync(Guid userId);

        /// <summary>
        /// Archives old addresses (soft delete addresses older than specified days)
        /// Used for data cleanup and maintenance
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="olderThanDays">Archive addresses older than this many days</param>
        /// <returns>Number of addresses archived</returns>
        Task<int> ArchiveOldAddressesAsync(Guid userId, int olderThanDays);

        /// <summary>
        /// Restores a soft-deleted address
        /// </summary>
        /// <param name="addressId">ID of the address to restore</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task RestoreAddressAsync(Guid addressId);

        /// <summary>
        /// Bulk restores multiple soft-deleted addresses
        /// </summary>
        /// <param name="addressIds">List of address IDs to restore</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task BulkRestoreAddressesAsync(List<Guid> addressIds);
    }
}
