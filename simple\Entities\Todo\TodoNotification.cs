using EtyraApp.Domain.Entities.Account;
using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EtyraApp.Domain.Entities;

public class TodoNotification : BaseEntity
{
    public int UserId { get; set; }
    
    [ForeignKey("UserId")]
    public User User { get; set; }

    public int TodoId { get; set; }
    
    [ForeignKey("TodoId")]
    public Todo Todo { get; set; }
    
    [MaxLength(50)] public string? Title { get; set; }
    [MaxLength(500)] public string? Message { get; set; }
    [MaxLength(50)] public string? Type { get; set; }
    public bool IsRead { get; set; }
}