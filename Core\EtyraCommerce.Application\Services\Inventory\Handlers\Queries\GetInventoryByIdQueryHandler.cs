using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Inventory;
using EtyraCommerce.Application.Services.Inventory.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Inventory.Handlers.Queries
{
    /// <summary>
    /// Handler for getting inventory by ID
    /// </summary>
    public class GetInventoryByIdQueryHandler : IRequestHandler<GetInventoryByIdQuery, CustomResponseDto<InventoryDto>>
    {
        private readonly IInventoryProcessService _inventoryProcessService;
        private readonly ILogger<GetInventoryByIdQueryHandler> _logger;

        public GetInventoryByIdQueryHandler(IInventoryProcessService inventoryProcessService, ILogger<GetInventoryByIdQueryHandler> logger)
        {
            _inventoryProcessService = inventoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<InventoryDto>> Handle(GetInventoryByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling get inventory by ID query: {InventoryId}", request.Id);

                // Validation
                if (request.Id == Guid.Empty)
                    return CustomResponseDto<InventoryDto>.BadRequest("Inventory ID is required");

                // Delegate to process service
                var result = await _inventoryProcessService.ProcessGetInventoryByIdAsync(request.Id);

                _logger.LogInformation("Get inventory by ID query handled. Success: {IsSuccess}", result.IsSuccess);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling get inventory by ID query: {InventoryId}", request.Id);
                return CustomResponseDto<InventoryDto>.InternalServerError("An error occurred while retrieving inventory");
            }
        }
    }
}
