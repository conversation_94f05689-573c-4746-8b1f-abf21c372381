using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.Order;
using EtyraCommerce.Application.Services.Order.Commands;
using EtyraCommerce.Domain.Enums;

namespace EtyraCommerce.Application.Services.Order
{
    /// <summary>
    /// Order process service interface for write operations and business logic
    /// </summary>
    public interface IOrderProcessService
    {
        #region Order Queries

        /// <summary>
        /// Gets order by ID with business logic
        /// </summary>
        Task<CustomResponseDto<OrderDto>> GetOrderByIdAsync(Guid orderId, bool includeItems = true, bool includeCustomer = false, string? languageCode = null);

        /// <summary>
        /// Searches orders with business logic
        /// </summary>
        Task<CustomResponseDto<PagedResult<OrderDto>>> SearchOrdersAsync(OrderSearchDto searchDto);

        /// <summary>
        /// Gets orders by customer ID with business logic
        /// </summary>
        Task<CustomResponseDto<PagedResult<OrderDto>>> GetOrdersByCustomerIdAsync(Guid customerId, int page = 1, int pageSize = 10);

        /// <summary>
        /// Gets order statistics with business logic
        /// </summary>
        Task<CustomResponseDto<OrderStatisticsDto>> GetOrderStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? customerId = null,
            string? currency = null,
            int topCustomersCount = 10,
            int recentOrdersCount = 5);

        /// <summary>
        /// Gets order by order number with business logic
        /// </summary>
        Task<CustomResponseDto<OrderDto>> GetOrderByOrderNumberAsync(string orderNumber, bool includeItems = true, bool includeCustomer = false, string? languageCode = null);

        #endregion

        // #region Order Commands

        /// <summary>
        /// Processes order creation
        /// </summary>
        Task<CustomResponseDto<OrderDto>> ProcessCreateOrderAsync(CreateOrderDto createOrderDto);

        /// <summary>
        /// Creates an order from shopping cart
        /// </summary>
        Task<CustomResponseDto<OrderDto>> CreateOrderFromCartAsync(CreateOrderFromCartCommand command);

        /// <summary>
        /// Processes order status update
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessUpdateOrderStatusAsync(
            Guid orderId,
            OrderStatus status,
            string? reason = null,
            string? trackingNumber = null,
            DateTime? expectedDeliveryDate = null,
            DateTime? actualDeliveryDate = null);

        /// <summary>
        /// Processes payment status update
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessUpdatePaymentStatusAsync(
            Guid orderId,
            PaymentStatus paymentStatus,
            string? paymentMethod = null,
            string? paymentReference = null,
            string? notes = null);

        /// <summary>
        /// Processes order cancellation
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessCancelOrderAsync(
            Guid orderId,
            string? reason = null,
            Guid? cancelledBy = null);

        /// <summary>
        /// Processes order confirmation
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessConfirmOrderAsync(Guid orderId);

        /// <summary>
        /// Processes order shipment
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessShipOrderAsync(
            Guid orderId,
            string? trackingNumber = null,
            DateTime? expectedDeliveryDate = null);

        /// <summary>
        /// Processes order delivery
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessDeliverOrderAsync(
            Guid orderId,
            DateTime? deliveryDate = null);

        /// <summary>
        /// Processes adding item to order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessAddOrderItemAsync(
            Guid orderId,
            CreateOrderItemDto orderItemDto);

        /// <summary>
        /// Processes removing item from order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessRemoveOrderItemAsync(
            Guid orderId,
            Guid productId);

        /// <summary>
        /// Processes updating order item quantity
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessUpdateOrderItemQuantityAsync(
            Guid orderId,
            Guid productId,
            int newQuantity);

        /// <summary>
        /// Processes applying discount to order
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessApplyOrderDiscountAsync(
            Guid orderId,
            decimal discountAmount);

        /// <summary>
        /// Processes setting shipping cost
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessSetShippingCostAsync(
            Guid orderId,
            decimal shippingCost,
            string? shippingMethod = null);

        /// <summary>
        /// Processes setting tax amount
        /// </summary>
        Task<CustomResponseDto<NoContentDto>> ProcessSetTaxAmountAsync(
            Guid orderId,
            decimal taxAmount);
    }
}
