using EtyraCommerce.Application.Repositories.Currency;
using EtyraCommerce.Persistence.Contexts;
using EtyraCommerce.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EtyraCommerce.Persistence.Repositories.Currency;

/// <summary>
/// Write repository implementation for Currency entity
/// </summary>
public class CurrencyWriteRepository : WriteRepository<Domain.Entities.Currency.Currency>, ICurrencyWriteRepository
{
    public CurrencyWriteRepository(EtyraCommerceDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Set currency as default (and unset others)
    /// </summary>
    /// <param name="currencyId">Currency ID to set as default</param>
    /// <returns>Updated currency or null if not found</returns>
    public async Task<Domain.Entities.Currency.Currency?> SetAsDefaultAsync(Guid currencyId)
    {
        // First, unset all other default currencies
        await Table
            .Where(c => c.IsDefault)
            .ExecuteUpdateAsync(c => c.SetProperty(x => x.IsDefault, false));

        // Then set the specified currency as default
        var currency = await Table.FirstOrDefaultAsync(c => c.Id == currencyId);
        if (currency != null)
        {
            currency.SetAsDefault();
            await _context.SaveChangesAsync();
        }

        return currency;
    }

    /// <summary>
    /// Update currency status
    /// </summary>
    /// <param name="id">Currency ID</param>
    /// <param name="isActive">New active status</param>
    /// <returns>Updated currency or null if not found</returns>
    public async Task<Domain.Entities.Currency.Currency?> UpdateStatusAsync(Guid id, bool isActive)
    {
        var currency = await Table.FirstOrDefaultAsync(c => c.Id == id);
        if (currency != null)
        {
            currency.IsActive = isActive;
            await _context.SaveChangesAsync();
        }

        return currency;
    }

    /// <summary>
    /// Update display order for multiple currencies
    /// </summary>
    /// <param name="orderUpdates">Dictionary of currency ID and new display order</param>
    /// <returns>Number of updated records</returns>
    public async Task<int> UpdateDisplayOrdersAsync(Dictionary<Guid, int> orderUpdates)
    {
        var updatedCount = 0;

        foreach (var update in orderUpdates)
        {
            var currency = await Table.FirstOrDefaultAsync(c => c.Id == update.Key);
            if (currency != null)
            {
                currency.DisplayOrder = update.Value;
                updatedCount++;
            }
        }

        if (updatedCount > 0)
        {
            await _context.SaveChangesAsync();
        }

        return updatedCount;
    }

    /// <summary>
    /// Bulk update currencies
    /// </summary>
    /// <param name="currencies">Currencies to update</param>
    /// <returns>Number of updated records</returns>
    public async Task<int> BulkUpdateAsync(IEnumerable<Domain.Entities.Currency.Currency> currencies)
    {
        var currencyList = currencies.ToList();
        
        foreach (var currency in currencyList)
        {
            _context.Entry(currency).State = EntityState.Modified;
        }

        return await _context.SaveChangesAsync();
    }
}
