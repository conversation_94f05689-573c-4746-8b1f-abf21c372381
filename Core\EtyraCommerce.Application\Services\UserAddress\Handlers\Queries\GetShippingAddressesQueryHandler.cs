using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.UserAddress.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.UserAddress.Handlers.Queries
{
    /// <summary>
    /// Handler for getting addresses suitable for shipping
    /// Delegates to UserAddressProcessService for business logic
    /// </summary>
    public class GetShippingAddressesQueryHandler : IRequestHandler<GetShippingAddressesQuery, CustomResponseDto<List<UserAddressDto>>>
    {
        private readonly IUserAddressProcessService _userAddressProcessService;
        private readonly ILogger<GetShippingAddressesQueryHandler> _logger;

        public GetShippingAddressesQueryHandler(
            IUserAddressProcessService userAddressProcessService,
            ILogger<GetShippingAddressesQueryHandler> logger)
        {
            _userAddressProcessService = userAddressProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<List<UserAddressDto>>> Handle(GetShippingAddressesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Handling GetShippingAddressesQuery for UserId: {UserId}, IncludeInactive: {IncludeInactive}",
                    request.UserId, request.IncludeInactive);

                // Validate request
                if (request.UserId == Guid.Empty)
                {
                    _logger.LogWarning("GetShippingAddressesQuery received with empty UserId");
                    return CustomResponseDto<List<UserAddressDto>>.BadRequest("User ID is required");
                }

                // Delegate to process service
                var result = await _userAddressProcessService.ProcessGetShippingAddressesAsync(request.UserId, request.IncludeInactive);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("GetShippingAddressesQuery handled successfully for UserId: {UserId}, Count: {Count}",
                        request.UserId, result.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("GetShippingAddressesQuery failed for UserId: {UserId}, Error: {Error}",
                        request.UserId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling GetShippingAddressesQuery for UserId: {UserId}", request.UserId);
                return CustomResponseDto<List<UserAddressDto>>.InternalServerError("An error occurred while retrieving shipping addresses");
            }
        }
    }
}
