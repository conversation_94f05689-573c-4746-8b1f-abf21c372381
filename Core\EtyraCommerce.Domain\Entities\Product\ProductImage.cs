namespace EtyraCommerce.Domain.Entities.Product
{
    /// <summary>
    /// Product image entity
    /// </summary>
    public class ProductImage : BaseEntity
    {
        /// <summary>
        /// Image URL or path
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Thumbnail image URL or path
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// Alternative text for accessibility
        /// </summary>
        public string? AltText { get; set; }

        /// <summary>
        /// Image title
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Image description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// Whether this is the main product image
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// Image type
        /// </summary>
        public ProductImageType Type { get; set; } = ProductImageType.Gallery;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// Image width in pixels
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// Image height in pixels
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// MIME type (e.g., "image/jpeg", "image/png")
        /// </summary>
        public string? MimeType { get; set; }

        /// <summary>
        /// Original filename
        /// </summary>
        public string? OriginalFileName { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Related product
        /// </summary>
        public Product Product { get; set; } = null!;

        /// <summary>
        /// Product ID
        /// </summary>
        public Guid ProductId { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the display URL (thumbnail if available, otherwise main image)
        /// </summary>
        public string DisplayUrl => !string.IsNullOrEmpty(ThumbnailUrl) ? ThumbnailUrl : ImageUrl;

        /// <summary>
        /// Gets the file extension from URL
        /// </summary>
        public string? FileExtension
        {
            get
            {
                if (string.IsNullOrEmpty(ImageUrl)) return null;
                return Path.GetExtension(ImageUrl)?.ToLowerInvariant();
            }
        }

        /// <summary>
        /// Checks if the image is a valid web format
        /// </summary>
        public bool IsValidWebFormat
        {
            get
            {
                var validExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg" };
                return validExtensions.Contains(FileExtension);
            }
        }

        /// <summary>
        /// Gets formatted file size
        /// </summary>
        public string FormattedFileSize
        {
            get
            {
                if (!FileSize.HasValue) return "Unknown";

                var size = FileSize.Value;
                if (size < 1024) return $"{size} B";
                if (size < 1024 * 1024) return $"{size / 1024:F1} KB";
                if (size < 1024 * 1024 * 1024) return $"{size / (1024 * 1024):F1} MB";
                return $"{size / (1024 * 1024 * 1024):F1} GB";
            }
        }

        /// <summary>
        /// Gets image dimensions as string
        /// </summary>
        public string? Dimensions
        {
            get
            {
                if (!Width.HasValue || !Height.HasValue) return null;
                return $"{Width} × {Height}";
            }
        }

        #endregion

        #region Business Methods

        /// <summary>
        /// Sets this image as the main product image
        /// </summary>
        public void SetAsMain()
        {
            IsMain = true;
            Type = ProductImageType.Main;
            MarkAsUpdated();
        }

        /// <summary>
        /// Removes main image status
        /// </summary>
        public void RemoveAsMain()
        {
            IsMain = false;
            if (Type == ProductImageType.Main)
                Type = ProductImageType.Gallery;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates sort order
        /// </summary>
        public void UpdateSortOrder(int order)
        {
            SortOrder = order;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates image metadata
        /// </summary>
        public void UpdateMetadata(string? altText = null, string? title = null, string? description = null)
        {
            if (altText != null) AltText = altText;
            if (title != null) Title = title;
            if (description != null) Description = description;
            MarkAsUpdated();
        }

        /// <summary>
        /// Updates image dimensions and file info
        /// </summary>
        public void UpdateFileInfo(int? width = null, int? height = null, long? fileSize = null, string? mimeType = null)
        {
            if (width.HasValue) Width = width;
            if (height.HasValue) Height = height;
            if (fileSize.HasValue) FileSize = fileSize;
            if (mimeType != null) MimeType = mimeType;
            MarkAsUpdated();
        }

        #endregion

        public override string ToString()
        {
            return $"ProductImage [Id: {Id}, ProductId: {ProductId}, Type: {Type}, IsMain: {IsMain}, Url: {ImageUrl}]";
        }
    }

    /// <summary>
    /// Product image type enumeration
    /// </summary>
    public enum ProductImageType
    {
        Main = 0,
        Gallery = 1,
        Thumbnail = 2,
        Zoom = 3,
        Variant = 4
    }
}
