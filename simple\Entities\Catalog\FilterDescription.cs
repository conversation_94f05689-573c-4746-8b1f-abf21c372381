﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Settings;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Catalog;

public class FilterDescription : BaseEntity
{

    [MaxLength(50)]
    public string Name { get; set; }

    public Language Language { get; set; }
    public int? LanguageId { get; set; }

    public int? FilterId { get; set; }
    public Filter Filter { get; set; }
}