﻿using EtyraApp.Domain.Entities.Common;
using EtyraApp.Domain.Entities.Integrations;
using EtyraApp.Domain.Entities.RelationsTable;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Catalog;

public class Product : BaseEntity
{
    [MaxLength(20)]
    public string Model { get; set; }

    [MaxLength(120)]
    public string? ProductName { get; set; }
    public int? Stars { get; set; }

    public int? AiPlatformId { get; set; }

    //[MaxLength(12)]
    //public string? HSCode { get; set; }

    //public HsCode HsCode{ get; set; }
    //public int HsCodeId { get; set; }

    [MaxLength(13)]
    public string? EAN { get; set; }

    [MaxLength(12)]
    public string? MPN { get; set; }

    [MaxLength(20)]
    public string? Barcode { get; set; }

    [MaxLength(20)]
    public string? Brand { get; set; }

    [MaxLength(12)]
    public string? UPC { get; set; }

    public decimal? Depth { get; set; }
    public decimal? Height { get; set; }
    public decimal? Width { get; set; }
    public decimal? Weight { get; set; }

    [MaxLength(500)]
    public string? Image { get; set; }

    [MaxLength(500)]
    public string? ThumbnailImage { get; set; }

    public ICollection<ProductDescription> ProductDescriptions { get; set; }
    public ICollection<ProductCategory> ProductCategories { get; set; }
    public ICollection<ProductImage>? ProductImages { get; set; }
    public ICollection<ProductDiscount>? ProductDiscounts { get; set; }
    public Todo? Todo { get; set; }

    public ICollection<WarehouseProduct>? WarehouseProducts { get; set; }
    public ICollection<BazaarProduct>? BazaarProducts { get; set; }
    public ICollection<OrderProduct>? OrderProducts { get; set; }
    public ICollection<ProductStore>? ProductStores { get; set; }

    public ICollection<SupplierProduct>? SupplierProducts { get; set; }
    public ICollection<ManufacturerProduct>? ManufacturerProducts { get; set; }
    public ICollection<IntegrationStoreProduct>? IntegrationStoreProducts { get; set; }
    public ICollection<IntegrationBazaarProduct>? IntegrationBazaarProducts { get; set; }

    public ICollection<ProductFilter>? ProductFilters { get; set; }
    public ICollection<ProductEmagCharacteristic>? ProductEmagCharacteristics { get; set; }


    public ICollection<IntegrationCompetitorProduct>? IntegrationCompetitorProducts { get; set; }
    public ICollection<ProductPriceAndQuantityChange>? ProductPriceAndQuantityChanges { get; set; }



}
