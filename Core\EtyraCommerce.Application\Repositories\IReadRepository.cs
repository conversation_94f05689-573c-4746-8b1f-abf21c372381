﻿using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Domain.Entities;
using System.Linq.Expressions;

namespace EtyraCommerce.Application.Repositories
{
    /// <summary>
    /// Read repository interface for querying entities
    /// </summary>
    /// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>

    public interface IReadRepository<T> : IRepository<T> where T : BaseEntity
    {
        /// <summary>
        /// Gets all entities
        /// </summary>
        Task<IQueryable<T>> GetAllAsync(bool tracking = true);

        /// <summary>
        /// Gets entities matching the expression
        /// </summary>
        Task<IQueryable<T>> GetWhereAsync(Expression<Func<T, bool>> expression, bool tracking = true);

        /// <summary>
        /// Gets first entity matching the expression or null
        /// </summary>
        Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> expression, bool tracking = true);

        /// <summary>
        /// Gets entity by ID or null if not found
        /// </summary>
        Task<T?> GetByIdAsync(Guid id, bool tracking = true);

        /// <summary>
        /// Checks if any entity matches the expression
        /// </summary>
        Task<bool> AnyAsync(Expression<Func<T, bool>> expression, bool tracking = false);

        /// <summary>
        /// Gets count of entities matching the expression
        /// </summary>
        Task<int> CountAsync(Expression<Func<T, bool>>? expression = null);

        /// <summary>
        /// Gets paginated results
        /// </summary>
        Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize, bool tracking = true);

        /// <summary>
        /// Gets paginated results matching the expression
        /// </summary>
        Task<PagedResult<T>> GetPagedWhereAsync(Expression<Func<T, bool>> expression, int pageNumber, int pageSize, bool tracking = true);
    }
}
