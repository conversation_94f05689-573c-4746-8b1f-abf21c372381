using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.Common;
using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Category.Queries
{
    /// <summary>
    /// Query for getting category tree (hierarchical structure)
    /// </summary>
    public class GetCategoryTreeQuery : IRequest<CustomResponseDto<List<CategoryDto>>>
    {
        /// <summary>
        /// Root category ID to start from (null for full tree)
        /// </summary>
        public Guid? RootCategoryId { get; set; }

        /// <summary>
        /// Maximum depth to retrieve (null for unlimited)
        /// </summary>
        public int? MaxDepth { get; set; }

        /// <summary>
        /// Whether to include only active categories
        /// </summary>
        public bool ActiveOnly { get; set; } = true;

        /// <summary>
        /// Whether to include only menu categories
        /// </summary>
        public bool MenuOnly { get; set; } = false;

        /// <summary>
        /// Whether to include descriptions in all languages
        /// </summary>
        public bool IncludeDescriptions { get; set; } = true;

        /// <summary>
        /// Specific language code to filter descriptions
        /// </summary>
        public string? LanguageCode { get; set; }

        /// <summary>
        /// Sort field
        /// </summary>
        public CategorySortField SortBy { get; set; } = CategorySortField.SortOrder;

        /// <summary>
        /// Sort direction
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

        /// <summary>
        /// Creates query for full category tree
        /// </summary>
        public static GetCategoryTreeQuery FullTree(bool activeOnly = true, bool menuOnly = false, int? maxDepth = null, string? languageCode = null)
        {
            return new GetCategoryTreeQuery
            {
                RootCategoryId = null,
                MaxDepth = maxDepth,
                ActiveOnly = activeOnly,
                MenuOnly = menuOnly,
                IncludeDescriptions = true,
                LanguageCode = languageCode
            };
        }

        /// <summary>
        /// Creates query for subtree starting from specific category
        /// </summary>
        public static GetCategoryTreeQuery SubTree(Guid rootCategoryId, bool activeOnly = true, bool menuOnly = false, int? maxDepth = null, string? languageCode = null)
        {
            return new GetCategoryTreeQuery
            {
                RootCategoryId = rootCategoryId,
                MaxDepth = maxDepth,
                ActiveOnly = activeOnly,
                MenuOnly = menuOnly,
                IncludeDescriptions = true,
                LanguageCode = languageCode
            };
        }

        /// <summary>
        /// Creates query for menu tree
        /// </summary>
        public static GetCategoryTreeQuery MenuTree(int? maxDepth = 3, string? languageCode = null)
        {
            return new GetCategoryTreeQuery
            {
                RootCategoryId = null,
                MaxDepth = maxDepth,
                ActiveOnly = true,
                MenuOnly = true,
                IncludeDescriptions = true,
                LanguageCode = languageCode
            };
        }
    }
}
