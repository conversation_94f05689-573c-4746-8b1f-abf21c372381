using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Product.Commands
{
    /// <summary>
    /// Command for deleting a product
    /// </summary>
    public class DeleteProductCommand : IRequest<CustomResponseDto<NoContentDto>>
    {
        /// <summary>
        /// Product ID to delete
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Whether to perform hard delete (permanent) or soft delete
        /// Default is false (soft delete)
        /// </summary>
        public bool HardDelete { get; set; } = false;

        /// <summary>
        /// Whether to force delete even if product has dependencies
        /// Default is false (check dependencies)
        /// </summary>
        public bool ForceDelete { get; set; } = false;

        /// <summary>
        /// Reason for deletion (optional)
        /// </summary>
        public string? DeletionReason { get; set; }

        /// <summary>
        /// Constructor for basic soft delete
        /// </summary>
        public DeleteProductCommand()
        {
        }

        /// <summary>
        /// Constructor with product ID
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        public DeleteProductCommand(Guid productId)
        {
            ProductId = productId;
        }

        /// <summary>
        /// Constructor with full options
        /// </summary>
        /// <param name="productId">Product ID to delete</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="forceDelete">Whether to force delete</param>
        /// <param name="deletionReason">Reason for deletion</param>
        public DeleteProductCommand(Guid productId, bool hardDelete = false, bool forceDelete = false, string? deletionReason = null)
        {
            ProductId = productId;
            HardDelete = hardDelete;
            ForceDelete = forceDelete;
            DeletionReason = deletionReason;
        }
    }
}
