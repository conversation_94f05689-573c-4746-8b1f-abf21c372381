using EtyraApp.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace EtyraApp.Domain.Entities.Analysis;

public class AiAgent : BaseEntity
{
    [MaxLength(100)]
    public string Name { get; set; }
    
    [MaxLength(500)]
    public string Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [MaxLength(50)]
    public string AgentType { get; set; } // ProductAnalysis, CompetitorAnalysis, SalesAnalysis, etc.
    
    [MaxLength(100)]
    public string Model { get; set; } // GPT-4, <PERSON>, etc.
    
    public int AiPlatformId { get; set; }
    
    [MaxLength(500)]
    public string SystemPrompt { get; set; }
    
    public DateTime LastRunTime { get; set; }
    
    public int RunFrequencyInHours { get; set; } // How often the agent should run
    
    [MaxLength(50)]
    public string Status { get; set; } // Running, Idle, Error, etc.
    
    [MaxLength(1000)]
    public string LastResult { get; set; } // Summary of the last run
    
    public int? StoreId { get; set; } // Optional store filter
    
    public int? BazaarId { get; set; } // Optional bazaar filter
    
    public int? CompetitorId { get; set; } // Optional competitor filter
    
    public int? CategoryId { get; set; } // Optional category filter

    public int? RelatedProductId { get; set; }

}