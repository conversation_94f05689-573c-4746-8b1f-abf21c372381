using EtyraCommerce.Application.DTOs.CustomResponse;
using MediatR;

namespace EtyraCommerce.Application.Services.Inventory.Commands
{
    /// <summary>
    /// Command to transfer stock between warehouses
    /// </summary>
    public class TransferStockCommand : IRequest<CustomResponseDto<bool>>
    {
        public Guid ProductId { get; set; }
        public Guid FromWarehouseId { get; set; }
        public Guid ToWarehouseId { get; set; }
        public int Quantity { get; set; }
        public string? Reference { get; set; }
        public string? Reason { get; set; }
        public Guid? UserId { get; set; }
    }
}
