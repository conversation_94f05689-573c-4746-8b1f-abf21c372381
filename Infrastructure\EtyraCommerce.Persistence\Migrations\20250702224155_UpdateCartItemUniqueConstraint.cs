﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EtyraCommerce.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UpdateCartItemUniqueConstraint : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_cart_items_cart_product",
                schema: "etyra_core",
                table: "cart_items");

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_cart_product_variant",
                schema: "etyra_core",
                table: "cart_items",
                columns: new[] { "ShoppingCartId", "ProductId", "VariantInfo" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_cart_items_products_ProductId",
                schema: "etyra_core",
                table: "cart_items",
                column: "ProductId",
                principalSchema: "etyra_core",
                principalTable: "products",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_cart_items_products_ProductId",
                schema: "etyra_core",
                table: "cart_items");

            migrationBuilder.DropIndex(
                name: "ix_cart_items_cart_product_variant",
                schema: "etyra_core",
                table: "cart_items");

            migrationBuilder.CreateIndex(
                name: "ix_cart_items_cart_product",
                schema: "etyra_core",
                table: "cart_items",
                columns: new[] { "ShoppingCartId", "ProductId" },
                unique: true);
        }
    }
}
