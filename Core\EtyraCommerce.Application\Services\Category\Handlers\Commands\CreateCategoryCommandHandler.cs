using EtyraCommerce.Application.DTOs.Category;
using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.Services.Category.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.Category.Handlers.Commands
{
    /// <summary>
    /// Handler for CreateCategoryCommand
    /// </summary>
    public class CreateCategoryCommandHandler : IRequestHandler<CreateCategoryCommand, CustomResponseDto<CategoryDto>>
    {
        private readonly ICategoryProcessService _categoryProcessService;
        private readonly ILogger<CreateCategoryCommandHandler> _logger;

        public CreateCategoryCommandHandler(
            ICategoryProcessService categoryProcessService,
            ILogger<CreateCategoryCommandHandler> logger)
        {
            _categoryProcessService = categoryProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<CategoryDto>> Handle(CreateCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing create category command for category: {CategoryName}", request.Name);

                // Validation
                if (string.IsNullOrWhiteSpace(request.Name))
                    return CustomResponseDto<CategoryDto>.BadRequest("Category name is required");

                if (request.Name.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category name cannot exceed 200 characters");

                if (!string.IsNullOrEmpty(request.Description) && request.Description.Length > 1000)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category description cannot exceed 1000 characters");

                if (!string.IsNullOrEmpty(request.Slug) && request.Slug.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Category slug cannot exceed 200 characters");

                if (!string.IsNullOrEmpty(request.ImageUrl) && request.ImageUrl.Length > 500)
                    return CustomResponseDto<CategoryDto>.BadRequest("Image URL cannot exceed 500 characters");

                if (!string.IsNullOrEmpty(request.Icon) && request.Icon.Length > 100)
                    return CustomResponseDto<CategoryDto>.BadRequest("Icon cannot exceed 100 characters");

                if (request.SortOrder < 0)
                    return CustomResponseDto<CategoryDto>.BadRequest("Sort order must be non-negative");

                if (!string.IsNullOrEmpty(request.MetaTitle) && request.MetaTitle.Length > 120)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta title cannot exceed 120 characters");

                if (!string.IsNullOrEmpty(request.MetaDescription) && request.MetaDescription.Length > 350)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta description cannot exceed 350 characters");

                if (!string.IsNullOrEmpty(request.MetaKeywords) && request.MetaKeywords.Length > 200)
                    return CustomResponseDto<CategoryDto>.BadRequest("Meta keywords cannot exceed 200 characters");

                // Validate descriptions
                if (request.Descriptions?.Any() == true)
                {
                    foreach (var description in request.Descriptions)
                    {
                        if (string.IsNullOrWhiteSpace(description.Name))
                            return CustomResponseDto<CategoryDto>.BadRequest("Description name is required for all languages");

                        if (description.Name.Length > 200)
                            return CustomResponseDto<CategoryDto>.BadRequest("Description name cannot exceed 200 characters");

                        if (!string.IsNullOrEmpty(description.Description) && description.Description.Length > 2000)
                            return CustomResponseDto<CategoryDto>.BadRequest("Description text cannot exceed 2000 characters");

                        if (string.IsNullOrWhiteSpace(description.LanguageCode))
                            return CustomResponseDto<CategoryDto>.BadRequest("Language code is required for all descriptions");
                    }

                    // Check for duplicate language codes
                    var duplicateLanguages = request.Descriptions
                        .GroupBy(d => d.LanguageCode.ToLowerInvariant())
                        .Where(g => g.Count() > 1)
                        .Select(g => g.Key)
                        .ToList();

                    if (duplicateLanguages.Any())
                        return CustomResponseDto<CategoryDto>.BadRequest($"Duplicate language codes found: {string.Join(", ", duplicateLanguages)}");
                }

                // Create DTO for business logic
                var createCategoryDto = new CreateCategoryDto
                {
                    Name = request.Name,
                    Description = request.Description,
                    Slug = request.Slug,
                    ParentCategoryId = request.ParentCategoryId,
                    ImageUrl = request.ImageUrl,
                    Icon = request.Icon,
                    SortOrder = request.SortOrder,
                    IsActive = request.IsActive,
                    ShowInMenu = request.ShowInMenu,
                    MetaTitle = request.MetaTitle,
                    MetaDescription = request.MetaDescription,
                    MetaKeywords = request.MetaKeywords,
                    Descriptions = request.Descriptions ?? new List<CreateCategoryDescriptionDto>()
                };

                // Delegate to process service
                var result = await _categoryProcessService.ProcessCreateCategoryAsync(createCategoryDto);

                _logger.LogInformation("Create category command processed successfully for category: {CategoryName}", request.Name);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing create category command for category: {CategoryName}", request.Name);
                return CustomResponseDto<CategoryDto>.InternalServerError("An error occurred while creating the category");
            }
        }
    }
}
