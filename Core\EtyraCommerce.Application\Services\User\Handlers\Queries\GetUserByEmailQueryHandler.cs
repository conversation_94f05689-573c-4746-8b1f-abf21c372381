using EtyraCommerce.Application.DTOs.CustomResponse;
using EtyraCommerce.Application.DTOs.User;
using EtyraCommerce.Application.Services.User.Queries;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EtyraCommerce.Application.Services.User.Handlers.Queries
{
    /// <summary>
    /// Handler for GetUserByEmailQuery - Validates and delegates to UserProcessService
    /// </summary>
    public class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQuery, CustomResponseDto<UserDto?>>
    {
        private readonly IUserProcessService _userProcessService;
        private readonly ILogger<GetUserByEmailQueryHandler> _logger;

        public GetUserByEmailQueryHandler(
            IUserProcessService userProcessService,
            ILogger<GetUserByEmailQueryHandler> logger)
        {
            _userProcessService = userProcessService;
            _logger = logger;
        }

        public async Task<CustomResponseDto<UserDto?>> Handle(GetUserByEmailQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing get user by email query for Email: {Email}", request.Email);

                // Validate email format
                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    _logger.LogWarning("Get user by email query failed: Email is required");
                    return CustomResponseDto<UserDto?>.BadRequest("Email is required");
                }

                // Delegate to UserProcessService for business logic
                var result = await _userProcessService.ProcessGetUserByEmailAsync(request.Email);

                if (result.IsSuccess)
                {
                    if (result.Data != null)
                    {
                        _logger.LogDebug("User found for Email: {Email}", request.Email);
                    }
                    else
                    {
                        _logger.LogDebug("User not found for Email: {Email}", request.Email);
                    }
                }
                else
                {
                    _logger.LogWarning("Error getting user by email for Email: {Email}. Reason: {Message}",
                        request.Email, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing get user by email query for Email: {Email}", request.Email);
                return CustomResponseDto<UserDto?>.InternalServerError("An error occurred while retrieving user by email");
            }
        }
    }
}
